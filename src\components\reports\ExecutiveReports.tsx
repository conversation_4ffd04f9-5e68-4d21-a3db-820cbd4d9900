import React, { useState, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '../ui/tabs';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  BarChart3, 
  Calendar,
  Download,
  FileText,
  Settings,
  Building
} from 'lucide-react';
import { subMonths, subYears } from 'date-fns';
import { Id } from '../../../convex/_generated/dataModel';

interface ExecutiveReportsProps {
  propertyIds?: Id<"properties">[];
}

interface PropertyPerformance {
  propertyId: string;
  propertyName: string;
  revenue: number;
  expenses: number;
  netIncome: number;
  occupancyRate: number;
  unitCount: number;
  occupiedUnits: number;
}

interface ReportConfig {
  _id: string;
  name: string;
  description: string;
  templateId: string;
  sections: string[];
  schedule?: {
    frequency: string;
  };
}

export const ExecutiveReports: React.FC<ExecutiveReportsProps> = ({ propertyIds }) => {
  const [dateRange, setDateRange] = useState<'monthly' | 'quarterly' | 'annual'>('monthly');
  const [selectedTab, setSelectedTab] = useState('summary');

  // Calculate date range
  const { startDate, endDate } = useMemo(() => {
    const now = new Date();
    let start: Date;
    
    switch (dateRange) {
      case 'monthly':
        start = subMonths(now, 1);
        break;
      case 'quarterly':
        start = subMonths(now, 3);
        break;
      case 'annual':
        start = subYears(now, 1);
        break;
      default:
        start = subMonths(now, 1);
    }
    
    return {
      startDate: start.getTime(),
      endDate: now.getTime(),
    };
  }, [dateRange]);

  // Fetch executive summary data
  const executiveData = useQuery(api.executiveReports.generateExecutiveSummary, {
    propertyIds,
    startDate,
    endDate,
    reportType: dateRange,
  });

  const reportTemplates = useQuery(api.executiveReports.getReportTemplates);
  const reportConfigs = useQuery(api.executiveReports.getReportConfigurations);

  if (!executiveData || !reportTemplates) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading executive reports...</p>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const { executiveSummary, keyMetrics, propertyPerformance } = executiveData;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Executive Reports</h1>
          <p className="text-gray-600">
            Board-level insights and comprehensive analytics
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={dateRange} onValueChange={(value: 'monthly' | 'quarterly' | 'annual') => setDateRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="annual">Annual</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Custom Report
          </Button>
          
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Executive Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(executiveSummary.totalRevenue)}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {executiveSummary.revenueGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1 text-red-600" />
              )}
              <span className={executiveSummary.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'}>
                {formatPercentage(Math.abs(executiveSummary.revenueGrowth))}
              </span>
              <span className="ml-1">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Income</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(executiveSummary.netIncome)}</div>
            <div className="text-xs text-muted-foreground">
              Profit Margin: {formatPercentage(executiveSummary.profitMargin)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Occupancy Rate</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(executiveSummary.occupancyRate)}</div>
            <div className="flex items-center gap-2 text-xs">
              <Badge variant={executiveSummary.occupancyRate >= 90 ? "default" : "secondary"}>
                {executiveSummary.occupancyRate >= 90 ? "Excellent" : "Good"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Collection Rate</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(executiveSummary.collectionRate)}</div>
            <div className="flex items-center gap-2 text-xs">
              <Badge variant={executiveSummary.collectionRate >= 90 ? "default" : "destructive"}>
                {executiveSummary.collectionRate >= 90 ? "Excellent" : "Needs Attention"}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="summary">Executive Summary</TabsTrigger>
          <TabsTrigger value="performance">Property Performance</TabsTrigger>
          <TabsTrigger value="operations">Operations</TabsTrigger>
          <TabsTrigger value="reports">Saved Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Financial Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Financial Overview</CardTitle>
                <CardDescription>Key financial metrics for the period</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-600">Revenue</div>
                    <div className="text-xl font-bold text-green-600">
                      {formatCurrency(keyMetrics.financial.totalRevenue)}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Expenses</div>
                    <div className="text-xl font-bold text-red-600">
                      {formatCurrency(keyMetrics.financial.totalExpenses)}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Pending</div>
                    <div className="text-lg font-semibold text-yellow-600">
                      {formatCurrency(keyMetrics.financial.pendingAmount)}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Overdue</div>
                    <div className="text-lg font-semibold text-red-600">
                      {formatCurrency(keyMetrics.financial.overdueAmount)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Operational Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Operational Metrics</CardTitle>
                <CardDescription>Key operational performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-600">Total Units</div>
                    <div className="text-xl font-bold">
                      {keyMetrics.operational.totalUnits}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Occupied</div>
                    <div className="text-xl font-bold text-blue-600">
                      {keyMetrics.operational.occupiedUnits}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Maintenance Tickets</div>
                    <div className="text-lg font-semibold">
                      {keyMetrics.operational.totalTickets}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Avg Resolution</div>
                    <div className="text-lg font-semibold">
                      {keyMetrics.operational.avgResolutionTime.toFixed(1)} days
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Analytics */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Analytics</CardTitle>
              <CardDescription>Payment method distribution and success rates</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {formatPercentage(keyMetrics.payments.successRate)}
                  </div>
                  <div className="text-sm text-gray-600">Success Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {keyMetrics.payments.mpesaPayments}
                  </div>
                  <div className="text-sm text-gray-600">M-PESA Payments</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {keyMetrics.payments.stripePayments}
                  </div>
                  <div className="text-sm text-gray-600">Stripe Payments</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Property Performance Comparison</CardTitle>
              <CardDescription>Revenue, expenses, and occupancy by property</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {propertyPerformance.map((property: PropertyPerformance) => (
                  <div key={property.propertyId} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="font-semibold text-lg">{property.propertyName}</h3>
                      <Badge variant={property.occupancyRate >= 90 ? "default" : "secondary"}>
                        {formatPercentage(property.occupancyRate)} Occupied
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div>
                        <div className="text-sm text-gray-600">Revenue</div>
                        <div className="text-lg font-semibold text-green-600">
                          {formatCurrency(property.revenue)}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Expenses</div>
                        <div className="text-lg font-semibold text-red-600">
                          {formatCurrency(property.expenses)}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Net Income</div>
                        <div className={`text-lg font-semibold ${property.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {formatCurrency(property.netIncome)}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Units</div>
                        <div className="text-lg font-semibold">
                          {property.occupiedUnits}/{property.unitCount}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="operations" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Maintenance Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Total Tickets</span>
                    <span className="font-semibold">{keyMetrics.operational.totalTickets}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Completed</span>
                    <span className="font-semibold text-green-600">{keyMetrics.operational.completedTickets}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Completion Rate</span>
                    <span className="font-semibold">
                      {formatPercentage(keyMetrics.operational.totalTickets > 0 ? 
                        (keyMetrics.operational.completedTickets / keyMetrics.operational.totalTickets) * 100 : 0)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg Resolution Time</span>
                    <span className="font-semibold">{keyMetrics.operational.avgResolutionTime.toFixed(1)} days</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Occupancy Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Current Occupancy</span>
                    <span className="font-semibold">{formatPercentage(executiveSummary.occupancyRate)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Units</span>
                    <span className="font-semibold">{keyMetrics.operational.totalUnits}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Occupied Units</span>
                    <span className="font-semibold text-blue-600">{keyMetrics.operational.occupiedUnits}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Vacant Units</span>
                    <span className="font-semibold text-yellow-600">
                      {keyMetrics.operational.totalUnits - keyMetrics.operational.occupiedUnits}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Saved Report Configurations</h3>
            <Button>
              <FileText className="h-4 w-4 mr-2" />
              Create New Report
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {reportConfigs?.map((config: ReportConfig) => (
              <Card key={config._id}>
                <CardHeader>
                  <CardTitle className="text-base">{config.name}</CardTitle>
                  <CardDescription>{config.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-sm">
                      <span className="font-medium">Template:</span> {config.templateId}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">Sections:</span> {config.sections.length}
                    </div>
                    {config.schedule && (
                      <Badge variant="outline">
                        Scheduled: {config.schedule.frequency}
                      </Badge>
                    )}
                  </div>
                  <div className="flex gap-2 mt-4">
                    <Button size="sm" variant="outline">Generate</Button>
                    <Button size="sm" variant="outline">Edit</Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Report Builder Modal */}
      {showReportBuilder && (
        <ReportBuilder
          templates={reportTemplates}
          onClose={() => setShowReportBuilder(false)}
          onSave={() => {
            setShowReportBuilder(false);
            // Refresh report configs
          }}
        />
      )}

      {/* Report Exporter Modal */}
      {showExporter && (
        <ReportExporter
          reportData={executiveData}
          onClose={() => setShowExporter(false)}
        />
      )}
    </div>
  );
};
