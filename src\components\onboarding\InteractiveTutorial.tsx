/**
 * Interactive tutorial component for guided learning
 */
import React, { useState, useEffect } from 'react';
import { useOnboarding } from './OnboardingProvider';
import { useAccessibility } from '../../contexts/AccessibilityContext';
import { AccessibleButton } from '../accessibility/AccessibleButton';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Separator } from '../ui/separator';

interface TutorialStep {
  id: string;
  title: string;
  description: string;
  instruction: string;
  expectedAction?: string;
  validation?: () => boolean;
  hint?: string;
}

interface Tutorial {
  id: string;
  title: string;
  description: string;
  estimatedTime: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  steps: TutorialStep[];
}

const tutorials: Tutorial[] = [
  {
    id: 'first-property',
    title: 'Add Your First Property',
    description: 'Learn how to add a property to your portfolio step by step',
    estimatedTime: '5 minutes',
    difficulty: 'beginner',
    steps: [
      {
        id: 'navigate-properties',
        title: 'Navigate to Properties',
        description: 'First, we need to go to the Properties section',
        instruction: 'Click on the "Properties" button in the navigation menu',
        expectedAction: 'navigate-to-properties',
        validation: () => window.location.pathname.includes('/properties'),
      },
      {
        id: 'click-add-property',
        title: 'Start Adding Property',
        description: 'Now we\'ll create a new property',
        instruction: 'Click the "Add Property" button',
        expectedAction: 'click-add-property',
        hint: 'Look for a button with a plus icon or "Add Property" text',
      },
      {
        id: 'fill-property-name',
        title: 'Enter Property Name',
        description: 'Give your property a descriptive name',
        instruction: 'Enter a name for your property in the "Property Name" field',
        expectedAction: 'fill-property-name',
        hint: 'Use a name that helps you identify the property, like "Sunset Apartments" or "Downtown Office Complex"',
      },
      {
        id: 'select-property-type',
        title: 'Choose Property Type',
        description: 'Select the type of property you\'re adding',
        instruction: 'Select the appropriate property type from the dropdown',
        expectedAction: 'select-property-type',
        hint: 'Choose Residential for apartments/houses, Commercial for offices/retail, or Mixed for combined use',
      },
      {
        id: 'save-property',
        title: 'Save Your Property',
        description: 'Complete the property creation process',
        instruction: 'Click the "Save Property" button to create your property',
        expectedAction: 'save-property',
      },
    ],
  },
  {
    id: 'accessibility-setup',
    title: 'Customize Accessibility Settings',
    description: 'Learn how to personalize EstatePulse for your accessibility needs',
    estimatedTime: '3 minutes',
    difficulty: 'beginner',
    steps: [
      {
        id: 'open-accessibility',
        title: 'Open Accessibility Settings',
        description: 'Access the accessibility customization options',
        instruction: 'Click on "Accessibility" in the navigation menu',
        expectedAction: 'navigate-to-accessibility',
        validation: () => window.location.pathname.includes('/accessibility'),
      },
      {
        id: 'choose-theme',
        title: 'Select a Theme',
        description: 'Choose a color theme that works best for you',
        instruction: 'Try selecting different themes from the "Color Theme" dropdown',
        expectedAction: 'select-theme',
        hint: 'High contrast themes can improve visibility, and color-blind friendly themes help with color differentiation',
      },
      {
        id: 'adjust-font-size',
        title: 'Adjust Font Size',
        description: 'Set a comfortable text size for reading',
        instruction: 'Change the font size setting to your preference',
        expectedAction: 'adjust-font-size',
        hint: 'Larger font sizes can make text easier to read',
      },
      {
        id: 'test-keyboard-navigation',
        title: 'Test Keyboard Navigation',
        description: 'Try navigating using only your keyboard',
        instruction: 'Press Tab to move between elements and Enter to activate buttons',
        expectedAction: 'test-keyboard',
        hint: 'Use Tab to move forward, Shift+Tab to move backward, and Enter or Space to activate',
      },
    ],
  },
];

interface InteractiveTutorialProps {
  tutorialId?: string;
  onComplete?: () => void;
  onExit?: () => void;
}

export function InteractiveTutorial({ 
  tutorialId, 
  onComplete, 
  onExit 
}: InteractiveTutorialProps) {
  const { announceToScreenReader } = useAccessibility();
  const [selectedTutorial, setSelectedTutorial] = useState<Tutorial | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    if (tutorialId) {
      const tutorial = tutorials.find(t => t.id === tutorialId);
      if (tutorial) {
        startTutorial(tutorial);
      }
    }
  }, [tutorialId]);

  const startTutorial = (tutorial: Tutorial) => {
    setSelectedTutorial(tutorial);
    setCurrentStep(0);
    setCompletedSteps(new Set());
    setIsActive(true);
    announceToScreenReader(`Starting tutorial: ${tutorial.title}. ${tutorial.steps.length} steps total.`);
  };

  const nextStep = () => {
    if (!selectedTutorial) return;

    const step = selectedTutorial.steps[currentStep];
    const newCompletedSteps = new Set(completedSteps);
    newCompletedSteps.add(step.id);
    setCompletedSteps(newCompletedSteps);

    if (currentStep < selectedTutorial.steps.length - 1) {
      const newStepIndex = currentStep + 1;
      setCurrentStep(newStepIndex);
      const nextStep = selectedTutorial.steps[newStepIndex];
      announceToScreenReader(`Step ${newStepIndex + 1}: ${nextStep.title}. ${nextStep.instruction}`);
    } else {
      completeTutorial();
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      const newStepIndex = currentStep - 1;
      setCurrentStep(newStepIndex);
      const prevStep = selectedTutorial.steps[newStepIndex];
      announceToScreenReader(`Step ${newStepIndex + 1}: ${prevStep.title}. ${prevStep.instruction}`);
    }
  };

  const completeTutorial = () => {
    if (!selectedTutorial) return;

    announceToScreenReader(`Tutorial completed: ${selectedTutorial.title}`);
    setIsActive(false);
    onComplete?.();
  };

  const exitTutorial = () => {
    if (!selectedTutorial) return;

    announceToScreenReader(`Tutorial exited: ${selectedTutorial.title}`);
    setIsActive(false);
    setSelectedTutorial(null);
    onExit?.();
  };

  const validateStep = () => {
    if (!selectedTutorial) return false;
    
    const step = selectedTutorial.steps[currentStep];
    if (step.validation) {
      return step.validation();
    }
    return true; // If no validation function, assume step is complete
  };

  if (!selectedTutorial && !tutorialId) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Interactive Tutorials</h1>
          <p className="text-muted-foreground mt-2">
            Learn EstatePulse features through hands-on, step-by-step tutorials.
          </p>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          {tutorials.map(tutorial => (
            <Card
              key={tutorial.id}
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => startTutorial(tutorial)}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  startTutorial(tutorial);
                }
              }}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <Badge variant="secondary" className="capitalize">
                    {tutorial.difficulty}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {tutorial.estimatedTime}
                  </span>
                </div>
                <CardTitle>{tutorial.title}</CardTitle>
                <CardDescription>{tutorial.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {tutorial.steps.length} steps
                  </span>
                  <AccessibleButton size="sm">
                    Start Tutorial
                  </AccessibleButton>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!isActive || !selectedTutorial) {
    return null;
  }

  const step = selectedTutorial.steps[currentStep];
  const progress = ((currentStep + 1) / selectedTutorial.steps.length) * 100;
  const isLastStep = currentStep === selectedTutorial.steps.length - 1;

  return (
    <div className="fixed bottom-4 right-4 w-96 z-40">
      <Card className="shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Badge variant="secondary" className="text-xs">
              Tutorial
            </Badge>
            <AccessibleButton
              variant="ghost"
              size="sm"
              onClick={exitTutorial}
              aria-label="Exit tutorial"
              className="h-6 w-6 p-0"
            >
              ×
            </AccessibleButton>
          </div>
          <CardTitle className="text-lg">{selectedTutorial.title}</CardTitle>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Step {currentStep + 1} of {selectedTutorial.steps.length}</span>
              <span>{Math.round(progress)}% complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">{step.title}</h3>
            <p className="text-sm text-muted-foreground mb-3">
              {step.description}
            </p>
            <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-md">
              <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                {step.instruction}
              </p>
            </div>
            {step.hint && (
              <div className="bg-yellow-50 dark:bg-yellow-950 p-3 rounded-md mt-2">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  💡 Hint: {step.hint}
                </p>
              </div>
            )}
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <AccessibleButton
              variant="outline"
              size="sm"
              onClick={previousStep}
              disabled={currentStep === 0}
            >
              Previous
            </AccessibleButton>

            <div className="flex gap-2">
              <AccessibleButton
                variant="ghost"
                size="sm"
                onClick={exitTutorial}
              >
                Exit
              </AccessibleButton>
              
              <AccessibleButton
                size="sm"
                onClick={nextStep}
                disabled={step.validation && !validateStep()}
              >
                {isLastStep ? 'Complete' : 'Next'}
              </AccessibleButton>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            Follow the instructions above to continue
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Tutorial launcher component
export function TutorialLauncher() {
  const [showTutorials, setShowTutorials] = useState(false);

  return (
    <>
      <AccessibleButton
        variant="outline"
        size="sm"
        onClick={() => setShowTutorials(true)}
        aria-label="Start interactive tutorial"
      >
        📚 Tutorials
      </AccessibleButton>

      {showTutorials && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="p-6">
              <InteractiveTutorial
                onComplete={() => setShowTutorials(false)}
                onExit={() => setShowTutorials(false)}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
}