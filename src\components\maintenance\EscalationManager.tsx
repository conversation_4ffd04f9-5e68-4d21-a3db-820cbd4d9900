import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { AlertTriangle, ArrowUp, Clock, User, MessageSquare } from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { useToast } from '../ui/use-toast';

interface EscalationManagerProps {
  ticketId: Id<"maintenanceTickets">;
  ticket: any;
  onEscalationComplete: () => void;
}

export const EscalationManager: React.FC<EscalationManagerProps> = ({
  ticketId,
  ticket,
  onEscalationComplete
}) => {
  const { toast } = useToast();
  const [escalationReason, setEscalationReason] = useState('');
  const [escalatedTo, setEscalatedTo] = useState<Id<"users"> | ''>('');
  const [notes, setNotes] = useState('');
  const [isEscalating, setIsEscalating] = useState(false);

  // Fetch potential escalation targets (managers, owners)
  const escalationTargets = useQuery(api.users.getEscalationTargets, {
    propertyId: ticket.propertyId
  });

  // Fetch existing escalations for this ticket
  const existingEscalations = useQuery(api.maintenance.getTicketEscalations, {
    ticketId
  });

  // Escalate ticket mutation
  const escalateTicket = useMutation(api.maintenance.escalateTicket);

  const escalationReasons = [
    { value: 'sla_breach', label: 'SLA Breach', description: 'Ticket has exceeded SLA deadline' },
    { value: 'vendor_unavailable', label: 'Vendor Unavailable', description: 'Assigned vendor is not responding' },
    { value: 'complexity', label: 'Complexity', description: 'Issue is more complex than initially assessed' },
    { value: 'cost_approval', label: 'Cost Approval', description: 'Requires approval for additional costs' },
    { value: 'tenant_complaint', label: 'Tenant Complaint', description: 'Tenant has escalated the issue' },
    { value: 'manual', label: 'Manual Escalation', description: 'Manual escalation by staff' }
  ];

  const handleEscalation = async () => {
    if (!escalationReason || !escalatedTo) {
      toast({
        title: "Error",
        description: "Please select escalation reason and target",
        variant: "destructive",
      });
      return;
    }

    if (!notes.trim()) {
      toast({
        title: "Error",
        description: "Please provide escalation notes",
        variant: "destructive",
      });
      return;
    }

    setIsEscalating(true);

    try {
      await escalateTicket({
        ticketId,
        escalatedTo: escalatedTo as Id<"users">,
        reason: escalationReason as any,
        notes: notes.trim()
      });

      toast({
        title: "Success",
        description: "Ticket escalated successfully",
      });

      onEscalationComplete();
    } catch (error) {
      console.error('Error escalating ticket:', error);
      toast({
        title: "Error",
        description: "Failed to escalate ticket. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsEscalating(false);
    }
  };

  const getEscalationLevelColor = (level: number) => {
    switch (level) {
      case 1: return 'bg-yellow-500';
      case 2: return 'bg-orange-500';
      case 3: return 'bg-red-500';
      default: return 'bg-purple-500';
    }
  };

  const shouldAutoEscalate = () => {
    const now = Date.now();
    const isOverdue = now > ticket.slaDeadline;
    const isNearDeadline = now > (ticket.slaDeadline - 2 * 60 * 60 * 1000); // 2 hours before deadline
    
    return isOverdue || (isNearDeadline && ticket.status === 'open');
  };

  const getAutoEscalationReason = () => {
    const now = Date.now();
    if (now > ticket.slaDeadline) {
      return 'sla_breach';
    }
    if (ticket.status === 'open' && now > (ticket.slaDeadline - 2 * 60 * 60 * 1000)) {
      return 'sla_breach';
    }
    return '';
  };

  React.useEffect(() => {
    const autoReason = getAutoEscalationReason();
    if (autoReason && !escalationReason) {
      setEscalationReason(autoReason);
    }
  }, [ticket]);

  return (
    <div className="space-y-6">
      {/* Escalation Alert */}
      {shouldAutoEscalate() && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertTriangle className="h-5 w-5" />
              <div>
                <h3 className="font-semibold">Escalation Required</h3>
                <p className="text-sm">
                  This ticket {Date.now() > ticket.slaDeadline ? 'has exceeded' : 'is approaching'} its SLA deadline and requires escalation.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Existing Escalations */}
      {existingEscalations && existingEscalations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ArrowUp className="h-5 w-5" />
              Escalation History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {existingEscalations.map((escalation, index) => (
                <div key={escalation._id} className="flex items-start gap-3 p-3 border rounded-lg">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${getEscalationLevelColor(escalation.escalationLevel)}`}>
                    {escalation.escalationLevel}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">Level {escalation.escalationLevel} Escalation</span>
                      <Badge variant="outline" className="capitalize">
                        {escalation.reason.replace('_', ' ')}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      Escalated to: <span className="font-medium">Manager</span> • {formatDistanceToNow(new Date(escalation.createdAt), { addSuffix: true })}
                    </p>
                    {escalation.resolution && (
                      <div className="text-sm bg-green-50 p-2 rounded border-l-4 border-green-500">
                        <strong>Resolution:</strong> {escalation.resolution}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* New Escalation Form */}
      <Card>
        <CardHeader>
          <CardTitle>Escalate Ticket</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Escalation Reason */}
          <div className="space-y-2">
            <Label htmlFor="escalationReason">Escalation Reason</Label>
            <Select value={escalationReason} onValueChange={setEscalationReason}>
              <SelectTrigger>
                <SelectValue placeholder="Select escalation reason" />
              </SelectTrigger>
              <SelectContent>
                {escalationReasons.map((reason) => (
                  <SelectItem key={reason.value} value={reason.value}>
                    <div>
                      <div className="font-medium">{reason.label}</div>
                      <div className="text-sm text-muted-foreground">{reason.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Escalation Target */}
          <div className="space-y-2">
            <Label htmlFor="escalatedTo">Escalate To</Label>
            <Select value={escalatedTo} onValueChange={setEscalatedTo}>
              <SelectTrigger>
                <SelectValue placeholder="Select escalation target" />
              </SelectTrigger>
              <SelectContent>
                {escalationTargets?.map((target) => (
                  <SelectItem key={target._id} value={target._id}>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <div>
                        <div className="font-medium">{target.name}</div>
                        <div className="text-sm text-muted-foreground capitalize">{target.role}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Escalation Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Escalation Notes</Label>
            <Textarea
              id="notes"
              placeholder="Provide detailed information about why this ticket needs escalation..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={4}
              required
            />
          </div>

          {/* Current Ticket Status */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">Current Ticket Status</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Priority:</span>
                <Badge className={`ml-2 ${
                  ticket.priority === 'emergency' ? 'bg-red-500' :
                  ticket.priority === 'high' ? 'bg-orange-500' :
                  ticket.priority === 'medium' ? 'bg-yellow-500' :
                  'bg-green-500'
                } text-white`}>
                  {ticket.priority}
                </Badge>
              </div>
              <div>
                <span className="text-muted-foreground">Status:</span>
                <Badge className="ml-2" variant="outline">
                  {ticket.status.replace('_', ' ')}
                </Badge>
              </div>
              <div>
                <span className="text-muted-foreground">SLA Deadline:</span>
                <span className={`ml-2 font-medium ${
                  Date.now() > ticket.slaDeadline ? 'text-red-600' : 'text-green-600'
                }`}>
                  {format(new Date(ticket.slaDeadline), 'MMM d, HH:mm')}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Created:</span>
                <span className="ml-2">{formatDistanceToNow(new Date(ticket.createdAt), { addSuffix: true })}</span>
              </div>
            </div>
          </div>

          <Separator />

          <div className="flex justify-end gap-4">
            <Button variant="outline" onClick={onEscalationComplete}>
              Cancel
            </Button>
            <Button 
              onClick={handleEscalation} 
              disabled={!escalationReason || !escalatedTo || !notes.trim() || isEscalating}
              className="bg-red-600 hover:bg-red-700"
            >
              {isEscalating ? 'Escalating...' : 'Escalate Ticket'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};