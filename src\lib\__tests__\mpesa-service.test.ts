import { describe, it, expect, vi, beforeEach } from "vitest";
import { MPESAService } from "../mpesa-service";
import { ConvexReactClient } from "convex/react";

// Mock ConvexReactClient
const mockConvex = {
  mutation: vi.fn(),
  action: vi.fn(),
  query: vi.fn(),
} as unknown as ConvexReactClient;

describe("MPESAService", () => {
  let mpesaService: MPESAService;

  beforeEach(() => {
    vi.clearAllMocks();
    mpesaService = new MPESAService(mockConvex);
  });

  describe("initiatePayment", () => {
    it("should successfully initiate payment with valid data", async () => {
      const mockPaymentResult = { paymentId: "payment123", status: "initiated" };
      const mockSTKResult = {
        success: true,
        checkoutRequestId: "ws_CO_123456789",
        merchantRequestId: "merchant123",
        message: "STK Push sent successfully",
      };

      (mockConvex.mutation as any).mockResolvedValue(mockPaymentResult);
      (mockConvex.action as any).mockResolvedValue(mockSTKResult);

      const result = await mpesaService.initiatePayment({
        invoiceId: "invoice123" as any,
        phoneNumber: "0712345678",
        amount: 1000,
        reference: "INV001",
      });

      expect(result.success).toBe(true);
      expect(result.paymentId).toBe("payment123");
      expect(result.checkoutRequestId).toBe("ws_CO_123456789");
      expect(mockConvex.mutation).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          phoneNumber: "254712345678", // Should format phone number
          amount: 1000,
        })
      );
    });

    it("should reject invalid phone numbers", async () => {
      const result = await mpesaService.initiatePayment({
        invoiceId: "invoice123" as any,
        phoneNumber: "123", // Invalid phone number
        amount: 1000,
        reference: "INV001",
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe("Phone number must be a valid Kenyan mobile number");
    });

    it("should handle payment initiation failure", async () => {
      (mockConvex.mutation as any).mockRejectedValue(new Error("Database error"));

      const result = await mpesaService.initiatePayment({
        invoiceId: "invoice123" as any,
        phoneNumber: "0712345678",
        amount: 1000,
        reference: "INV001",
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe("Database error");
    });
  });

  describe("checkPaymentStatus", () => {
    it("should return completed status for successful payment", async () => {
      const mockStatusResult = {
        success: true,
        resultCode: 0,
        resultDesc: "The service request is processed successfully.",
      };

      (mockConvex.action as any).mockResolvedValue(mockStatusResult);

      const result = await mpesaService.checkPaymentStatus("ws_CO_123456789");

      expect(result.status).toBe("completed");
      expect(result.message).toBe("Payment completed successfully");
    });

    it("should return failed status for cancelled payment", async () => {
      const mockStatusResult = {
        success: true,
        resultCode: 1032,
        resultDesc: "Request cancelled by user",
      };

      (mockConvex.action as any).mockResolvedValue(mockStatusResult);

      const result = await mpesaService.checkPaymentStatus("ws_CO_123456789");

      expect(result.status).toBe("failed");
      expect(result.message).toBe("Payment cancelled by user");
    });

    it("should return pending status for processing payment", async () => {
      const mockStatusResult = {
        success: true,
        resultCode: 1037,
        resultDesc: "DS timeout user cannot be reached",
      };

      (mockConvex.action as any).mockResolvedValue(mockStatusResult);

      const result = await mpesaService.checkPaymentStatus("ws_CO_123456789");

      expect(result.status).toBe("pending");
    });
  });

  describe("phone number formatting", () => {
    it("should format Kenyan phone numbers correctly", async () => {
      const testCases = [
        { input: "0712345678", expected: "254712345678" },
        { input: "254712345678", expected: "254712345678" },
        { input: "712345678", expected: "254712345678" },
        { input: "+254712345678", expected: "254712345678" },
      ];

      for (const testCase of testCases) {
        (mockConvex.mutation as any).mockResolvedValue({ paymentId: "test", status: "initiated" });
        (mockConvex.action as any).mockResolvedValue({ success: true });

        await mpesaService.initiatePayment({
          invoiceId: "invoice123" as any,
          phoneNumber: testCase.input,
          amount: 1000,
          reference: "INV001",
        });

        expect(mockConvex.mutation).toHaveBeenCalledWith(
          expect.any(Object),
          expect.objectContaining({
            phoneNumber: testCase.expected,
          })
        );
      }
    });
  });

  describe("static methods", () => {
    it("should format amount correctly", () => {
      const formatted = MPESAService.formatAmount(1000);
      expect(formatted).toMatch(/KES\s*1,000\.00/);
    });

    it("should provide correct result code descriptions", () => {
      expect(MPESAService.getResultCodeDescription(0)).toBe("Success");
      expect(MPESAService.getResultCodeDescription(1032)).toBe("Cancelled by User");
      expect(MPESAService.getResultCodeDescription(9999)).toBe("Request cancelled by user");
      expect(MPESAService.getResultCodeDescription(99999)).toBe("Unknown error (Code: 99999)");
    });
  });

  describe("getPaymentDetails", () => {
    it("should return payment details", async () => {
      const mockPayment = {
        _id: "payment123",
        amount: 1000,
        status: "completed",
        method: "mpesa",
      };

      (mockConvex.query as any).mockResolvedValue(mockPayment);

      const result = await mpesaService.getPaymentDetails("payment123" as any);

      expect(result).toEqual(mockPayment);
      expect(mockConvex.query).toHaveBeenCalledWith(
        expect.any(Object),
        { paymentId: "payment123" }
      );
    });

    it("should handle errors gracefully", async () => {
      (mockConvex.query as any).mockRejectedValue(new Error("Network error"));

      const result = await mpesaService.getPaymentDetails("payment123" as any);

      expect(result).toBeNull();
    });
  });

  describe("getInvoicePayments", () => {
    it("should return payments for an invoice", async () => {
      const mockPayments = [
        { _id: "payment1", amount: 500, status: "completed" },
        { _id: "payment2", amount: 500, status: "pending" },
      ];

      (mockConvex.query as any).mockResolvedValue(mockPayments);

      const result = await mpesaService.getInvoicePayments("invoice123" as any);

      expect(result).toEqual(mockPayments);
      expect(mockConvex.query).toHaveBeenCalledWith(
        expect.any(Object),
        { invoiceId: "invoice123" }
      );
    });

    it("should return empty array on error", async () => {
      (mockConvex.query as any).mockRejectedValue(new Error("Network error"));

      const result = await mpesaService.getInvoicePayments("invoice123" as any);

      expect(result).toEqual([]);
    });
  });
});