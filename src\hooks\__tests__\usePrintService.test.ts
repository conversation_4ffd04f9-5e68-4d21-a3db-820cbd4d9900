import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { usePrintService } from '../usePrintService'

const mockElectronAPI = {
  printDocument: vi.fn(),
  printToPDF: vi.fn()
}

// Mock window.open for browser fallback
const mockWindowOpen = vi.fn()
const mockPrintWindow = {
  document: {
    write: vi.fn(),
    close: vi.fn()
  },
  print: vi.fn(),
  close: vi.fn()
}

beforeEach(() => {
  // @ts-ignore
  global.window.electronAPI = mockElectronAPI
  // @ts-ignore
  global.window.open = mockWindowOpen
  mockWindowOpen.mockReturnValue(mockPrintWindow)
  vi.clearAllMocks()
})

describe('usePrintService', () => {
  it('prints document using native Electron API', async () => {
    mockElectronAPI.printDocument.mockResolvedValue({ success: true })

    const { result } = renderHook(() => usePrintService())

    await act(async () => {
      const printResult = await result.current.printDocument('<html><body>Test</body></html>', {
        color: true,
        landscape: false
      })

      expect(mockElectronAPI.printDocument).toHaveBeenCalledWith(
        '<html><body>Test</body></html>',
        { color: true, landscape: false }
      )
      expect(printResult.success).toBe(true)
    })
  })

  it('falls back to browser print when Electron API unavailable', async () => {
    // @ts-ignore
    global.window.electronAPI = undefined

    const { result } = renderHook(() => usePrintService())

    await act(async () => {
      const printResult = await result.current.printDocument('<html><body>Test</body></html>')

      expect(mockWindowOpen).toHaveBeenCalledWith('', '_blank')
      expect(mockPrintWindow.document.write).toHaveBeenCalledWith('<html><body>Test</body></html>')
      expect(mockPrintWindow.print).toHaveBeenCalled()
      expect(mockPrintWindow.close).toHaveBeenCalled()
      expect(printResult.success).toBe(true)
    })
  })

  it('handles print failure in browser fallback', async () => {
    // @ts-ignore
    global.window.electronAPI = undefined
    mockWindowOpen.mockReturnValue(null)

    const { result } = renderHook(() => usePrintService())

    await act(async () => {
      const printResult = await result.current.printDocument('<html><body>Test</body></html>')

      expect(printResult.success).toBe(false)
      expect(printResult.error).toBe('Could not open print window')
    })
  })

  it('generates PDF using Electron API', async () => {
    const mockBuffer = [1, 2, 3, 4, 5]
    mockElectronAPI.printToPDF.mockResolvedValue({
      success: true,
      buffer: mockBuffer
    })

    const { result } = renderHook(() => usePrintService())

    await act(async () => {
      const pdfResult = await result.current.printToPDF('<html><body>Test</body></html>', {
        pageSize: 'A4',
        landscape: false
      })

      expect(mockElectronAPI.printToPDF).toHaveBeenCalledWith(
        '<html><body>Test</body></html>',
        { pageSize: 'A4', landscape: false }
      )
      expect(pdfResult.success).toBe(true)
      expect(pdfResult.blob).toBeInstanceOf(Blob)
    })
  })

  it('throws error for PDF generation in web environment', async () => {
    // @ts-ignore
    global.window.electronAPI = undefined

    const { result } = renderHook(() => usePrintService())

    await act(async () => {
      await expect(result.current.printToPDF('<html><body>Test</body></html>')).rejects.toThrow(
        'PDF generation not available in web environment'
      )
    })
  })

  it('downloads PDF with generated filename', async () => {
    const mockBuffer = [1, 2, 3, 4, 5]
    mockElectronAPI.printToPDF.mockResolvedValue({
      success: true,
      buffer: mockBuffer
    })

    // Mock URL.createObjectURL and document methods
    const mockURL = 'blob:mock-url'
    const mockCreateObjectURL = vi.fn().mockReturnValue(mockURL)
    const mockRevokeObjectURL = vi.fn()
    const mockClick = vi.fn()
    const mockAnchor = {
      href: '',
      download: '',
      click: mockClick
    }

    // @ts-ignore
    global.URL = {
      createObjectURL: mockCreateObjectURL,
      revokeObjectURL: mockRevokeObjectURL
    }
    
    // @ts-ignore
    global.document = {
      createElement: vi.fn().mockReturnValue(mockAnchor),
      body: {
        appendChild: vi.fn(),
        removeChild: vi.fn()
      }
    }

    const { result } = renderHook(() => usePrintService())

    await act(async () => {
      const downloadResult = await result.current.downloadPDF(
        '<html><body>Test</body></html>',
        'test.pdf'
      )

      expect(downloadResult.success).toBe(true)
      expect(mockAnchor.href).toBe(mockURL)
      expect(mockAnchor.download).toBe('test.pdf')
      expect(mockClick).toHaveBeenCalled()
      expect(mockRevokeObjectURL).toHaveBeenCalledWith(mockURL)
    })
  })

  it('prints property report with formatted data', async () => {
    mockElectronAPI.printDocument.mockResolvedValue({ success: true })

    const propertyData = {
      name: 'Test Property',
      address: '123 Test St',
      type: 'residential',
      totalUnits: 100,
      occupiedUnits: 85,
      monthlyRevenue: 50000,
      annualRevenue: 600000,
      outstandingPayments: 5000
    }

    const { result } = renderHook(() => usePrintService())

    await act(async () => {
      const printResult = await result.current.printPropertyReport(propertyData)

      expect(mockElectronAPI.printDocument).toHaveBeenCalled()
      const htmlContent = mockElectronAPI.printDocument.mock.calls[0][0]
      expect(htmlContent).toContain('Test Property')
      expect(htmlContent).toContain('123 Test St')
      expect(htmlContent).toContain('85.0%') // Occupancy rate
      expect(printResult.success).toBe(true)
    })
  })

  it('prints lease agreement with tenant data', async () => {
    mockElectronAPI.printDocument.mockResolvedValue({ success: true })

    const leaseData = {
      tenantName: 'John Doe',
      propertyName: 'Test Property',
      unitNumber: 'A101',
      landlordName: 'Jane Smith',
      startDate: new Date('2024-01-01').getTime(),
      endDate: new Date('2024-12-31').getTime(),
      monthlyRent: 2000,
      deposit: 4000
    }

    const { result } = renderHook(() => usePrintService())

    await act(async () => {
      const printResult = await result.current.printLeaseAgreement(leaseData)

      expect(mockElectronAPI.printDocument).toHaveBeenCalled()
      const htmlContent = mockElectronAPI.printDocument.mock.calls[0][0]
      expect(htmlContent).toContain('John Doe')
      expect(htmlContent).toContain('Test Property')
      expect(htmlContent).toContain('A101')
      expect(htmlContent).toContain('$2,000')
      expect(printResult.success).toBe(true)
    })
  })

  it('prints maintenance report with ticket data', async () => {
    mockElectronAPI.printDocument.mockResolvedValue({ success: true })

    const tickets = [
      {
        id: 'T001',
        propertyName: 'Test Property',
        unitNumber: 'A101',
        description: 'Leaky faucet',
        priority: 'high',
        status: 'pending',
        createdAt: new Date('2024-01-01').getTime(),
        vendorName: 'Plumber Co'
      },
      {
        id: 'T002',
        propertyName: 'Test Property',
        unitNumber: null,
        description: 'Elevator maintenance',
        priority: 'emergency',
        status: 'completed',
        createdAt: new Date('2024-01-02').getTime(),
        vendorName: null
      }
    ]

    const { result } = renderHook(() => usePrintService())

    await act(async () => {
      const printResult = await result.current.printMaintenanceReport(tickets)

      expect(mockElectronAPI.printDocument).toHaveBeenCalled()
      const htmlContent = mockElectronAPI.printDocument.mock.calls[0][0]
      expect(htmlContent).toContain('T001')
      expect(htmlContent).toContain('T002')
      expect(htmlContent).toContain('Leaky faucet')
      expect(htmlContent).toContain('Elevator maintenance')
      expect(htmlContent).toContain('Plumber Co')
      expect(htmlContent).toContain('Unassigned')
      expect(printResult.success).toBe(true)
    })
  })
})