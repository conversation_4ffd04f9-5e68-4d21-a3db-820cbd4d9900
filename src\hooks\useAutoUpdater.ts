import { useState, useEffect, useCallback } from 'react'

export interface UpdateInfo {
  version: string
  releaseNotes?: string
  releaseDate?: string
}

export interface UpdateStatus {
  type: 'checking' | 'available' | 'not-available' | 'download-progress' | 'downloaded' | 'error'
  version?: string
  releaseNotes?: string
  releaseDate?: string
  percent?: number
  bytesPerSecond?: number
  transferred?: number
  total?: number
  error?: string
}

export const useAutoUpdater = () => {
  const [updateStatus, setUpdateStatus] = useState<UpdateStatus | null>(null)
  const [isChecking, setIsChecking] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)
  const [currentVersion, setCurrentVersion] = useState<string>('')
  const [autoLaunch, setAutoLaunchState] = useState(false)

  useEffect(() => {
    if (!window.electronAPI) {
      return // Not in Electron environment
    }

    // Get current version
    window.electronAPI.getAppVersion().then(setCurrentVersion)
    
    // Get auto-launch setting
    window.electronAPI.getAutoLaunch().then(setAutoLaunchState)

    // Listen for update status
    const handleUpdateStatus = (status: UpdateStatus) => {
      setUpdateStatus(status)
      
      switch (status.type) {
        case 'checking':
          setIsChecking(true)
          setIsDownloading(false)
          break
        case 'available':
        case 'not-available':
        case 'error':
          setIsChecking(false)
          setIsDownloading(false)
          break
        case 'download-progress':
          setIsChecking(false)
          setIsDownloading(true)
          break
        case 'downloaded':
          setIsChecking(false)
          setIsDownloading(false)
          break
      }
    }

    const handleUpdateAction = (action: string) => {
      switch (action) {
        case 'show-update-dialog':
          // This could trigger a modal or notification
          console.log('Show update dialog requested')
          break
        case 'install-update':
          installUpdate()
          break
      }
    }

    window.electronAPI.onUpdateStatus(handleUpdateStatus)
    window.electronAPI.onUpdateAction(handleUpdateAction)

    return () => {
      window.electronAPI.removeAllListeners('update-status')
      window.electronAPI.removeAllListeners('update-action')
    }
  }, [])

  const checkForUpdates = useCallback(async () => {
    if (!window.electronAPI) {
      throw new Error('Auto-updater not available in web environment')
    }

    setIsChecking(true)
    try {
      const result = await window.electronAPI.checkForUpdates()
      if (!result.success) {
        throw new Error(result.error || 'Failed to check for updates')
      }
      return result.updateInfo
    } catch (error) {
      setIsChecking(false)
      throw error
    }
  }, [])

  const downloadUpdate = useCallback(async () => {
    if (!window.electronAPI) {
      throw new Error('Auto-updater not available in web environment')
    }

    setIsDownloading(true)
    try {
      const result = await window.electronAPI.downloadUpdate()
      if (!result.success) {
        throw new Error(result.error || 'Failed to download update')
      }
    } catch (error) {
      setIsDownloading(false)
      throw error
    }
  }, [])

  const installUpdate = useCallback(async () => {
    if (!window.electronAPI) {
      throw new Error('Auto-updater not available in web environment')
    }

    await window.electronAPI.installUpdate()
  }, [])

  const setAutoLaunch = useCallback(async (enable: boolean) => {
    if (!window.electronAPI) {
      throw new Error('Auto-launch not available in web environment')
    }

    const result = await window.electronAPI.setAutoLaunch(enable)
    if (result.success) {
      setAutoLaunchState(enable)
    } else {
      throw new Error(result.error || 'Failed to set auto-launch')
    }
  }, [])

  const reportCrash = useCallback(async (crashData: any) => {
    if (!window.electronAPI) {
      console.warn('Crash reporting not available in web environment')
      return
    }

    try {
      await window.electronAPI.reportCrash(crashData)
    } catch (error) {
      console.error('Failed to report crash:', error)
    }
  }, [])

  const isUpdateAvailable = updateStatus?.type === 'available'
  const isUpdateDownloaded = updateStatus?.type === 'downloaded'
  const downloadProgress = updateStatus?.type === 'download-progress' ? updateStatus.percent || 0 : 0

  return {
    // State
    updateStatus,
    isChecking,
    isDownloading,
    currentVersion,
    autoLaunch,
    isUpdateAvailable,
    isUpdateDownloaded,
    downloadProgress,
    
    // Actions
    checkForUpdates,
    downloadUpdate,
    installUpdate,
    setAutoLaunch,
    reportCrash
  }
}