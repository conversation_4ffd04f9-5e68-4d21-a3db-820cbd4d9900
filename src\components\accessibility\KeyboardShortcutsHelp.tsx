/**
 * Keyboard shortcuts help dialog with accessible presentation
 */
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../ui/dialog';
import { AccessibleButton } from './AccessibleButton';
import { useKeyboardShortcutsHelp, commonShortcuts, propertyManagementShortcuts } from '../../hooks/useKeyboardShortcuts';
import { useAccessibility } from '../../contexts/AccessibilityContext';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { KEYBOARD_KEYS } from '../../lib/accessibility';

interface KeyboardShortcutsHelpProps {
  additionalShortcuts?: Array<{
    key: string;
    ctrlKey?: boolean;
    altKey?: boolean;
    shiftKey?: boolean;
    metaKey?: boolean;
    description: string;
    category?: string;
  }>;
}

export function KeyboardShortcutsHelp({ additionalShortcuts = [] }: KeyboardShortcutsHelpProps) {
  const { settings } = useAccessibility();
  const [open, setOpen] = React.useState(false);
  
  const allShortcuts = [
    ...commonShortcuts,
    ...propertyManagementShortcuts,
    ...additionalShortcuts,
  ];
  
  const { formatShortcut, getShortcutsByCategory } = useKeyboardShortcutsHelp(allShortcuts);
  const shortcutsByCategory = getShortcutsByCategory();

  // Handle keyboard shortcut to open help
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === '?' && event.shiftKey && settings.keyboardShortcuts) {
        event.preventDefault();
        setOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [settings.keyboardShortcuts]);

  if (!settings.keyboardShortcuts) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <AccessibleButton
          variant="outline"
          size="sm"
          aria-label="Show keyboard shortcuts help"
          shortcut="Shift + ?"
        >
          <span className="sr-only">Keyboard shortcuts</span>
          <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
            ?
          </kbd>
        </AccessibleButton>
      </DialogTrigger>
      
      <DialogContent 
        className="max-w-2xl max-h-[80vh] overflow-y-auto"
        aria-describedby="shortcuts-description"
      >
        <DialogHeader>
          <DialogTitle>Keyboard Shortcuts</DialogTitle>
          <DialogDescription id="shortcuts-description">
            Use these keyboard shortcuts to navigate and interact with the application more efficiently.
            Press Escape to close this dialog.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {Object.entries(shortcutsByCategory).map(([category, shortcuts]) => (
            <div key={category}>
              <h3 className="text-lg font-semibold mb-3">{category}</h3>
              <div className="space-y-2">
                {shortcuts.map((shortcut, index) => (
                  <div
                    key={`${category}-${index}`}
                    className="flex items-center justify-between py-2 px-3 rounded-md bg-muted/50"
                  >
                    <span className="text-sm">{shortcut.description}</span>
                    <Badge variant="secondary" className="font-mono text-xs">
                      {formatShortcut(shortcut)}
                    </Badge>
                  </div>
                ))}
              </div>
              {Object.keys(shortcutsByCategory).indexOf(category) < Object.keys(shortcutsByCategory).length - 1 && (
                <Separator className="mt-4" />
              )}
            </div>
          ))}

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">General Navigation</h3>
            <div className="space-y-2 text-sm text-muted-foreground">
              <p>• Use <Badge variant="outline" className="font-mono text-xs mx-1">Tab</Badge> to navigate between interactive elements</p>
              <p>• Use <Badge variant="outline" className="font-mono text-xs mx-1">Shift + Tab</Badge> to navigate backwards</p>
              <p>• Use <Badge variant="outline" className="font-mono text-xs mx-1">Enter</Badge> or <Badge variant="outline" className="font-mono text-xs mx-1">Space</Badge> to activate buttons and links</p>
              <p>• Use <Badge variant="outline" className="font-mono text-xs mx-1">Arrow keys</Badge> to navigate within menus and lists</p>
              <p>• Use <Badge variant="outline" className="font-mono text-xs mx-1">Escape</Badge> to close dialogs and menus</p>
            </div>
          </div>

          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-md">
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              Accessibility Tip
            </h4>
            <p className="text-sm text-blue-800 dark:text-blue-200">
              You can disable keyboard shortcuts in the accessibility settings if they interfere with your screen reader or other assistive technology.
            </p>
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <AccessibleButton
            variant="outline"
            onClick={() => setOpen(false)}
            aria-label="Close keyboard shortcuts help"
          >
            Close
          </AccessibleButton>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Floating keyboard shortcuts button for easy access
export function KeyboardShortcutsButton() {
  const { settings } = useAccessibility();

  if (!settings.keyboardShortcuts) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-40">
      <KeyboardShortcutsHelp />
    </div>
  );
}