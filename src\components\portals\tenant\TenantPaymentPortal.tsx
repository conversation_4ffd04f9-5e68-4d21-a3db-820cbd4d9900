import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../../convex/_generated/api';
import { Id } from '../../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs';
import { Separator } from '../../ui/separator';
import { Alert, AlertDescription } from '../../ui/alert';
import { useToast } from '../../ui/use-toast';
import { 
  CreditCard, 
  Smartphone, 
  // DollarSign, 
  // Calendar,
  Download,
  CheckCircle,
  Clock,
  AlertCircle,
  Receipt
} from 'lucide-react';

interface TenantPaymentPortalProps {
  tenantId: Id<"users">;
  portalId: Id<"portals">;
}

export const TenantPaymentPortal: React.FC<TenantPaymentPortalProps> = ({ tenantId, portalId }) => {
  const { toast } = useToast();
  const [selectedInvoice] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'mpesa' | 'stripe'>('mpesa');

  const portal = useQuery(api.portals.getPortalById, { portalId });
  const tenant = useQuery(api.users.getById, { id: tenantId });
  const pendingInvoices = useQuery(api.invoices.getPendingInvoices, { tenantId });
  const paidInvoices = useQuery(api.invoices.getPaidInvoices, { tenantId, limit: 10 });
  const activeLease = useQuery(api.leases.getActiveLease, { tenantId });

  const processPayment = useMutation(api.payments.processPayment);

  const handlePayment = async (invoiceId: Id<"invoices">, amount: number) => {
    try {
      const result = await processPayment({
        invoiceId,
        amount,
        method: paymentMethod,
        metadata: {
          tenantId,
          portalId,
        },
      });

      toast({
        title: "Payment Initiated",
        description: paymentMethod === 'mpesa' 
          ? "Please check your phone for the M-PESA prompt"
          : "Processing your card payment...",
      });

      // Handle payment result
      if (result.status === 'completed') {
        toast({
          title: "Payment Successful",
          description: "Your payment has been processed successfully.",
        });
      }
    } catch (error) {
      toast({
        title: "Payment Failed",
        description: error instanceof Error ? error.message : "Failed to process payment",
        variant: "destructive",
      });
    }
  };

  const downloadReceipt = (_invoiceId: string) => {
    // In a real implementation, this would generate and download a PDF receipt
    toast({
      title: "Receipt Downloaded",
      description: "Your payment receipt has been downloaded.",
    });
  };

  const InvoiceCard: React.FC<{
    invoice: any;
    showPayButton?: boolean;
  }> = ({ invoice, showPayButton = false }) => (
    <Card className={`${selectedInvoice === invoice._id ? 'ring-2 ring-primary' : ''}`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className={`p-2 rounded-full ${
              invoice.status === 'paid' ? 'bg-green-100 text-green-600' :
              invoice.status === 'pending' ? 'bg-yellow-100 text-yellow-600' :
              'bg-red-100 text-red-600'
            }`}>
              {invoice.status === 'paid' ? <CheckCircle className="w-4 h-4" /> :
               invoice.status === 'pending' ? <Clock className="w-4 h-4" /> :
               <AlertCircle className="w-4 h-4" />}
            </div>
            <div>
              <p className="font-medium">${invoice.amount}</p>
              <p className="text-sm text-muted-foreground">
                {invoice.type.charAt(0).toUpperCase() + invoice.type.slice(1)}
              </p>
            </div>
          </div>
          <Badge variant={
            invoice.status === 'paid' ? 'default' :
            invoice.status === 'pending' ? 'secondary' :
            'destructive'
          }>
            {invoice.status}
          </Badge>
        </div>

        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Due Date:</span>
            <span>{new Date(invoice.dueDate).toLocaleDateString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Created:</span>
            <span>{new Date(invoice.createdAt).toLocaleDateString()}</span>
          </div>
          {invoice.paidAt && (
            <div className="flex justify-between">
              <span className="text-muted-foreground">Paid:</span>
              <span>{new Date(invoice.paidAt).toLocaleDateString()}</span>
            </div>
          )}
        </div>

        {invoice.items && invoice.items.length > 0 && (
          <div className="mt-3 pt-3 border-t">
            <p className="text-sm font-medium mb-2">Items:</p>
            <div className="space-y-1">
              {invoice.items.map((item: any, index: number) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>{item.description}</span>
                  <span>${item.amount}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex justify-between items-center mt-4 pt-3 border-t">
          {showPayButton && invoice.status === 'pending' ? (
            <Button 
              onClick={() => handlePayment(invoice._id, invoice.amount)}
              className="flex-1 mr-2"
            >
              <CreditCard className="w-4 h-4 mr-2" />
              Pay ${invoice.amount}
            </Button>
          ) : (
            <div className="flex-1" />
          )}
          
          {invoice.status === 'paid' && (
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => downloadReceipt(invoice._id)}
            >
              <Download className="w-4 h-4 mr-2" />
              Receipt
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const PaymentMethodSelector: React.FC = () => (
    <Card>
      <CardHeader>
        <CardTitle>Payment Method</CardTitle>
        <CardDescription>
          Choose your preferred payment method
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card 
            className={`cursor-pointer transition-all ${
              paymentMethod === 'mpesa' ? 'ring-2 ring-primary bg-primary/5' : 'hover:shadow-md'
            }`}
            onClick={() => setPaymentMethod('mpesa')}
          >
            <CardContent className="p-4 text-center">
              <Smartphone className="w-8 h-8 mx-auto mb-2 text-green-600" />
              <h3 className="font-medium">M-PESA</h3>
              <p className="text-sm text-muted-foreground">
                Pay with your mobile money
              </p>
            </CardContent>
          </Card>

          <Card 
            className={`cursor-pointer transition-all ${
              paymentMethod === 'stripe' ? 'ring-2 ring-primary bg-primary/5' : 'hover:shadow-md'
            }`}
            onClick={() => setPaymentMethod('stripe')}
          >
            <CardContent className="p-4 text-center">
              <CreditCard className="w-8 h-8 mx-auto mb-2 text-blue-600" />
              <h3 className="font-medium">Credit/Debit Card</h3>
              <p className="text-sm text-muted-foreground">
                Pay with Visa, Mastercard, etc.
              </p>
            </CardContent>
          </Card>
        </div>

        {paymentMethod === 'mpesa' && (
          <Alert>
            <Smartphone className="h-4 w-4" />
            <AlertDescription>
              You will receive an M-PESA prompt on your registered phone number to complete the payment.
            </AlertDescription>
          </Alert>
        )}

        {paymentMethod === 'stripe' && (
          <Alert>
            <CreditCard className="h-4 w-4" />
            <AlertDescription>
              Your card information is processed securely through Stripe. We do not store your card details.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );

  if (!portal || !tenant) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Payment Portal</h1>
          <p className="text-muted-foreground">
            Manage your rent payments and view payment history
          </p>
        </div>
        <div className="text-right">
          {activeLease && (
            <div>
              <p className="text-sm text-muted-foreground">Monthly Rent</p>
              <p className="text-2xl font-bold">${activeLease.monthlyRent}</p>
            </div>
          )}
        </div>
      </div>

      <Tabs defaultValue="pending" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="pending" className="flex items-center">
            <Clock className="w-4 h-4 mr-2" />
            Pending ({pendingInvoices?.length || 0})
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center">
            <Receipt className="w-4 h-4 mr-2" />
            History
          </TabsTrigger>
          <TabsTrigger value="methods" className="flex items-center">
            <CreditCard className="w-4 h-4 mr-2" />
            Payment Methods
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="space-y-6">
          {pendingInvoices && pendingInvoices.length > 0 ? (
            <>
              <div className="grid gap-4">
                {pendingInvoices.map((invoice: any) => (
                  <InvoiceCard 
                    key={invoice._id} 
                    invoice={invoice} 
                    showPayButton={true}
                  />
                ))}
              </div>
              
              <PaymentMethodSelector />
            </>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-600" />
                <h3 className="text-lg font-medium mb-2">All Caught Up!</h3>
                <p className="text-muted-foreground">
                  You have no pending payments at this time.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          {paidInvoices && paidInvoices.length > 0 ? (
            <div className="grid gap-4">
              {paidInvoices.map((invoice: any) => (
                <InvoiceCard 
                  key={invoice._id} 
                  invoice={invoice} 
                  showPayButton={false}
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Receipt className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No Payment History</h3>
                <p className="text-muted-foreground">
                  Your payment history will appear here once you make payments.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="methods" className="space-y-6">
          <PaymentMethodSelector />
          
          <Card>
            <CardHeader>
              <CardTitle>Payment Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">M-PESA Payments</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Instant payment confirmation</li>
                    <li>• No additional fees</li>
                    <li>• Available 24/7</li>
                    <li>• SMS receipt included</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Card Payments</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Secure payment processing</li>
                    <li>• Visa, Mastercard accepted</li>
                    <li>• Instant confirmation</li>
                    <li>• Email receipt provided</li>
                  </ul>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h4 className="font-medium mb-2">Payment Schedule</h4>
                <p className="text-sm text-muted-foreground">
                  Rent is due on the 1st of each month. Late fees may apply after the grace period.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};