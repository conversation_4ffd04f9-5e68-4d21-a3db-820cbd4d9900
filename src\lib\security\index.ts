// Security module exports
export * from './input-validation';
export * from './rate-limiting';
export * from './session-management';
export * from './audit-logging';
export * from './security-middleware';
export * from './data-encryption';
export * from './data-retention';
export * from './consent-management';
export * from './privacy-controls';

// Re-export commonly used utilities
export {
  InputSanitizer,
  InputValidator,
  ValidationSchemas,
  RateLimitConfigs
} from './input-validation';

export {
  RateLimiter,
  DDoSProtection,
  RequestFingerprinting,
  SecurityMiddleware as SecurityMiddlewareCore
} from './rate-limiting';

export {
  SessionManager,
  TokenSecurity,
  SecurityEventLogger,
  SecurityEventType,
  DEFAULT_SESSION_CONFIG
} from './session-management';

export {
  auditLogger,
  AuditLogger,
  AuditEventType,
  AuditSeverity
} from './audit-logging';

export {
  SecurityMiddleware,
  useSecurityContext,
  usePermissions,
  withSecurity
} from './security-middleware';

export {
  DataEncryption,
  KeyManager,
  DataMasking,
  SecureDataDeletion,
  DEFAULT_DATA_CLASSIFICATION
} from './data-encryption';

export {
  dataRetentionManager,
  DataRetentionManager,
  DeletionMethod,
  RetentionStatus,
  DEFAULT_RETENTION_POLICIES
} from './data-retention';

export {
  consentManager,
  ConsentManager,
  ConsentType,
  ConsentStatus,
  LegalBasis,
  DEFAULT_CONSENT_CONFIGURATIONS
} from './consent-management';

export {
  privacyControlsManager,
  PrivacyControlsManager,
  PrivacyControlType,
  RequestStatus,
  ExportFormat,
  DEFAULT_DATA_CATEGORIES
} from './privacy-controls';

// Security configuration
export interface SecurityConfig {
  session: {
    maxAge: number;
    renewalThreshold: number;
    maxConcurrentSessions: number;
  };
  rateLimit: {
    enabled: boolean;
    windowMs: number;
    maxRequests: number;
  };
  audit: {
    enabled: boolean;
    maxEvents: number;
    retentionDays: number;
  };
  ddos: {
    enabled: boolean;
    threshold: number;
    blockDuration: number;
  };
  encryption: {
    enabled: boolean;
    keyRotationDays: number;
    algorithm: string;
  };
  dataRetention: {
    enabled: boolean;
    defaultRetentionDays: number;
    autoDeleteExpired: boolean;
  };
  privacy: {
    consentRequired: boolean;
    autoProcessRequests: boolean;
    exportFormats: string[];
    maxExportSize: number;
  };
  compliance: {
    gdprCompliant: boolean;
    auditTrailRequired: boolean;
    dataMinimization: boolean;
    rightToBeForgotten: boolean;
  };
}

export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  session: {
    maxAge: 8 * 60 * 60 * 1000, // 8 hours
    renewalThreshold: 30 * 60 * 1000, // 30 minutes
    maxConcurrentSessions: 3
  },
  rateLimit: {
    enabled: true,
    windowMs: 15 * 60 * 60 * 1000, // 15 minutes
    maxRequests: 1000
  },
  audit: {
    enabled: true,
    maxEvents: 10000,
    retentionDays: 90
  },
  ddos: {
    enabled: true,
    threshold: 100, // requests per minute
    blockDuration: 60 * 60 * 1000 // 1 hour
  },
  encryption: {
    enabled: true,
    keyRotationDays: 90,
    algorithm: 'AES-GCM'
  },
  dataRetention: {
    enabled: true,
    defaultRetentionDays: 2555, // 7 years
    autoDeleteExpired: true
  },
  privacy: {
    consentRequired: true,
    autoProcessRequests: true,
    exportFormats: ['json', 'csv', 'pdf'],
    maxExportSize: 100 * 1024 * 1024 // 100MB
  },
  compliance: {
    gdprCompliant: true,
    auditTrailRequired: true,
    dataMinimization: true,
    rightToBeForgotten: true
  }
};