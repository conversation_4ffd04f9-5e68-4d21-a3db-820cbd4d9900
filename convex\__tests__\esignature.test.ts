import { convexTest } from "convex-test";
import { expect, test } from "vitest";
import { api } from "../_generated/api";
import schema from "../schema";

test("e-signature workflow", async () => {
  const t = convexTest(schema);

  // Create test data
  const userId = await t.mutation(api.users.createUser, {
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "tenant",
  });

  const ownerId = await t.mutation(api.users.createUser, {
    email: "<EMAIL>",
    name: "<PERSON>er",
    role: "owner",
  });

  const propertyId = await t.mutation(api.properties.createProperty, {
    name: "Test Property",
    type: "residential",
    address: {
      street: "123 Test St",
      city: "Test City",
      state: "TS",
      country: "Test Country",
      postalCode: "12345",
    },
    ownerId,
    branding: {
      primaryColor: "#000000",
      secondaryColor: "#ffffff",
    },
    settings: {
      currency: "USD",
      timezone: "UTC",
      language: "en",
      autoRentReminders: true,
      maintenanceSLA: 24,
    },
  });

  const unitId = await t.mutation(api.units.createUnit, {
    propertyId,
    unitNumber: "101",
    type: "apartment",
    size: 1000,
    rent: 1500,
    deposit: 1500,
    amenities: ["parking", "balcony"],
  });

  const leaseId = await t.mutation(api.leases.createLease, {
    propertyId,
    unitId,
    tenantId: userId,
    startDate: Date.now(),
    endDate: Date.now() + (365 * 24 * 60 * 60 * 1000), // 1 year
    monthlyRent: 1500,
    deposit: 1500,
    terms: {
      noticePeriod: 30,
      lateFeePercentage: 5,
      gracePeriod: 5,
      renewalOption: true,
    },
  });

  // Test creating signature request
  const signatureRequestId = await t.mutation(api.esignature.createSignatureRequest, {
    leaseId,
    documentUrl: "https://example.com/lease.pdf",
    signers: [
      {
        email: "<EMAIL>",
        name: "John Tenant",
        role: "tenant",
        order: 1,
      },
      {
        email: "<EMAIL>",
        name: "Jane Owner",
        role: "landlord",
        order: 2,
      },
    ],
  });

  expect(signatureRequestId).toBeDefined();

  // Verify signature request was created
  const signatureRequest = await t.query(api.esignature.getSignatureRequest, {
    id: signatureRequestId,
  });

  expect(signatureRequest).toBeDefined();
  expect(signatureRequest?.signatureRequest.status).toBe("pending");
  expect(signatureRequest?.signatureRequest.signers).toHaveLength(2);

  // Test updating signature status
  await t.mutation(api.esignature.updateSignatureStatus, {
    signatureRequestId,
    status: "signed",
    signerEmail: "<EMAIL>",
    signerStatus: "signed",
    signedAt: Date.now(),
    signedDocumentUrl: "https://example.com/lease-signed.pdf",
  });

  // Verify lease status was updated
  const updatedLease = await t.query(api.leases.getLeaseById, { id: leaseId });
  expect(updatedLease?.lease.eSignatureStatus).toBe("signed");
  expect(updatedLease?.lease.status).toBe("active");

  // Test getting signature requests by lease
  const leaseSignatureRequests = await t.query(api.esignature.getSignatureRequestsByLease, {
    leaseId,
  });

  expect(leaseSignatureRequests).toHaveLength(1);
  expect(leaseSignatureRequests[0].status).toBe("signed");

  // Test resending signature request (should work even after signed for testing)
  await t.mutation(api.esignature.resendSignatureRequest, {
    signatureRequestId,
    signerEmail: "<EMAIL>",
  });

  // Test cancelling signature request
  const newSignatureRequestId = await t.mutation(api.esignature.createSignatureRequest, {
    leaseId,
    documentUrl: "https://example.com/lease-v2.pdf",
    signers: [
      {
        email: "<EMAIL>",
        name: "John Tenant",
        role: "tenant",
        order: 1,
      },
    ],
  });

  await t.mutation(api.esignature.cancelSignatureRequest, {
    signatureRequestId: newSignatureRequestId,
    reason: "Test cancellation",
  });

  const cancelledRequest = await t.query(api.esignature.getSignatureRequest, {
    id: newSignatureRequestId,
  });

  expect(cancelledRequest?.signatureRequest.status).toBe("expired");
});

test("signature request validation", async () => {
  const t = convexTest(schema);

  // Create minimal test data
  const userId = await t.mutation(api.users.createUser, {
    email: "<EMAIL>",
    name: "John Tenant",
    role: "tenant",
  });

  const ownerId = await t.mutation(api.users.createUser, {
    email: "<EMAIL>",
    name: "Jane Owner",
    role: "owner",
  });

  const propertyId = await t.mutation(api.properties.createProperty, {
    name: "Test Property",
    type: "residential",
    address: {
      street: "123 Test St",
      city: "Test City",
      state: "TS",
      country: "Test Country",
      postalCode: "12345",
    },
    ownerId,
    branding: {
      primaryColor: "#000000",
      secondaryColor: "#ffffff",
    },
    settings: {
      currency: "USD",
      timezone: "UTC",
      language: "en",
      autoRentReminders: true,
      maintenanceSLA: 24,
    },
  });

  const unitId = await t.mutation(api.units.createUnit, {
    propertyId,
    unitNumber: "101",
    type: "apartment",
    size: 1000,
    rent: 1500,
    deposit: 1500,
    amenities: [],
  });

  const leaseId = await t.mutation(api.leases.createLease, {
    propertyId,
    unitId,
    tenantId: userId,
    startDate: Date.now(),
    endDate: Date.now() + (365 * 24 * 60 * 60 * 1000),
    monthlyRent: 1500,
    deposit: 1500,
    terms: {
      noticePeriod: 30,
      lateFeePercentage: 5,
      gracePeriod: 5,
      renewalOption: true,
    },
  });

  // Test creating signature request with invalid lease ID
  await expect(
    t.mutation(api.esignature.createSignatureRequest, {
      leaseId: "invalid-lease-id" as any,
      documentUrl: "https://example.com/lease.pdf",
      signers: [
        {
          email: "<EMAIL>",
          name: "John Tenant",
          role: "tenant",
          order: 1,
        },
      ],
    })
  ).rejects.toThrow();

  // Test updating non-existent signature request
  await expect(
    t.mutation(api.esignature.updateSignatureStatus, {
      signatureRequestId: "invalid-request-id" as any,
      status: "signed",
    })
  ).rejects.toThrow("Signature request not found");
});