/**
 * Offline Storage System using IndexedDB
 * Handles local caching and offline queue management
 */

import { openDB, DBSchema, IDBPDatabase } from 'idb';
import { Id } from '../../convex/_generated/dataModel';

// Database schema
interface EstatePulseDB extends DBSchema {
  properties: {
    key: string;
    value: {
      id: string;
      data: any;
      lastModified: number;
      version: number;
    };
    indexes: { 'by-lastModified': number };
  };
  units: {
    key: string;
    value: {
      id: string;
      data: any;
      lastModified: number;
      version: number;
      propertyId: string;
    };
    indexes: { 'by-property': string; 'by-lastModified': number };
  };
  leases: {
    key: string;
    value: {
      id: string;
      data: any;
      lastModified: number;
      version: number;
      propertyId: string;
    };
    indexes: { 'by-property': string; 'by-lastModified': number };
  };
  maintenanceTickets: {
    key: string;
    value: {
      id: string;
      data: any;
      lastModified: number;
      version: number;
      propertyId: string;
    };
    indexes: { 'by-property': string; 'by-status': string; 'by-lastModified': number };
  };
  offlineQueue: {
    key: string;
    value: {
      id: string;
      operation: 'create' | 'update' | 'delete';
      entityType: string;
      entityId?: string;
      data: any;
      timestamp: number;
      retryCount: number;
      status: 'pending' | 'failed' | 'completed';
    };
    indexes: { 'by-status': string; 'by-timestamp': number };
  };
  syncMetadata: {
    key: string;
    value: {
      entityType: string;
      lastSyncTime: number;
      totalRecords: number;
    };
  };
  userPreferences: {
    key: string;
    value: {
      key: string;
      value: any;
      timestamp: number;
    };
  };
}

class OfflineStorageManager {
  private db: IDBPDatabase<EstatePulseDB> | null = null;
  private dbName = 'EstatePulseDB';
  private dbVersion = 1;

  async initialize(): Promise<void> {
    try {
      this.db = await openDB<EstatePulseDB>(this.dbName, this.dbVersion, {
        upgrade(db) {
          // Properties store
          if (!db.objectStoreNames.contains('properties')) {
            const propertiesStore = db.createObjectStore('properties', { keyPath: 'id' });
            propertiesStore.createIndex('by-lastModified', 'lastModified');
          }

          // Units store
          if (!db.objectStoreNames.contains('units')) {
            const unitsStore = db.createObjectStore('units', { keyPath: 'id' });
            unitsStore.createIndex('by-property', 'propertyId');
            unitsStore.createIndex('by-lastModified', 'lastModified');
          }

          // Leases store
          if (!db.objectStoreNames.contains('leases')) {
            const leasesStore = db.createObjectStore('leases', { keyPath: 'id' });
            leasesStore.createIndex('by-property', 'propertyId');
            leasesStore.createIndex('by-lastModified', 'lastModified');
          }

          // Maintenance tickets store
          if (!db.objectStoreNames.contains('maintenanceTickets')) {
            const ticketsStore = db.createObjectStore('maintenanceTickets', { keyPath: 'id' });
            ticketsStore.createIndex('by-property', 'propertyId');
            ticketsStore.createIndex('by-status', 'data.status');
            ticketsStore.createIndex('by-lastModified', 'lastModified');
          }

          // Offline queue store
          if (!db.objectStoreNames.contains('offlineQueue')) {
            const queueStore = db.createObjectStore('offlineQueue', { keyPath: 'id' });
            queueStore.createIndex('by-status', 'status');
            queueStore.createIndex('by-timestamp', 'timestamp');
          }

          // Sync metadata store
          if (!db.objectStoreNames.contains('syncMetadata')) {
            db.createObjectStore('syncMetadata', { keyPath: 'entityType' });
          }

          // User preferences store
          if (!db.objectStoreNames.contains('userPreferences')) {
            db.createObjectStore('userPreferences', { keyPath: 'key' });
          }
        },
      });
    } catch (error) {
      console.error('Failed to initialize IndexedDB:', error);
      throw error;
    }
  }

  private ensureDB(): IDBPDatabase<EstatePulseDB> {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  // Generic cache operations
  async cacheEntity(
    storeName: keyof EstatePulseDB,
    id: string,
    data: any,
    propertyId?: string
  ): Promise<void> {
    const db = this.ensureDB();
    const now = Date.now();

    const cacheEntry = {
      id,
      data,
      lastModified: now,
      version: data.updatedAt || now,
      ...(propertyId && { propertyId }),
    };

    await db.put(storeName as any, cacheEntry);
  }

  async getCachedEntity(
    storeName: keyof EstatePulseDB,
    id: string
  ): Promise<any | null> {
    const db = this.ensureDB();
    const entry = await db.get(storeName as any, id);
    return entry ? entry.data : null;
  }

  async getCachedEntitiesByProperty(
    storeName: keyof EstatePulseDB,
    propertyId: string
  ): Promise<any[]> {
    const db = this.ensureDB();
    
    if (storeName === 'properties') {
      // Properties don't have propertyId index
      const allProperties = await db.getAll(storeName as any);
      return allProperties.map(entry => entry.data);
    }

    const entries = await db.getAllFromIndex(storeName as any, 'by-property', propertyId);
    return entries.map(entry => entry.data);
  }

  async getAllCachedEntities(storeName: keyof EstatePulseDB): Promise<any[]> {
    const db = this.ensureDB();
    const entries = await db.getAll(storeName as any);
    return entries.map(entry => entry.data);
  }

  async deleteCachedEntity(
    storeName: keyof EstatePulseDB,
    id: string
  ): Promise<void> {
    const db = this.ensureDB();
    await db.delete(storeName as any, id);
  }

  // Offline queue operations
  async addToOfflineQueue(operation: {
    operation: 'create' | 'update' | 'delete';
    entityType: string;
    entityId?: string;
    data: any;
  }): Promise<string> {
    const db = this.ensureDB();
    const id = crypto.randomUUID();
    
    const queueEntry = {
      id,
      ...operation,
      timestamp: Date.now(),
      retryCount: 0,
      status: 'pending' as const,
    };

    await db.add('offlineQueue', queueEntry);
    return id;
  }

  async getOfflineQueue(): Promise<any[]> {
    const db = this.ensureDB();
    return await db.getAllFromIndex('offlineQueue', 'by-status', 'pending');
  }

  async updateQueueItemStatus(
    id: string,
    status: 'pending' | 'failed' | 'completed',
    retryCount?: number
  ): Promise<void> {
    const db = this.ensureDB();
    const item = await db.get('offlineQueue', id);
    
    if (item) {
      item.status = status;
      if (retryCount !== undefined) {
        item.retryCount = retryCount;
      }
      await db.put('offlineQueue', item);
    }
  }

  async removeFromOfflineQueue(id: string): Promise<void> {
    const db = this.ensureDB();
    await db.delete('offlineQueue', id);
  }

  async clearCompletedQueueItems(): Promise<void> {
    const db = this.ensureDB();
    const completedItems = await db.getAllFromIndex('offlineQueue', 'by-status', 'completed');
    
    const tx = db.transaction('offlineQueue', 'readwrite');
    for (const item of completedItems) {
      await tx.store.delete(item.id);
    }
    await tx.done;
  }

  // Sync metadata operations
  async updateSyncMetadata(
    entityType: string,
    lastSyncTime: number,
    totalRecords: number
  ): Promise<void> {
    const db = this.ensureDB();
    await db.put('syncMetadata', {
      entityType,
      lastSyncTime,
      totalRecords,
    });
  }

  async getSyncMetadata(entityType: string): Promise<{
    lastSyncTime: number;
    totalRecords: number;
  } | null> {
    const db = this.ensureDB();
    return await db.get('syncMetadata', entityType) || null;
  }

  // User preferences
  async setUserPreference(key: string, value: any): Promise<void> {
    const db = this.ensureDB();
    await db.put('userPreferences', {
      key,
      value,
      timestamp: Date.now(),
    });
  }

  async getUserPreference(key: string): Promise<any> {
    const db = this.ensureDB();
    const pref = await db.get('userPreferences', key);
    return pref ? pref.value : null;
  }

  // Cache management
  async getCacheSize(): Promise<number> {
    const db = this.ensureDB();
    let totalSize = 0;

    const stores = ['properties', 'units', 'leases', 'maintenanceTickets'] as const;
    
    for (const store of stores) {
      const count = await db.count(store);
      totalSize += count;
    }

    return totalSize;
  }

  async clearCache(entityType?: string): Promise<void> {
    const db = this.ensureDB();

    if (entityType) {
      await db.clear(entityType as keyof EstatePulseDB);
    } else {
      const stores = ['properties', 'units', 'leases', 'maintenanceTickets'] as const;
      const tx = db.transaction(stores, 'readwrite');
      
      for (const store of stores) {
        await tx.objectStore(store).clear();
      }
      
      await tx.done;
    }
  }

  async getStorageUsage(): Promise<{
    used: number;
    quota: number;
    percentage: number;
  }> {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      const used = estimate.usage || 0;
      const quota = estimate.quota || 0;
      const percentage = quota > 0 ? (used / quota) * 100 : 0;

      return { used, quota, percentage };
    }

    return { used: 0, quota: 0, percentage: 0 };
  }

  // Cleanup old cache entries
  async cleanupOldEntries(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<void> {
    const db = this.ensureDB();
    const cutoffTime = Date.now() - maxAge;

    const stores = ['properties', 'units', 'leases', 'maintenanceTickets'] as const;
    
    for (const storeName of stores) {
      const oldEntries = await db.getAllFromIndex(storeName, 'by-lastModified', IDBKeyRange.upperBound(cutoffTime));
      
      if (oldEntries.length > 0) {
        const tx = db.transaction(storeName, 'readwrite');
        for (const entry of oldEntries) {
          await tx.store.delete(entry.id);
        }
        await tx.done;
      }
    }
  }

  async close(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// Global storage manager instance
export const offlineStorageManager = new OfflineStorageManager();

// Initialize storage on module load
offlineStorageManager.initialize().catch(console.error);