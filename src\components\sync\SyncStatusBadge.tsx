/**
 * Sync Status Badge Component
 * Shows current sync status and provides quick access to reconciliation
 */

import React, { useState } from 'react';
import { useConvex } from 'convex/react';
import { useOfflineReconciliation } from '../../hooks/useOfflineReconciliation';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '../ui/dialog';
import { ReconciliationManager } from './ReconciliationManager';
import { 
  CheckCircle, 
  AlertTriangle, 
  Clock, 
  RefreshCw,
  Wifi,
  WifiOff
} from 'lucide-react';

interface SyncStatusBadgeProps {
  showDetails?: boolean;
  className?: string;
}

export function SyncStatusBadge({ showDetails = true, className = '' }: SyncStatusBadgeProps) {
  const convex = useConvex();
  const { 
    pendingOperationsCount, 
    isReconciliationNeeded,
    getStatusSummary,
    state
  } = useOfflineReconciliation(convex);
  
  const [showReconciliation, setShowReconciliation] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Listen for online/offline events
  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const statusSummary = getStatusSummary();

  const getStatusConfig = () => {
    if (!isOnline) {
      return {
        variant: 'secondary' as const,
        icon: <WifiOff className="h-3 w-3" />,
        text: 'Offline',
        color: 'text-gray-600'
      };
    }

    switch (statusSummary.status) {
      case 'reconciling':
        return {
          variant: 'default' as const,
          icon: <RefreshCw className="h-3 w-3 animate-spin" />,
          text: 'Syncing',
          color: 'text-blue-600'
        };
      case 'error':
        return {
          variant: 'destructive' as const,
          icon: <AlertTriangle className="h-3 w-3" />,
          text: 'Error',
          color: 'text-red-600'
        };
      case 'pending':
        return {
          variant: 'secondary' as const,
          icon: <Clock className="h-3 w-3" />,
          text: `${pendingOperationsCount} pending`,
          color: 'text-yellow-600'
        };
      case 'conflicts':
        return {
          variant: 'destructive' as const,
          icon: <AlertTriangle className="h-3 w-3" />,
          text: `${state.conflicts.length} conflicts`,
          color: 'text-orange-600'
        };
      case 'synced':
        return {
          variant: 'outline' as const,
          icon: <CheckCircle className="h-3 w-3" />,
          text: 'Synced',
          color: 'text-green-600'
        };
    }
  };

  const config = getStatusConfig();

  const BadgeContent = (
    <Badge 
      variant={config.variant} 
      className={`flex items-center space-x-1 cursor-pointer ${className}`}
    >
      {config.icon}
      <span className="text-xs">{config.text}</span>
      {isOnline && (
        <Wifi className="h-3 w-3 ml-1" />
      )}
    </Badge>
  );

  if (!showDetails) {
    return BadgeContent;
  }

  return (
    <Dialog open={showReconciliation} onOpenChange={setShowReconciliation}>
      <DialogTrigger asChild>
        {BadgeContent}
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>Sync Status</DialogTitle>
        </DialogHeader>
        <ReconciliationManager 
          onClose={() => setShowReconciliation(false)}
          autoStart={false}
        />
      </DialogContent>
    </Dialog>
  );
}

// Compact version for use in headers/toolbars
export function CompactSyncStatus({ className = '' }: { className?: string }) {
  const convex = useConvex();
  const { 
    pendingOperationsCount, 
    getStatusSummary,
    autoReconcile,
    state
  } = useOfflineReconciliation(convex);
  
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const statusSummary = getStatusSummary();

  const handleQuickSync = async () => {
    if (pendingOperationsCount > 0 && !state.isReconciling) {
      try {
        await autoReconcile();
      } catch (error) {
        console.error('Quick sync failed:', error);
      }
    }
  };

  if (!isOnline) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <WifiOff className="h-4 w-4 text-gray-500" />
        <span className="text-sm text-gray-600">Offline</span>
      </div>
    );
  }

  if (statusSummary.status === 'synced') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <CheckCircle className="h-4 w-4 text-green-500" />
        <span className="text-sm text-green-600">Synced</span>
      </div>
    );
  }

  if (statusSummary.status === 'pending' && pendingOperationsCount > 0) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={handleQuickSync}
        disabled={state.isReconciling}
        className={`flex items-center space-x-2 ${className}`}
      >
        {state.isReconciling ? (
          <RefreshCw className="h-4 w-4 animate-spin" />
        ) : (
          <Clock className="h-4 w-4 text-yellow-500" />
        )}
        <span className="text-sm">
          {state.isReconciling ? 'Syncing...' : `${pendingOperationsCount} pending`}
        </span>
      </Button>
    );
  }

  if (statusSummary.status === 'conflicts') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <AlertTriangle className="h-4 w-4 text-orange-500" />
        <span className="text-sm text-orange-600">
          {state.conflicts.length} conflicts
        </span>
      </div>
    );
  }

  if (statusSummary.status === 'error') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <AlertTriangle className="h-4 w-4 text-red-500" />
        <span className="text-sm text-red-600">Sync error</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
      <span className="text-sm text-blue-600">Syncing...</span>
    </div>
  );
}