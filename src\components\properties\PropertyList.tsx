import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select } from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

interface PropertyListProps {
  onPropertySelect?: (propertyId: Id<"properties">) => void;
  onCreateNew?: () => void;
  onEditProperty?: (propertyId: Id<"properties">) => void;
}

export const PropertyList: React.FC<PropertyListProps> = ({
  onPropertySelect,
  onCreateNew,
  onEditProperty,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'residential' | 'commercial' | 'mixed'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');

  // Get all properties
  const allProperties = useQuery(api.properties.getProperties, {});

  // Filter properties based on search and filters
  const filteredProperties = React.useMemo(() => {
    if (!allProperties) return [];

    return allProperties.filter((property: any) => {
      // Search filter
      const matchesSearch = searchTerm === '' || 
        property.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.address.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.address.street.toLowerCase().includes(searchTerm.toLowerCase());

      // Type filter
      const matchesType = typeFilter === 'all' || property.type === typeFilter;

      // Status filter
      const matchesStatus = statusFilter === 'all' || 
        (statusFilter === 'active' && property.isActive) ||
        (statusFilter === 'inactive' && !property.isActive);

      return matchesSearch && matchesType && matchesStatus;
    });
  }, [allProperties, searchTerm, typeFilter, statusFilter]);

  const formatAddress = (address: any) => {
    return `${address.street}, ${address.city}, ${address.state}`;
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'residential':
        return 'bg-blue-100 text-blue-800';
      case 'commercial':
        return 'bg-green-100 text-green-800';
      case 'mixed':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!allProperties) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Properties</h2>
          <p className="text-gray-600">
            Manage your property portfolio ({filteredProperties.length} properties)
          </p>
        </div>
        {onCreateNew && (
          <Button onClick={onCreateNew}>
            Create New Property
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Input
                placeholder="Search properties..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div>
              <Select
                value={typeFilter}
                onValueChange={(value) => setTypeFilter(value as any)}
              >
                <option value="all">All Types</option>
                <option value="residential">Residential</option>
                <option value="commercial">Commercial</option>
                <option value="mixed">Mixed Use</option>
              </Select>
            </div>

            <div>
              <Select
                value={statusFilter}
                onValueChange={(value) => setStatusFilter(value as any)}
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Property Grid */}
      {filteredProperties.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-gray-500">
              <h3 className="text-lg font-semibold mb-2">No properties found</h3>
              <p className="mb-4">
                {searchTerm || typeFilter !== 'all' || statusFilter !== 'all'
                  ? 'Try adjusting your search filters'
                  : 'Get started by creating your first property'
                }
              </p>
              {onCreateNew && (
                <Button onClick={onCreateNew}>
                  Create New Property
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProperties.map((property: any) => (
            <Card
              key={property._id}
              className={`cursor-pointer transition-shadow hover:shadow-lg ${
                !property.isActive ? 'opacity-75' : ''
              }`}
              onClick={() => onPropertySelect?.(property._id)}
            >
              <CardHeader>
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{property.name}</CardTitle>
                  <div className="flex flex-col items-end space-y-2">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize ${getTypeColor(
                        property.type
                      )}`}
                    >
                      {property.type}
                    </span>
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        property.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {property.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-600">Address</p>
                    <p className="text-sm font-medium">{formatAddress(property.address)}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Currency</p>
                      <p className="font-medium">{property.settings?.currency || 'KES'}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">SLA</p>
                      <p className="font-medium">{property.settings?.maintenanceSLA || 24}h</p>
                    </div>
                  </div>

                  {property.branding?.customDomain && (
                    <div>
                      <p className="text-sm text-gray-600">Custom Domain</p>
                      <p className="text-sm font-medium text-blue-600">
                        {property.branding.customDomain}
                      </p>
                    </div>
                  )}

                  <div className="flex justify-between items-center pt-2 border-t">
                    <p className="text-xs text-gray-500">
                      Created {new Date(property.createdAt).toLocaleDateString()}
                    </p>
                    {onEditProperty && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          onEditProperty(property._id);
                        }}
                      >
                        Edit
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};