import { convexTest } from 'convex-test';
import { describe, it, expect, beforeEach } from 'vitest';
import { api } from '../_generated/api';
import schema from '../schema';

describe('Properties API', () => {
  let t: any;

  beforeEach(async () => {
    t = convexTest(schema);
  });

  describe('createProperty', () => {
    it('should create a property successfully', async () => {
      // Create a test user first
      const userId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyData = {
        name: 'Sunset Apartments',
        type: 'residential' as const,
        address: {
          street: '123 Main Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: userId,
        branding: {
          logo: 'https://example.com/logo.png',
          primaryColor: '#3B82F6',
          secondaryColor: '#1E40AF',
          fontFamily: 'Inter',
        },
        settings: {
          currency: 'KES',
          timezone: 'Africa/Nairobi',
          language: 'en',
        },
      };

      const propertyId = await t.mutation(api.properties.create, propertyData);

      expect(propertyId).toBeDefined();

      // Verify the property was created
      const property = await t.query(api.properties.getById, { id: propertyId });
      expect(property).toMatchObject({
        name: 'Sunset Apartments',
        type: 'residential',
        ownerId: userId,
      });
    });

    it('should validate required fields', async () => {
      const invalidData = {
        name: '',
        type: 'residential' as const,
        address: {
          street: '',
          city: '',
          state: '',
          postalCode: '',
          country: '',
        },
      };

      await expect(
        t.mutation(api.properties.create, invalidData)
      ).rejects.toThrow();
    });

    it('should enforce unique property names per owner', async () => {
      const userId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyData = {
        name: 'Duplicate Property',
        type: 'residential' as const,
        address: {
          street: '123 Main Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: userId,
      };

      // Create first property
      await t.mutation(api.properties.create, propertyData);

      // Try to create duplicate
      await expect(
        t.mutation(api.properties.create, propertyData)
      ).rejects.toThrow('Property name already exists');
    });
  });

  describe('updateProperty', () => {
    it('should update property successfully', async () => {
      const userId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: 'Original Name',
        type: 'residential' as const,
        address: {
          street: '123 Main Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: userId,
      });

      await t.mutation(api.properties.update, {
        id: propertyId,
        name: 'Updated Name',
        type: 'commercial' as const,
      });

      const updatedProperty = await t.query(api.properties.getById, { id: propertyId });
      expect(updatedProperty?.name).toBe('Updated Name');
      expect(updatedProperty?.type).toBe('commercial');
    });

    it('should require proper authorization', async () => {
      const ownerId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const unauthorizedUserId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Unauthorized User',
          role: 'tenant',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: 'Test Property',
        type: 'residential' as const,
        address: {
          street: '123 Main Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: ownerId,
      });

      // Mock unauthorized user context
      await expect(
        t.mutation(api.properties.update, {
          id: propertyId,
          name: 'Unauthorized Update',
        })
      ).rejects.toThrow('Unauthorized');
    });
  });

  describe('getProperties', () => {
    it('should return properties for authorized user', async () => {
      const userId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      // Create multiple properties
      await t.mutation(api.properties.create, {
        name: 'Property 1',
        type: 'residential' as const,
        address: {
          street: '123 Main Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: userId,
      });

      await t.mutation(api.properties.create, {
        name: 'Property 2',
        type: 'commercial' as const,
        address: {
          street: '456 Business Ave',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00200',
          country: 'Kenya',
        },
        ownerId: userId,
      });

      const properties = await t.query(api.properties.getAll);
      expect(properties).toHaveLength(2);
      expect(properties[0].name).toBe('Property 1');
      expect(properties[1].name).toBe('Property 2');
    });

    it('should filter properties by type', async () => {
      const userId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      await t.mutation(api.properties.create, {
        name: 'Residential Property',
        type: 'residential' as const,
        address: {
          street: '123 Main Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: userId,
      });

      await t.mutation(api.properties.create, {
        name: 'Commercial Property',
        type: 'commercial' as const,
        address: {
          street: '456 Business Ave',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00200',
          country: 'Kenya',
        },
        ownerId: userId,
      });

      const residentialProperties = await t.query(api.properties.getByType, {
        type: 'residential',
      });
      expect(residentialProperties).toHaveLength(1);
      expect(residentialProperties[0].name).toBe('Residential Property');
    });
  });

  describe('deleteProperty', () => {
    it('should delete property successfully', async () => {
      const userId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: 'To Be Deleted',
        type: 'residential' as const,
        address: {
          street: '123 Main Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: userId,
      });

      await t.mutation(api.properties.remove, { id: propertyId });

      const deletedProperty = await t.query(api.properties.getById, { id: propertyId });
      expect(deletedProperty).toBeNull();
    });

    it('should prevent deletion if property has active leases', async () => {
      const userId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: 'Property with Leases',
        type: 'residential' as const,
        address: {
          street: '123 Main Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: userId,
      });

      // Create a unit and lease
      const unitId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('units', {
          propertyId,
          unitNumber: 'A101',
          type: 'apartment',
          size: 1200,
          rent: 50000,
          status: 'occupied',
          amenities: [],
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      await t.run(async (ctx: any) => {
        return await ctx.db.insert('leases', {
          propertyId,
          unitId,
          tenantId: userId,
          startDate: Date.now(),
          endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
          monthlyRent: 50000,
          deposit: 100000,
          status: 'active',
          documents: [],
          eSignatureStatus: 'signed',
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      await expect(
        t.mutation(api.properties.remove, { id: propertyId })
      ).rejects.toThrow('Cannot delete property with active leases');
    });
  });

  describe('getPropertyAnalytics', () => {
    it('should calculate property analytics correctly', async () => {
      const userId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: 'Analytics Property',
        type: 'residential' as const,
        address: {
          street: '123 Main Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: userId,
      });

      // Create units with different statuses
      await t.run(async (ctx: any) => {
        await ctx.db.insert('units', {
          propertyId,
          unitNumber: 'A101',
          type: 'apartment',
          size: 1200,
          rent: 50000,
          status: 'occupied',
          amenities: [],
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        await ctx.db.insert('units', {
          propertyId,
          unitNumber: 'A102',
          type: 'apartment',
          size: 1000,
          rent: 45000,
          status: 'vacant',
          amenities: [],
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const analytics = await t.query(api.properties.getAnalytics, { id: propertyId });
      
      expect(analytics).toMatchObject({
        totalUnits: 2,
        occupiedUnits: 1,
        vacantUnits: 1,
        occupancyRate: 50,
        totalRent: 95000,
        collectedRent: 50000,
      });
    });
  });
});