import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Shield, 
  FileText, 
  Upload, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  Users,
  Building,
  TrendingUp,
  Eye,
  Download,
  Plus
} from 'lucide-react';
import { DocumentUpload } from './DocumentUpload';
import { DocumentVerification } from './DocumentVerification';
import { DocumentList } from './DocumentList';
import { ComplianceMonitoring } from './ComplianceMonitoring';
import { RegulatoryReporting } from './RegulatoryReporting';
import { ComplianceChecklist } from './ComplianceChecklist';
import { Id } from '../../convex/_generated/dataModel';

interface ComplianceManagementProps {
  userId?: string;
  propertyId?: string;
}

export function ComplianceManagement({ userId, propertyId }: ComplianceManagementProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>('');
  const [showUpload, setShowUpload] = useState(false);

  // Fetch compliance data
  const userDocuments = useQuery(
    api.compliance.getUserKYCDocuments,
    userId ? { userId: userId as Id<"users"> } : "skip"
  );

  const complianceChecklists = useQuery(
    api.compliance.getComplianceChecklists,
    propertyId ? { propertyId: propertyId as Id<"properties"> } : {}
  );

  const auditTrail = useQuery(
    api.compliance.getAuditTrail,
    { limit: 10 }
  );

  // Calculate compliance metrics
  const totalDocuments = userDocuments?.length || 0;
  const verifiedDocuments = userDocuments?.filter(doc => doc.verificationStatus === 'verified').length || 0;
  const pendingDocuments = userDocuments?.filter(doc => doc.verificationStatus === 'pending').length || 0;
  const rejectedDocuments = userDocuments?.filter(doc => doc.verificationStatus === 'rejected').length || 0;
  
  const complianceRate = totalDocuments > 0 ? Math.round((verifiedDocuments / totalDocuments) * 100) : 0;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return 'text-green-600';
      case 'rejected': return 'text-red-600';
      case 'pending': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const handleDocumentSelect = (documentId: string) => {
    setSelectedDocumentId(documentId);
    setActiveTab('verification');
  };

  const handleUploadComplete = (documentId: string) => {
    setShowUpload(false);
    setSelectedDocumentId(documentId);
    setActiveTab('verification');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Compliance Management
          </h1>
          <p className="text-gray-600 mt-1">
            Manage documents, verify compliance, and track regulatory requirements
          </p>
        </div>
        <Button onClick={() => setShowUpload(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Upload Document
        </Button>
      </div>

      {/* Compliance Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Documents</p>
                <p className="text-2xl font-bold">{totalDocuments}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Verified</p>
                <p className="text-2xl font-bold text-green-600">{verifiedDocuments}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Review</p>
                <p className="text-2xl font-bold text-yellow-600">{pendingDocuments}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Compliance Rate</p>
                <p className="text-2xl font-bold text-blue-600">{complianceRate}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts */}
      {rejectedDocuments > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            You have {rejectedDocuments} rejected document{rejectedDocuments > 1 ? 's' : ''} that need attention.
          </AlertDescription>
        </Alert>
      )}

      {pendingDocuments > 0 && (
        <Alert>
          <Clock className="h-4 w-4" />
          <AlertDescription>
            {pendingDocuments} document{pendingDocuments > 1 ? 's are' : ' is'} pending verification.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="verification">Verification</TabsTrigger>
          <TabsTrigger value="checklists">Checklists</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="reporting">Reporting</TabsTrigger>
          <TabsTrigger value="audit">Audit Trail</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Documents */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Recent Documents
                </CardTitle>
              </CardHeader>
              <CardContent>
                {userDocuments?.slice(0, 5).map((doc) => (
                  <div key={doc._id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                    <div>
                      <p className="font-medium text-sm">{doc.fileName}</p>
                      <p className="text-xs text-gray-500">
                        {new Date(doc.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <Badge 
                      variant="secondary" 
                      className={getStatusColor(doc.verificationStatus)}
                    >
                      {doc.verificationStatus}
                    </Badge>
                  </div>
                )) || (
                  <p className="text-gray-500 text-center py-4">No documents uploaded yet</p>
                )}
              </CardContent>
            </Card>

            {/* Compliance Checklists */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Compliance Checklists
                </CardTitle>
              </CardHeader>
              <CardContent>
                {complianceChecklists?.slice(0, 3).map((checklist) => (
                  <div key={checklist._id} className="py-2 border-b last:border-b-0">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-sm">{checklist.name}</p>
                        <p className="text-xs text-gray-500">{checklist.entityType}</p>
                      </div>
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                )) || (
                  <p className="text-gray-500 text-center py-4">No checklists available</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button 
                  variant="outline" 
                  className="h-20 flex-col gap-2"
                  onClick={() => setShowUpload(true)}
                >
                  <Upload className="h-6 w-6" />
                  Upload Document
                </Button>
                <Button 
                  variant="outline" 
                  className="h-20 flex-col gap-2"
                  onClick={() => setActiveTab('verification')}
                >
                  <Eye className="h-6 w-6" />
                  Review Documents
                </Button>
                <Button 
                  variant="outline" 
                  className="h-20 flex-col gap-2"
                  onClick={() => setActiveTab('audit')}
                >
                  <Download className="h-6 w-6" />
                  Generate Report
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents">
          <DocumentList 
            userId={userId} 
            onDocumentSelect={handleDocumentSelect}
          />
        </TabsContent>

        <TabsContent value="verification">
          <DocumentVerification 
            userId={userId}
            documentId={selectedDocumentId}
            showAllDocuments={!selectedDocumentId}
          />
        </TabsContent>

        <TabsContent value="checklists">
          <ComplianceChecklist 
            propertyId={propertyId}
            entityType={userId ? "tenant" : undefined}
            entityId={userId}
          />
        </TabsContent>

        <TabsContent value="monitoring">
          <ComplianceMonitoring propertyId={propertyId} />
        </TabsContent>

        <TabsContent value="reporting">
          <RegulatoryReporting propertyId={propertyId} />
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Audit Trail
              </CardTitle>
              <CardDescription>
                Track all compliance-related activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              {auditTrail?.map((entry) => (
                <div key={entry._id} className="flex items-start gap-3 py-3 border-b last:border-b-0">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <FileText className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{entry.details.description}</p>
                    <p className="text-xs text-gray-500">
                      {entry.action} • {new Date(entry.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              )) || (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No audit entries found</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Upload Modal */}
      {showUpload && userId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-4 border-b flex items-center justify-between">
              <h2 className="text-lg font-semibold">Upload Document</h2>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setShowUpload(false)}
              >
                <XCircle className="h-4 w-4" />
              </Button>
            </div>
            <div className="p-4">
              <DocumentUpload 
                userId={userId}
                onUploadComplete={handleUploadComplete}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}