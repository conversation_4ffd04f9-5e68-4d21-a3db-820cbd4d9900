/**
 * Tests for AccessibleButton component
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { AccessibilityProvider } from '../../../contexts/AccessibilityContext';
import { AccessibleButton } from '../AccessibleButton';

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <AccessibilityProvider>{children}</AccessibilityProvider>
);

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

describe('AccessibleButton', () => {
  it('renders with proper ARIA attributes', () => {
    render(
      <AccessibleButton aria-label="Test button">
        Click me
      </AccessibleButton>,
      { wrapper: TestWrapper }
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'Test button');
  });

  it('handles keyboard navigation correctly', async () => {
    const user = userEvent.setup();
    const mockClick = vi.fn();

    render(
      <AccessibleButton onClick={mockClick}>
        Click me
      </AccessibleButton>,
      { wrapper: TestWrapper }
    );

    const button = screen.getByRole('button');
    
    // Test Enter key
    button.focus();
    await user.keyboard('{Enter}');
    expect(mockClick).toHaveBeenCalledTimes(1);

    // Test Space key
    await user.keyboard(' ');
    expect(mockClick).toHaveBeenCalledTimes(2);
  });

  it('shows loading state correctly', () => {
    render(
      <AccessibleButton loading loadingText="Processing...">
        Submit
      </AccessibleButton>,
      { wrapper: TestWrapper }
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-busy', 'true');
    expect(button).toBeDisabled();
    expect(screen.getByText('Processing...')).toBeInTheDocument();
  });

  it('handles confirmation flow', async () => {
    const user = userEvent.setup();
    const mockClick = vi.fn();

    render(
      <AccessibleButton
        onClick={mockClick}
        requiresConfirmation
        confirmationText="Are you sure?"
      >
        Delete
      </AccessibleButton>,
      { wrapper: TestWrapper }
    );

    const button = screen.getByRole('button');
    
    // First click should show confirmation
    await user.click(button);
    expect(button).toHaveAttribute('data-confirming', 'true');
    expect(mockClick).not.toHaveBeenCalled();

    // Second click should execute action
    await user.click(button);
    expect(mockClick).toHaveBeenCalledTimes(1);
  });

  it('cancels confirmation with Escape key', async () => {
    const user = userEvent.setup();
    const mockClick = vi.fn();

    render(
      <AccessibleButton
        onClick={mockClick}
        requiresConfirmation
        confirmationText="Are you sure?"
      >
        Delete
      </AccessibleButton>,
      { wrapper: TestWrapper }
    );

    const button = screen.getByRole('button');
    
    // Show confirmation
    await user.click(button);
    expect(button).toHaveAttribute('data-confirming', 'true');

    // Cancel with Escape
    await user.keyboard('{Escape}');
    expect(button).toHaveAttribute('data-confirming', 'false');
    expect(mockClick).not.toHaveBeenCalled();
  });

  it('includes keyboard shortcut in aria-label', () => {
    render(
      <AccessibleButton shortcut="Ctrl+S">
        Save
      </AccessibleButton>,
      { wrapper: TestWrapper }
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'Save (Ctrl+S)');
  });

  it('handles disabled state correctly', () => {
    const mockClick = vi.fn();

    render(
      <AccessibleButton onClick={mockClick} disabled>
        Disabled Button
      </AccessibleButton>,
      { wrapper: TestWrapper }
    );

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    
    fireEvent.click(button);
    expect(mockClick).not.toHaveBeenCalled();
  });

  it('applies focus styles correctly', () => {
    render(
      <AccessibleButton>
        Focus me
      </AccessibleButton>,
      { wrapper: TestWrapper }
    );

    const button = screen.getByRole('button');
    expect(button).toHaveClass('focus-visible:ring-2');
    expect(button).toHaveClass('focus-visible:ring-ring');
    expect(button).toHaveClass('focus-visible:outline-none');
  });
});