import { describe, it, expect } from "vitest";
import { 
  validateMaintenanceTicketData,
  sanitizeMaintenanceTicketData,
  validateVendorData,
  sanitizeVendorData,
  validateTicketStatus,
  validateEscalationData,
  validateVendorAssignment,
  calculateSLADeadline
} from "../lib/validation";

describe("Maintenance Ticket Data Model Validation", () => {

  it("should validate maintenance ticket data correctly", () => {
    const validData = {
      propertyId: "property123",
      tenantId: "tenant123",
      title: "Leaking faucet",
      description: "The kitchen faucet is leaking water",
      category: "plumbing",
      priority: "medium",
    };

    expect(() => validateMaintenanceTicketData(validData)).not.toThrow();
  });

  it("should throw error for missing ticket title", () => {
    const invalidData = {
      propertyId: "property123",
      tenantId: "tenant123",
      title: "",
      description: "The kitchen faucet is leaking water",
      category: "plumbing",
      priority: "medium",
    };

    expect(() => validateMaintenanceTicketData(invalidData)).toThrow("Ticket title is required");
  });

  it("should throw error for invalid priority", () => {
    const invalidData = {
      propertyId: "property123",
      tenantId: "tenant123",
      title: "Leaking faucet",
      description: "The kitchen faucet is leaking water",
      category: "plumbing",
      priority: "invalid",
    };

    expect(() => validateMaintenanceTicketData(invalidData)).toThrow("Invalid priority level");
  });

  it("should throw error for invalid category", () => {
    const invalidData = {
      propertyId: "property123",
      tenantId: "tenant123",
      title: "Leaking faucet",
      description: "The kitchen faucet is leaking water",
      category: "invalid",
      priority: "medium",
    };

    expect(() => validateMaintenanceTicketData(invalidData)).toThrow("Invalid maintenance category");
  });

  it("should sanitize maintenance ticket data correctly", () => {
    const dirtyData = {
      title: "  Leaking faucet  ",
      description: "  The kitchen faucet is leaking water  ",
      category: "  plumbing  ",
    };

    const sanitized = sanitizeMaintenanceTicketData(dirtyData);
    expect(sanitized.title).toBe("Leaking faucet");
    expect(sanitized.description).toBe("The kitchen faucet is leaking water");
    expect(sanitized.category).toBe("plumbing");
  });

  it("should validate ticket status correctly", () => {
    const validStatuses = ["open", "assigned", "in_progress", "completed", "closed", "escalated"];
    
    validStatuses.forEach(status => {
      expect(() => validateTicketStatus(status)).not.toThrow();
    });
  });

  it("should throw error for invalid ticket status", () => {
    expect(() => validateTicketStatus("invalid")).toThrow("Invalid ticket status");
  });

  it("should calculate SLA deadline correctly for different priorities", () => {
    const now = Date.now();
    const maintenanceSLA = 24; // 24 hours

    // Emergency: 2 hours
    const emergencyDeadline = calculateSLADeadline("emergency", maintenanceSLA);
    expect(emergencyDeadline).toBeGreaterThan(now);
    expect(emergencyDeadline).toBeLessThanOrEqual(now + 2 * 60 * 60 * 1000 + 100);

    // High: 4 hours  
    const highDeadline = calculateSLADeadline("high", maintenanceSLA);
    expect(highDeadline).toBeGreaterThan(now);
    expect(highDeadline).toBeLessThanOrEqual(now + 4 * 60 * 60 * 1000 + 100);

    // Medium: 24 hours (default SLA)
    const mediumDeadline = calculateSLADeadline("medium", maintenanceSLA);
    expect(mediumDeadline).toBeGreaterThan(now);
    expect(mediumDeadline).toBeLessThanOrEqual(now + 24 * 60 * 60 * 1000 + 100);

    // Low: 48 hours (double the SLA)
    const lowDeadline = calculateSLADeadline("low", maintenanceSLA);
    expect(lowDeadline).toBeGreaterThan(now);
    expect(lowDeadline).toBeLessThanOrEqual(now + 48 * 60 * 60 * 1000 + 100);
  });

  it("should validate escalation data correctly", () => {
    const validData = {
      ticketId: "ticket123",
      escalatedTo: "manager123",
      reason: "sla_breach",
      notes: "Ticket has exceeded SLA deadline",
    };

    expect(() => validateEscalationData(validData)).not.toThrow();
  });

  it("should throw error for invalid escalation reason", () => {
    const invalidData = {
      ticketId: "ticket123",
      escalatedTo: "manager123",
      reason: "invalid_reason",
      notes: "Ticket has exceeded SLA deadline",
    };

    expect(() => validateEscalationData(invalidData)).toThrow("Invalid escalation reason");
  });

  it("should validate vendor assignment data correctly", () => {
    const validData = {
      ticketId: "ticket123",
      vendorId: "vendor123",
      estimatedCost: 2000,
      estimatedDuration: 2,
    };

    expect(() => validateVendorAssignment(validData)).not.toThrow();
  });

  it("should throw error for negative estimated cost", () => {
    const invalidData = {
      ticketId: "ticket123",
      vendorId: "vendor123",
      estimatedCost: -100,
      estimatedDuration: 2,
    };

    expect(() => validateVendorAssignment(invalidData)).toThrow("Estimated cost cannot be negative");
  });

});

describe("Vendor Management Validation", () => {
  it("should validate vendor data correctly", () => {
    const validData = {
      userId: "user123",
      companyName: "Best Plumbing Co",
      contactPerson: "John Plumber",
      phone: "+254700000000",
      email: "<EMAIL>",
      address: {
        street: "456 Vendor St",
        city: "Test City",
        state: "Test State",
        country: "Test Country",
        postalCode: "12345",
      },
      specialties: ["plumbing"],
      serviceAreas: ["property123"],
    };

    expect(() => validateVendorData(validData)).not.toThrow();
  });

  it("should throw error for missing company name", () => {
    const invalidData = {
      userId: "user123",
      companyName: "",
      contactPerson: "John Plumber",
      phone: "+254700000000",
      email: "<EMAIL>",
      address: {
        street: "456 Vendor St",
        city: "Test City",
        state: "Test State",
        country: "Test Country",
        postalCode: "12345",
      },
      specialties: ["plumbing"],
      serviceAreas: ["property123"],
    };

    expect(() => validateVendorData(invalidData)).toThrow("Company name is required");
  });

  it("should throw error for invalid phone number", () => {
    const invalidData = {
      userId: "user123",
      companyName: "Best Plumbing Co",
      contactPerson: "John Plumber",
      phone: "invalid-phone",
      email: "<EMAIL>",
      address: {
        street: "456 Vendor St",
        city: "Test City",
        state: "Test State",
        country: "Test Country",
        postalCode: "12345",
      },
      specialties: ["plumbing"],
      serviceAreas: ["property123"],
    };

    expect(() => validateVendorData(invalidData)).toThrow("Valid phone number is required");
  });

  it("should throw error for empty specialties", () => {
    const invalidData = {
      userId: "user123",
      companyName: "Best Plumbing Co",
      contactPerson: "John Plumber",
      phone: "+254700000000",
      email: "<EMAIL>",
      address: {
        street: "456 Vendor St",
        city: "Test City",
        state: "Test State",
        country: "Test Country",
        postalCode: "12345",
      },
      specialties: [],
      serviceAreas: ["property123"],
    };

    expect(() => validateVendorData(invalidData)).toThrow("At least one specialty is required");
  });

  it("should sanitize vendor data correctly", () => {
    const dirtyData = {
      companyName: "  Best Plumbing Co  ",
      contactPerson: "  John Plumber  ",
      phone: " +*********** 000 ",
      email: "  <EMAIL>  ",
      address: {
        street: "  456 Vendor St  ",
        city: "  Test City  ",
        state: "  Test State  ",
        country: "  Test Country  ",
        postalCode: "  12345  ",
      },
    };

    const sanitized = sanitizeVendorData(dirtyData);
    expect(sanitized.companyName).toBe("Best Plumbing Co");
    expect(sanitized.contactPerson).toBe("John Plumber");
    expect(sanitized.phone).toBe("+254700000000");
    expect(sanitized.email).toBe("<EMAIL>");
    expect(sanitized.address.street).toBe("456 Vendor St");
  });

});