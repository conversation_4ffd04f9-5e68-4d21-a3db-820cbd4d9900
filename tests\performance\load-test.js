import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up to 10 users
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 20 }, // Ramp up to 20 users
    { duration: '5m', target: 20 }, // Stay at 20 users
    { duration: '2m', target: 0 },  // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests should be below 500ms
    http_req_failed: ['rate<0.1'],    // Error rate should be below 10%
    errors: ['rate<0.1'],             // Custom error rate should be below 10%
  },
};

const BASE_URL = 'http://localhost:5173';

// Mock authentication token
const AUTH_TOKEN = 'mock-jwt-token';

export function setup() {
  // Setup test data
  console.log('Setting up performance test data...');
  
  // Create test users, properties, etc.
  const setupData = {
    users: [],
    properties: [],
    units: [],
  };
  
  return setupData;
}

export default function (data) {
  // Test scenarios
  const scenarios = [
    testAuthentication,
    testPropertyListing,
    testPropertyCreation,
    testUnitManagement,
    testPaymentProcessing,
    testMaintenanceTickets,
    testRealTimeUpdates,
  ];
  
  // Randomly select a scenario to simulate realistic user behavior
  const scenario = scenarios[Math.floor(Math.random() * scenarios.length)];
  scenario();
  
  sleep(1); // Wait 1 second between requests
}

function testAuthentication() {
  const loginPayload = {
    email: `user${Math.floor(Math.random() * 1000)}@example.com`,
    password: 'password123',
  };
  
  const response = http.post(`${BASE_URL}/api/auth/signin`, JSON.stringify(loginPayload), {
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  const success = check(response, {
    'login status is 200 or 401': (r) => r.status === 200 || r.status === 401,
    'login response time < 200ms': (r) => r.timings.duration < 200,
  });
  
  if (!success) {
    errorRate.add(1);
  }
}

function testPropertyListing() {
  const response = http.get(`${BASE_URL}/api/properties`, {
    headers: {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
  });
  
  const success = check(response, {
    'properties list status is 200': (r) => r.status === 200,
    'properties list response time < 300ms': (r) => r.timings.duration < 300,
    'properties list has data': (r) => {
      try {
        const data = JSON.parse(r.body);
        return Array.isArray(data);
      } catch {
        return false;
      }
    },
  });
  
  if (!success) {
    errorRate.add(1);
  }
}

function testPropertyCreation() {
  const propertyData = {
    name: `Test Property ${Math.floor(Math.random() * 10000)}`,
    type: 'residential',
    address: {
      street: `${Math.floor(Math.random() * 999)} Test Street`,
      city: 'Nairobi',
      state: 'Nairobi County',
      postalCode: '00100',
      country: 'Kenya',
    },
  };
  
  const response = http.post(`${BASE_URL}/api/properties`, JSON.stringify(propertyData), {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
  });
  
  const success = check(response, {
    'property creation status is 201': (r) => r.status === 201,
    'property creation response time < 500ms': (r) => r.timings.duration < 500,
    'property creation returns ID': (r) => {
      try {
        const data = JSON.parse(r.body);
        return data._id !== undefined;
      } catch {
        return false;
      }
    },
  });
  
  if (!success) {
    errorRate.add(1);
  }
}

function testUnitManagement() {
  // Simulate getting units for a property
  const propertyId = 'test-property-id';
  
  const response = http.get(`${BASE_URL}/api/properties/${propertyId}/units`, {
    headers: {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
  });
  
  const success = check(response, {
    'units list status is 200 or 404': (r) => r.status === 200 || r.status === 404,
    'units list response time < 250ms': (r) => r.timings.duration < 250,
  });
  
  if (!success) {
    errorRate.add(1);
  }
}

function testPaymentProcessing() {
  const paymentData = {
    invoiceId: 'test-invoice-id',
    amount: 50000,
    method: 'mpesa',
    phoneNumber: '254712345678',
  };
  
  const response = http.post(`${BASE_URL}/api/payments/mpesa`, JSON.stringify(paymentData), {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
  });
  
  const success = check(response, {
    'payment processing status is 200 or 400': (r) => r.status === 200 || r.status === 400,
    'payment processing response time < 1000ms': (r) => r.timings.duration < 1000,
  });
  
  if (!success) {
    errorRate.add(1);
  }
}

function testMaintenanceTickets() {
  const ticketData = {
    title: `Test Ticket ${Math.floor(Math.random() * 1000)}`,
    description: 'This is a test maintenance ticket for performance testing',
    priority: 'medium',
    category: 'general',
  };
  
  const response = http.post(`${BASE_URL}/api/maintenance/tickets`, JSON.stringify(ticketData), {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
  });
  
  const success = check(response, {
    'ticket creation status is 201': (r) => r.status === 201,
    'ticket creation response time < 400ms': (r) => r.timings.duration < 400,
  });
  
  if (!success) {
    errorRate.add(1);
  }
}

function testRealTimeUpdates() {
  // Test WebSocket connection for real-time updates
  const response = http.get(`${BASE_URL}/api/realtime/status`, {
    headers: {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
  });
  
  const success = check(response, {
    'realtime status is 200': (r) => r.status === 200,
    'realtime response time < 100ms': (r) => r.timings.duration < 100,
  });
  
  if (!success) {
    errorRate.add(1);
  }
}

export function teardown(data) {
  // Cleanup test data
  console.log('Cleaning up performance test data...');
}