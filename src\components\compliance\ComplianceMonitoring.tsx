import { useState, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Alert, AlertDescription } from '../ui/alert';
import { Progress } from '../ui/progress';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  TrendingDown,
  Eye,
  RefreshCw,
  Calendar,
  FileText,
  Users,
  Building,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { Id } from '../../../convex/_generated/dataModel';

interface ComplianceMonitoringProps {
  propertyId?: string;
}

export function ComplianceMonitoring({ propertyId }: ComplianceMonitoringProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedEntity, setSelectedEntity] = useState<{
    id: string;
    type: 'tenant' | 'vendor' | 'property' | 'lease';
  } | null>(null);

  // Fetch compliance data
  const complianceChecklists = useQuery(
    api.compliance.getComplianceChecklists,
    propertyId ? { propertyId: propertyId as Id<"properties"> } : {}
  );

  const complianceAlerts = useQuery(
    api.compliance.getComplianceAlerts,
    propertyId ? { propertyId: propertyId as Id<"properties">, limit: 20 } : { limit: 20 }
  );

  const auditTrail = useQuery(
    api.compliance.getAuditTrail,
    { limit: 10 }
  );

  // Mutations
  const runComplianceCheck = useMutation(api.compliance.runComplianceCheck);
  const scheduleReview = useMutation(api.compliance.scheduleComplianceReview);

  // Calculate metrics
  const totalAlerts = complianceAlerts?.length || 0;
  const criticalAlerts = complianceAlerts?.filter(alert => alert.severity === 'critical').length || 0;
  const highAlerts = complianceAlerts?.filter(alert => alert.severity === 'error').length || 0;
  const mediumAlerts = complianceAlerts?.filter(alert => alert.severity === 'warning').length || 0;

  const handleRunComplianceCheck = async (entityId: string, entityType: 'tenant' | 'vendor' | 'property' | 'lease') => {
    try {
      await runComplianceCheck({
        entityId,
        entityType,
        propertyId: propertyId as Id<"properties">,
        triggeredBy: "current-user" as Id<"users">, // Replace with actual user ID
      });
    } catch (error) {
      console.error('Failed to run compliance check:', error);
    }
  };

  const handleScheduleReview = async (entityId: string, entityType: 'tenant' | 'vendor' | 'property' | 'lease') => {
    try {
      const reviewDate = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7 days from now
      await scheduleReview({
        entityId,
        entityType,
        reviewDate,
        reviewType: 'manual',
        scheduledBy: "current-user" as Id<"users">, // Replace with actual user ID
      });
    } catch (error) {
      console.error('Failed to schedule review:', error);
    }
  };

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'warning': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default: return <AlertCircle className="h-4 w-4 text-blue-500" />;
    }
  };

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Compliance Monitoring
          </h1>
          <p className="text-gray-600 mt-1">
            Monitor compliance status, track alerts, and manage regulatory requirements
          </p>
        </div>
        <Button 
          onClick={() => window.location.reload()} 
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Alert Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Alerts</p>
                <p className="text-2xl font-bold">{totalAlerts}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critical</p>
                <p className="text-2xl font-bold text-red-600">{criticalAlerts}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">High Priority</p>
                <p className="text-2xl font-bold text-orange-600">{highAlerts}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Medium Priority</p>
                <p className="text-2xl font-bold text-yellow-600">{mediumAlerts}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Critical Alerts Banner */}
      {criticalAlerts > 0 && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            You have {criticalAlerts} critical compliance alert{criticalAlerts > 1 ? 's' : ''} that require immediate attention.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="checklists">Checklists</TabsTrigger>
          <TabsTrigger value="audit">Audit Trail</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Compliance Status Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Compliance Status Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Overall Compliance Rate</span>
                    <span className="text-sm font-bold">85%</span>
                  </div>
                  <Progress value={85} className="w-full" />
                  
                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">142</p>
                      <p className="text-xs text-gray-500">Compliant</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-red-600">25</p>
                      <p className="text-xs text-gray-500">Non-Compliant</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                {auditTrail?.slice(0, 5).map((entry) => (
                  <div key={entry._id} className="flex items-start gap-3 py-2 border-b last:border-b-0">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <FileText className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{entry.details.description}</p>
                      <p className="text-xs text-gray-500">
                        {new Date(entry.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )) || (
                  <p className="text-gray-500 text-center py-4">No recent activity</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button 
                  variant="outline" 
                  className="h-20 flex-col gap-2"
                  onClick={() => setActiveTab('alerts')}
                >
                  <AlertTriangle className="h-6 w-6" />
                  View All Alerts
                </Button>
                <Button 
                  variant="outline" 
                  className="h-20 flex-col gap-2"
                  onClick={() => setActiveTab('checklists')}
                >
                  <CheckCircle className="h-6 w-6" />
                  Manage Checklists
                </Button>
                <Button 
                  variant="outline" 
                  className="h-20 flex-col gap-2"
                  onClick={() => setActiveTab('audit')}
                >
                  <FileText className="h-6 w-6" />
                  Audit Trail
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Compliance Alerts
              </CardTitle>
              <CardDescription>
                Active compliance alerts requiring attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              {complianceAlerts?.length ? (
                <div className="space-y-3">
                  {complianceAlerts.map((alert) => (
                    <div 
                      key={`${alert.entityId}-${alert.createdAt}`}
                      className={`p-4 rounded-lg border ${getAlertColor(alert.severity)}`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          {getAlertIcon(alert.severity)}
                          <div>
                            <p className="font-medium text-sm">{alert.message}</p>
                            <p className="text-xs opacity-75">
                              {alert.entityType} • {new Date(alert.createdAt).toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleRunComplianceCheck(alert.entityId, alert.entityType)}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            Review
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleScheduleReview(alert.entityId, alert.entityType)}
                          >
                            <Calendar className="h-3 w-3 mr-1" />
                            Schedule
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <p className="text-gray-500">No active compliance alerts</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="checklists" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Compliance Checklists
              </CardTitle>
              <CardDescription>
                Manage compliance requirements and checklists
              </CardDescription>
            </CardHeader>
            <CardContent>
              {complianceChecklists?.map((checklist) => (
                <Card key={checklist._id} className="mb-4">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold">{checklist.name}</h3>
                        <p className="text-sm text-gray-500">{checklist.description}</p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="secondary">{checklist.entityType}</Badge>
                          <span className="text-xs text-gray-400">
                            {checklist.requirements.length} requirements
                          </span>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleRunComplianceCheck('sample-entity', checklist.entityType)}
                        >
                          <RefreshCw className="h-4 w-4 mr-1" />
                          Run Check
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )) || (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No compliance checklists found</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Compliance Audit Trail
              </CardTitle>
              <CardDescription>
                Complete history of compliance-related activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              {auditTrail?.map((entry) => (
                <div key={entry._id} className="flex items-start gap-3 py-3 border-b last:border-b-0">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <FileText className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{entry.details.description}</p>
                    <div className="flex items-center gap-4 mt-1">
                      <Badge variant="outline" className="text-xs">
                        {entry.action}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {entry.entityType}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(entry.timestamp).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              )) || (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No audit entries found</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}