import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { <PERSON><PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "../ui/tabs";
import { Badge } from "../ui/badge";
import { Smartphone, CreditCard, Building } from "lucide-react";
import { MPESAPayment } from "./MPESAPayment";
import { StripePayment } from "./StripePayment";
import { Id } from "../../../convex/_generated/dataModel";

interface PaymentOptionsProps {
  invoiceId: Id<"invoices">;
  amount: number;
  reference: string;
  description: string;
  customerEmail?: string;
  customerName?: string;
  preferredMethod?: "mpesa" | "stripe" | "bank_transfer";
  onPaymentSuccess?: (paymentId: Id<"payments">, method: string) => void;
  onPaymentFailure?: (error: string, method: string) => void;
}

export const PaymentOptions: React.FC<PaymentOptionsProps> = ({
  invoiceId,
  amount,
  reference,
  description,
  customerEmail,
  customerName,
  preferredMethod = "mpesa",
  onPaymentSuccess,
  onPaymentFailure,
}) => {
  const [selectedMethod, setSelectedMethod] = useState(preferredMethod);

  const handlePaymentSuccess = (paymentId: Id<"payments">) => {
    onPaymentSuccess?.(paymentId, selectedMethod);
  };

  const handlePaymentFailure = (error: string) => {
    onPaymentFailure?.(error, selectedMethod);
  };

  const paymentMethods = [
    {
      id: "mpesa",
      name: "M-PESA",
      description: "Pay with your mobile money",
      icon: Smartphone,
      color: "text-green-600",
      available: true,
      recommended: true,
    },
    {
      id: "stripe",
      name: "Card Payment",
      description: "Pay with credit/debit card",
      icon: CreditCard,
      color: "text-blue-600",
      available: true,
      recommended: false,
    },
    {
      id: "bank_transfer",
      name: "Bank Transfer",
      description: "Direct bank transfer",
      icon: Building,
      color: "text-gray-600",
      available: false,
      recommended: false,
    },
  ];

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Payment Options</CardTitle>
        <CardDescription>
          Choose your preferred payment method for {reference}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={selectedMethod} onValueChange={(value) => setSelectedMethod(value as "mpesa" | "stripe" | "bank_transfer")} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            {paymentMethods.map((method) => (
              <TabsTrigger
                key={method.id}
                value={method.id}
                disabled={!method.available}
                className="flex items-center gap-2"
              >
                <method.icon className={`h-4 w-4 ${method.color}`} />
                {method.name}
                {method.recommended && (
                  <Badge variant="secondary" className="text-xs">
                    Recommended
                  </Badge>
                )}
              </TabsTrigger>
            ))}
          </TabsList>

          <div className="mt-6">
            <TabsContent value="mpesa" className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <Smartphone className="h-5 w-5 text-green-600" />
                <h3 className="font-semibold">M-PESA Payment</h3>
                <Badge variant="secondary">Recommended</Badge>
              </div>
              <p className="text-sm text-muted-foreground mb-4">
                Pay instantly using your M-PESA mobile money account. You'll receive an STK push notification on your phone.
              </p>
              <MPESAPayment
                invoiceId={invoiceId}
                amount={amount}
                reference={reference}
                onPaymentSuccess={handlePaymentSuccess}
                onPaymentFailure={handlePaymentFailure}
              />
            </TabsContent>

            <TabsContent value="stripe" className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <CreditCard className="h-5 w-5 text-blue-600" />
                <h3 className="font-semibold">Card Payment</h3>
              </div>
              <p className="text-sm text-muted-foreground mb-4">
                Pay securely with your credit or debit card. Powered by Stripe for maximum security.
              </p>
              <StripePayment
                invoiceId={invoiceId}
                amount={amount}
                description={description}
                customerEmail={customerEmail}
                customerName={customerName}
                onPaymentSuccess={handlePaymentSuccess}
                onPaymentFailure={handlePaymentFailure}
              />
            </TabsContent>

            <TabsContent value="bank_transfer" className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <Building className="h-5 w-5 text-gray-600" />
                <h3 className="font-semibold">Bank Transfer</h3>
                <Badge variant="outline">Coming Soon</Badge>
              </div>
              <p className="text-sm text-muted-foreground mb-4">
                Direct bank transfer option will be available soon. Please use M-PESA or card payment for now.
              </p>
              <Card className="p-4 bg-muted">
                <p className="text-sm text-center text-muted-foreground">
                  Bank transfer functionality is currently under development.
                  Please choose an alternative payment method.
                </p>
              </Card>
            </TabsContent>
          </div>
        </Tabs>

        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">Payment Summary</h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>Invoice:</span>
              <span>{reference}</span>
            </div>
            <div className="flex justify-between">
              <span>Amount:</span>
              <span className="font-medium">KES {amount.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span>Description:</span>
              <span>{description}</span>
            </div>
          </div>
        </div>

        <div className="mt-4 text-xs text-muted-foreground text-center">
          <p>All payments are processed securely. Your payment information is encrypted and protected.</p>
        </div>
      </CardContent>
    </Card>
  );
};