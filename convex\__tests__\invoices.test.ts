import { convexTest } from "convex-test";
import { expect, test } from "vitest";
import { api } from "../_generated/api";
import schema from "../schema";

test("rent roll generation and invoice management", async () => {
  const t = convexTest(schema);

  // Create test data
  const ownerId = await t.mutation(api.users.createUser, {
    email: "<EMAIL>",
    name: "<PERSON> Owner",
    role: "owner",
  });

  const tenantId = await t.mutation(api.users.createUser, {
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "tenant",
  });

  const propertyId = await t.mutation(api.properties.createProperty, {
    name: "Test Property",
    type: "residential",
    address: {
      street: "123 Test St",
      city: "Test City",
      state: "TS",
      country: "Test Country",
      postalCode: "12345",
    },
    ownerId,
    branding: {
      primaryColor: "#000000",
      secondaryColor: "#ffffff",
    },
    settings: {
      currency: "USD",
      timezone: "UTC",
      language: "en",
      autoRentReminders: true,
      maintenanceSLA: 24,
    },
  });

  const unitId = await t.mutation(api.units.createUnit, {
    propertyId,
    unitNumber: "101",
    type: "apartment",
    size: 1000,
    rent: 1500,
    deposit: 1500,
    amenities: [],
  });

  const leaseId = await t.mutation(api.leases.createLease, {
    propertyId,
    unitId,
    tenantId,
    startDate: Date.now(),
    endDate: Date.now() + (365 * 24 * 60 * 60 * 1000), // 1 year
    monthlyRent: 1500,
    deposit: 1500,
    terms: {
      noticePeriod: 30,
      lateFeePercentage: 5,
      gracePeriod: 5,
      renewalOption: true,
    },
  });

  // Activate the lease
  await t.mutation(api.leases.updateLeaseStatus, {
    id: leaseId,
    status: "active",
  });

  // Test rent roll generation
  const currentDate = new Date();
  const rentRoll = await t.mutation(api.invoices.generateRentRoll, {
    propertyId,
    month: currentDate.getMonth() + 1,
    year: currentDate.getFullYear(),
    includeLateFees: false,
  });

  expect(rentRoll.entries).toHaveLength(1);
  expect(rentRoll.summary.totalUnits).toBe(1);
  expect(rentRoll.summary.totalRent).toBe(1500);
  expect(rentRoll.entries[0].rent).toBe(1500);
  expect(rentRoll.entries[0].status).toBe("pending");

  // Test individual invoice creation
  const invoiceId = await t.mutation(api.invoices.createInvoice, {
    leaseId,
    amount: 100,
    dueDate: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days from now
    type: "maintenance",
    items: [
      {
        description: "Plumbing repair",
        amount: 100,
        quantity: 1,
      },
    ],
  });

  expect(invoiceId).toBeDefined();

  // Test getting invoices by lease
  const leaseInvoices = await t.query(api.invoices.getInvoicesByLease, {
    leaseId,
  });

  expect(leaseInvoices.length).toBeGreaterThanOrEqual(2); // Rent roll + maintenance invoice

  // Test updating invoice status
  await t.mutation(api.invoices.updateInvoiceStatus, {
    invoiceId,
    status: "paid",
    paidAt: Date.now(),
    paymentMethod: "stripe",
  });

  // Verify invoice was updated
  const updatedInvoice = await t.query(api.invoices.getInvoiceById, {
    id: invoiceId,
  });

  expect(updatedInvoice?.invoice.status).toBe("paid");
  expect(updatedInvoice?.invoice.paymentMethod).toBe("stripe");

  // Test getting invoices by tenant
  const tenantInvoices = await t.query(api.invoices.getInvoicesByTenant, {
    tenantId,
  });

  expect(tenantInvoices.length).toBeGreaterThanOrEqual(2);

  // Test financial summary
  const financialSummary = await t.query(api.invoices.getPropertyFinancialSummary, {
    propertyId,
  });

  expect(financialSummary.summary.totalInvoiced).toBeGreaterThan(0);
  expect(financialSummary.summary.totalPaid).toBeGreaterThan(0);
  expect(financialSummary.invoiceCount.total).toBeGreaterThanOrEqual(2);
});

test("recurring invoice generation", async () => {
  const t = convexTest(schema);

  // Create minimal test data
  const ownerId = await t.mutation(api.users.createUser, {
    email: "<EMAIL>",
    name: "Jane Owner",
    role: "owner",
  });

  const tenantId = await t.mutation(api.users.createUser, {
    email: "<EMAIL>",
    name: "John Tenant",
    role: "tenant",
  });

  const propertyId = await t.mutation(api.properties.createProperty, {
    name: "Test Property",
    type: "residential",
    address: {
      street: "123 Test St",
      city: "Test City",
      state: "TS",
      country: "Test Country",
      postalCode: "12345",
    },
    ownerId,
    branding: {
      primaryColor: "#000000",
      secondaryColor: "#ffffff",
    },
    settings: {
      currency: "USD",
      timezone: "UTC",
      language: "en",
      autoRentReminders: true,
      maintenanceSLA: 24,
    },
  });

  const unitId = await t.mutation(api.units.createUnit, {
    propertyId,
    unitNumber: "101",
    type: "apartment",
    size: 1000,
    rent: 1200,
    deposit: 1200,
    amenities: [],
  });

  const leaseId = await t.mutation(api.leases.createLease, {
    propertyId,
    unitId,
    tenantId,
    startDate: Date.now(),
    endDate: Date.now() + (365 * 24 * 60 * 60 * 1000),
    monthlyRent: 1200,
    deposit: 1200,
    terms: {
      noticePeriod: 30,
      lateFeePercentage: 5,
      gracePeriod: 5,
      renewalOption: true,
    },
  });

  // Activate the lease
  await t.mutation(api.leases.updateLeaseStatus, {
    id: leaseId,
    status: "active",
  });

  // Test recurring invoice generation
  const result = await t.mutation(api.invoices.generateRecurringInvoices, {
    targetDate: Date.now(),
  });

  expect(result.generatedCount).toBe(1);
  expect(result.invoiceIds).toHaveLength(1);

  // Verify invoice was created
  const invoices = await t.query(api.invoices.getInvoicesByLease, {
    leaseId,
  });

  expect(invoices).toHaveLength(1);
  expect(invoices[0].type).toBe("rent");
  expect(invoices[0].amount).toBe(1200);
  expect(invoices[0].status).toBe("pending");

  // Test that running again doesn't create duplicate invoices
  const secondResult = await t.mutation(api.invoices.generateRecurringInvoices, {
    targetDate: Date.now(),
  });

  expect(secondResult.generatedCount).toBe(0); // No new invoices should be created
});

test("overdue invoice detection", async () => {
  const t = convexTest(schema);

  // Create test data
  const ownerId = await t.mutation(api.users.createUser, {
    email: "<EMAIL>",
    name: "Jane Owner",
    role: "owner",
  });

  const tenantId = await t.mutation(api.users.createUser, {
    email: "<EMAIL>",
    name: "John Tenant",
    role: "tenant",
  });

  const propertyId = await t.mutation(api.properties.createProperty, {
    name: "Test Property",
    type: "residential",
    address: {
      street: "123 Test St",
      city: "Test City",
      state: "TS",
      country: "Test Country",
      postalCode: "12345",
    },
    ownerId,
    branding: {
      primaryColor: "#000000",
      secondaryColor: "#ffffff",
    },
    settings: {
      currency: "USD",
      timezone: "UTC",
      language: "en",
      autoRentReminders: true,
      maintenanceSLA: 24,
    },
  });

  const unitId = await t.mutation(api.units.createUnit, {
    propertyId,
    unitNumber: "101",
    type: "apartment",
    size: 1000,
    rent: 1000,
    deposit: 1000,
    amenities: [],
  });

  const leaseId = await t.mutation(api.leases.createLease, {
    propertyId,
    unitId,
    tenantId,
    startDate: Date.now(),
    endDate: Date.now() + (365 * 24 * 60 * 60 * 1000),
    monthlyRent: 1000,
    deposit: 1000,
    terms: {
      noticePeriod: 30,
      lateFeePercentage: 5,
      gracePeriod: 5,
      renewalOption: true,
    },
  });

  // Create an overdue invoice (due date in the past)
  const overdueInvoiceId = await t.mutation(api.invoices.createInvoice, {
    leaseId,
    amount: 1000,
    dueDate: Date.now() - (10 * 24 * 60 * 60 * 1000), // 10 days ago
    type: "rent",
    items: [
      {
        description: "Monthly rent",
        amount: 1000,
        quantity: 1,
      },
    ],
  });

  // Test getting overdue invoices
  const overdueInvoices = await t.query(api.invoices.getOverdueInvoices, {
    propertyId,
  });

  expect(overdueInvoices).toHaveLength(1);
  expect(overdueInvoices[0].invoice._id).toBe(overdueInvoiceId);
  expect(overdueInvoices[0].daysOverdue).toBeGreaterThan(0);

  // Verify the invoice status was updated to overdue
  const updatedInvoice = await t.query(api.invoices.getInvoiceById, {
    id: overdueInvoiceId,
  });

  expect(updatedInvoice?.invoice.status).toBe("overdue");
});