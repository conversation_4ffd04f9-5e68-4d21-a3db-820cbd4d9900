/**
 * Reconciliation Manager Component
 * Provides UI for managing offline sync reconciliation and conflict resolution
 */

import React, { useState, useEffect } from "react";
import { useConvex } from "convex/react";
import { useOfflineReconciliation } from "../../hooks/useOfflineReconciliation";
import {
  ConflictInfo,
  ConflictResolutionStrategy,
} from "../../lib/offline-sync-reconciliation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Button } from "../ui/button";
import { Progress } from "../ui/progress";
import { Badge } from "../ui/badge";
import { Alert, AlertDescription } from "../ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  Settings,
  Eye,
  Play,
  X,
} from "lucide-react";

interface ReconciliationManagerProps {
  onClose?: () => void;
  autoStart?: boolean;
}

export function ReconciliationManager({
  onClose,
  autoStart = false,
}: ReconciliationManagerProps) {
  const convex = useConvex();
  const {
    state,
    preview,
    pendingOperationsCount,
    isReconciliationNeeded,
    startReconciliation,
    previewReconciliation,
    resolveConflict,
    autoReconcile,
    clearState,
    getStatusSummary,
  } = useOfflineReconciliation(convex);

  const [selectedStrategy, setSelectedStrategy] =
    useState<ConflictResolutionStrategy>("last_write_wins");
  const [showPreview, setShowPreview] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  const statusSummary = getStatusSummary();

  // Auto-start reconciliation if requested
  useEffect(() => {
    if (autoStart && isReconciliationNeeded && !state.isReconciling) {
      autoReconcile();
    }
  }, [autoStart, isReconciliationNeeded, state.isReconciling, autoReconcile]);

  const handlePreview = async () => {
    try {
      setShowPreview(true);
      await previewReconciliation({ strategy: selectedStrategy });
    } catch (error) {
      console.error("Preview failed:", error);
    }
  };

  const handleStartReconciliation = async () => {
    try {
      await startReconciliation({
        strategy: selectedStrategy,
        batchSize: 50,
        maxRetries: 3,
      });
    } catch (error) {
      console.error("Reconciliation failed:", error);
    }
  };

  const getStatusIcon = () => {
    switch (statusSummary.status) {
      case "reconciling":
        return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />;
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "conflicts":
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case "synced":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
  };

  const getStatusColor = () => {
    switch (statusSummary.status) {
      case "reconciling":
        return "blue";
      case "error":
        return "red";
      case "pending":
        return "yellow";
      case "conflicts":
        return "orange";
      case "synced":
        return "green";
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-4 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <div>
            <h2 className="text-2xl font-bold">Sync Reconciliation</h2>
            <p className="text-sm text-gray-600">{statusSummary.message}</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge variant="outline" className={`text-${getStatusColor()}-600`}>
            {statusSummary.status.toUpperCase()}
          </Badge>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      {state.isReconciling && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{state.currentOperation}</span>
                <span>{Math.round(state.progress)}%</span>
              </div>
              <Progress value={state.progress} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Alert */}
      {state.error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="conflicts">
            Conflicts{" "}
            {state.conflicts.length > 0 && `(${state.conflicts.length})`}
          </TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Pending Operations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {pendingOperationsCount}
                </div>
                <p className="text-xs text-gray-600">
                  Operations waiting to sync
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Conflicts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {state.conflicts.length}
                </div>
                <p className="text-xs text-gray-600">Requiring resolution</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Strategy</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm font-medium">
                  {selectedStrategy.replace("_", " ")}
                </div>
                <p className="text-xs text-gray-600">Conflict resolution</p>
              </CardContent>
            </Card>
          </div>

          {/* Preview Section */}
          {showPreview && preview && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  Reconciliation Preview
                </CardTitle>
                <CardDescription>
                  What will happen when reconciliation runs
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium">
                      Operations to Sync
                    </div>
                    <div className="text-2xl font-bold">
                      {preview.operationsToSync}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium">
                      Potential Conflicts
                    </div>
                    <div className="text-2xl font-bold">
                      {preview.potentialConflicts.length}
                    </div>
                  </div>
                </div>

                <div>
                  <div className="text-sm font-medium mb-2">
                    Estimated Duration
                  </div>
                  <div className="text-lg">
                    {Math.ceil(preview.estimatedDuration / 1000)}s
                  </div>
                </div>

                {preview.potentialConflicts.length > 0 && (
                  <div>
                    <div className="text-sm font-medium mb-2">
                      Potential Conflicts
                    </div>
                    <div className="space-y-2">
                      {preview.potentialConflicts
                        .slice(0, 3)
                        .map((conflict, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-orange-50 rounded"
                          >
                            <span className="text-sm">
                              {conflict.entityType}: {conflict.entityId}
                            </span>
                            <Badge variant="outline">
                              {conflict.conflictType}
                            </Badge>
                          </div>
                        ))}
                      {preview.potentialConflicts.length > 3 && (
                        <div className="text-sm text-gray-600">
                          +{preview.potentialConflicts.length - 3} more
                          conflicts
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <Button
              onClick={handlePreview}
              variant="outline"
              disabled={state.isReconciling || !isReconciliationNeeded}
            >
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>

            <Button
              onClick={handleStartReconciliation}
              disabled={state.isReconciling || !isReconciliationNeeded}
            >
              <Play className="h-4 w-4 mr-2" />
              Start Reconciliation
            </Button>

            <Button
              onClick={() => autoReconcile()}
              variant="outline"
              disabled={state.isReconciling || !isReconciliationNeeded}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Auto Reconcile
            </Button>
          </div>
        </TabsContent>

        {/* Conflicts Tab */}
        <TabsContent value="conflicts" className="space-y-4">
          {state.conflicts.length === 0 ? (
            <Card>
              <CardContent className="pt-6 text-center">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Conflicts</h3>
                <p className="text-gray-600">
                  All changes can be synchronized automatically.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {state.conflicts.map((conflict, index) => (
                <ConflictResolutionCard
                  key={`${conflict.entityType}-${conflict.entityId}`}
                  conflict={conflict}
                  onResolve={resolveConflict}
                />
              ))}
            </div>
          )}
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Reconciliation Settings</CardTitle>
              <CardDescription>
                Configure how conflicts should be resolved
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Conflict Resolution Strategy
                </label>
                <select
                  value={selectedStrategy}
                  onChange={(e) =>
                    setSelectedStrategy(
                      e.target.value as ConflictResolutionStrategy
                    )
                  }
                  className="w-full p-2 border rounded-md"
                >
                  <option value="last_write_wins">Last Write Wins</option>
                  <option value="local_wins">Local Changes Win</option>
                  <option value="server_wins">Server Changes Win</option>
                  <option value="merge_fields">Merge Fields</option>
                  <option value="manual">Manual Resolution</option>
                </select>
                <p className="text-xs text-gray-600 mt-1">
                  How to handle conflicts when both local and server data have
                  changed
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results" className="space-y-4">
          {state.result ? (
            <ReconciliationResults result={state.result} />
          ) : (
            <Card>
              <CardContent className="pt-6 text-center">
                <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Results Yet</h3>
                <p className="text-gray-600">
                  Run reconciliation to see results here.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Conflict Resolution Card Component
function ConflictResolutionCard({
  conflict,
  onResolve,
}: {
  conflict: ConflictInfo;
  onResolve: (
    conflict: ConflictInfo,
    resolution: "local" | "server" | "merge",
    mergedData?: any
  ) => Promise<void>;
}) {
  const [isResolving, setIsResolving] = useState(false);

  const handleResolve = async (resolution: "local" | "server" | "merge") => {
    setIsResolving(true);
    try {
      await onResolve(conflict, resolution);
    } catch (error) {
      console.error("Failed to resolve conflict:", error);
    } finally {
      setIsResolving(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            {conflict.entityType}: {conflict.entityId}
          </CardTitle>
          <Badge variant="outline">{conflict.conflictType}</Badge>
        </div>
        <CardDescription>
          Local version: {new Date(conflict.localVersion).toLocaleString()} |
          Server version: {new Date(conflict.serverVersion).toLocaleString()}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium mb-2">Local Changes</h4>
            <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-32">
              {JSON.stringify(conflict.localData, null, 2)}
            </pre>
          </div>
          <div>
            <h4 className="font-medium mb-2">Server Changes</h4>
            <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-32">
              {JSON.stringify(conflict.serverData, null, 2)}
            </pre>
          </div>
        </div>

        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleResolve("local")}
            disabled={isResolving}
          >
            Use Local
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleResolve("server")}
            disabled={isResolving}
          >
            Use Server
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleResolve("merge")}
            disabled={isResolving}
          >
            Merge
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// Results Component
function ReconciliationResults({ result }: { result: any }) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            {result.success ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <AlertTriangle className="h-5 w-5 text-red-500" />
            )}
            <span>
              Reconciliation {result.success ? "Completed" : "Failed"}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <div className="text-sm font-medium">Synced</div>
              <div className="text-2xl font-bold text-green-600">
                {result.syncedOperations}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium">Resolved</div>
              <div className="text-2xl font-bold text-blue-600">
                {result.resolvedConflicts}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium">Failed</div>
              <div className="text-2xl font-bold text-red-600">
                {result.failedOperations}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium">Conflicts</div>
              <div className="text-2xl font-bold text-orange-600">
                {result.conflicts.length}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {result.errors.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Errors</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {result.errors.map((error: string, index: number) => (
                <div
                  key={index}
                  className="text-sm text-red-600 bg-red-50 p-2 rounded"
                >
                  {error}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
