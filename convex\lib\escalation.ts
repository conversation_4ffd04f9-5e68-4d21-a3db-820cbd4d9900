import { v } from "convex/values";
import { internalMutation, internalQuery } from "../_generated/server";
import { Doc, Id } from "../_generated/dataModel";

// Main function called by cron job
export const checkAndProcessSLABreaches = internalMutation({
  args: {},
  handler: async (ctx) => {
    // Check for tickets needing escalation or warnings
    const { ticketsNeedingEscalation, ticketsNeedingSLAWarning } = await checkSLABreaches(ctx, {});

    // Auto-escalate tickets that have breached SLA
    if (ticketsNeedingEscalation.length > 0) {
      const escalationData = ticketsNeedingEscalation.map(({ ticket, reason }) => ({
        ticketId: ticket._id,
        reason,
      }));

      await autoEscalateTickets(ctx, { tickets: escalationData });
    }

    // Send SLA warnings
    if (ticketsNeedingSLAWarning.length > 0) {
      const warningData = ticketsNeedingSLAWarning.map(({ ticket, reason }) => ({
        ticketId: ticket._id,
        reason,
      }));

      await sendSLAWarnings(ctx, { tickets: warningData });
    }

    return {
      escalatedTickets: ticketsNeedingEscalation.length,
      warningsSent: ticketsNeedingSLAWarning.length,
      timestamp: Date.now(),
    };
  },
});

// Escalation Rules and Triggers

export const checkSLABreaches = internalQuery({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    // Get all active tickets that might need escalation
    const activeTickets = await ctx.db
      .query("maintenanceTickets")
      .filter((q) => 
        q.and(
          q.neq(q.field("status"), "completed"),
          q.neq(q.field("status"), "closed"),
          q.neq(q.field("status"), "escalated")
        )
      )
      .collect();

    const ticketsNeedingEscalation = [];
    const ticketsNeedingSLAWarning = [];

    for (const ticket of activeTickets) {
      // Check if SLA deadline has passed
      if (now > ticket.slaDeadline) {
        ticketsNeedingEscalation.push({
          ticket,
          reason: "sla_breach" as const,
          urgency: "high" as const,
        });
      }
      // Check if escalation warning deadline has passed (80% of SLA)
      else if (ticket.escalationDeadline && now > ticket.escalationDeadline) {
        ticketsNeedingSLAWarning.push({
          ticket,
          reason: "sla_warning" as const,
          urgency: "medium" as const,
        });
      }
    }

    return {
      ticketsNeedingEscalation,
      ticketsNeedingSLAWarning,
    };
  },
});

export const autoEscalateTickets = internalMutation({
  args: {
    tickets: v.array(v.object({
      ticketId: v.id("maintenanceTickets"),
      reason: v.union(
        v.literal("sla_breach"),
        v.literal("vendor_unavailable"),
        v.literal("complexity"),
        v.literal("cost_approval"),
        v.literal("tenant_complaint")
      ),
    })),
  },
  handler: async (ctx, args) => {
    const escalatedTickets = [];

    for (const { ticketId, reason } of args.tickets) {
      const ticket = await ctx.db.get(ticketId);
      if (!ticket) continue;

      // Get property to find manager for escalation
      const property = await ctx.db.get(ticket.propertyId);
      if (!property || !property.managerId) continue;

      // Check if already escalated recently
      const recentEscalations = await ctx.db
        .query("maintenanceEscalations")
        .withIndex("by_ticket", (q) => q.eq("ticketId", ticketId))
        .filter((q) => q.gt(q.field("createdAt"), Date.now() - 24 * 60 * 60 * 1000)) // Last 24 hours
        .collect();

      if (recentEscalations.length > 0) continue;

      const now = Date.now();

      // Determine escalation target based on current escalation level
      const allEscalations = await ctx.db
        .query("maintenanceEscalations")
        .withIndex("by_ticket", (q) => q.eq("ticketId", ticketId))
        .collect();

      const escalationLevel = allEscalations.length + 1;
      let escalatedTo = property.managerId;

      // For higher escalation levels, escalate to property owner
      if (escalationLevel > 1) {
        escalatedTo = property.ownerId;
      }

      // Create escalation record
      const escalationId = await ctx.db.insert("maintenanceEscalations", {
        ticketId,
        propertyId: ticket.propertyId,
        escalationLevel,
        escalatedTo,
        reason,
        escalationRules: {
          triggerCondition: reason,
          autoEscalate: true,
          notificationSent: true,
        },
        createdAt: now,
      });

      // Update ticket status and add escalation note
      await ctx.db.patch(ticketId, {
        status: "escalated",
        escalationHistory: [
          ...ticket.escalationHistory,
          {
            escalatedBy: property.managerId, // System escalation
            escalatedTo,
            reason: `Auto-escalated due to ${reason}`,
            timestamp: now,
          },
        ],
        notes: [
          ...ticket.notes,
          {
            userId: property.managerId,
            message: `Ticket auto-escalated due to ${reason}`,
            timestamp: now,
            type: "escalation" as const,
          },
        ],
        updatedAt: now,
      });

      // Create escalation notification
      await ctx.db.insert("notifications", {
        userId: escalatedTo,
        title: `Auto-Escalated Maintenance Ticket: ${ticket.title}`,
        message: `A ${ticket.priority} priority maintenance ticket has been auto-escalated due to ${reason}`,
        type: "maintenance_escalated",
        priority: "urgent",
        isRead: false,
        actionUrl: `/maintenance/${ticketId}`,
        metadata: { 
          ticketId,
          escalationId,
        },
        createdAt: now,
      });

      // Notify original assignees about escalation
      const notificationTargets = [ticket.tenantId];
      if (ticket.vendorId) {
        notificationTargets.push(ticket.vendorId);
      }

      for (const targetUserId of notificationTargets) {
        if (targetUserId !== escalatedTo) {
          await ctx.db.insert("notifications", {
            userId: targetUserId,
            title: `Maintenance Ticket Escalated: ${ticket.title}`,
            message: `Your maintenance ticket has been escalated due to ${reason}`,
            type: "maintenance_update",
            priority: "high",
            isRead: false,
            actionUrl: `/maintenance/${ticketId}`,
            metadata: { ticketId },
            createdAt: now,
          });
        }
      }

      escalatedTickets.push({
        ticketId,
        escalationId,
        escalatedTo,
        reason,
      });
    }

    return escalatedTickets;
  },
});

export const sendSLAWarnings = internalMutation({
  args: {
    tickets: v.array(v.object({
      ticketId: v.id("maintenanceTickets"),
      reason: v.string(),
    })),
  },
  handler: async (ctx, args) => {
    const warningsSent = [];

    for (const { ticketId, reason } of args.tickets) {
      const ticket = await ctx.db.get(ticketId);
      if (!ticket) continue;

      const property = await ctx.db.get(ticket.propertyId);
      if (!property) continue;

      const now = Date.now();
      const timeRemaining = ticket.slaDeadline - now;
      const hoursRemaining = Math.max(0, Math.floor(timeRemaining / (1000 * 60 * 60)));

      // Notify all stakeholders about SLA warning
      const notificationTargets = [ticket.tenantId];
      
      if (ticket.vendorId) {
        notificationTargets.push(ticket.vendorId);
      }
      
      if (property.managerId) {
        notificationTargets.push(property.managerId);
      }

      for (const targetUserId of notificationTargets) {
        await ctx.db.insert("notifications", {
          userId: targetUserId,
          title: `SLA Warning: ${ticket.title}`,
          message: `This maintenance ticket is approaching its SLA deadline. ${hoursRemaining} hours remaining.`,
          type: "sla_warning",
          priority: "high",
          isRead: false,
          actionUrl: `/maintenance/${ticketId}`,
          metadata: { ticketId },
          createdAt: now,
        });
      }

      warningsSent.push({
        ticketId,
        notificationsSent: notificationTargets.length,
        hoursRemaining,
      });
    }

    return warningsSent;
  },
});

export const checkVendorAvailability = internalQuery({
  args: {
    vendorId: v.id("users"),
    category: v.union(
      v.literal("plumbing"),
      v.literal("electrical"),
      v.literal("hvac"),
      v.literal("appliance"),
      v.literal("structural"),
      v.literal("cleaning"),
      v.literal("security"),
      v.literal("other")
    ),
  },
  handler: async (ctx, args) => {
    const vendor = await ctx.db
      .query("vendors")
      .withIndex("by_user", (q) => q.eq("userId", args.vendorId))
      .first();

    if (!vendor || !vendor.isActive || !vendor.isVerified) {
      return { available: false, reason: "vendor_inactive" };
    }

    // Check if vendor has the required specialty
    if (!vendor.specialties.includes(args.category)) {
      return { available: false, reason: "specialty_mismatch" };
    }

    // Check current workload
    const activeTickets = await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_vendor_status", (q) => 
        q.eq("vendorId", args.vendorId).eq("status", "assigned")
      )
      .collect();

    const inProgressTickets = await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_vendor_status", (q) => 
        q.eq("vendorId", args.vendorId).eq("status", "in_progress")
      )
      .collect();

    const totalActiveTickets = activeTickets.length + inProgressTickets.length;

    // Simple workload check - if vendor has more than 5 active tickets, consider unavailable
    if (totalActiveTickets >= 5) {
      return { 
        available: false, 
        reason: "overloaded",
        activeTickets: totalActiveTickets,
      };
    }

    // Check working hours (simplified - assumes current time is in vendor's timezone)
    const now = new Date();
    const currentDay = now.toLocaleLowerCase().slice(0, 3) + 
                      now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase().slice(3);
    
    const isWorkingDay = vendor.availability.workingDays.includes(currentDay as any);
    
    if (!isWorkingDay && !vendor.availability.emergencyAvailable) {
      return { 
        available: false, 
        reason: "outside_working_hours",
        workingDays: vendor.availability.workingDays,
      };
    }

    return { 
      available: true, 
      activeTickets: totalActiveTickets,
      performance: vendor.performance,
    };
  },
});

export const suggestVendorReassignment = internalQuery({
  args: {
    ticketId: v.id("maintenanceTickets"),
  },
  handler: async (ctx, args) => {
    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) return null;

    // Get all vendors that can handle this category and service this property
    const allVendors = await ctx.db
      .query("vendors")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .collect();

    const availableVendors = [];

    for (const vendor of allVendors) {
      if (!vendor.isVerified) continue;
      if (!vendor.specialties.includes(ticket.category)) continue;
      if (!vendor.serviceAreas.includes(ticket.propertyId)) continue;
      if (vendor.userId === ticket.vendorId) continue; // Skip current vendor

      const availability = await checkVendorAvailability(ctx, {
        vendorId: vendor.userId,
        category: ticket.category,
      });

      if (availability.available) {
        availableVendors.push({
          vendor,
          availability,
          score: calculateVendorScore(vendor, ticket.priority),
        });
      }
    }

    // Sort by score (best vendors first)
    availableVendors.sort((a, b) => b.score - a.score);

    return availableVendors.slice(0, 3); // Return top 3 alternatives
  },
});

// Helper function to calculate vendor score based on performance and priority
function calculateVendorScore(vendor: Doc<"vendors">, priority: string): number {
  const { performance } = vendor;
  
  let score = 0;
  
  // Base score from rating (0-5 scale, weight: 30%)
  score += (performance.averageRating / 5) * 30;
  
  // SLA compliance (0-100 scale, weight: 30%)
  score += (performance.slaCompliance / 100) * 30;
  
  // Response time (lower is better, weight: 20%)
  // Assume good response time is under 2 hours
  const responseTimeScore = Math.max(0, (2 - performance.averageResponseTime) / 2);
  score += responseTimeScore * 20;
  
  // Experience (total jobs, weight: 10%)
  const experienceScore = Math.min(1, performance.totalJobs / 100);
  score += experienceScore * 10;
  
  // Priority bonus for emergency availability
  if (priority === "emergency" && vendor.availability.emergencyAvailable) {
    score += 10;
  }
  
  // Completion rate bonus
  if (performance.totalJobs > 0) {
    const completionRate = performance.completedJobs / performance.totalJobs;
    score += completionRate * 10;
  }
  
  return Math.min(100, score);
}