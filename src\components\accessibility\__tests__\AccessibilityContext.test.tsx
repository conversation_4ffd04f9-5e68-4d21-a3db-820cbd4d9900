/**
 * Tests for AccessibilityContext
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { AccessibilityProvider, useAccessibility } from '../../../contexts/AccessibilityContext';

// Test component that uses the accessibility context
const TestComponent = () => {
  const { settings, updateSettings, announceToScreenReader, resetToDefaults } = useAccessibility();

  return (
    <div>
      <div data-testid="theme">{settings.theme}</div>
      <div data-testid="font-size">{settings.fontSize}</div>
      <div data-testid="reduce-motion">{settings.reduceMotion.toString()}</div>
      
      <button onClick={() => updateSettings({ theme: 'high-contrast' })}>
        Change Theme
      </button>
      <button onClick={() => updateSettings({ fontSize: 'large' })}>
        Change Font Size
      </button>
      <button onClick={() => updateSettings({ reduceMotion: true })}>
        Enable Reduce Motion
      </button>
      <button onClick={() => announceToScreenReader('Test announcement')}>
        Announce
      </button>
      <button onClick={resetToDefaults}>
        Reset
      </button>
    </div>
  );
};

describe('AccessibilityContext', () => {
  beforeEach(() => {
    localStorage.clear();
    // Mock matchMedia
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });
  });

  it('provides default settings', () => {
    render(
      <AccessibilityProvider>
        <TestComponent />
      </AccessibilityProvider>
    );

    expect(screen.getByTestId('theme')).toHaveTextContent('default');
    expect(screen.getByTestId('font-size')).toHaveTextContent('medium');
    expect(screen.getByTestId('reduce-motion')).toHaveTextContent('false');
  });

  it('updates theme setting', () => {
    render(
      <AccessibilityProvider>
        <TestComponent />
      </AccessibilityProvider>
    );

    fireEvent.click(screen.getByText('Change Theme'));
    expect(screen.getByTestId('theme')).toHaveTextContent('high-contrast');
  });

  it('updates font size setting', () => {
    render(
      <AccessibilityProvider>
        <TestComponent />
      </AccessibilityProvider>
    );

    fireEvent.click(screen.getByText('Change Font Size'));
    expect(screen.getByTestId('font-size')).toHaveTextContent('large');
  });

  it('updates reduce motion setting', () => {
    render(
      <AccessibilityProvider>
        <TestComponent />
      </AccessibilityProvider>
    );

    fireEvent.click(screen.getByText('Enable Reduce Motion'));
    expect(screen.getByTestId('reduce-motion')).toHaveTextContent('true');
  });

  it('persists settings to localStorage', () => {
    render(
      <AccessibilityProvider>
        <TestComponent />
      </AccessibilityProvider>
    );

    fireEvent.click(screen.getByText('Change Theme'));
    
    const savedSettings = JSON.parse(localStorage.getItem('accessibility-settings') || '{}');
    expect(savedSettings.theme).toBe('high-contrast');
  });

  it('loads settings from localStorage', () => {
    localStorage.setItem('accessibility-settings', JSON.stringify({
      theme: 'high-contrast',
      fontSize: 'large',
      reduceMotion: true,
    }));

    render(
      <AccessibilityProvider>
        <TestComponent />
      </AccessibilityProvider>
    );

    expect(screen.getByTestId('theme')).toHaveTextContent('high-contrast');
    expect(screen.getByTestId('font-size')).toHaveTextContent('large');
    expect(screen.getByTestId('reduce-motion')).toHaveTextContent('true');
  });

  it('resets settings to defaults', () => {
    render(
      <AccessibilityProvider>
        <TestComponent />
      </AccessibilityProvider>
    );

    // Change some settings
    fireEvent.click(screen.getByText('Change Theme'));
    fireEvent.click(screen.getByText('Change Font Size'));
    
    // Reset
    fireEvent.click(screen.getByText('Reset'));
    
    expect(screen.getByTestId('theme')).toHaveTextContent('default');
    expect(screen.getByTestId('font-size')).toHaveTextContent('medium');
  });

  it('creates screen reader announcements', () => {
    const mockAppendChild = vi.spyOn(document.body, 'appendChild');
    const mockRemoveChild = vi.spyOn(document.body, 'removeChild');

    render(
      <AccessibilityProvider>
        <TestComponent />
      </AccessibilityProvider>
    );

    fireEvent.click(screen.getByText('Announce'));
    
    expect(mockAppendChild).toHaveBeenCalled();
    
    // Check that the announcement element was created with correct attributes
    const calls = mockAppendChild.mock.calls;
    const announcementElement = calls[calls.length - 1][0] as HTMLElement;
    expect(announcementElement.getAttribute('aria-live')).toBe('polite');
    expect(announcementElement.getAttribute('aria-atomic')).toBe('true');
    expect(announcementElement.textContent).toBe('Test announcement');

    mockAppendChild.mockRestore();
    mockRemoveChild.mockRestore();
  });

  it('detects system preferences for reduced motion', () => {
    // Mock reduced motion preference
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });

    render(
      <AccessibilityProvider>
        <TestComponent />
      </AccessibilityProvider>
    );

    expect(screen.getByTestId('reduce-motion')).toHaveTextContent('true');
  });

  it('detects system preferences for high contrast', () => {
    // Mock high contrast preference
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: query === '(prefers-contrast: high)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });

    render(
      <AccessibilityProvider>
        <TestComponent />
      </AccessibilityProvider>
    );

    expect(screen.getByTestId('theme')).toHaveTextContent('high-contrast');
  });
});