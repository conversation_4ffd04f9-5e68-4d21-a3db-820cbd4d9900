import { test as setup, expect } from '@playwright/test';
import { TEST_CONFIG, TestDataGenerator, MockResponses } from '../test-config';

const authFile = 'tests/.auth/user.json';

// Setup authentication for tests
setup('authenticate as owner', async ({ page }) => {
  // Mock authentication API
  await page.route('**/api/auth/signin', async route => {
    const user = TEST_CONFIG.TEST_USERS.OWNER;
    await route.fulfill(MockResponses.authResponse(user));
  });

  await page.route('**/api/auth/verify', async route => {
    await route.fulfill(MockResponses.successResponse({
      isAuthenticated: true,
      user: TEST_CONFIG.TEST_USERS.OWNER,
    }));
  });

  // Perform authentication steps
  await page.goto('/');
  await page.getByLabel(/email/i).fill(TEST_CONFIG.TEST_USERS.OWNER.email);
  await page.getByLabel(/password/i).fill(TEST_CONFIG.TEST_USERS.OWNER.password);
  await page.getByRole('button', { name: /sign in/i }).click();

  // Wait for successful authentication
  await expect(page).toHaveURL(/\/dashboard/);

  // Save authentication state
  await page.context().storageState({ path: authFile });
});

// Setup test data
setup('create test data', async ({ request }) => {
  // Create test properties, units, etc.
  const testProperty = TestDataGenerator.generateProperty('residential');
  
  // Mock API calls for test data creation
  console.log('Test data setup completed');
});