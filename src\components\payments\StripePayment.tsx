import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Alert, AlertDescription } from "../ui/alert";
import { Badge } from "../ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Loader2, CreditCard, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { useConvex } from "convex/react";
import { getStripeService, StripeService, StripePaymentResult } from "../../lib/stripe-service";
import { Id } from "../../../convex/_generated/dataModel";

interface StripePaymentProps {
  invoiceId: Id<"invoices">;
  amount: number;
  description: string;
  customerEmail?: string;
  customerName?: string;
  onPaymentSuccess?: (paymentId: Id<"payments">) => void;
  onPaymentFailure?: (error: string) => void;
}

export const StripePayment: React.FC<StripePaymentProps> = ({
  invoiceId,
  amount,
  description,
  customerEmail = "",
  customerName = "",
  onPaymentSuccess,
  onPaymentFailure,
}) => {
  const convex = useConvex();
  const stripeService = getStripeService(convex);

  const [email, setEmail] = useState(customerEmail);
  const [name, setName] = useState(customerName);
  const [currency, setCurrency] = useState("KES");
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentResult, setPaymentResult] = useState<StripePaymentResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [stripeLoaded, setStripeLoaded] = useState(false);

  // Load Stripe.js
  useEffect(() => {
    const loadStripe = async () => {
      if ((window as any).Stripe) {
        setStripeLoaded(true);
        return;
      }

      const script = document.createElement("script");
      script.src = "https://js.stripe.com/v3/";
      script.onload = () => setStripeLoaded(true);
      script.onerror = () => setError("Failed to load Stripe");
      document.head.appendChild(script);
    };

    loadStripe();
  }, []);

  // Handle payment submission
  const handlePayment = async () => {
    if (!email.trim() || !name.trim()) {
      setError("Please fill in all required fields");
      return;
    }

    if (!stripeLoaded) {
      setError("Stripe is not loaded yet. Please try again.");
      return;
    }

    setIsProcessing(true);
    setError(null);
    setPaymentResult(null);

    try {
      const result = await stripeService.initiatePayment({
        invoiceId,
        amount,
        currency,
        customerEmail: email,
        description,
      });

      setPaymentResult(result);

      if (result.success && result.clientSecret) {
        // Initialize Stripe Elements for card payment
        const stripe = (window as any).Stripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);
        if (!stripe) {
          throw new Error("Stripe failed to initialize");
        }

        // Redirect to Stripe Checkout or handle with Elements
        // For simplicity, we'll simulate a successful payment here
        // In a real implementation, you would use Stripe Elements or Checkout
        
        // Simulate payment processing
        setTimeout(() => {
          if (result.paymentId) {
            onPaymentSuccess?.(result.paymentId);
          }
          setIsProcessing(false);
        }, 2000);
      } else {
        setError(result.error || result.message);
        setIsProcessing(false);
        onPaymentFailure?.(result.error || result.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Payment failed";
      setError(errorMessage);
      setIsProcessing(false);
      onPaymentFailure?.(errorMessage);
    }
  };

  // Get status badge
  const getStatusBadge = () => {
    if (!paymentResult) return null;

    const statusConfig = {
      requires_payment_method: { variant: "secondary" as const, icon: AlertCircle, text: "Requires Payment Method" },
      requires_confirmation: { variant: "secondary" as const, icon: AlertCircle, text: "Requires Confirmation" },
      requires_action: { variant: "secondary" as const, icon: AlertCircle, text: "Requires Action" },
      processing: { variant: "secondary" as const, icon: Loader2, text: "Processing" },
      succeeded: { variant: "default" as const, icon: CheckCircle, text: "Succeeded" },
      payment_failed: { variant: "destructive" as const, icon: XCircle, text: "Failed" },
      canceled: { variant: "destructive" as const, icon: XCircle, text: "Canceled" },
    };

    const config = statusConfig[paymentResult.status as keyof typeof statusConfig];
    if (!config) return null;

    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.text}
      </Badge>
    );
  };

  const supportedCurrencies = StripeService.getSupportedCurrencies();

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <div className="flex items-center justify-center mb-2">
          <CreditCard className="h-8 w-8 text-blue-600" />
        </div>
        <CardTitle>Card Payment</CardTitle>
        <CardDescription>
          Pay {StripeService.formatAmount(amount, currency)} via Stripe
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!paymentResult && (
          <>
            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isProcessing}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="name">Full Name *</Label>
              <Input
                id="name"
                type="text"
                placeholder="John Doe"
                value={name}
                onChange={(e) => setName(e.target.value)}
                disabled={isProcessing}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select value={currency} onValueChange={setCurrency} disabled={isProcessing}>
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {supportedCurrencies.map((curr) => (
                    <SelectItem key={curr.code} value={curr.code}>
                      {curr.symbol} {curr.name} ({curr.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="text-sm text-muted-foreground">
              <p>Amount: {StripeService.formatAmount(amount, currency)}</p>
              <p>Description: {description}</p>
            </div>

            <Button
              onClick={handlePayment}
              disabled={isProcessing || !email.trim() || !name.trim() || !stripeLoaded}
              className="w-full"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing Payment...
                </>
              ) : (
                <>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Pay {StripeService.formatAmount(amount, currency)}
                </>
              )}
            </Button>

            {!stripeLoaded && (
              <div className="flex items-center justify-center text-sm text-muted-foreground">
                <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                Loading Stripe...
              </div>
            )}
          </>
        )}

        {paymentResult && paymentResult.success && (
          <div className="space-y-4">
            <Alert>
              <CreditCard className="h-4 w-4" />
              <AlertDescription>
                Payment initiated successfully. You will be redirected to complete the payment.
              </AlertDescription>
            </Alert>

            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Status:</span>
              {getStatusBadge()}
            </div>

            {isProcessing && (
              <div className="flex items-center justify-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Processing payment...</span>
              </div>
            )}

            {paymentResult.paymentIntentId && (
              <p className="text-xs text-muted-foreground text-center">
                Payment ID: {paymentResult.paymentIntentId}
              </p>
            )}
          </div>
        )}

        {error && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {paymentResult && !paymentResult.success && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              {paymentResult.error || paymentResult.message}
            </AlertDescription>
          </Alert>
        )}

        <div className="text-xs text-muted-foreground text-center space-y-1">
          <p>Secure payment powered by Stripe</p>
          <p>Your payment information is encrypted and secure</p>
        </div>
      </CardContent>
    </Card>
  );
};