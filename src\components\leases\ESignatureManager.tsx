import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { 
  FileText, 
  Send, 
  Clock, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Mail,
  User,
  Calendar,
  AlertTriangle
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '../ui/alert-dialog';

interface ESignatureManagerProps {
  leaseId: Id<"leases">;
  documentUrl?: string;
  onSignatureCompleted?: () => void;
}

interface SignerFormData {
  email: string;
  name: string;
  role: 'tenant' | 'landlord' | 'witness';
  order: number;
}

export function ESignatureManager({ leaseId, documentUrl, onSignatureCompleted }: ESignatureManagerProps) {
  const leaseData = useQuery(api.leases.getLeaseById, { id: leaseId });
  const signatureRequests = useQuery(api.esignature.getSignatureRequestsByLease, { leaseId });
  
  const createSignatureRequest = useMutation(api.esignature.createSignatureRequest);
  const updateSignatureStatus = useMutation(api.esignature.updateSignatureStatus);
  const resendSignatureRequest = useMutation(api.esignature.resendSignatureRequest);
  const cancelSignatureRequest = useMutation(api.esignature.cancelSignatureRequest);

  const [signers, setSigners] = useState<SignerFormData[]>([
    { email: '', name: '', role: 'tenant', order: 1 },
    { email: '', name: '', role: 'landlord', order: 2 },
  ]);
  const [isCreating, setIsCreating] = useState(false);
  const [showSignerForm, setShowSignerForm] = useState(false);

  // Initialize signer data from lease information
  React.useEffect(() => {
    if (leaseData && signers[0].email === '') {
      const { lease, tenant, property } = leaseData;
      const updatedSigners = [...signers];
      
      if (tenant) {
        updatedSigners[0] = {
          ...updatedSigners[0],
          email: tenant.email,
          name: tenant.name,
        };
      }
      
      // For landlord, we'd typically get this from property owner/manager
      // For now, we'll leave it empty for manual entry
      
      setSigners(updatedSigners);
    }
  }, [leaseData]);

  if (!leaseData) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { lease, property, unit, tenant } = leaseData;
  const activeRequest = signatureRequests?.find(req => req.status === 'pending');
  const completedRequest = signatureRequests?.find(req => req.status === 'signed');

  const handleCreateSignatureRequest = async () => {
    if (!documentUrl) {
      alert('Document URL is required to create signature request');
      return;
    }

    // Validate signers
    const validSigners = signers.filter(signer => 
      signer.email && signer.name && signer.email.includes('@')
    );

    if (validSigners.length === 0) {
      alert('At least one valid signer is required');
      return;
    }

    setIsCreating(true);
    try {
      await createSignatureRequest({
        leaseId,
        documentUrl,
        signers: validSigners,
        expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days from now
      });
      setShowSignerForm(false);
    } catch (error) {
      console.error('Error creating signature request:', error);
      alert('Failed to create signature request');
    } finally {
      setIsCreating(false);
    }
  };

  const handleResendRequest = async (requestId: Id<"signatureRequests">, signerEmail: string) => {
    try {
      await resendSignatureRequest({
        signatureRequestId: requestId,
        signerEmail,
      });
      alert('Signature request resent successfully');
    } catch (error) {
      console.error('Error resending signature request:', error);
      alert('Failed to resend signature request');
    }
  };

  const handleCancelRequest = async (requestId: Id<"signatureRequests">) => {
    try {
      await cancelSignatureRequest({
        signatureRequestId: requestId,
        reason: 'Cancelled by property manager',
      });
    } catch (error) {
      console.error('Error cancelling signature request:', error);
      alert('Failed to cancel signature request');
    }
  };

  const handleSimulateSignature = async (requestId: Id<"signatureRequests">) => {
    // This is for testing purposes - simulate a completed signature
    try {
      await updateSignatureStatus({
        signatureRequestId: requestId,
        status: 'signed',
        signedDocumentUrl: `${documentUrl}?signed=true`,
      });
      onSignatureCompleted?.();
    } catch (error) {
      console.error('Error simulating signature:', error);
    }
  };

  const updateSigner = (index: number, field: keyof SignerFormData, value: string | number) => {
    const updatedSigners = [...signers];
    updatedSigners[index] = { ...updatedSigners[index], [field]: value };
    setSigners(updatedSigners);
  };

  const addSigner = () => {
    setSigners([...signers, { 
      email: '', 
      name: '', 
      role: 'witness', 
      order: signers.length + 1 
    }]);
  };

  const removeSigner = (index: number) => {
    if (signers.length > 1) {
      setSigners(signers.filter((_, i) => i !== index));
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'signed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'declined':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'expired':
        return <AlertTriangle className="h-5 w-5 text-gray-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'signed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'declined':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="space-y-6">
      {/* Current Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            E-Signature Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!activeRequest && !completedRequest && (
            <div className="text-center py-6">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Signature Request</h3>
              <p className="text-gray-600 mb-4">
                Create a signature request to send the lease agreement for electronic signature.
              </p>
              {documentUrl ? (
                <Button onClick={() => setShowSignerForm(true)}>
                  <Send className="h-4 w-4 mr-2" />
                  Create Signature Request
                </Button>
              ) : (
                <p className="text-sm text-red-600">
                  Document must be generated before creating signature request
                </p>
              )}
            </div>
          )}

          {activeRequest && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getStatusIcon(activeRequest.status)}
                  <span className="font-semibold">Signature Request Active</span>
                  <Badge className={getStatusColor(activeRequest.status)}>
                    {activeRequest.status}
                  </Badge>
                </div>
                <div className="text-sm text-gray-600">
                  Expires: {formatDate(activeRequest.expiresAt)}
                </div>
              </div>

              {/* Signers Status */}
              <div className="space-y-3">
                <h4 className="font-medium">Signers</h4>
                {activeRequest.signers.map((signer, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="font-medium">{signer.name}</p>
                        <p className="text-sm text-gray-600">{signer.email}</p>
                        <p className="text-xs text-gray-500 capitalize">{signer.role}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(signer.status)}>
                        {signer.status}
                      </Badge>
                      {signer.status === 'pending' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleResendRequest(activeRequest._id, signer.email)}
                        >
                          <RefreshCw className="h-3 w-3 mr-1" />
                          Resend
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="sm">
                      Cancel Request
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Cancel Signature Request</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to cancel this signature request? 
                        All signers will be notified and the request will be marked as expired.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={() => handleCancelRequest(activeRequest._id)}>
                        Cancel Request
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                {/* Development helper - simulate signature completion */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSimulateSignature(activeRequest._id)}
                  className="text-blue-600"
                >
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Simulate Signature (Dev)
                </Button>
              </div>
            </div>
          )}

          {completedRequest && !activeRequest && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-semibold text-green-800">Signature Completed</span>
                <Badge className="bg-green-100 text-green-800">Signed</Badge>
              </div>
              
              <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-800">
                  All parties have signed the lease agreement. The lease is now active.
                </p>
                {completedRequest.signedDocumentUrl && (
                  <Button variant="outline" size="sm" className="mt-2">
                    Download Signed Document
                  </Button>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Signer Form */}
      {showSignerForm && (
        <Card>
          <CardHeader>
            <CardTitle>Configure Signers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {signers.map((signer, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-3">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Signer {index + 1}</h4>
                    {signers.length > 1 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeSigner(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor={`name-${index}`}>Name</Label>
                      <Input
                        id={`name-${index}`}
                        value={signer.name}
                        onChange={(e) => updateSigner(index, 'name', e.target.value)}
                        placeholder="Full name"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor={`email-${index}`}>Email</Label>
                      <Input
                        id={`email-${index}`}
                        type="email"
                        value={signer.email}
                        onChange={(e) => updateSigner(index, 'email', e.target.value)}
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor={`role-${index}`}>Role</Label>
                      <select
                        id={`role-${index}`}
                        value={signer.role}
                        onChange={(e) => updateSigner(index, 'role', e.target.value)}
                        className="w-full h-10 px-3 py-2 border border-input bg-background rounded-md text-sm"
                      >
                        <option value="tenant">Tenant</option>
                        <option value="landlord">Landlord</option>
                        <option value="witness">Witness</option>
                      </select>
                    </div>
                    
                    <div>
                      <Label htmlFor={`order-${index}`}>Signing Order</Label>
                      <Input
                        id={`order-${index}`}
                        type="number"
                        min="1"
                        value={signer.order}
                        onChange={(e) => updateSigner(index, 'order', parseInt(e.target.value) || 1)}
                      />
                    </div>
                  </div>
                </div>
              ))}
              
              <Button variant="outline" onClick={addSigner}>
                Add Signer
              </Button>
              
              <Separator />
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowSignerForm(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateSignatureRequest} disabled={isCreating}>
                  {isCreating ? 'Creating...' : 'Create Signature Request'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}