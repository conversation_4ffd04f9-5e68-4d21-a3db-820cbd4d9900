import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { ConvexReactClient } from "convex/react";

export interface StripePaymentRequest {
  invoiceId: Id<"invoices">;
  amount: number;
  currency: string;
  customerEmail: string;
  description: string;
  paymentMethodId?: string;
}

export interface StripePaymentResult {
  success: boolean;
  paymentId?: Id<"payments">;
  paymentIntentId?: string;
  clientSecret?: string;
  status?: string;
  message: string;
  error?: string;
}

export interface StripeCustomer {
  id: string;
  email: string;
  name: string;
  phone?: string;
}

export interface RefundResult {
  success: boolean;
  refundId?: string;
  amount?: number;
  status?: string;
  error?: string;
}

export class StripeService {
  constructor(private convex: ConvexReactClient) {}

  /**
   * Initialize Stripe payment
   */
  async initiatePayment(request: StripePaymentRequest): Promise<StripePaymentResult> {
    try {
      // Validate amount
      if (request.amount <= 0) {
        return {
          success: false,
          message: "Invalid payment amount",
          error: "Amount must be greater than zero",
        };
      }

      // Validate email
      if (!this.isValidEmail(request.customerEmail)) {
        return {
          success: false,
          message: "Invalid email address",
          error: "Please provide a valid email address",
        };
      }

      // Create pending payment record
      const paymentResult = await this.convex.mutation(api.payments.initiateStripePayment, {
        invoiceId: request.invoiceId,
        amount: request.amount,
        currency: request.currency,
      });

      if (!paymentResult.paymentId) {
        return {
          success: false,
          message: "Failed to create payment record",
          error: "Unable to initialize payment",
        };
      }

      // Process Stripe payment
      const stripeResult = await this.convex.action(api.payments.processStripePayment, {
        paymentId: paymentResult.paymentId,
        amount: request.amount,
        currency: request.currency,
        paymentMethodId: request.paymentMethodId,
        customerEmail: request.customerEmail,
        description: request.description,
      });

      return {
        success: stripeResult.success,
        paymentId: paymentResult.paymentId,
        paymentIntentId: stripeResult.paymentIntentId,
        clientSecret: stripeResult.clientSecret,
        status: stripeResult.status,
        message: stripeResult.message,
      };
    } catch (error) {
      console.error("Stripe payment initiation failed:", error);
      return {
        success: false,
        message: "Payment initiation failed",
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Create Stripe customer
   */
  async createCustomer(email: string, name: string, phone?: string): Promise<StripeCustomer | null> {
    try {
      const result = await this.convex.action(api.payments.createStripeCustomer, {
        email,
        name,
        phone,
      });

      if (result.success) {
        return {
          id: result.customerId,
          email: result.customer.email,
          name: result.customer.name,
          phone: result.customer.phone,
        };
      }

      return null;
    } catch (error) {
      console.error("Failed to create Stripe customer:", error);
      return null;
    }
  }

  /**
   * Refund payment
   */
  async refundPayment(
    paymentIntentId: string,
    amount?: number,
    reason?: string
  ): Promise<RefundResult> {
    try {
      const result = await this.convex.action(api.payments.refundStripePayment, {
        paymentIntentId,
        amount,
        reason,
      });

      return {
        success: result.success,
        refundId: result.refundId,
        amount: result.amount,
        status: result.status,
      };
    } catch (error) {
      console.error("Stripe refund failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Refund failed",
      };
    }
  }

  /**
   * Get payment details by payment ID
   */
  async getPaymentDetails(paymentId: Id<"payments">) {
    try {
      return await this.convex.query(api.payments.getPayment, { paymentId });
    } catch (error) {
      console.error("Failed to get payment details:", error);
      return null;
    }
  }

  /**
   * Get all payments for an invoice
   */
  async getInvoicePayments(invoiceId: Id<"invoices">) {
    try {
      return await this.convex.query(api.payments.getPaymentsByInvoice, { invoiceId });
    } catch (error) {
      console.error("Failed to get invoice payments:", error);
      return [];
    }
  }

  /**
   * Validate email address
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Format amount for display
   */
  static formatAmount(amount: number, currency: string = "KES"): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
      minimumFractionDigits: 2,
    }).format(amount);
  }

  /**
   * Convert amount to cents for Stripe
   */
  static toCents(amount: number): number {
    return Math.round(amount * 100);
  }

  /**
   * Convert amount from cents
   */
  static fromCents(cents: number): number {
    return cents / 100;
  }

  /**
   * Get supported currencies
   */
  static getSupportedCurrencies(): Array<{ code: string; name: string; symbol: string }> {
    return [
      { code: "KES", name: "Kenyan Shilling", symbol: "KSh" },
      { code: "USD", name: "US Dollar", symbol: "$" },
      { code: "EUR", name: "Euro", symbol: "€" },
      { code: "GBP", name: "British Pound", symbol: "£" },
    ];
  }

  /**
   * Get Stripe payment status descriptions
   */
  static getStatusDescription(status: string): string {
    const descriptions: Record<string, string> = {
      requires_payment_method: "Waiting for payment method",
      requires_confirmation: "Requires confirmation",
      requires_action: "Requires additional action",
      processing: "Processing payment",
      requires_capture: "Requires capture",
      canceled: "Payment canceled",
      succeeded: "Payment succeeded",
      payment_failed: "Payment failed",
    };

    return descriptions[status] || `Unknown status: ${status}`;
  }
}

// Singleton instance
let stripeServiceInstance: StripeService | null = null;

export const getStripeService = (convex: ConvexReactClient): StripeService => {
  if (!stripeServiceInstance) {
    stripeServiceInstance = new StripeService(convex);
  }
  return stripeServiceInstance;
};