/**
 * Skip links component for keyboard navigation accessibility
 */
import React from 'react';
import { useAccessibility } from '../../contexts/AccessibilityContext';

interface SkipLink {
  href: string;
  label: string;
}

interface SkipLinksProps {
  links?: SkipLink[];
}

const defaultLinks: SkipLink[] = [
  { href: '#main-content', label: 'Skip to main content' },
  { href: '#navigation', label: 'Skip to navigation' },
  { href: '#search', label: 'Skip to search' },
  { href: '#footer', label: 'Skip to footer' },
];

export function SkipLinks({ links = defaultLinks }: SkipLinksProps) {
  const { settings } = useAccessibility();

  if (!settings.skipLinks) {
    return null;
  }

  return (
    <div className="skip-links">
      {links.map((link) => (
        <a
          key={link.href}
          href={link.href}
          className="
            sr-only
            focus:not-sr-only
            focus:absolute
            focus:top-4
            focus:left-4
            focus:z-50
            focus:px-4
            focus:py-2
            focus:bg-primary
            focus:text-primary-foreground
            focus:rounded-md
            focus:shadow-lg
            focus:ring-2
            focus:ring-ring
            focus:ring-offset-2
            focus:outline-none
            focus:no-underline
            font-medium
            text-sm
          "
          onClick={(e) => {
            e.preventDefault();
            const target = document.querySelector(link.href);
            if (target) {
              target.scrollIntoView({ behavior: 'smooth', block: 'start' });
              // Focus the target element if it's focusable
              if (target instanceof HTMLElement && target.tabIndex >= 0) {
                target.focus();
              }
            }
          }}
        >
          {link.label}
        </a>
      ))}
    </div>
  );
}

// Landmark components for better navigation structure
export function MainContent({ 
  children, 
  className = '' 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <main id="main-content" className={className} role="main">
      {children}
    </main>
  );
}

export function NavigationLandmark({ 
  children, 
  className = '',
  ariaLabel = 'Main navigation'
}: { 
  children: React.ReactNode; 
  className?: string;
  ariaLabel?: string;
}) {
  return (
    <nav id="navigation" className={className} role="navigation" aria-label={ariaLabel}>
      {children}
    </nav>
  );
}

export function SearchLandmark({ 
  children, 
  className = '' 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <div id="search" className={className} role="search">
      {children}
    </div>
  );
}

export function FooterLandmark({ 
  children, 
  className = '' 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <footer id="footer" className={className} role="contentinfo">
      {children}
    </footer>
  );
}