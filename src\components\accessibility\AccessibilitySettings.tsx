/**
 * Accessibility settings panel for users to customize their experience
 */
import React from 'react';
import { useAccessibility } from '../../contexts/AccessibilityContext';
import { AccessibleForm, AccessibleSelect } from './AccessibleForm';
import { AccessibleButton } from './AccessibleButton';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { Separator } from '../ui/separator';
import { SelectItem } from '../ui/select';
import { getAllThemes } from '../../lib/accessibility-themes';

export function AccessibilitySettings() {
  const { settings, updateSettings, resetToDefaults, announceToScreenReader } = useAccessibility();
  const themes = getAllThemes();

  const handleThemeChange = (themeName: string) => {
    updateSettings({ theme: themeName as any });
    announceToScreenReader(`Theme changed to ${themeName}`);
  };

  const handleFontSizeChange = (fontSize: string) => {
    updateSettings({ fontSize: fontSize as any });
    announceToScreenReader(`Font size changed to ${fontSize}`);
  };

  const handleFontWeightChange = (fontWeight: string) => {
    updateSettings({ fontWeight: fontWeight as any });
    announceToScreenReader(`Font weight changed to ${fontWeight}`);
  };

  const handleFocusSizeChange = (focusSize: string) => {
    updateSettings({ focusIndicatorSize: focusSize as any });
    announceToScreenReader(`Focus indicator size changed to ${focusSize}`);
  };

  const handleSwitchChange = (key: keyof typeof settings, value: boolean) => {
    updateSettings({ [key]: value });
    announceToScreenReader(`${key.replace(/([A-Z])/g, ' $1').toLowerCase()} ${value ? 'enabled' : 'disabled'}`);
  };

  return (
    <div className="space-y-6 max-w-2xl">
      <div>
        <h1 className="text-2xl font-bold">Accessibility Settings</h1>
        <p className="text-muted-foreground mt-2">
          Customize your experience to meet your accessibility needs. Changes are saved automatically.
        </p>
      </div>

      {/* Theme Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Visual Appearance</CardTitle>
          <CardDescription>
            Adjust colors, contrast, and visual elements for better visibility
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <AccessibleSelect
            label="Color Theme"
            description="Choose a theme that works best for your vision needs"
            value={settings.theme}
            onValueChange={handleThemeChange}
          >
            {themes.map((theme) => (
              <SelectItem key={theme.name} value={theme.name}>
                {theme.displayName}
              </SelectItem>
            ))}
          </AccessibleSelect>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="high-contrast-focus">High Contrast Focus Indicators</Label>
              <p className="text-sm text-muted-foreground">
                Make focus indicators more visible with higher contrast
              </p>
            </div>
            <Switch
              id="high-contrast-focus"
              checked={settings.highContrastFocus}
              onCheckedChange={(checked) => handleSwitchChange('highContrastFocus', checked)}
              aria-describedby="high-contrast-focus-desc"
            />
          </div>

          <AccessibleSelect
            label="Focus Indicator Size"
            description="Adjust the size of focus indicators around interactive elements"
            value={settings.focusIndicatorSize}
            onValueChange={handleFocusSizeChange}
          >
            <SelectItem value="small">Small</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="large">Large</SelectItem>
          </AccessibleSelect>
        </CardContent>
      </Card>

      {/* Typography Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Typography</CardTitle>
          <CardDescription>
            Adjust text size and weight for better readability
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <AccessibleSelect
            label="Font Size"
            description="Choose a comfortable text size for reading"
            value={settings.fontSize}
            onValueChange={handleFontSizeChange}
          >
            <SelectItem value="small">Small (14px)</SelectItem>
            <SelectItem value="medium">Medium (16px)</SelectItem>
            <SelectItem value="large">Large (18px)</SelectItem>
            <SelectItem value="extra-large">Extra Large (20px)</SelectItem>
          </AccessibleSelect>

          <AccessibleSelect
            label="Font Weight"
            description="Adjust text thickness for better visibility"
            value={settings.fontWeight}
            onValueChange={handleFontWeightChange}
          >
            <SelectItem value="normal">Normal</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="bold">Bold</SelectItem>
          </AccessibleSelect>
        </CardContent>
      </Card>

      {/* Motion Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Motion and Animation</CardTitle>
          <CardDescription>
            Control animations and transitions for comfort and accessibility
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="reduce-motion">Reduce Motion</Label>
              <p className="text-sm text-muted-foreground">
                Minimize animations and transitions that may cause discomfort
              </p>
            </div>
            <Switch
              id="reduce-motion"
              checked={settings.reduceMotion}
              onCheckedChange={(checked) => handleSwitchChange('reduceMotion', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Screen Reader Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Screen Reader Support</CardTitle>
          <CardDescription>
            Configure announcements and descriptions for screen readers
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="announce-changes">Announce Changes</Label>
              <p className="text-sm text-muted-foreground">
                Announce important changes and updates to screen readers
              </p>
            </div>
            <Switch
              id="announce-changes"
              checked={settings.announceChanges}
              onCheckedChange={(checked) => handleSwitchChange('announceChanges', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="verbose-descriptions">Verbose Descriptions</Label>
              <p className="text-sm text-muted-foreground">
                Provide more detailed descriptions of interface elements
              </p>
            </div>
            <Switch
              id="verbose-descriptions"
              checked={settings.verboseDescriptions}
              onCheckedChange={(checked) => handleSwitchChange('verboseDescriptions', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Keyboard Navigation Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Keyboard Navigation</CardTitle>
          <CardDescription>
            Configure keyboard navigation and shortcuts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="skip-links">Skip Links</Label>
              <p className="text-sm text-muted-foreground">
                Show skip links for faster keyboard navigation
              </p>
            </div>
            <Switch
              id="skip-links"
              checked={settings.skipLinks}
              onCheckedChange={(checked) => handleSwitchChange('skipLinks', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="keyboard-shortcuts">Keyboard Shortcuts</Label>
              <p className="text-sm text-muted-foreground">
                Enable keyboard shortcuts for common actions
              </p>
            </div>
            <Switch
              id="keyboard-shortcuts"
              checked={settings.keyboardShortcuts}
              onCheckedChange={(checked) => handleSwitchChange('keyboardShortcuts', checked)}
            />
          </div>
        </CardContent>
      </Card>

      <Separator />

      {/* Reset Settings */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Reset Settings</h3>
          <p className="text-sm text-muted-foreground">
            Restore all accessibility settings to their default values
          </p>
        </div>
        <AccessibleButton
          variant="outline"
          onClick={resetToDefaults}
          requiresConfirmation
          confirmationText="Reset all accessibility settings to defaults?"
          successMessage="Accessibility settings have been reset to defaults"
        >
          Reset to Defaults
        </AccessibleButton>
      </div>
    </div>
  );
}