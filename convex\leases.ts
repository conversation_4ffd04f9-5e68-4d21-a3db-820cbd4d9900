import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { validateLeaseData, validateDateRange, validatePositiveNumber, validateNonNegativeNumber } from "./lib/validation";

// Get all leases with optional filters
export const getLeases = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    tenantId: v.optional(v.id("users")),
    status: v.optional(v.union(v.literal("active"), v.literal("expired"), v.literal("terminated"), v.literal("pending"))),
    unitId: v.optional(v.id("units")),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("leases");

    if (args.propertyId) {
      query = query.withIndex("by_property", (q) => q.eq("propertyId", args.propertyId));
    } else if (args.tenantId) {
      query = query.withIndex("by_tenant", (q) => q.eq("tenantId", args.tenantId));
    } else if (args.unitId) {
      query = query.withIndex("by_unit", (q) => q.eq("unitId", args.unitId));
    } else if (args.status) {
      query = query.withIndex("by_status", (q) => q.eq("status", args.status));
    }

    const leases = await query.collect();

    // Apply additional filters if needed
    return leases.filter(lease => {
      if (args.status && lease.status !== args.status) return false;
      if (args.propertyId && lease.propertyId !== args.propertyId) return false;
      if (args.tenantId && lease.tenantId !== args.tenantId) return false;
      if (args.unitId && lease.unitId !== args.unitId) return false;
      return true;
    });
  },
});

// Get lease by ID with related data
export const getLeaseById = query({
  args: { id: v.id("leases") },
  handler: async (ctx, args) => {
    const lease = await ctx.db.get(args.id);
    if (!lease) {
      throw new Error("Lease not found");
    }

    // Get related data
    const property = await ctx.db.get(lease.propertyId);
    const unit = await ctx.db.get(lease.unitId);
    const tenant = await ctx.db.get(lease.tenantId);

    return {
      lease,
      property,
      unit,
      tenant,
    };
  },
});

// Create a new lease
export const createLease = mutation({
  args: {
    propertyId: v.id("properties"),
    unitId: v.id("units"),
    tenantId: v.id("users"),
    startDate: v.number(),
    endDate: v.number(),
    monthlyRent: v.number(),
    deposit: v.number(),
    terms: v.object({
      noticePeriod: v.number(),
      lateFeePercentage: v.number(),
      gracePeriod: v.number(),
      renewalOption: v.boolean(),
    }),
    documentUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Validate lease data
    validateLeaseData(args);
    validateDateRange(args.startDate, args.endDate);
    validatePositiveNumber(args.monthlyRent, "Monthly rent");
    validateNonNegativeNumber(args.deposit, "Deposit");

    // Verify property exists
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Property not found");
    }

    // Verify unit exists and belongs to the property
    const unit = await ctx.db.get(args.unitId);
    if (!unit) {
      throw new Error("Unit not found");
    }
    if (unit.propertyId !== args.propertyId) {
      throw new Error("Unit does not belong to the specified property");
    }

    // Verify tenant exists and has tenant role
    const tenant = await ctx.db.get(args.tenantId);
    if (!tenant) {
      throw new Error("Tenant not found");
    }
    if (tenant.role !== "tenant") {
      throw new Error("User must have tenant role");
    }

    // Check if unit is available (not occupied by active lease)
    const existingActiveLease = await ctx.db
      .query("leases")
      .withIndex("by_unit", (q) => q.eq("unitId", args.unitId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (existingActiveLease) {
      throw new Error("Unit is already occupied by an active lease");
    }

    // Validate lease terms
    if (args.terms.noticePeriod < 0) {
      throw new Error("Notice period cannot be negative");
    }
    if (args.terms.lateFeePercentage < 0 || args.terms.lateFeePercentage > 100) {
      throw new Error("Late fee percentage must be between 0 and 100");
    }
    if (args.terms.gracePeriod < 0) {
      throw new Error("Grace period cannot be negative");
    }

    const now = Date.now();

    // Create the lease
    const leaseId = await ctx.db.insert("leases", {
      propertyId: args.propertyId,
      unitId: args.unitId,
      tenantId: args.tenantId,
      startDate: args.startDate,
      endDate: args.endDate,
      monthlyRent: args.monthlyRent,
      deposit: args.deposit,
      status: "pending", // Start as pending until signed
      terms: args.terms,
      eSignatureStatus: "pending",
      documentUrl: args.documentUrl,
      createdAt: now,
      updatedAt: now,
    });

    // Update unit status to occupied if lease starts immediately
    if (args.startDate <= now) {
      await ctx.db.patch(args.unitId, {
        status: "occupied",
        updatedAt: now,
      });
    }

    return leaseId;
  },
});

// Update lease status
export const updateLeaseStatus = mutation({
  args: {
    id: v.id("leases"),
    status: v.union(v.literal("active"), v.literal("expired"), v.literal("terminated"), v.literal("pending")),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const lease = await ctx.db.get(args.id);
    if (!lease) {
      throw new Error("Lease not found");
    }

    const now = Date.now();

    // Update lease status
    await ctx.db.patch(args.id, {
      status: args.status,
      updatedAt: now,
    });

    // Update unit status based on lease status
    if (args.status === "active") {
      await ctx.db.patch(lease.unitId, {
        status: "occupied",
        updatedAt: now,
      });
    } else if (args.status === "terminated" || args.status === "expired") {
      await ctx.db.patch(lease.unitId, {
        status: "vacant",
        updatedAt: now,
      });
    }

    return args.id;
  },
});

// Update e-signature status
export const updateESignatureStatus = mutation({
  args: {
    id: v.id("leases"),
    status: v.union(v.literal("pending"), v.literal("signed"), v.literal("expired")),
    documentUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const lease = await ctx.db.get(args.id);
    if (!lease) {
      throw new Error("Lease not found");
    }

    const updates: any = {
      eSignatureStatus: args.status,
      updatedAt: Date.now(),
    };

    if (args.documentUrl) {
      updates.documentUrl = args.documentUrl;
    }

    // If signed, activate the lease
    if (args.status === "signed" && lease.status === "pending") {
      updates.status = "active";
    }

    await ctx.db.patch(args.id, updates);

    return args.id;
  },
});

// Renew lease
export const renewLease = mutation({
  args: {
    id: v.id("leases"),
    newEndDate: v.number(),
    newMonthlyRent: v.optional(v.number()),
    newTerms: v.optional(v.object({
      noticePeriod: v.number(),
      lateFeePercentage: v.number(),
      gracePeriod: v.number(),
      renewalOption: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    const lease = await ctx.db.get(args.id);
    if (!lease) {
      throw new Error("Lease not found");
    }

    if (lease.status !== "active") {
      throw new Error("Only active leases can be renewed");
    }

    if (!lease.terms.renewalOption) {
      throw new Error("This lease does not have a renewal option");
    }

    // Validate new end date
    if (args.newEndDate <= lease.endDate) {
      throw new Error("New end date must be after current end date");
    }

    // Validate new rent if provided
    if (args.newMonthlyRent !== undefined) {
      validatePositiveNumber(args.newMonthlyRent, "New monthly rent");
    }

    const now = Date.now();
    const updates: any = {
      endDate: args.newEndDate,
      updatedAt: now,
    };

    if (args.newMonthlyRent !== undefined) {
      updates.monthlyRent = args.newMonthlyRent;
    }

    if (args.newTerms) {
      updates.terms = args.newTerms;
    }

    await ctx.db.patch(args.id, updates);

    return args.id;
  },
});

// Terminate lease
export const terminateLease = mutation({
  args: {
    id: v.id("leases"),
    terminationDate: v.number(),
    reason: v.string(),
    earlyTermination: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const lease = await ctx.db.get(args.id);
    if (!lease) {
      throw new Error("Lease not found");
    }

    if (lease.status !== "active") {
      throw new Error("Only active leases can be terminated");
    }

    const now = Date.now();

    // Check if it's early termination
    const isEarlyTermination = args.terminationDate < lease.endDate;
    
    if (isEarlyTermination && !args.earlyTermination) {
      throw new Error("Early termination flag must be set for termination before lease end date");
    }

    // Update lease status
    await ctx.db.patch(args.id, {
      status: "terminated",
      updatedAt: now,
    });

    // Update unit status to vacant
    await ctx.db.patch(lease.unitId, {
      status: "vacant",
      updatedAt: now,
    });

    // Create a record of the termination (could be stored in documents or notifications)
    // For now, we'll create a notification
    await ctx.db.insert("notifications", {
      userId: lease.tenantId,
      title: "Lease Terminated",
      message: `Your lease has been terminated. Reason: ${args.reason}`,
      type: "system",
      isRead: false,
      metadata: {
        leaseId: args.id,
      },
      createdAt: now,
    });

    return args.id;
  },
});

// Get leases expiring soon
export const getLeasesExpiringSoon = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    daysAhead: v.optional(v.number()), // Default to 30 days
  },
  handler: async (ctx, args) => {
    const daysAhead = args.daysAhead || 30;
    const cutoffDate = Date.now() + (daysAhead * 24 * 60 * 60 * 1000);

    let query = ctx.db.query("leases").withIndex("by_status", (q) => q.eq("status", "active"));
    
    const activeLeases = await query.collect();

    // Filter leases expiring within the specified timeframe
    let expiringLeases = activeLeases.filter(lease => 
      lease.endDate <= cutoffDate && lease.endDate > Date.now()
    );

    // Filter by property if specified
    if (args.propertyId) {
      expiringLeases = expiringLeases.filter(lease => lease.propertyId === args.propertyId);
    }

    // Get related data for each lease
    const leasesWithData = await Promise.all(
      expiringLeases.map(async (lease) => {
        const property = await ctx.db.get(lease.propertyId);
        const unit = await ctx.db.get(lease.unitId);
        const tenant = await ctx.db.get(lease.tenantId);
        
        return {
          lease,
          property,
          unit,
          tenant,
          daysUntilExpiry: Math.ceil((lease.endDate - Date.now()) / (24 * 60 * 60 * 1000)),
        };
      })
    );

    return leasesWithData.sort((a, b) => a.lease.endDate - b.lease.endDate);
  },
});

// Get tenant lease history
export const getTenantLeaseHistory = query({
  args: { tenantId: v.id("users") },
  handler: async (ctx, args) => {
    const leases = await ctx.db
      .query("leases")
      .withIndex("by_tenant", (q) => q.eq("tenantId", args.tenantId))
      .collect();

    // Get related data for each lease
    const leasesWithData = await Promise.all(
      leases.map(async (lease) => {
        const property = await ctx.db.get(lease.propertyId);
        const unit = await ctx.db.get(lease.unitId);
        
        return {
          lease,
          property,
          unit,
        };
      })
    );

    return leasesWithData.sort((a, b) => b.lease.createdAt - a.lease.createdAt);
  },
});

// Get lease analytics for a property
export const getLeaseAnalytics = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    const allLeases = await ctx.db
      .query("leases")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();

    const activeLeases = allLeases.filter(lease => lease.status === "active");
    const expiredLeases = allLeases.filter(lease => lease.status === "expired");
    const terminatedLeases = allLeases.filter(lease => lease.status === "terminated");
    const pendingLeases = allLeases.filter(lease => lease.status === "pending");

    // Calculate average lease duration
    const completedLeases = [...expiredLeases, ...terminatedLeases];
    const avgLeaseDuration = completedLeases.length > 0 
      ? completedLeases.reduce((sum, lease) => sum + (lease.endDate - lease.startDate), 0) / completedLeases.length
      : 0;

    // Calculate average monthly rent
    const avgMonthlyRent = activeLeases.length > 0
      ? activeLeases.reduce((sum, lease) => sum + lease.monthlyRent, 0) / activeLeases.length
      : 0;

    // Calculate total monthly revenue
    const totalMonthlyRevenue = activeLeases.reduce((sum, lease) => sum + lease.monthlyRent, 0);

    // Get leases expiring in next 30 days
    const thirtyDaysFromNow = Date.now() + (30 * 24 * 60 * 60 * 1000);
    const expiringSoon = activeLeases.filter(lease => lease.endDate <= thirtyDaysFromNow);

    return {
      totalLeases: allLeases.length,
      activeLeases: activeLeases.length,
      expiredLeases: expiredLeases.length,
      terminatedLeases: terminatedLeases.length,
      pendingLeases: pendingLeases.length,
      avgLeaseDurationDays: Math.round(avgLeaseDuration / (24 * 60 * 60 * 1000)),
      avgMonthlyRent,
      totalMonthlyRevenue,
      expiringSoonCount: expiringSoon.length,
      renewalRate: expiredLeases.length > 0 ? (expiredLeases.length / (expiredLeases.length + terminatedLeases.length)) * 100 : 0,
    };
  },
});
// Get active lease for tenant (for tenant portal)
export const getActiveLease = query({
  args: { tenantId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("leases")
      .withIndex("by_tenant", (q) => q.eq("tenantId", args.tenantId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();
  },
});