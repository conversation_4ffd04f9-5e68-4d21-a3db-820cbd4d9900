/**
 * Help center with searchable documentation and tutorials
 */
import React, { useState, useMemo } from 'react';
import { useAccessibility } from '../../contexts/AccessibilityContext';
import { AccessibleInput } from '../accessibility/AccessibleForm';
import { AccessibleButton } from '../accessibility/AccessibleButton';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Separator } from '../ui/separator';

interface HelpArticle {
  id: string;
  title: string;
  description: string;
  content: string;
  category: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  lastUpdated: string;
}

interface HelpVideo {
  id: string;
  title: string;
  description: string;
  duration: string;
  category: string;
  thumbnailUrl?: string;
  videoUrl: string;
}

const helpArticles: HelpArticle[] = [
  {
    id: 'getting-started',
    title: 'Getting Started with EstatePulse',
    description: 'Learn the basics of navigating and using EstatePulse',
    content: `
# Getting Started with EstatePulse

Welcome to EstatePulse! This guide will help you get started with the platform.

## First Steps

1. **Complete your profile** - Make sure your user profile is complete with accurate information
2. **Set up your properties** - Add your properties to start managing them
3. **Configure accessibility settings** - Customize the interface to meet your needs

## Navigation

Use the main navigation menu to access different sections:
- **Properties**: Manage your property portfolio
- **Leases**: Handle tenant agreements and renewals
- **Maintenance**: Track and manage maintenance requests
- **Payments**: Process rent payments and track finances
- **Reports**: Generate comprehensive property reports

## Keyboard Shortcuts

Press Shift+? to see all available keyboard shortcuts for faster navigation.

## Getting Help

- Look for help icons (?) throughout the interface for contextual help
- Use this help center to search for specific topics
- Contact support if you need additional assistance
    `,
    category: 'Getting Started',
    tags: ['basics', 'navigation', 'setup'],
    difficulty: 'beginner',
    lastUpdated: '2024-01-15',
  },
  {
    id: 'accessibility-features',
    title: 'Accessibility Features Guide',
    description: 'Learn about the accessibility features available in EstatePulse',
    content: `
# Accessibility Features in EstatePulse

EstatePulse is designed to be accessible to all users. Here are the key accessibility features:

## Keyboard Navigation

- **Tab Navigation**: Use Tab to move forward, Shift+Tab to move backward
- **Arrow Keys**: Navigate within menus, lists, and tables
- **Enter/Space**: Activate buttons and links
- **Escape**: Close dialogs and menus

## Screen Reader Support

- Compatible with NVDA, JAWS, VoiceOver, and other screen readers
- Proper ARIA labels and descriptions
- Live announcements for important changes
- Structured headings for easy navigation

## Visual Accessibility

- **High Contrast Theme**: Improved visibility with high contrast colors
- **Color-Blind Friendly Themes**: Alternative color schemes for different types of color vision
- **Adjustable Font Size**: Choose from small, medium, large, or extra-large text
- **Reduced Motion**: Minimize animations for users sensitive to motion

## Customization

Visit the Accessibility Settings page to customize your experience:
1. Go to the main menu
2. Click on "Accessibility"
3. Adjust settings to meet your needs
4. Changes are saved automatically

## Skip Links

Press Tab when a page loads to reveal skip links that help you jump to main content areas quickly.
    `,
    category: 'Accessibility',
    tags: ['accessibility', 'keyboard', 'screen-reader', 'themes'],
    difficulty: 'beginner',
    lastUpdated: '2024-01-15',
  },
  {
    id: 'property-management',
    title: 'Managing Properties',
    description: 'Complete guide to adding and managing properties',
    content: `
# Managing Properties

Learn how to effectively manage your property portfolio in EstatePulse.

## Adding a New Property

1. Navigate to the Properties section
2. Click "Add Property"
3. Fill in the required information:
   - Property name
   - Address
   - Property type (Residential, Commercial, Mixed)
   - Number of units
4. Upload property images (optional)
5. Save the property

## Property Types

- **Residential**: Apartments, houses, condos
- **Commercial**: Offices, retail spaces, warehouses  
- **Mixed**: Properties with both residential and commercial units

## Managing Units

After creating a property, you can add individual units:
1. Go to the property details page
2. Click "Add Unit"
3. Specify unit details:
   - Unit number/name
   - Size (square footage)
   - Rent amount
   - Amenities

## Property Analytics

View important metrics for each property:
- Occupancy rate
- Monthly revenue
- Maintenance costs
- Tenant satisfaction scores

## Best Practices

- Keep property information up to date
- Upload high-quality photos
- Regularly review and update rent prices
- Monitor occupancy trends
    `,
    category: 'Property Management',
    tags: ['properties', 'units', 'management', 'analytics'],
    difficulty: 'intermediate',
    lastUpdated: '2024-01-15',
  },
  {
    id: 'payment-processing',
    title: 'Payment Processing Guide',
    description: 'Learn how to process payments and manage financial transactions',
    content: `
# Payment Processing

EstatePulse supports multiple payment methods for tenant convenience.

## Supported Payment Methods

### M-PESA
- Mobile money payments popular in East Africa
- Instant STK push notifications
- Real-time payment confirmation
- Low transaction fees

### Credit/Debit Cards (Stripe)
- International card payments
- Secure processing with PCI compliance
- Support for multiple currencies
- Automatic receipt generation

## Setting Up Payments

1. Configure payment methods in settings
2. Set up your M-PESA business account
3. Connect your Stripe account for card payments
4. Test payments in sandbox mode first

## Payment Tracking

- View all payments in the Payments dashboard
- Generate payment reports
- Track late payments and fees
- Send automated payment reminders

## Tenant Payment Experience

Tenants can:
- View outstanding balances
- Make payments through their portal
- Download payment receipts
- Set up payment reminders

## Troubleshooting

Common payment issues:
- Failed M-PESA payments: Check phone number format
- Card declined: Verify card details and limits
- Duplicate payments: Contact support for refunds
    `,
    category: 'Payments',
    tags: ['payments', 'mpesa', 'stripe', 'transactions'],
    difficulty: 'intermediate',
    lastUpdated: '2024-01-15',
  },
];

const helpVideos: HelpVideo[] = [
  {
    id: 'intro-video',
    title: 'EstatePulse Overview',
    description: 'A comprehensive overview of EstatePulse features and capabilities',
    duration: '5:30',
    category: 'Getting Started',
    videoUrl: '#',
  },
  {
    id: 'accessibility-demo',
    title: 'Accessibility Features Demo',
    description: 'See how to use keyboard navigation and accessibility features',
    duration: '3:45',
    category: 'Accessibility',
    videoUrl: '#',
  },
  {
    id: 'property-setup',
    title: 'Setting Up Your First Property',
    description: 'Step-by-step guide to adding and configuring a property',
    duration: '7:20',
    category: 'Property Management',
    videoUrl: '#',
  },
];

export function HelpCenter() {
  const { announceToScreenReader } = useAccessibility();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedArticle, setSelectedArticle] = useState<HelpArticle | null>(null);

  // Filter articles based on search and category
  const filteredArticles = useMemo(() => {
    return helpArticles.filter(article => {
      const matchesSearch = searchQuery === '' || 
        article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        article.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
      
      return matchesSearch && matchesCategory;
    });
  }, [searchQuery, selectedCategory]);

  const categories = ['all', ...Array.from(new Set(helpArticles.map(article => article.category)))];

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    announceToScreenReader(`Searching help articles for: ${query}. Found ${filteredArticles.length} results.`);
  };

  const handleArticleSelect = (article: HelpArticle) => {
    setSelectedArticle(article);
    announceToScreenReader(`Opened article: ${article.title}`);
  };

  const handleBackToList = () => {
    setSelectedArticle(null);
    announceToScreenReader('Returned to help article list');
  };

  if (selectedArticle) {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex items-center gap-4">
          <AccessibleButton
            variant="outline"
            onClick={handleBackToList}
            aria-label="Back to help articles"
          >
            ← Back to Help
          </AccessibleButton>
          <div>
            <Badge variant="secondary">{selectedArticle.category}</Badge>
            <Badge variant="outline" className="ml-2 capitalize">
              {selectedArticle.difficulty}
            </Badge>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">{selectedArticle.title}</CardTitle>
            <CardDescription>{selectedArticle.description}</CardDescription>
            <div className="text-sm text-muted-foreground">
              Last updated: {new Date(selectedArticle.lastUpdated).toLocaleDateString()}
            </div>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm max-w-none">
              {selectedArticle.content.split('\n').map((line, index) => {
                if (line.startsWith('# ')) {
                  return <h1 key={index} className="text-2xl font-bold mt-6 mb-4">{line.slice(2)}</h1>;
                } else if (line.startsWith('## ')) {
                  return <h2 key={index} className="text-xl font-semibold mt-5 mb-3">{line.slice(3)}</h2>;
                } else if (line.startsWith('### ')) {
                  return <h3 key={index} className="text-lg font-medium mt-4 mb-2">{line.slice(4)}</h3>;
                } else if (line.startsWith('- ')) {
                  return <li key={index} className="ml-4">{line.slice(2)}</li>;
                } else if (line.match(/^\d+\./)) {
                  return <li key={index} className="ml-4 list-decimal">{line.replace(/^\d+\.\s*/, '')}</li>;
                } else if (line.trim() === '') {
                  return <br key={index} />;
                } else {
                  return <p key={index} className="mb-3">{line}</p>;
                }
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Help Center</h1>
        <p className="text-muted-foreground mt-2">
          Find answers to your questions and learn how to use EstatePulse effectively.
        </p>
      </div>

      <Card>
        <CardContent className="pt-6">
          <AccessibleInput
            label="Search Help Articles"
            placeholder="Search for help topics, features, or questions..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="mb-4"
          />
          
          <div className="flex flex-wrap gap-2">
            {categories.map(category => (
              <AccessibleButton
                key={category}
                variant={selectedCategory === category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="capitalize"
              >
                {category}
              </AccessibleButton>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="articles" className="space-y-4">
        <TabsList>
          <TabsTrigger value="articles">Articles</TabsTrigger>
          <TabsTrigger value="videos">Video Tutorials</TabsTrigger>
        </TabsList>

        <TabsContent value="articles" className="space-y-4">
          {filteredArticles.length === 0 ? (
            <Card>
              <CardContent className="pt-6 text-center">
                <p className="text-muted-foreground">
                  No articles found matching your search criteria.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredArticles.map(article => (
                <Card
                  key={article.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleArticleSelect(article)}
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleArticleSelect(article);
                    }
                  }}
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <Badge variant="secondary" className="text-xs">
                        {article.category}
                      </Badge>
                      <Badge variant="outline" className="text-xs capitalize">
                        {article.difficulty}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg">{article.title}</CardTitle>
                    <CardDescription>{article.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-1 mb-3">
                      {article.tags.map(tag => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Updated {new Date(article.lastUpdated).toLocaleDateString()}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="videos" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {helpVideos.map(video => (
              <Card key={video.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <Badge variant="secondary" className="text-xs w-fit">
                    {video.category}
                  </Badge>
                  <CardTitle className="text-lg">{video.title}</CardTitle>
                  <CardDescription>{video.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      Duration: {video.duration}
                    </span>
                    <AccessibleButton size="sm" variant="outline">
                      Watch Video
                    </AccessibleButton>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}