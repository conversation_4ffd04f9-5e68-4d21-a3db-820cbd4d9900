/**
 * Hook for managing keyboard shortcuts with accessibility support
 */
import { useEffect, useCallback } from 'react';
import { useAccessibility } from '../contexts/AccessibilityContext';
import { KEYBOARD_KEYS } from '../lib/accessibility';

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  category?: string;
  disabled?: boolean;
}

interface UseKeyboardShortcutsOptions {
  shortcuts: KeyboardShortcut[];
  enabled?: boolean;
  preventDefault?: boolean;
  stopPropagation?: boolean;
}

export function useKeyboardShortcuts({
  shortcuts,
  enabled = true,
  preventDefault = true,
  stopPropagation = true,
}: UseKeyboardShortcutsOptions) {
  const { settings, announceToScreenReader } = useAccessibility();

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't handle shortcuts if disabled in settings or hook
    if (!settings.keyboardShortcuts || !enabled) return;

    // Don't handle shortcuts when typing in form elements
    const target = event.target as HTMLElement;
    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true' ||
      target.getAttribute('role') === 'textbox'
    ) {
      return;
    }

    // Find matching shortcut
    const matchingShortcut = shortcuts.find(shortcut => {
      if (shortcut.disabled) return false;
      
      return (
        shortcut.key.toLowerCase() === event.key.toLowerCase() &&
        !!shortcut.ctrlKey === event.ctrlKey &&
        !!shortcut.altKey === event.altKey &&
        !!shortcut.shiftKey === event.shiftKey &&
        !!shortcut.metaKey === event.metaKey
      );
    });

    if (matchingShortcut) {
      if (preventDefault) event.preventDefault();
      if (stopPropagation) event.stopPropagation();
      
      matchingShortcut.action();
      
      // Announce shortcut activation to screen readers
      announceToScreenReader(`Activated: ${matchingShortcut.description}`);
    }
  }, [shortcuts, settings.keyboardShortcuts, enabled, preventDefault, stopPropagation, announceToScreenReader]);

  useEffect(() => {
    if (!enabled || !settings.keyboardShortcuts) return;

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown, enabled, settings.keyboardShortcuts]);

  // Helper function to format shortcut display
  const formatShortcut = useCallback((shortcut: KeyboardShortcut): string => {
    const parts: string[] = [];
    
    if (shortcut.ctrlKey) parts.push('Ctrl');
    if (shortcut.altKey) parts.push('Alt');
    if (shortcut.shiftKey) parts.push('Shift');
    if (shortcut.metaKey) parts.push('Cmd');
    
    parts.push(shortcut.key.toUpperCase());
    
    return parts.join(' + ');
  }, []);

  return {
    formatShortcut,
    isEnabled: enabled && settings.keyboardShortcuts,
  };
}

// Common keyboard shortcuts for the application
export const commonShortcuts: KeyboardShortcut[] = [
  {
    key: '/',
    action: () => {
      const searchInput = document.querySelector('[role="search"] input, [type="search"]') as HTMLInputElement;
      searchInput?.focus();
    },
    description: 'Focus search',
    category: 'Navigation',
  },
  {
    key: 'h',
    action: () => {
      window.location.hash = '#main-content';
      const mainContent = document.getElementById('main-content');
      mainContent?.focus();
    },
    description: 'Go to main content',
    category: 'Navigation',
  },
  {
    key: 'n',
    action: () => {
      const navigation = document.getElementById('navigation');
      navigation?.focus();
    },
    description: 'Go to navigation',
    category: 'Navigation',
  },
  {
    key: '?',
    shiftKey: true,
    action: () => {
      // This would open a help dialog
      console.log('Show keyboard shortcuts help');
    },
    description: 'Show keyboard shortcuts help',
    category: 'Help',
  },
  {
    key: KEYBOARD_KEYS.ESCAPE,
    action: () => {
      // Close any open modals or dropdowns
      const openModal = document.querySelector('[role="dialog"][aria-modal="true"]');
      if (openModal) {
        const closeButton = openModal.querySelector('[aria-label*="close"], [aria-label*="Close"]') as HTMLButtonElement;
        closeButton?.click();
      }
    },
    description: 'Close modal or dialog',
    category: 'Navigation',
  },
];

// Property management specific shortcuts
export const propertyManagementShortcuts: KeyboardShortcut[] = [
  {
    key: 'p',
    ctrlKey: true,
    action: () => {
      // Navigate to properties page
      window.location.href = '/properties';
    },
    description: 'Go to properties',
    category: 'Property Management',
  },
  {
    key: 'u',
    ctrlKey: true,
    action: () => {
      // Navigate to units page
      window.location.href = '/units';
    },
    description: 'Go to units',
    category: 'Property Management',
  },
  {
    key: 'l',
    ctrlKey: true,
    action: () => {
      // Navigate to leases page
      window.location.href = '/leases';
    },
    description: 'Go to leases',
    category: 'Lease Management',
  },
  {
    key: 'm',
    ctrlKey: true,
    action: () => {
      // Navigate to maintenance page
      window.location.href = '/maintenance';
    },
    description: 'Go to maintenance',
    category: 'Maintenance',
  },
  {
    key: 'f',
    ctrlKey: true,
    action: () => {
      // Navigate to financial dashboard
      window.location.href = '/financial';
    },
    description: 'Go to financial dashboard',
    category: 'Financial',
  },
];

// Hook for showing keyboard shortcuts help
export function useKeyboardShortcutsHelp(shortcuts: KeyboardShortcut[]) {
  const { formatShortcut } = useKeyboardShortcuts({ shortcuts: [] });

  const getShortcutsByCategory = useCallback(() => {
    const categories: Record<string, KeyboardShortcut[]> = {};
    
    shortcuts.forEach(shortcut => {
      const category = shortcut.category || 'General';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(shortcut);
    });
    
    return categories;
  }, [shortcuts]);

  return {
    formatShortcut,
    getShortcutsByCategory,
  };
}