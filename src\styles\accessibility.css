/**
 * Accessibility-focused CSS styles
 */

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Show screen reader content when focused */
.sr-only:focus,
.sr-only:active {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Focus indicators */
.focus-visible:focus-visible {
  outline: var(--focus-ring-width, 2px) var(--focus-ring-style, solid) var(--color-ring, #2563eb);
  outline-offset: var(--focus-ring-offset, 2px);
}

/* High contrast focus indicators */
.high-contrast-focus:focus-visible {
  outline: 3px solid #000;
  outline-offset: 2px;
  box-shadow: 0 0 0 5px #fff, 0 0 0 8px #000;
}

/* Skip links */
.skip-links a {
  transform: translateY(-100%);
  transition: transform var(--transition-duration, 150ms) ease-in-out;
}

.skip-links a:focus {
  transform: translateY(0);
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Force reduced motion when setting is enabled */
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .auto-high-contrast {
    filter: contrast(150%);
  }
}

/* Color blind friendly patterns */
.pattern-success {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    currentColor 2px,
    currentColor 4px
  );
}

.pattern-warning {
  background-image: repeating-linear-gradient(
    -45deg,
    transparent,
    transparent 2px,
    currentColor 2px,
    currentColor 4px
  );
}

.pattern-error {
  background-image: repeating-linear-gradient(
    90deg,
    transparent,
    transparent 2px,
    currentColor 2px,
    currentColor 4px
  );
}

/* Font size adjustments */
.font-size-small {
  font-size: var(--base-font-size, 14px);
}

.font-size-medium {
  font-size: var(--base-font-size, 16px);
}

.font-size-large {
  font-size: var(--base-font-size, 18px);
}

.font-size-extra-large {
  font-size: var(--base-font-size, 20px);
}

/* Font weight adjustments */
.font-weight-normal {
  font-weight: var(--base-font-weight, 400);
}

.font-weight-medium {
  font-weight: var(--base-font-weight, 500);
}

.font-weight-bold {
  font-weight: var(--base-font-weight, 600);
}

/* Interactive element sizing for touch accessibility */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Keyboard navigation indicators */
.keyboard-navigation [tabindex="0"]:focus,
.keyboard-navigation button:focus,
.keyboard-navigation input:focus,
.keyboard-navigation select:focus,
.keyboard-navigation textarea:focus,
.keyboard-navigation a:focus {
  outline: var(--focus-ring-width, 2px) solid var(--color-ring, #2563eb);
  outline-offset: var(--focus-ring-offset, 2px);
}

/* Table accessibility */
table[role="table"] {
  border-collapse: collapse;
}

table[role="table"] th,
table[role="table"] td {
  border: 1px solid var(--color-border, #e2e8f0);
}

table[role="table"] th[aria-sort] {
  cursor: pointer;
  user-select: none;
}

table[role="table"] th[aria-sort]:hover {
  background-color: var(--color-muted, #f8fafc);
}

/* Form accessibility */
.form-field {
  position: relative;
}

.form-field input:invalid,
.form-field textarea:invalid,
.form-field select:invalid {
  border-color: var(--color-destructive, #dc2626);
}

.form-field [aria-invalid="true"] {
  border-color: var(--color-destructive, #dc2626);
}

/* Error message styling */
[role="alert"] {
  padding: 0.5rem;
  border-radius: 0.375rem;
  background-color: var(--color-destructive, #dc2626);
  color: var(--color-destructive-foreground, #ffffff);
}

/* Loading states */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Confirmation states */
[data-confirming="true"] {
  animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
}

/* Landmark styling */
[role="main"] {
  min-height: 200px;
}

[role="navigation"] {
  border-bottom: 1px solid var(--color-border, #e2e8f0);
}

[role="banner"] {
  border-bottom: 1px solid var(--color-border, #e2e8f0);
}

[role="contentinfo"] {
  border-top: 1px solid var(--color-border, #e2e8f0);
  margin-top: 2rem;
}

/* Dialog accessibility */
[role="dialog"] {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 50;
  max-height: 90vh;
  overflow-y: auto;
}

[role="dialog"][aria-modal="true"] {
  backdrop-filter: blur(4px);
}

/* Tooltip accessibility */
[role="tooltip"] {
  position: absolute;
  z-index: 40;
  padding: 0.5rem;
  background-color: var(--color-popover, #ffffff);
  border: 1px solid var(--color-border, #e2e8f0);
  border-radius: 0.375rem;
  box-shadow: var(--shadow-md);
  font-size: 0.875rem;
  max-width: 200px;
}

/* Menu accessibility */
[role="menu"] {
  padding: 0.25rem;
  background-color: var(--color-popover, #ffffff);
  border: 1px solid var(--color-border, #e2e8f0);
  border-radius: 0.375rem;
  box-shadow: var(--shadow-md);
}

[role="menuitem"] {
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 0.25rem;
}

[role="menuitem"]:hover,
[role="menuitem"]:focus {
  background-color: var(--color-accent, #f1f5f9);
}

/* Progress indicator accessibility */
[role="progressbar"] {
  background-color: var(--color-secondary, #f1f5f9);
  border-radius: 9999px;
  overflow: hidden;
}

[role="progressbar"] > div {
  height: 100%;
  background-color: var(--color-primary, #1e40af);
  transition: width var(--transition-duration, 150ms) ease-in-out;
}

/* Alert accessibility */
[role="alert"] {
  padding: 1rem;
  border-radius: 0.375rem;
  border-left: 4px solid currentColor;
}

[role="alert"].alert-info {
  background-color: var(--color-info, #0ea5e9);
  color: var(--color-info-foreground, #ffffff);
}

[role="alert"].alert-success {
  background-color: var(--color-success, #16a34a);
  color: var(--color-success-foreground, #ffffff);
}

[role="alert"].alert-warning {
  background-color: var(--color-warning, #d97706);
  color: var(--color-warning-foreground, #ffffff);
}

[role="alert"].alert-error {
  background-color: var(--color-error, #dc2626);
  color: var(--color-error-foreground, #ffffff);
}

/* Print accessibility */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
}