import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { User, Star, Clock, DollarSign, CheckCircle, AlertTriangle } from 'lucide-react';
import { useToast } from '../ui/use-toast';

interface VendorAssignmentProps {
  ticketId: Id<"maintenanceTickets">;
  ticket: any; // Ticket data
  onAssignmentComplete: () => void;
}

export const VendorAssignment: React.FC<VendorAssignmentProps> = ({
  ticketId,
  ticket,
  onAssignmentComplete
}) => {
  const { toast } = useToast();
  const [selectedVendorId, setSelectedVendorId] = useState<Id<"users"> | ''>('');
  const [estimatedCost, setEstimatedCost] = useState('');
  const [estimatedDuration, setEstimatedDuration] = useState('');
  const [notes, setNotes] = useState('');
  const [isAssigning, setIsAssigning] = useState(false);

  // Fetch available vendors for this ticket category
  const vendors = useQuery(api.vendors.getBySpecialty, { 
    specialty: ticket.category,
    propertyId: ticket.propertyId 
  });

  // Assign vendor mutation
  const assignVendor = useMutation(api.maintenance.assignVendor);

  const handleAssignment = async () => {
    if (!selectedVendorId) {
      toast({
        title: "Error",
        description: "Please select a vendor",
        variant: "destructive",
      });
      return;
    }

    setIsAssigning(true);

    try {
      await assignVendor({
        ticketId,
        vendorId: selectedVendorId as Id<"users">,
        estimatedCost: estimatedCost ? parseFloat(estimatedCost) : undefined,
        estimatedDuration: estimatedDuration ? parseFloat(estimatedDuration) : undefined,
        notes: notes || undefined
      });

      toast({
        title: "Success",
        description: "Vendor assigned successfully",
      });

      onAssignmentComplete();
    } catch (error) {
      console.error('Error assigning vendor:', error);
      toast({
        title: "Error",
        description: "Failed to assign vendor. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAssigning(false);
    }
  };

  const getPerformanceColor = (rating: number) => {
    if (rating >= 4.5) return 'text-green-600';
    if (rating >= 3.5) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAvailabilityStatus = (vendor: any) => {
    const now = new Date();
    const currentDay = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase(); // e.g., 'monday'
    const currentHour = now.getHours();
    
    const isWorkingDay = vendor.availability.workingDays.includes(currentDay);
    const startHour = parseInt(vendor.availability.workingHours.start.split(':')[0]);
    const endHour = parseInt(vendor.availability.workingHours.end.split(':')[0]);
    const isWorkingHour = currentHour >= startHour && currentHour <= endHour;

    if (ticket.priority === 'emergency' && vendor.availability.emergencyAvailable) {
      return { status: 'available', label: 'Emergency Available', color: 'bg-green-500' };
    }

    if (isWorkingDay && isWorkingHour) {
      return { status: 'available', label: 'Available Now', color: 'bg-green-500' };
    }

    if (isWorkingDay && !isWorkingHour) {
      return { status: 'later', label: 'Available Later Today', color: 'bg-yellow-500' };
    }

    return { status: 'unavailable', label: 'Not Available Today', color: 'bg-red-500' };
  };

  if (!vendors) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading vendors...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (vendors.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Vendors Available</h3>
          <p className="text-muted-foreground text-center">
            No vendors are available for {ticket.category} services in this property.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Assign Vendor</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Vendor Selection */}
          <div className="space-y-4">
            <Label>Select Vendor</Label>
            <div className="grid gap-4">
              {vendors.map((vendor: any) => {
                const availability = getAvailabilityStatus(vendor);
                const isSelected = selectedVendorId === vendor.userId;
                
                return (
                  <div
                    key={vendor._id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedVendorId(vendor.userId)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <User className="h-4 w-4" />
                          <h3 className="font-semibold">{vendor.companyName}</h3>
                          <Badge className={`${availability.color} text-white text-xs`}>
                            {availability.label}
                          </Badge>
                        </div>
                        
                        <p className="text-sm text-muted-foreground mb-2">
                          Contact: {vendor.contactPerson} • {vendor.phone}
                        </p>
                        
                        <div className="flex items-center gap-4 text-sm">
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span className={getPerformanceColor(vendor.performance.averageRating)}>
                              {vendor.performance.averageRating.toFixed(1)}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span>{vendor.performance.slaCompliance}% SLA</span>
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4 text-blue-500" />
                            <span>{vendor.performance.averageResponseTime.toFixed(1)}h response</span>
                          </div>
                          
                          {vendor.pricing.hourlyRate && (
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4 text-green-500" />
                              <span>${vendor.pricing.hourlyRate}/hr</span>
                            </div>
                          )}
                        </div>
                        
                        <div className="mt-2">
                          <div className="text-xs text-muted-foreground">
                            Specialties: {vendor.specialties.join(', ')}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Completed Jobs: {vendor.performance.completedJobs} / {vendor.performance.totalJobs}
                          </div>
                        </div>
                      </div>
                      
                      {isSelected && (
                        <CheckCircle className="h-5 w-5 text-blue-500" />
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <Separator />

          {/* Assignment Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="estimatedCost">Estimated Cost (Optional)</Label>
              <Input
                id="estimatedCost"
                type="number"
                step="0.01"
                placeholder="0.00"
                value={estimatedCost}
                onChange={(e) => setEstimatedCost(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="estimatedDuration">Estimated Duration (Hours)</Label>
              <Input
                id="estimatedDuration"
                type="number"
                step="0.5"
                placeholder="0.0"
                value={estimatedDuration}
                onChange={(e) => setEstimatedDuration(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Assignment Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Add any specific instructions or notes for the vendor..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-4">
            <Button variant="outline" onClick={onAssignmentComplete}>
              Cancel
            </Button>
            <Button 
              onClick={handleAssignment} 
              disabled={!selectedVendorId || isAssigning}
            >
              {isAssigning ? 'Assigning...' : 'Assign Vendor'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};