import React, { useState, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '../ui/tabs';
import { But<PERSON> } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  PieChart, 
  BarChart3, 
  Calendar,
  Download
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart as RechartsPieChart, Cell, Pie } from 'recharts';
import { format, subMonths, subYears } from 'date-fns';
import { Id } from '../../../convex/_generated/dataModel';
import { OccupancyPerformanceAnalytics } from './OccupancyPerformanceAnalytics';

interface FinancialDashboardProps {
  propertyId?: Id<"properties">;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export const FinancialDashboard: React.FC<FinancialDashboardProps> = ({ propertyId }) => {
  const [dateRange, setDateRange] = useState<'month' | 'quarter' | 'year'>('month');
  const [selectedTab, setSelectedTab] = useState('overview');

  // Calculate date range
  const { startDate, endDate } = useMemo(() => {
    const now = new Date();
    let start: Date;
    
    switch (dateRange) {
      case 'month':
        start = subMonths(now, 1);
        break;
      case 'quarter':
        start = subMonths(now, 3);
        break;
      case 'year':
        start = subYears(now, 1);
        break;
      default:
        start = subMonths(now, 1);
    }
    
    return {
      startDate: start.getTime(),
      endDate: now.getTime(),
    };
  }, [dateRange]);

  // Fetch financial data
  const dashboardData = useQuery(api.financialAnalytics.getFinancialDashboard, {
    propertyId,
    startDate,
    endDate,
  });

  const kpiData = useQuery(api.financialAnalytics.getFinancialKPIs, {
    propertyId,
    period: dateRange,
  });

  const plStatement = useQuery(api.financialAnalytics.generatePLStatement, {
    propertyId,
    startDate,
    endDate,
  });

  const cashFlowData = useQuery(api.financialAnalytics.getCashFlowAnalysis, {
    propertyId,
    months: dateRange === 'year' ? 12 : dateRange === 'quarter' ? 3 : 1,
  });

  if (!dashboardData || !kpiData || !plStatement || !cashFlowData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading financial data...</p>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Prepare chart data
  const monthlyChartData = dashboardData.monthlyBreakdown.map(month => ({
    name: `${month.year}-${String(month.month).padStart(2, '0')}`,
    revenue: month.revenue,
    expenses: month.expenses,
    netIncome: month.netIncome,
    month: month.month,
    year: month.year,
  }));

  const revenueBreakdownData = [
    { name: 'Rent', value: plStatement.revenue.rent, color: COLORS[0] },
    { name: 'Deposits', value: plStatement.revenue.deposits, color: COLORS[1] },
    { name: 'Maintenance', value: plStatement.revenue.maintenance, color: COLORS[2] },
    { name: 'Other', value: plStatement.revenue.other, color: COLORS[3] },
  ].filter(item => item.value > 0);

  const expenseBreakdownData = Object.entries(plStatement.expenses.byCategory)
    .filter(([_, value]) => value > 0)
    .map(([category, value], index) => ({
      name: category.charAt(0).toUpperCase() + category.slice(1),
      value,
      color: COLORS[index % COLORS.length],
    }));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Financial Dashboard</h1>
          <p className="text-gray-600">
            Comprehensive financial analytics and reporting
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={dateRange} onValueChange={(value: 'month' | 'quarter' | 'year') => setDateRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">Last Month</SelectItem>
              <SelectItem value="quarter">Last Quarter</SelectItem>
              <SelectItem value="year">Last Year</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(kpiData.kpis.totalRevenue)}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {kpiData.kpis.revenueGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1 text-red-600" />
              )}
              <span className={kpiData.kpis.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'}>
                {formatPercentage(Math.abs(kpiData.kpis.revenueGrowth))}
              </span>
              <span className="ml-1">from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Income</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(kpiData.kpis.netIncome)}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {kpiData.kpis.profitGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1 text-red-600" />
              )}
              <span className={kpiData.kpis.profitGrowth >= 0 ? 'text-green-600' : 'text-red-600'}>
                {formatPercentage(Math.abs(kpiData.kpis.profitGrowth))}
              </span>
              <span className="ml-1">from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Profit Margin</CardTitle>
            <PieChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(kpiData.kpis.profitMargin)}</div>
            <div className="text-xs text-muted-foreground">
              Industry avg: 15-25%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Collection Rate</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(kpiData.kpis.collectionRate)}</div>
            <div className="flex items-center gap-2 text-xs">
              <Badge variant={kpiData.kpis.collectionRate >= 90 ? "default" : "destructive"}>
                {kpiData.kpis.collectionRate >= 90 ? "Excellent" : "Needs Attention"}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="pl-statement">P&L Statement</TabsTrigger>
          <TabsTrigger value="cash-flow">Cash Flow</TabsTrigger>
          <TabsTrigger value="occupancy">Occupancy</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
                <CardDescription>Monthly revenue and expenses over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={monthlyChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => formatCurrency(value)} />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Area type="monotone" dataKey="revenue" stackId="1" stroke="#8884d8" fill="#8884d8" />
                    <Area type="monotone" dataKey="expenses" stackId="2" stroke="#82ca9d" fill="#82ca9d" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Revenue Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Revenue Breakdown</CardTitle>
                <CardDescription>Revenue sources distribution</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={revenueBreakdownData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {revenueBreakdownData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Outstanding Amounts */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Pending Collections</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-yellow-600">
                  {formatCurrency(kpiData.kpis.pendingAmount)}
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {dashboardData.invoiceMetrics.pendingInvoices} pending invoices
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Overdue Amounts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-red-600">
                  {formatCurrency(kpiData.kpis.overdueAmount)}
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {dashboardData.invoiceMetrics.overdueInvoices} overdue invoices
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="pl-statement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Profit & Loss Statement</CardTitle>
              <CardDescription>
                Period: {format(new Date(startDate), 'MMM dd, yyyy')} - {format(new Date(endDate), 'MMM dd, yyyy')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Revenue Section */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Revenue</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Rent Revenue</span>
                      <span className="font-medium">{formatCurrency(plStatement.revenue.rent)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Deposit Revenue</span>
                      <span className="font-medium">{formatCurrency(plStatement.revenue.deposits)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Maintenance Revenue</span>
                      <span className="font-medium">{formatCurrency(plStatement.revenue.maintenance)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Other Revenue</span>
                      <span className="font-medium">{formatCurrency(plStatement.revenue.other)}</span>
                    </div>
                    <div className="border-t pt-2 flex justify-between font-semibold">
                      <span>Total Revenue</span>
                      <span>{formatCurrency(plStatement.revenue.total)}</span>
                    </div>
                  </div>
                </div>

                {/* Expenses Section */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Expenses</h3>
                  <div className="space-y-2">
                    {Object.entries(plStatement.expenses.byCategory).map(([category, amount]) => (
                      amount > 0 && (
                        <div key={category} className="flex justify-between">
                          <span className="capitalize">{category}</span>
                          <span className="font-medium">{formatCurrency(amount)}</span>
                        </div>
                      )
                    ))}
                    <div className="border-t pt-2 flex justify-between font-semibold">
                      <span>Total Expenses</span>
                      <span>{formatCurrency(plStatement.expenses.total)}</span>
                    </div>
                  </div>
                </div>

                {/* Profit Section */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold">Net Income</span>
                    <span className={`text-xl font-bold ${plStatement.profitLoss.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(plStatement.profitLoss.netIncome)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center mt-2">
                    <span>Gross Margin</span>
                    <span className="font-medium">{formatPercentage(plStatement.profitLoss.grossMargin)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cash-flow" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Cash Flow Chart */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Cash Flow Analysis</CardTitle>
                <CardDescription>Monthly cash inflows and outflows</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={cashFlowData.historicalData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="monthName" />
                    <YAxis tickFormatter={(value) => formatCurrency(value)} />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Bar dataKey="cashIn" fill="#10b981" name="Cash In" />
                    <Bar dataKey="cashOut" fill="#ef4444" name="Cash Out" />
                    <Bar dataKey="netCashFlow" fill="#3b82f6" name="Net Flow" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Cash Flow Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Cash Flow Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="text-sm text-gray-600">Total Cash In</div>
                  <div className="text-xl font-bold text-green-600">
                    {formatCurrency(cashFlowData.summary.totalCashIn)}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Total Cash Out</div>
                  <div className="text-xl font-bold text-red-600">
                    {formatCurrency(cashFlowData.summary.totalCashOut)}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Net Cash Flow</div>
                  <div className={`text-xl font-bold ${cashFlowData.summary.totalNetFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatCurrency(cashFlowData.summary.totalNetFlow)}
                  </div>
                </div>
                <div className="pt-4 border-t">
                  <div className="text-sm text-gray-600">Average Monthly Inflow</div>
                  <div className="text-lg font-semibold">
                    {formatCurrency(cashFlowData.summary.averageMonthlyInflow)}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Forecast */}
          {cashFlowData.forecast.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>3-Month Forecast</CardTitle>
                <CardDescription>Projected cash flow based on recent trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {cashFlowData.forecast.map((month, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="font-semibold">{month.monthName} {month.year}</div>
                      <div className="mt-2 space-y-1">
                        <div className="flex justify-between text-sm">
                          <span>Projected In:</span>
                          <span className="text-green-600">{formatCurrency(month.projectedCashIn)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Projected Out:</span>
                          <span className="text-red-600">{formatCurrency(month.projectedCashOut)}</span>
                        </div>
                        <div className="flex justify-between font-medium border-t pt-1">
                          <span>Net Flow:</span>
                          <span className={month.projectedNetFlow >= 0 ? 'text-green-600' : 'text-red-600'}>
                            {formatCurrency(month.projectedNetFlow)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="occupancy" className="space-y-4">
          <OccupancyPerformanceAnalytics propertyId={propertyId} />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Expense Breakdown */}
            {expenseBreakdownData.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Expense Breakdown</CardTitle>
                  <CardDescription>Maintenance expenses by category</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={expenseBreakdownData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {expenseBreakdownData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            )}

            {/* Payment Methods */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
                <CardDescription>Distribution of payment methods used</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>M-PESA</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ 
                            width: `${(dashboardData.paymentMetrics.mpesaPayments / dashboardData.paymentMetrics.totalPayments) * 100}%` 
                          }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">
                        {dashboardData.paymentMetrics.mpesaPayments}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Stripe</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ 
                            width: `${(dashboardData.paymentMetrics.stripePayments / dashboardData.paymentMetrics.totalPayments) * 100}%` 
                          }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">
                        {dashboardData.paymentMetrics.stripePayments}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Key Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Key Performance Indicators</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatPercentage(dashboardData.paymentMetrics.successfulPayments / dashboardData.paymentMetrics.totalPayments * 100)}
                  </div>
                  <div className="text-sm text-gray-600">Payment Success Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {kpiData.trends.monthlyAverage > 0 ? formatCurrency(kpiData.trends.monthlyAverage) : 'N/A'}
                  </div>
                  <div className="text-sm text-gray-600">Monthly Average Revenue</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {dashboardData.invoiceMetrics.totalInvoices}
                  </div>
                  <div className="text-sm text-gray-600">Total Invoices</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};