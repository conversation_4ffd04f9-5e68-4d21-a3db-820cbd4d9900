/**
 * Component to register onboarding flows based on user role
 */
import { useEffect } from 'react';
import { useOnboarding } from './OnboardingProvider';
import { allOnboardingFlows } from './OnboardingFlows';
import { useAuth } from '../../lib/auth-context';

export function OnboardingSetup() {
  const { registerFlow } = useOnboarding();
  const { user } = useAuth();

  useEffect(() => {
    // Register all onboarding flows
    allOnboardingFlows.forEach(flow => {
      registerFlow(flow);
    });
  }, [registerFlow]);

  // Auto-start role-specific onboarding for new users
  useEffect(() => {
    if (user?.role) {
      // This would typically check if it's the user's first login
      // For demo purposes, we'll just register the flows
      console.log(`User role: ${user.role} - onboarding flows registered`);
    }
  }, [user?.role]);

  return null; // This component doesn't render anything
}