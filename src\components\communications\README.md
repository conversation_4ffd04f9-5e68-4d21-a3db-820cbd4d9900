# Communication System

The Estate Pulse communication system provides comprehensive SMS, WhatsApp, and email messaging capabilities with template management, notification preferences, and delivery tracking.

## Features

### 1. Multi-Channel Messaging
- **SMS**: Send text messages via Twilio
- **WhatsApp**: Send WhatsApp messages via Twilio Business API
- **Email**: Email notifications (future implementation)

### 2. Message Templates
- Create reusable message templates with dynamic variables
- Support for different categories (payment reminders, maintenance updates, etc.)
- Template versioning and management
- Variable substitution with personalized data

### 3. Notification Preferences
- User-configurable notification settings per channel
- Quiet hours configuration
- Language preferences
- Property-specific preferences

### 4. Bulk Messaging
- Send messages to multiple recipients
- Batch processing with status tracking
- Failure handling and retry logic

### 5. Delivery Tracking
- Real-time message status updates
- Delivery confirmations
- Error logging and reporting

## Components

### CommunicationDashboard
Main dashboard component that provides:
- Overview of messaging statistics
- Quick access to all communication features
- Recent message history
- Performance metrics

### CommunicationCenter
Message sending interface with:
- Single message sending
- Bulk message capabilities
- Message history view
- Real-time status updates

### MessageTemplates
Template management interface for:
- Creating and editing templates
- Template categorization
- Variable management
- Template preview functionality

### NotificationPreferences
User preference management for:
- Channel-specific settings
- Quiet hours configuration
- Language selection
- Property-specific preferences

## Backend Implementation

### Convex Functions

#### Message Operations
- `sendSMS`: Send individual SMS messages
- `sendWhatsApp`: Send individual WhatsApp messages
- `sendBulkMessages`: Send messages to multiple recipients
- `getMessages`: Retrieve message history with filtering
- `getMessageStatus`: Get delivery status of specific messages

#### Template Management
- `createMessageTemplate`: Create new message templates
- `updateMessageTemplate`: Update existing templates
- `deleteMessageTemplate`: Remove templates
- `getMessageTemplates`: Retrieve templates with filtering

#### Notification Preferences
- `saveNotificationPreferences`: Save user preferences
- `getNotificationPreferences`: Retrieve user preferences

### Database Schema

#### Messages Table
```typescript
{
  type: "sms" | "whatsapp" | "email",
  recipient: string,
  content: string,
  status: "pending" | "sent" | "delivered" | "failed" | "read",
  propertyId?: Id<"properties">,
  userId?: Id<"users">,
  templateId?: string,
  externalId?: string, // Twilio SID
  sentAt?: number,
  deliveredAt?: number,
  readAt?: number,
  error?: string,
  createdAt: number,
  updatedAt: number
}
```

#### Message Templates Table
```typescript
{
  name: string,
  type: "sms" | "whatsapp" | "email",
  category: "payment_reminder" | "maintenance_update" | "lease_expiry" | "welcome" | "general" | "emergency",
  subject?: string, // For email templates
  content: string,
  variables: string[], // Available template variables
  isActive: boolean,
  propertyId?: Id<"properties">,
  createdBy: Id<"users">,
  createdAt: number,
  updatedAt: number
}
```

#### Notification Preferences Table
```typescript
{
  userId: Id<"users">,
  propertyId?: Id<"properties">,
  preferences: {
    sms: { enabled: boolean, paymentReminders: boolean, ... },
    whatsapp: { enabled: boolean, paymentReminders: boolean, ... },
    email: { enabled: boolean, paymentReminders: boolean, ... },
    inApp: { enabled: boolean, paymentReminders: boolean, ... }
  },
  quietHours: {
    enabled: boolean,
    startTime: string,
    endTime: string,
    timezone: string
  },
  language: string,
  createdAt: number,
  updatedAt: number
}
```

## Twilio Integration

### TwilioService Class
The `TwilioService` class provides:
- SMS sending via Twilio Messaging API
- WhatsApp messaging via Twilio WhatsApp Business API
- Phone number formatting for Kenyan numbers
- Bulk messaging capabilities
- Message status tracking
- Account information retrieval

### Configuration
Required environment variables:
```
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
TWILIO_WHATSAPP_NUMBER=your_whatsapp_business_number
```

### Phone Number Formatting
The system automatically formats Kenyan phone numbers:
- `**********` → `+************`
- `*********` → `+************`
- `************` → `+************`

## Usage Examples

### Sending a Single SMS
```typescript
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';

const sendSMS = useMutation(api.communications.sendSMS);

await sendSMS({
  phoneNumber: '+************',
  message: 'Your rent payment is due tomorrow.',
  propertyId: 'property_id',
});
```

### Using Message Templates
```typescript
const template = {
  name: 'Payment Reminder',
  type: 'sms',
  category: 'payment_reminder',
  content: 'Dear {{tenant_name}}, your rent of KES {{amount}} is due on {{due_date}}.',
  variables: ['tenant_name', 'amount', 'due_date'],
};

// Variables will be replaced when sending:
// "Dear John Doe, your rent of KES 50,000 is due on 2024-02-15."
```

### Bulk Messaging
```typescript
const recipients = [
  { phoneNumber: '+************', personalizedData: { tenant_name: 'John' } },
  { phoneNumber: '+254723456789', personalizedData: { tenant_name: 'Jane' } },
];

await sendBulkMessages({
  recipients,
  message: 'Hello {{tenant_name}}, this is a general announcement.',
  type: 'sms',
  propertyId: 'property_id',
});
```

## Testing

### Unit Tests
- Twilio service functionality
- Phone number formatting
- Error handling
- Bulk messaging logic

### Integration Tests
- Convex function testing
- Database operations
- Message template management
- Notification preferences

### Component Tests
- React component rendering
- User interactions
- Form validation
- State management

## Security Considerations

1. **API Key Protection**: Twilio credentials stored securely in environment variables
2. **Input Validation**: Phone numbers and message content validated before sending
3. **Rate Limiting**: Implement rate limiting to prevent abuse
4. **User Permissions**: Check user permissions before sending messages
5. **Data Privacy**: Respect user notification preferences and quiet hours

## Performance Optimization

1. **Batch Processing**: Bulk messages processed in batches to avoid API limits
2. **Caching**: Template and preference data cached for better performance
3. **Async Processing**: Message sending handled asynchronously
4. **Error Handling**: Robust error handling with retry logic

## Future Enhancements

1. **Email Integration**: Add email messaging capabilities
2. **Advanced Templates**: Rich text templates with images and attachments
3. **Scheduling**: Schedule messages for future delivery
4. **Analytics**: Advanced messaging analytics and reporting
5. **A/B Testing**: Test different message templates for effectiveness
6. **Webhooks**: Handle delivery status webhooks from Twilio
7. **Multi-language**: Support for multiple languages in templates
8. **AI Integration**: AI-powered message optimization and personalization