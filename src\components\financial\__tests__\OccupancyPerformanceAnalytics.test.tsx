import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ConvexProvider } from 'convex/react';
import { ConvexReactClient } from 'convex/react';
import { OccupancyPerformanceAnalytics } from '../OccupancyPerformanceAnalytics';

// Mock Convex client
const mockConvexClient = new ConvexReactClient('https://test.convex.cloud');

// Mock useQuery
const mockUseQuery = vi.fn();

vi.mock('convex/react', async () => {
  const actual = await vi.importActual('convex/react');
  return {
    ...actual,
    useQuery: mockUseQuery,
  };
});

// Mock data
const mockOccupancyData = {
  properties: [{
    propertyId: 'property1',
    propertyName: 'Test Property',
    propertyType: 'residential',
    currentOccupancy: {
      totalUnits: 100,
      occupiedUnits: 85,
      vacantUnits: 10,
      maintenanceUnits: 5,
      occupancyRate: 85,
    },
    monthlyTrends: [
      {
        month: 1,
        year: 2024,
        monthName: 'January',
        totalUnits: 100,
        occupiedUnits: 80,
        occupancyRate: 80,
        newLeases: 5,
        terminatedLeases: 2,
        renewedLeases: 3,
      },
      {
        month: 2,
        year: 2024,
        monthName: 'February',
        totalUnits: 100,
        occupiedUnits: 85,
        occupancyRate: 85,
        newLeases: 8,
        terminatedLeases: 3,
        renewedLeases: 2,
      },
    ],
    heatmapData: [
      {
        unitId: 'unit1',
        unitNumber: '101',
        status: 'occupied',
        occupancyDays: 350,
        vacancyDays: 15,
        lastOccupied: Date.now(),
        currentTenant: 'tenant1',
      },
      {
        unitId: 'unit2',
        unitNumber: '102',
        status: 'vacant',
        occupancyDays: 200,
        vacancyDays: 165,
        lastOccupied: Date.now() - (30 * 24 * 60 * 60 * 1000),
        currentTenant: null,
      },
    ],
  }],
  generatedAt: Date.now(),
};

const mockRetentionData = {
  summary: {
    totalLeases: 120,
    activeLeases: 85,
    terminatedLeases: 25,
    expiredLeases: 10,
    retentionRate: 75,
    turnoverRate: 25,
    averageLeaseDuration: 450,
    totalTenants: 90,
    activeTenants: 85,
  },
  monthlyTrends: [
    {
      month: 1,
      year: 2024,
      monthName: 'January',
      newLeases: 5,
      terminatedLeases: 2,
      renewedLeases: 3,
      turnoverRate: 20,
      retentionRate: 80,
    },
  ],
  tenantLifecycle: [
    {
      tenantId: 'tenant1',
      totalLeases: 2,
      totalDuration: 800,
      averageLeaseDuration: 400,
      isActive: true,
      lastLeaseEnd: Date.now(),
      renewalCount: 1,
    },
  ],
  period: { startDate: Date.now() - (365 * 24 * 60 * 60 * 1000), endDate: Date.now() },
  generatedAt: Date.now(),
};

const mockRentOptimization = {
  recommendations: [{
    propertyId: 'property1',
    propertyName: 'Test Property',
    unitAnalysis: [
      {
        unitId: 'unit1',
        unitNumber: '101',
        currentRent: 50000,
        marketRate: 55000,
        recommendedRent: 52000,
        potentialIncrease: 2000,
        potentialIncreasePercent: 4,
        reasoning: 'High occupancy allows for increase',
        riskLevel: 'low' as const,
        occupancyHistory: {
          daysOccupied: 350,
          daysVacant: 15,
          occupancyRate: 95.9,
        },
      },
    ],
    propertyRecommendations: {
      averageCurrentRent: 50000,
      averageMarketRate: 55000,
      totalPotentialIncrease: 15000,
      highPerformingUnits: 70,
      underperformingUnits: 15,
      vacancyRisk: 15,
    },
  }],
  generatedAt: Date.now(),
};

const mockMarketBenchmarking = {
  properties: [{
    propertyId: 'property1',
    propertyName: 'Test Property',
    propertyType: 'residential',
    metrics: {
      occupancyRate: 85,
      averageRent: 50000,
      revenuePerUnit: 600000,
      maintenanceCostPerUnit: 50000,
      profitMargin: 25,
      tenantRetentionRate: 75,
      averageLeaseDuration: 450,
    },
    benchmarkComparison: {
      occupancyRanking: 3,
      rentRanking: 5,
      profitabilityRanking: 2,
      overallScore: 78.5,
      percentile: 80,
    },
    recommendations: [
      'Focus on improving occupancy rate through marketing',
      'Consider rent optimization for underperforming units',
    ],
  }],
  marketAverages: {
    occupancyRate: 82,
    averageRent: 48000,
    revenuePerUnit: 580000,
    maintenanceCostPerUnit: 55000,
    profitMargin: 22,
    tenantRetentionRate: 70,
    averageLeaseDuration: 400,
  },
  totalProperties: 10,
  generatedAt: Date.now(),
};



describe('OccupancyPerformanceAnalytics', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup mock responses for different queries
    mockUseQuery.mockImplementation((query) => {
      if (query.toString().includes('getOccupancyAnalytics')) {
        return mockOccupancyData;
      }
      if (query.toString().includes('getTenantRetentionAnalytics')) {
        return mockRetentionData;
      }
      if (query.toString().includes('getRentOptimizationRecommendations')) {
        return mockRentOptimization;
      }
      if (query.toString().includes('getMarketBenchmarking')) {
        return mockMarketBenchmarking;
      }
      return null;
    });
  });

  const renderComponent = (propertyId?: string) => {
    return render(
      <ConvexProvider client={mockConvexClient}>
        <OccupancyPerformanceAnalytics propertyId={propertyId as any} />
      </ConvexProvider>
    );
  };

  it('renders loading state initially', () => {
    mockUseQuery.mockReturnValue(null);
    
    renderComponent();
    
    expect(screen.getByText('Loading analytics data...')).toBeInTheDocument();
  });

  it('renders occupancy analytics with key metrics', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('Occupancy & Performance Analytics')).toBeInTheDocument();
    });

    // Check key metrics cards
    expect(screen.getByText('Current Occupancy')).toBeInTheDocument();
    expect(screen.getByText('85.0%')).toBeInTheDocument();
    expect(screen.getByText('85 of 100 units')).toBeInTheDocument();

    expect(screen.getByText('Tenant Retention')).toBeInTheDocument();
    expect(screen.getByText('75.0%')).toBeInTheDocument();

    expect(screen.getByText('Avg Lease Duration')).toBeInTheDocument();
    expect(screen.getByText('450 days')).toBeInTheDocument();
  });

  it('displays occupancy heatmap correctly', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('Unit Occupancy Heatmap')).toBeInTheDocument();
    });

    // Check heatmap legend
    expect(screen.getByText('High (90%+)')).toBeInTheDocument();
    expect(screen.getByText('Medium (70-89%)')).toBeInTheDocument();
    expect(screen.getByText('Low (1-69%)')).toBeInTheDocument();
    expect(screen.getByText('Vacant (0%)')).toBeInTheDocument();
  });

  it('shows tenant retention analytics', async () => {
    renderComponent();

    // Switch to retention tab
    const retentionTab = screen.getByRole('tab', { name: /tenant retention/i });
    retentionTab.click();

    await waitFor(() => {
      expect(screen.getByText('Retention Rate')).toBeInTheDocument();
      expect(screen.getByText('Turnover Rate')).toBeInTheDocument();
    });

    expect(screen.getByText('85 of 90 tenants retained')).toBeInTheDocument();
  });

  it('displays rent optimization recommendations', async () => {
    renderComponent();

    // Switch to optimization tab
    const optimizationTab = screen.getByRole('tab', { name: /rent optimization/i });
    optimizationTab.click();

    await waitFor(() => {
      expect(screen.getByText('Unit-Level Recommendations')).toBeInTheDocument();
    });

    expect(screen.getByText('Potential Increase')).toBeInTheDocument();
    expect(screen.getByText('High Performers')).toBeInTheDocument();
    expect(screen.getByText('70')).toBeInTheDocument(); // High performing units
  });

  it('shows market benchmarking data', async () => {
    renderComponent();

    // Switch to benchmarking tab
    const benchmarkingTab = screen.getByRole('tab', { name: /market benchmarking/i });
    benchmarkingTab.click();

    await waitFor(() => {
      expect(screen.getByText('Market Comparison')).toBeInTheDocument();
    });

    expect(screen.getByText('Overall Score')).toBeInTheDocument();
    expect(screen.getByText('78.5')).toBeInTheDocument();
    expect(screen.getByText('80.0% percentile')).toBeInTheDocument();
  });

  it('handles time range selection', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    // The select should be present and functional
    const select = screen.getByRole('combobox');
    expect(select).toBeInTheDocument();
  });

  it('displays improvement recommendations', async () => {
    renderComponent();

    // Switch to benchmarking tab
    const benchmarkingTab = screen.getByRole('tab', { name: /market benchmarking/i });
    benchmarkingTab.click();

    await waitFor(() => {
      expect(screen.getByText('Improvement Recommendations')).toBeInTheDocument();
    });

    expect(screen.getByText('Focus on improving occupancy rate through marketing')).toBeInTheDocument();
    expect(screen.getByText('Consider rent optimization for underperforming units')).toBeInTheDocument();
  });

  it('formats currency values correctly', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('KES 15,000')).toBeInTheDocument(); // Potential monthly increase
    });
  });

  it('handles property-specific data when propertyId is provided', async () => {
    renderComponent('property1');

    await waitFor(() => {
      expect(mockUseQuery).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          propertyId: 'property1',
        })
      );
    });
  });

  it('shows occupancy trends chart', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('Occupancy Trends')).toBeInTheDocument();
      expect(screen.getByText('Monthly occupancy rates and lease activity')).toBeInTheDocument();
    });
  });

  it('displays lease activity chart', async () => {
    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('Lease Activity')).toBeInTheDocument();
      expect(screen.getByText('Monthly new leases, terminations, and renewals')).toBeInTheDocument();
    });
  });
});