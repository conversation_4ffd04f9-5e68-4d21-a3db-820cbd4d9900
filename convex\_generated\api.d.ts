/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth from "../auth.js";
import type * as communications from "../communications.js";
import type * as compliance from "../compliance.js";
import type * as documents from "../documents.js";
import type * as esignature from "../esignature.js";
import type * as executiveReports from "../executiveReports.js";
import type * as financialAnalytics from "../financialAnalytics.js";
import type * as invoices from "../invoices.js";
import type * as leases from "../leases.js";
import type * as lib_escalation from "../lib/escalation.js";
import type * as lib_scheduledTasks from "../lib/scheduledTasks.js";
import type * as lib_validation from "../lib/validation.js";
import type * as maintenance from "../maintenance.js";
import type * as notificationTriggers from "../notificationTriggers.js";
import type * as payments from "../payments.js";
import type * as permissions from "../permissions.js";
import type * as portals from "../portals.js";
import type * as properties from "../properties.js";
import type * as realtimeSync from "../realtimeSync.js";
import type * as scheduledReports from "../scheduledReports.js";
import type * as scheduledReportsInternal from "../scheduledReportsInternal.js";
import type * as twilioService from "../twilioService.js";
import type * as units from "../units.js";
import type * as users from "../users.js";
import type * as vendors from "../vendors.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  auth: typeof auth;
  communications: typeof communications;
  compliance: typeof compliance;
  documents: typeof documents;
  esignature: typeof esignature;
  executiveReports: typeof executiveReports;
  financialAnalytics: typeof financialAnalytics;
  invoices: typeof invoices;
  leases: typeof leases;
  "lib/escalation": typeof lib_escalation;
  "lib/scheduledTasks": typeof lib_scheduledTasks;
  "lib/validation": typeof lib_validation;
  maintenance: typeof maintenance;
  notificationTriggers: typeof notificationTriggers;
  payments: typeof payments;
  permissions: typeof permissions;
  portals: typeof portals;
  properties: typeof properties;
  realtimeSync: typeof realtimeSync;
  scheduledReports: typeof scheduledReports;
  scheduledReportsInternal: typeof scheduledReportsInternal;
  twilioService: typeof twilioService;
  units: typeof units;
  users: typeof users;
  vendors: typeof vendors;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
