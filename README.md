# 🏢 EstatePulse

> **Modern Property Management Platform** - A comprehensive, cross-platform desktop application for property managers, landlords, and real estate professionals.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Electron](https://img.shields.io/badge/Electron-191970?logo=Electron&logoColor=white)](https://www.electronjs.org/)
[![React](https://img.shields.io/badge/React-20232A?logo=react&logoColor=61DAFB)](https://reactjs.org/)

---

## 🌟 Overview

EstatePulse is a feature-rich property management solution that streamlines operations for property managers and landlords. Built with modern technologies, it offers real-time synchronization, comprehensive reporting, and seamless integrations with payment processors and communication platforms.

### ✨ Key Highlights

- 🖥️ **Cross-Platform Desktop App** - Native performance on Windows, macOS, and Linux
- ⚡ **Real-Time Synchronization** - Instant updates across all devices and users
- 🎨 **Modern Interface** - Intuitive design with accessibility features
- 🔐 **Enterprise Security** - Role-based access control and data encryption
- 📱 **White-Label Portals** - Customizable tenant and vendor portals
- 💳 **Payment Integration** - M-PESA, Stripe, and bank transfer support

---

## 🚀 Features

### 🏠 **Property Management**
- Multi-property portfolio management
- Unit tracking and availability management
- Property analytics and performance metrics
- Document management and storage
- Maintenance scheduling and tracking

### 👥 **User Management**
- Role-based access control (Owner, Manager, Tenant, Vendor)
- User onboarding and KYC verification
- Activity tracking and audit logs
- Communication preferences management

### 📋 **Lease Management**
- Digital lease creation and management
- E-signature integration (DocuSign, HelloSign)
- Automated lease renewals and notifications
- Rent roll generation and reporting
- Lease document templates

### 🔧 **Maintenance System**
- SLA-based ticket management
- Vendor assignment and performance tracking
- Escalation workflows
- Photo and document attachments
- Cost tracking and reporting

### 💰 **Financial Management**
- Invoice generation and management
- Payment processing (M-PESA, Stripe)
- Financial reporting and analytics
- Cash flow analysis
- Automated payment reminders

### 📊 **Analytics & Reporting**
- Property performance dashboards
- Occupancy analytics
- Financial reports (P&L, Cash Flow)
- Maintenance analytics
- Custom report generation

### 🌐 **Communication Hub**
- Multi-channel notifications (SMS, WhatsApp, Email)
- Message templates and automation
- Bulk messaging capabilities
- Communication logs and delivery tracking
- Notification preferences management

### 🎨 **White-Label Portals**
- Customizable tenant portals
- Branded property websites
- Custom domain support
- Portal analytics and engagement tracking
- Mobile-responsive design

---

## 🛠️ Technology Stack

### **Frontend**
- **React 18** - Modern UI library with hooks and concurrent features
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - High-quality component library
- **Radix UI** - Accessible component primitives
- **Zustand** - Lightweight state management

### **Desktop Platform**
- **Electron** - Cross-platform desktop framework
- **Vite** - Fast build tool and dev server
- **Auto-updater** - Seamless application updates
- **Native integrations** - File system, notifications, printing

### **Backend & Database**
- **Convex** - Real-time database and API platform
- **Real-time sync** - Instant data synchronization
- **Serverless functions** - Scalable backend logic
- **File storage** - Secure document and image storage

### **Integrations**
- **Payment Processors**: M-PESA, Stripe
- **E-Signature**: DocuSign, HelloSign
- **Communications**: Twilio (SMS/WhatsApp)
- **Email**: SendGrid, AWS SES
- **Analytics**: PostHog, Sentry

### **Security & Compliance**
- **Data encryption** - At rest and in transit
- **GDPR compliance** - Privacy controls and data retention
- **Audit logging** - Comprehensive activity tracking
- **Session management** - Secure authentication
- **Rate limiting** - API protection

---

## 🏗️ Architecture

```mermaid
graph TB
    A[Desktop App - Electron] --> B[React Renderer]
    A --> C[Main Process]
    A --> D[Preload Scripts]
    
    B --> E[Convex Real-time DB]
    C --> F[Native APIs]
    C --> G[Auto Updater]
    
    E --> H[Payment APIs]
    E --> I[Communication APIs]
    E --> J[E-Signature APIs]
    
    K[Tenant Portal] --> E
    L[Vendor Portal] --> E
```

### **Core Components**

- **Main Process**: Window management, native API access, security
- **Renderer Process**: React application with modern UI
- **Preload Scripts**: Secure IPC bridge between processes
- **Convex Backend**: Real-time database and serverless functions
- **Portal System**: White-label tenant and vendor interfaces

---

## 📁 Project Structure

```text
estate-pulse/
├── 📁 src/                          # React application source
│   ├── 📁 components/               # Reusable UI components
│   │   ├── 📁 properties/           # Property management components
│   │   ├── 📁 leases/              # Lease management components
│   │   ├── 📁 maintenance/         # Maintenance system components
│   │   ├── 📁 payments/            # Payment processing components
│   │   ├── 📁 communications/      # Communication hub components
│   │   ├── 📁 portals/             # Portal management components
│   │   ├── 📁 analytics/           # Analytics and reporting
│   │   ├── 📁 ui/                  # Base UI components
│   │   └── 📁 accessibility/       # Accessibility components
│   ├── 📁 lib/                     # Utility functions and services
│   │   ├── 📁 security/            # Security utilities
│   │   ├── 📁 monitoring/          # Performance monitoring
│   │   └── 📁 validation/          # Data validation
│   ├── 📁 hooks/                   # Custom React hooks
│   ├── 📁 contexts/                # React context providers
│   ├── 📁 types/                   # TypeScript definitions
│   └── 📄 main.tsx                 # Application entry point
├── 📁 electron/                     # Electron processes
│   ├── 📄 main.ts                  # Main process
│   └── 📄 preload.ts               # Preload scripts
├── 📁 convex/                       # Backend functions
│   ├── 📄 schema.ts                # Database schema
│   ├── 📄 properties.ts            # Property management
│   ├── 📄 leases.ts                # Lease management
│   ├── 📄 maintenance.ts           # Maintenance system
│   ├── 📄 payments.ts              # Payment processing
│   ├── 📄 communications.ts        # Communication hub
│   ├── 📄 portals.ts               # Portal management
│   └── 📁 lib/                     # Shared backend utilities
├── 📁 tests/                        # Test suites
│   ├── 📁 e2e/                     # End-to-end tests
│   ├── 📁 integration/             # Integration tests
│   └── 📁 performance/             # Performance tests
├── 📁 build/                        # Build configuration
├── 📁 public/                       # Static assets
└── 📁 dist/                         # Built application
```

---

## 🚀 Getting Started

### **Prerequisites**

- **Node.js** 18.0.0 or higher
- **npm** 8.0.0 or higher (or **yarn** 1.22.0+)
- **Git** for version control

### **Installation**

1. **Clone the repository**

   ```bash
   git clone https://github.com/your-org/estate-pulse.git
   cd estate-pulse
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment setup**

   ```bash
   cp .env.example .env.local
   ```

   Configure your environment variables:

   ```env
   # Convex Configuration
   VITE_CONVEX_URL=your_convex_deployment_url
   
   # Payment Processors
   VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_key
   MPESA_CONSUMER_KEY=your_mpesa_key
   MPESA_CONSUMER_SECRET=your_mpesa_secret
   
   # Communication Services
   TWILIO_ACCOUNT_SID=your_twilio_sid
   TWILIO_AUTH_TOKEN=your_twilio_token
   
   # E-Signature Services
   DOCUSIGN_INTEGRATION_KEY=your_docusign_key
   ```

4. **Initialize Convex backend**

   ```bash
   npx convex dev
   ```

### **Development**

Start the development environment:

```bash
npm run dev
```

This command will:
- Start the Vite development server
- Launch Electron in development mode
- Enable hot reload for both processes
- Open developer tools

### **Building for Production**

Build the complete application:

```bash
npm run build:electron
```

Build specific components:

```bash
npm run build:renderer    # React application only
npm run build:main        # Electron main process only
npm run build:preload     # Preload scripts only
```

### **Testing**

Run the test suite:

```bash
npm run test              # Unit tests
npm run test:e2e          # End-to-end tests
npm run test:integration  # Integration tests
npm run test:performance  # Performance tests
```

---

## 📋 Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development mode with hot reload |
| `npm run build:electron` | Build complete Electron application |
| `npm run build:renderer` | Build React renderer process |
| `npm run build:main` | Build Electron main process |
| `npm run build:preload` | Build preload scripts |
| `npm run type-check` | Run TypeScript type checking |
| `npm run lint` | Run ESLint code analysis |
| `npm run lint:fix` | Fix ESLint issues automatically |
| `npm run format` | Format code with Prettier |
| `npm run test` | Run unit tests |
| `npm run test:watch` | Run tests in watch mode |
| `npm run test:e2e` | Run end-to-end tests |
| `npm run test:coverage` | Generate test coverage report |
| `npm run convex:dev` | Start Convex development server |
| `npm run convex:deploy` | Deploy Convex functions |

---

## 🔧 Configuration

### **Environment Variables**

Create a `.env.local` file with the following variables:

```env
# Required - Convex Backend
VITE_CONVEX_URL=https://your-deployment.convex.cloud

# Optional - Payment Integration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_SHORTCODE=your_mpesa_shortcode
MPESA_PASSKEY=your_mpesa_passkey

# Optional - Communication Services
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Optional - E-Signature Integration
DOCUSIGN_INTEGRATION_KEY=your_docusign_integration_key
DOCUSIGN_USER_ID=your_docusign_user_id
DOCUSIGN_ACCOUNT_ID=your_docusign_account_id

# Optional - Monitoring
VITE_SENTRY_DSN=your_sentry_dsn
VITE_POSTHOG_KEY=your_posthog_key
```

### **Convex Schema**

The application uses a comprehensive database schema with the following main entities:

- **Users** - Authentication and role management
- **Properties** - Property information and settings
- **Units** - Individual rental units
- **Leases** - Lease agreements and terms
- **Maintenance Tickets** - Service requests and tracking
- **Invoices** - Billing and payment records
- **Communications** - Message logs and templates
- **Portals** - White-label portal configurations

---

## 🧪 Testing

### **Test Structure**

```text
tests/
├── unit/                 # Component and function tests
├── integration/          # API and service integration tests
├── e2e/                 # End-to-end user workflows
└── performance/         # Load and performance tests
```

### **Running Tests**

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:e2e
npm run test:performance

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### **Test Configuration**

- **Unit Tests**: Jest + React Testing Library
- **E2E Tests**: Playwright
- **Performance Tests**: Custom benchmarking
- **Coverage**: Istanbul/NYC

---

## 🚀 Deployment

### **Desktop Application**

Build and package the desktop application:

```bash
# Build for current platform
npm run build:electron

# Build for all platforms (requires platform-specific setup)
npm run build:all

# Build for specific platform
npm run build:win    # Windows
npm run build:mac    # macOS
npm run build:linux  # Linux
```

### **Auto-Updates**

The application includes automatic update functionality:

1. Configure update server in `electron-builder.json`
2. Set up release pipeline in GitHub Actions
3. Updates are checked on application startup
4. Users are notified of available updates

### **Backend Deployment**

Deploy Convex functions:

```bash
# Deploy to production
npx convex deploy --prod

# Deploy with specific configuration
npx convex deploy --prod --config production.json
```

---

## 🤝 Contributing

We welcome contributions from the community! Please read our [Contributing Guide](CONTRIBUTING.md) for details on our development process.

### **Development Workflow**

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### **Code Standards**

- **TypeScript** for type safety
- **ESLint** for code quality
- **Prettier** for code formatting
- **Conventional Commits** for commit messages
- **Jest** for unit testing
- **Playwright** for E2E testing

### **Pull Request Process**

1. Ensure all tests pass
2. Update documentation as needed
3. Add tests for new functionality
4. Follow the existing code style
5. Include a clear description of changes

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🆘 Support

### **Documentation**

- [User Guide](docs/user-guide.md)
- [API Documentation](docs/api.md)
- [Deployment Guide](docs/deployment.md)
- [Troubleshooting](docs/troubleshooting.md)

### **Community**

- [GitHub Issues](https://github.com/your-org/estate-pulse/issues) - Bug reports and feature requests
- [Discussions](https://github.com/your-org/estate-pulse/discussions) - Community discussions
- [Discord](https://discord.gg/estate-pulse) - Real-time community chat

### **Commercial Support**

For enterprise support, custom development, or consulting services, please contact us at [<EMAIL>](mailto:<EMAIL>).

---

## 🙏 Acknowledgments

- [Convex](https://convex.dev) - Real-time backend platform
- [Electron](https://electronjs.org) - Cross-platform desktop framework
- [React](https://reactjs.org) - UI library
- [Tailwind CSS](https://tailwindcss.com) - CSS framework
- [shadcn/ui](https://ui.shadcn.com) - Component library

---

<div align="center">

**Built with ❤️ by the EstatePulse Team**

[Website](https://estatepulse.com) • [Documentation](https://docs.estatepulse.com) • [Support](mailto:<EMAIL>)

</div>