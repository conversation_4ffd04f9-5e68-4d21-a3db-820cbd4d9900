import { renderHook, act } from '@testing-library/react';
import { useMonitoring } from '../useMonitoring';
import { withMonitoring } from '../withMonitoring';
import { MonitoringService } from '../../lib/monitoring';

// Mock the monitoring service
jest.mock('../../lib/monitoring', () => ({
  MonitoringService: {
    getInstance: jest.fn(() => ({
      identifyUser: jest.fn(),
      isFeatureEnabled: jest.fn(),
      getFeatureFlag: jest.fn(),
    })),
  },
  trackEvent: jest.fn(),
  reportError: jest.fn(),
  trackPerformance: jest.fn(),
  monitorQuery: jest.fn(),
}));

// Mock performance API
Object.defineProperty(window, 'performance', {
  value: {
    now: jest.fn(() => Date.now()),
  },
});

describe('useMonitoring', () => {
  const mockMonitoringService = {
    identifyUser: jest.fn(),
    isFeatureEnabled: jest.fn(),
    getFeatureFlag: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (MonitoringService.getInstance as jest.Mock).mockReturnValue(mockMonitoringService);
  });

  it('should initialize with default options', () => {
    const { result } = renderHook(() => useMonitoring());

    expect(result.current).toHaveProperty('track');
    expect(result.current).toHaveProperty('captureError');
    expect(result.current).toHaveProperty('measurePerformance');
    expect(result.current).toHaveProperty('recordMetric');
    expect(result.current).toHaveProperty('monitorDatabaseQuery');
    expect(result.current).toHaveProperty('isFeatureEnabled');
    expect(result.current).toHaveProperty('getFeatureFlag');
    expect(result.current).toHaveProperty('identifyUser');
    expect(result.current).toHaveProperty('trackPageView');
    expect(result.current).toHaveProperty('trackComponentMount');
    expect(result.current).toHaveProperty('trackComponentUnmount');
  });

  it('should identify user when userId is provided', () => {
    const userId = 'test-user-123';
    const userProperties = { name: 'Test User', role: 'admin' };

    renderHook(() => useMonitoring({ userId, userProperties }));

    expect(mockMonitoringService.identifyUser).toHaveBeenCalledWith(userId, userProperties);
  });

  it('should track events with additional context', () => {
    const { trackEvent } = require('../../lib/monitoring');
    const { result } = renderHook(() => useMonitoring());

    act(() => {
      result.current.track('test_event', { custom: 'property' });
    });

    expect(trackEvent).toHaveBeenCalledWith('test_event', {
      custom: 'property',
      timestamp: expect.any(Number),
      page: expect.any(String),
    });
  });

  it('should capture errors with enhanced context', () => {
    const { reportError } = require('../../lib/monitoring');
    const { result } = renderHook(() => useMonitoring());
    const testError = new Error('Test error');

    act(() => {
      result.current.captureError(testError, { component: 'TestComponent' });
    });

    expect(reportError).toHaveBeenCalledWith(testError, {
      component: 'TestComponent',
      page: expect.any(String),
      userAgent: expect.any(String),
      timestamp: expect.any(Number),
    });
  });

  it('should measure performance correctly', async () => {
    const { trackPerformance } = require('../../lib/monitoring');
    const { result } = renderHook(() => useMonitoring());
    
    const mockFn = jest.fn().mockResolvedValue('success');
    
    await act(async () => {
      await result.current.measurePerformance('test_operation', mockFn);
    });

    expect(mockFn).toHaveBeenCalled();
    expect(trackPerformance).toHaveBeenCalledWith('test_operation', expect.any(Number), {
      page: expect.any(String),
      success: true,
    });
  });

  it('should handle performance measurement errors', async () => {
    const { trackPerformance } = require('../../lib/monitoring');
    const { result } = renderHook(() => useMonitoring());
    
    const mockFn = jest.fn().mockRejectedValue(new Error('Test error'));
    
    await act(async () => {
      try {
        await result.current.measurePerformance('test_operation', mockFn);
      } catch (error) {
        // Expected to throw
      }
    });

    expect(trackPerformance).toHaveBeenCalledWith('test_operation', expect.any(Number), {
      page: expect.any(String),
      success: false,
      error: 'Test error',
    });
  });

  it('should record custom metrics', () => {
    const { trackPerformance } = require('../../lib/monitoring');
    const { result } = renderHook(() => useMonitoring());

    act(() => {
      result.current.recordMetric('custom_metric', 42, { type: 'test' });
    });

    expect(trackPerformance).toHaveBeenCalledWith('custom_metric', 42, {
      type: 'test',
      page: expect.any(String),
      timestamp: expect.any(Number),
    });
  });

  it('should monitor database queries', async () => {
    const { monitorQuery } = require('../../lib/monitoring');
    const { result } = renderHook(() => useMonitoring());
    
    const mockQueryFn = jest.fn().mockResolvedValue({ data: 'test' });
    
    await act(async () => {
      await result.current.monitorDatabaseQuery('test_query', mockQueryFn, { table: 'users' });
    });

    expect(monitorQuery).toHaveBeenCalledWith('test_query', mockQueryFn, {
      table: 'users',
      page: expect.any(String),
    });
  });

  it('should check feature flags', () => {
    mockMonitoringService.isFeatureEnabled.mockReturnValue(true);
    mockMonitoringService.getFeatureFlag.mockReturnValue('enabled');
    
    const { result } = renderHook(() => useMonitoring());

    act(() => {
      const isEnabled = result.current.isFeatureEnabled('test_flag');
      const flagValue = result.current.getFeatureFlag('test_flag');
      
      expect(isEnabled).toBe(true);
      expect(flagValue).toBe('enabled');
    });

    expect(mockMonitoringService.isFeatureEnabled).toHaveBeenCalledWith('test_flag');
    expect(mockMonitoringService.getFeatureFlag).toHaveBeenCalledWith('test_flag');
  });

  it('should track component lifecycle', () => {
    const { trackEvent } = require('../../lib/monitoring');
    const { result } = renderHook(() => useMonitoring());

    act(() => {
      result.current.trackComponentMount('TestComponent', { prop1: 'value1' });
    });

    expect(trackEvent).toHaveBeenCalledWith('component_mount', {
      component: 'TestComponent',
      props: ['prop1'],
      page: expect.any(String),
      timestamp: expect.any(Number),
    });

    act(() => {
      result.current.trackComponentUnmount('TestComponent', 1000);
    });

    expect(trackEvent).toHaveBeenCalledWith('component_unmount', {
      component: 'TestComponent',
      duration: 1000,
      page: expect.any(String),
      timestamp: expect.any(Number),
    });
  });

  it('should track page views', () => {
    const { trackEvent } = require('../../lib/monitoring');
    const { result } = renderHook(() => useMonitoring());

    act(() => {
      result.current.trackPageView('/test-page', { section: 'dashboard' });
    });

    expect(trackEvent).toHaveBeenCalledWith('page_view', {
      section: 'dashboard',
      page: '/test-page',
      referrer: expect.any(String),
      timestamp: expect.any(Number),
    });
  });
});

describe('withMonitoring HOC', () => {
  it('should wrap component with monitoring', () => {
    const TestComponent = ({ title }: { title: string }) => <div>{title}</div>;
    const MonitoredComponent = withMonitoring(TestComponent, 'TestComponent');

    expect(MonitoredComponent.displayName).toBe('withMonitoring(TestComponent)');
  });
});