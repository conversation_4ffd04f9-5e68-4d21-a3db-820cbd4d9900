/**
 * Accessible button component with proper ARIA attributes and keyboard navigation
 */
import React, { forwardRef } from 'react';
import { Button, ButtonProps } from '../ui/button';
import { useAccessibility } from '../../contexts/AccessibilityContext';
import { KEYBOARD_KEYS } from '../../lib/accessibility';

interface AccessibleButtonProps extends ButtonProps {
  // ARIA attributes
  'aria-label'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-haspopup'?: boolean | 'menu' | 'listbox' | 'tree' | 'grid' | 'dialog';
  'aria-controls'?: string;
  'aria-pressed'?: boolean;
  
  // Loading state
  loading?: boolean;
  loadingText?: string;
  
  // Confirmation
  requiresConfirmation?: boolean;
  confirmationText?: string;
  
  // Keyboard shortcuts
  shortcut?: string;
  
  // Screen reader announcements
  successMessage?: string;
  errorMessage?: string;
}

export const AccessibleButton = forwardRef<HTMLButtonElement, AccessibleButtonProps>(
  ({
    children,
    loading = false,
    loadingText = 'Loading...',
    requiresConfirmation = false,
    confirmationText = 'Are you sure?',
    shortcut,
    successMessage,
    errorMessage,
    onClick,
    onKeyDown,
    disabled,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy,
    'aria-expanded': ariaExpanded,
    'aria-haspopup': ariaHasPopup,
    'aria-controls': ariaControls,
    'aria-pressed': ariaPressed,
    ...props
  }, ref) => {
    const { announceToScreenReader } = useAccessibility();
    const [isConfirming, setIsConfirming] = React.useState(false);

    const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
      if (loading || disabled) return;

      if (requiresConfirmation && !isConfirming) {
        setIsConfirming(true);
        announceToScreenReader(confirmationText, 'assertive');
        
        // Auto-cancel confirmation after 5 seconds
        setTimeout(() => setIsConfirming(false), 5000);
        return;
      }

      try {
        await onClick?.(event);
        
        if (successMessage) {
          announceToScreenReader(successMessage);
        }
        
        if (isConfirming) {
          setIsConfirming(false);
        }
      } catch (error) {
        if (errorMessage) {
          announceToScreenReader(errorMessage, 'assertive');
        }
      }
    };

    const handleKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
      // Handle escape to cancel confirmation
      if (event.key === KEYBOARD_KEYS.ESCAPE && isConfirming) {
        setIsConfirming(false);
        announceToScreenReader('Action cancelled');
        return;
      }

      // Handle space and enter for activation
      if (event.key === KEYBOARD_KEYS.SPACE || event.key === KEYBOARD_KEYS.ENTER) {
        event.preventDefault();
        handleClick(event as any);
        return;
      }

      onKeyDown?.(event);
    };

    // Generate accessible label
    const getAccessibleLabel = () => {
      if (ariaLabel) return ariaLabel;
      
      let label = '';
      
      if (loading) {
        label = loadingText;
      } else if (isConfirming) {
        label = `${confirmationText} Press Enter to confirm or Escape to cancel`;
      } else if (typeof children === 'string') {
        label = children;
      }
      
      if (shortcut) {
        label += ` (${shortcut})`;
      }
      
      return label;
    };

    // Generate description
    const getDescription = () => {
      const descriptions = [];
      
      if (ariaDescribedBy) {
        descriptions.push(ariaDescribedBy);
      }
      
      if (requiresConfirmation && !isConfirming) {
        descriptions.push('Requires confirmation');
      }
      
      if (shortcut) {
        descriptions.push(`Keyboard shortcut: ${shortcut}`);
      }
      
      return descriptions.length > 0 ? descriptions.join(', ') : undefined;
    };

    return (
      <Button
        ref={ref}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        disabled={disabled || loading}
        aria-label={getAccessibleLabel()}
        aria-describedby={getDescription()}
        aria-expanded={ariaExpanded}
        aria-haspopup={ariaHasPopup}
        aria-controls={ariaControls}
        aria-pressed={ariaPressed}
        aria-busy={loading}
        data-confirming={isConfirming}
        className={`
          ${props.className || ''}
          ${isConfirming ? 'ring-2 ring-warning ring-offset-2' : ''}
          focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2
          focus-visible:outline-none
        `}
        {...props}
      >
        {loading ? (
          <span className="flex items-center gap-2">
            <span className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
            {loadingText}
          </span>
        ) : isConfirming ? (
          <span className="flex items-center gap-2">
            <span className="text-warning">⚠</span>
            {confirmationText}
          </span>
        ) : (
          children
        )}
      </Button>
    );
  }
);

AccessibleButton.displayName = 'AccessibleButton';