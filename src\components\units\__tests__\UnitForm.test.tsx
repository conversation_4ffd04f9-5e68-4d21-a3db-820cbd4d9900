import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { UnitForm } from '../UnitForm';

// Mock Convex hooks
vi.mock('convex/react', () => ({
  useMutation: vi.fn(() => vi.fn()),
  useQuery: vi.fn(() => null),
}));

describe('UnitForm', () => {
  const mockPropertyId = 'property-123' as any;

  it('renders unit form with required fields', () => {
    render(<UnitForm propertyId={mockPropertyId} />);
    
    expect(screen.getByLabelText(/unit number/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/unit type/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/size/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/monthly rent/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/security deposit/i)).toBeInTheDocument();
  });

  it('shows bedroom and bathroom fields for apartments', () => {
    render(<UnitForm propertyId={mockPropertyId} />);
    
    // Should show bedroom/bathroom fields by default (apartment type)
    expect(screen.getByLabelText(/bedrooms/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/bathrooms/i)).toBeInTheDocument();
  });

  it('hides bedroom and bathroom fields for non-apartment types', () => {
    render(<UnitForm propertyId={mockPropertyId} />);
    
    // Change to office type
    const typeSelect = screen.getByLabelText(/unit type/i);
    fireEvent.change(typeSelect, { target: { value: 'office' } });
    
    // Should not show bedroom/bathroom fields
    expect(screen.queryByLabelText(/bedrooms/i)).not.toBeInTheDocument();
    expect(screen.queryByLabelText(/bathrooms/i)).not.toBeInTheDocument();
  });

  it('shows validation errors for empty required fields', async () => {
    render(<UnitForm propertyId={mockPropertyId} />);
    
    const submitButton = screen.getByRole('button', { name: /create unit/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/unit number is required/i)).toBeInTheDocument();
      expect(screen.getByText(/size must be greater than 0/i)).toBeInTheDocument();
      expect(screen.getByText(/rent must be greater than 0/i)).toBeInTheDocument();
    });
  });

  it('allows adding and removing amenities', () => {
    render(<UnitForm propertyId={mockPropertyId} />);
    
    const amenityInput = screen.getByPlaceholderText(/add amenity/i);
    const addButton = screen.getByRole('button', { name: /add/i });
    
    // Add an amenity
    fireEvent.change(amenityInput, { target: { value: 'Air Conditioning' } });
    fireEvent.click(addButton);
    
    expect(screen.getByText('Air Conditioning')).toBeInTheDocument();
    
    // Remove the amenity
    const removeButton = screen.getByText('×');
    fireEvent.click(removeButton);
    
    expect(screen.queryByText('Air Conditioning')).not.toBeInTheDocument();
  });
});