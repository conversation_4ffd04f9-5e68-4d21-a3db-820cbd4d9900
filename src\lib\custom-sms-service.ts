// Custom SMS Service to replace Twilio SMS functionality
export interface CustomSMSConfig {
  apiUrl: string;
  apiKey: string;
  senderId?: string;
}

export interface SMSMessage {
  phoneNumber: string;
  message: string;
  reference?: string;
}

export interface SMSResult {
  success: boolean;
  messageId?: string;
  status?: string;
  error?: string;
  cost?: number;
  balance?: number;
}

export interface SMSTemplate {
  content: string;
  variables: string[];
}

export interface PersonalizationData {
  [key: string]: string | number | boolean;
}

export interface SMSDeliveryStatus {
  messageId: string;
  status: 'pending' | 'sent' | 'delivered' | 'failed' | 'expired';
  deliveredAt?: Date;
  failureReason?: string;
  cost?: number;
}

export interface BulkSMSRequest {
  messages: SMSMessage[];
  template?: SMSTemplate;
  personalizedData?: Record<string, PersonalizationData>;
}

export interface BulkSMSResult {
  success: boolean;
  totalMessages: number;
  successfulMessages: number;
  failedMessages: number;
  results: SMSResult[];
  totalCost?: number;
  error?: string;
}

export class CustomSMSService {
  private config: CustomSMSConfig;

  constructor(config: CustomSMSConfig) {
    this.config = config;
  }

  /**
   * Send a single SMS message
   */
  async sendSMS(message: SMSMessage): Promise<SMSResult> {
    try {
      // Format phone number to international format
      const formattedNumber = this.formatPhoneNumber(message.phoneNumber);
      
      if (!this.isValidPhoneNumber(formattedNumber)) {
        return {
          success: false,
          error: "Invalid phone number format",
        };
      }

      // TODO: Replace with actual SMS API integration
      // This is a placeholder implementation
      const response = await fetch(`${this.config.apiUrl}/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: formattedNumber,
          message: message.message,
          from: this.config.senderId || 'EstatePulse',
          reference: message.reference,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        };
      }

      const data = await response.json();
      
      return {
        success: true,
        messageId: data.messageId || data.id,
        status: data.status || 'sent',
        cost: data.cost,
        balance: data.balance,
      };
    } catch (error) {
      console.error('SMS sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Send templated SMS message
   */
  async sendTemplatedSMS(
    phoneNumber: string,
    template: SMSTemplate,
    data: PersonalizationData
  ): Promise<SMSResult> {
    try {
      // Process template with personalization data
      let processedMessage = template.content;
      
      Object.entries(data).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        processedMessage = processedMessage.replace(regex, String(value));
      });

      return await this.sendSMS({
        phoneNumber,
        message: processedMessage,
      });
    } catch (error) {
      console.error('Templated SMS sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Template processing failed',
      };
    }
  }

  /**
   * Send bulk SMS messages
   */
  async sendBulkSMS(request: BulkSMSRequest): Promise<BulkSMSResult> {
    try {
      const results: SMSResult[] = [];
      let successfulMessages = 0;
      let failedMessages = 0;
      let totalCost = 0;

      for (const message of request.messages) {
        let processedMessage = message.message;

        // Apply template if provided
        if (request.template && request.personalizedData) {
          const personalData = request.personalizedData[message.phoneNumber];
          if (personalData) {
            processedMessage = request.template.content;
            Object.entries(personalData).forEach(([key, value]) => {
              const regex = new RegExp(`{{${key}}}`, 'g');
              processedMessage = processedMessage.replace(regex, String(value));
            });
          }
        }

        const result = await this.sendSMS({
          ...message,
          message: processedMessage,
        });

        results.push(result);

        if (result.success) {
          successfulMessages++;
          if (result.cost) totalCost += result.cost;
        } else {
          failedMessages++;
        }

        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      return {
        success: successfulMessages > 0,
        totalMessages: request.messages.length,
        successfulMessages,
        failedMessages,
        results,
        totalCost: totalCost > 0 ? totalCost : undefined,
      };
    } catch (error) {
      console.error('Bulk SMS sending failed:', error);
      return {
        success: false,
        totalMessages: request.messages.length,
        successfulMessages: 0,
        failedMessages: request.messages.length,
        results: [],
        error: error instanceof Error ? error.message : 'Bulk SMS failed',
      };
    }
  }

  /**
   * Check SMS delivery status
   */
  async getDeliveryStatus(messageId: string): Promise<SMSDeliveryStatus | null> {
    try {
      // TODO: Replace with actual SMS API integration
      const response = await fetch(`${this.config.apiUrl}/status/${messageId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      
      return {
        messageId,
        status: data.status || 'pending',
        deliveredAt: data.deliveredAt ? new Date(data.deliveredAt) : undefined,
        failureReason: data.failureReason,
        cost: data.cost,
      };
    } catch (error) {
      console.error('Failed to get delivery status:', error);
      return null;
    }
  }

  /**
   * Get account balance
   */
  async getBalance(): Promise<{ balance: number; currency: string } | null> {
    try {
      // TODO: Replace with actual SMS API integration
      const response = await fetch(`${this.config.apiUrl}/balance`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      
      return {
        balance: data.balance || 0,
        currency: data.currency || 'KES',
      };
    } catch (error) {
      console.error('Failed to get balance:', error);
      return null;
    }
  }

  /**
   * Format phone number to international format
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, '');

    // Convert to international format for Kenya
    if (cleaned.startsWith('0')) {
      cleaned = '254' + cleaned.substring(1);
    } else if (cleaned.startsWith('7') || cleaned.startsWith('1')) {
      cleaned = '254' + cleaned;
    } else if (!cleaned.startsWith('254')) {
      cleaned = '254' + cleaned;
    }

    return '+' + cleaned;
  }

  /**
   * Validate Kenyan phone number
   */
  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Remove + and check for valid Kenyan format
    const cleaned = phoneNumber.replace(/^\+/, '');
    const kenyanPattern = /^254[17]\d{8}$/;
    return kenyanPattern.test(cleaned);
  }
}

// Factory function to create CustomSMSService instance
export function createCustomSMSService(): CustomSMSService {
  const config: CustomSMSConfig = {
    apiUrl: process.env.CUSTOM_SMS_API_URL || '',
    apiKey: process.env.CUSTOM_SMS_API_KEY || '',
    senderId: process.env.CUSTOM_SMS_SENDER_ID || 'EstatePulse',
  };

  if (!config.apiUrl || !config.apiKey) {
    throw new Error('Custom SMS API configuration missing. Please set CUSTOM_SMS_API_URL and CUSTOM_SMS_API_KEY environment variables.');
  }

  return new CustomSMSService(config);
}
