import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

interface UnitListProps {
  propertyId: Id<"properties">;
  onUnitSelect?: (unitId: Id<"units">) => void;
  onCreateNew?: () => void;
  onEditUnit?: (unitId: Id<"units">) => void;
}

export const UnitList: React.FC<UnitListProps> = ({
  propertyId,
  onUnitSelect,
  onCreateNew,
  onEditUnit,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'vacant' | 'occupied' | 'maintenance'>('all');
  const [typeFilter, setTypeFilter] = useState<'all' | 'apartment' | 'office' | 'retail' | 'parking'>('all');
  const [sortBy, setSortBy] = useState<'unitNumber' | 'rent' | 'size'>('unitNumber');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Get all units for the property
  const allUnits = useQuery(api.units.getUnitsByProperty, { propertyId });
  const updateUnitStatus = useMutation(api.units.updateUnitStatus);

  // Filter and sort units
  const filteredAndSortedUnits = React.useMemo(() => {
    if (!allUnits) return [];

    let filtered = allUnits.filter((unit: any) => {
      // Search filter
      const matchesSearch = searchTerm === '' || 
        unit.unitNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (unit.description && unit.description.toLowerCase().includes(searchTerm.toLowerCase()));

      // Status filter
      const matchesStatus = statusFilter === 'all' || unit.status === statusFilter;

      // Type filter
      const matchesType = typeFilter === 'all' || unit.type === typeFilter;

      return matchesSearch && matchesStatus && matchesType;
    });

    // Sort units
    filtered.sort((a: any, b: any) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'unitNumber':
          aValue = a.unitNumber;
          bValue = b.unitNumber;
          break;
        case 'rent':
          aValue = a.rent;
          bValue = b.rent;
          break;
        case 'size':
          aValue = a.size;
          bValue = b.size;
          break;
        default:
          return 0;
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [allUnits, searchTerm, statusFilter, typeFilter, sortBy, sortOrder]);

  const handleStatusChange = async (unitId: Id<"units">, newStatus: 'vacant' | 'occupied' | 'maintenance') => {
    try {
      await updateUnitStatus({ id: unitId, status: newStatus });
    } catch (error) {
      console.error('Error updating unit status:', error);
      alert('Failed to update unit status. Please try again.');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'vacant':
        return 'bg-green-100 text-green-800';
      case 'occupied':
        return 'bg-blue-100 text-blue-800';
      case 'maintenance':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'apartment':
        return 'bg-purple-100 text-purple-800';
      case 'office':
        return 'bg-indigo-100 text-indigo-800';
      case 'retail':
        return 'bg-pink-100 text-pink-800';
      case 'parking':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (!allUnits) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Units</h2>
          <p className="text-gray-600">
            Manage property units ({filteredAndSortedUnits.length} units)
          </p>
        </div>
        {onCreateNew && (
          <Button onClick={onCreateNew}>
            Create New Unit
          </Button>
        )}
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <Input
                placeholder="Search units..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div>
              <Select
                value={statusFilter}
                onValueChange={(value) => setStatusFilter(value as any)}
              >
                <option value="all">All Status</option>
                <option value="vacant">Vacant</option>
                <option value="occupied">Occupied</option>
                <option value="maintenance">Maintenance</option>
              </Select>
            </div>

            <div>
              <Select
                value={typeFilter}
                onValueChange={(value) => setTypeFilter(value as any)}
              >
                <option value="all">All Types</option>
                <option value="apartment">Apartment</option>
                <option value="office">Office</option>
                <option value="retail">Retail</option>
                <option value="parking">Parking</option>
              </Select>
            </div>

            <div>
              <Select
                value={sortBy}
                onValueChange={(value) => setSortBy(value as any)}
              >
                <option value="unitNumber">Sort by Unit #</option>
                <option value="rent">Sort by Rent</option>
                <option value="size">Sort by Size</option>
              </Select>
            </div>

            <div>
              <Select
                value={sortOrder}
                onValueChange={(value) => setSortOrder(value as any)}
              >
                <option value="asc">Ascending</option>
                <option value="desc">Descending</option>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Units Grid */}
      {filteredAndSortedUnits.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-gray-500">
              <h3 className="text-lg font-semibold mb-2">No units found</h3>
              <p className="mb-4">
                {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                  ? 'Try adjusting your search filters'
                  : 'Get started by creating your first unit'
                }
              </p>
              {onCreateNew && (
                <Button onClick={onCreateNew}>
                  Create New Unit
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAndSortedUnits.map((unit: any) => (
            <Card
              key={unit._id}
              className="cursor-pointer transition-shadow hover:shadow-lg"
              onClick={() => onUnitSelect?.(unit._id)}
            >
              <CardHeader>
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{unit.unitNumber}</CardTitle>
                  <div className="flex flex-col items-end space-y-2">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize ${getTypeColor(
                        unit.type
                      )}`}
                    >
                      {unit.type}
                    </span>
                    <Select
                      value={unit.status}
                      onValueChange={(value) => {
                        handleStatusChange(unit._id, value as any);
                      }}
                    >
                      <SelectTrigger className={`text-xs ${getStatusColor(unit.status)}`}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="vacant">Vacant</SelectItem>
                        <SelectItem value="occupied">Occupied</SelectItem>
                        <SelectItem value="maintenance">Maintenance</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Size</p>
                      <p className="font-medium">{unit.size} sq ft</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Rent</p>
                      <p className="font-medium text-green-600">{formatCurrency(unit.rent)}</p>
                    </div>
                  </div>

                  {unit.type === 'apartment' && (
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Bedrooms</p>
                        <p className="font-medium">{unit.bedrooms || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Bathrooms</p>
                        <p className="font-medium">{unit.bathrooms || 'N/A'}</p>
                      </div>
                    </div>
                  )}

                  <div>
                    <p className="text-gray-600 text-sm">Deposit</p>
                    <p className="font-medium text-sm">{formatCurrency(unit.deposit)}</p>
                  </div>

                  {unit.amenities && unit.amenities.length > 0 && (
                    <div>
                      <p className="text-gray-600 text-sm">Amenities</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {unit.amenities.slice(0, 3).map((amenity: any, index: number) => (
                          <span
                            key={index}
                            className="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                          >
                            {amenity}
                          </span>
                        ))}
                        {unit.amenities.length > 3 && (
                          <span className="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                            +{unit.amenities.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between items-center pt-2 border-t">
                    <p className="text-xs text-gray-500">
                      Created {new Date(unit.createdAt).toLocaleDateString()}
                    </p>
                    {onEditUnit && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          onEditUnit(unit._id);
                        }}
                      >
                        Edit
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};