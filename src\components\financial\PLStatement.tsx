import React, { useState, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { 
  Download, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  FileText,
  Calendar
} from 'lucide-react';
import { format, subMonths, subYears, startOfMonth, endOfMonth } from 'date-fns';
import { Id } from '../../../convex/_generated/dataModel';

interface PLStatementProps {
  propertyId?: Id<"properties">;
}

export const PLStatement: React.FC<PLStatementProps> = ({ propertyId }) => {
  const [period, setPeriod] = useState<'month' | 'quarter' | 'year'>('month');
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  // Calculate date range based on period
  const { startDate, endDate, periodLabel } = useMemo(() => {
    const now = new Date();
    let start: Date;
    let end: Date;
    let label: string;

    switch (period) {
      case 'month':
        start = startOfMonth(new Date(selectedYear, selectedMonth));
        end = endOfMonth(new Date(selectedYear, selectedMonth));
        label = format(start, 'MMMM yyyy');
        break;
      case 'quarter':
        const quarterStart = Math.floor(selectedMonth / 3) * 3;
        start = startOfMonth(new Date(selectedYear, quarterStart));
        end = endOfMonth(new Date(selectedYear, quarterStart + 2));
        label = `Q${Math.floor(selectedMonth / 3) + 1} ${selectedYear}`;
        break;
      case 'year':
        start = new Date(selectedYear, 0, 1);
        end = new Date(selectedYear, 11, 31);
        label = selectedYear.toString();
        break;
      default:
        start = startOfMonth(now);
        end = endOfMonth(now);
        label = format(now, 'MMMM yyyy');
    }

    return {
      startDate: start.getTime(),
      endDate: end.getTime(),
      periodLabel: label,
    };
  }, [period, selectedMonth, selectedYear]);

  // Fetch P&L data
  const plData = useQuery(api.financialAnalytics.generatePLStatement, {
    propertyId,
    startDate,
    endDate,
  });

  // Fetch comparison data (previous period)
  const previousPeriodStart = useMemo(() => {
    switch (period) {
      case 'month':
        return subMonths(new Date(startDate), 1).getTime();
      case 'quarter':
        return subMonths(new Date(startDate), 3).getTime();
      case 'year':
        return subYears(new Date(startDate), 1).getTime();
      default:
        return subMonths(new Date(startDate), 1).getTime();
    }
  }, [startDate, period]);

  const previousPeriodEnd = useMemo(() => {
    return startDate - 1;
  }, [startDate]);

  const previousPlData = useQuery(api.financialAnalytics.generatePLStatement, {
    propertyId,
    startDate: previousPeriodStart,
    endDate: previousPeriodEnd,
  });

  if (!plData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading P&L statement...</p>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const calculateChange = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) {
      return <TrendingUp className="h-4 w-4 text-green-600" />;
    } else if (change < 0) {
      return <TrendingDown className="h-4 w-4 text-red-600" />;
    }
    return null;
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  // Generate years for dropdown (current year and 2 years back)
  const availableYears = Array.from({ length: 3 }, (_, i) => new Date().getFullYear() - i);
  const months = Array.from({ length: 12 }, (_, i) => ({
    value: i,
    label: format(new Date(2023, i), 'MMMM'),
  }));

  const handleExport = () => {
    // Create CSV content
    const csvContent = [
      ['Profit & Loss Statement'],
      [`Period: ${periodLabel}`],
      [''],
      ['REVENUE'],
      ['Rent Revenue', formatCurrency(plData.revenue.rent)],
      ['Deposit Revenue', formatCurrency(plData.revenue.deposits)],
      ['Maintenance Revenue', formatCurrency(plData.revenue.maintenance)],
      ['Other Revenue', formatCurrency(plData.revenue.other)],
      ['Total Revenue', formatCurrency(plData.revenue.total)],
      [''],
      ['EXPENSES'],
      ...Object.entries(plData.expenses.byCategory).map(([category, amount]) => [
        category.charAt(0).toUpperCase() + category.slice(1),
        formatCurrency(amount)
      ]),
      ['Total Expenses', formatCurrency(plData.expenses.total)],
      [''],
      ['PROFIT & LOSS'],
      ['Gross Profit', formatCurrency(plData.profitLoss.grossProfit)],
      ['Gross Margin', formatPercentage(plData.profitLoss.grossMargin)],
      ['Net Income', formatCurrency(plData.profitLoss.netIncome)],
    ].map(row => row.join(',')).join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `pl-statement-${periodLabel.replace(/\s+/g, '-').toLowerCase()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <FileText className="h-6 w-6" />
            Profit & Loss Statement
          </h1>
          <p className="text-gray-600">
            Financial performance for {periodLabel}
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={period} onValueChange={(value: 'month' | 'quarter' | 'year') => setPeriod(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">Monthly</SelectItem>
              <SelectItem value="quarter">Quarterly</SelectItem>
              <SelectItem value="year">Yearly</SelectItem>
            </SelectContent>
          </Select>

          {period === 'month' && (
            <Select value={selectedMonth.toString()} onValueChange={(value) => setSelectedMonth(parseInt(value))}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {months.map((month) => (
                  <SelectItem key={month.value} value={month.value.toString()}>
                    {month.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}

          <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availableYears.map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(plData.revenue.total)}</div>
            {previousPlData && (
              <div className="flex items-center text-xs text-muted-foreground">
                {getChangeIcon(calculateChange(plData.revenue.total, previousPlData.revenue.total))}
                <span className={`ml-1 ${getChangeColor(calculateChange(plData.revenue.total, previousPlData.revenue.total))}`}>
                  {formatPercentage(Math.abs(calculateChange(plData.revenue.total, previousPlData.revenue.total)))}
                </span>
                <span className="ml-1">vs previous period</span>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(plData.expenses.total)}</div>
            {previousPlData && (
              <div className="flex items-center text-xs text-muted-foreground">
                {getChangeIcon(calculateChange(plData.expenses.total, previousPlData.expenses.total))}
                <span className={`ml-1 ${getChangeColor(calculateChange(plData.expenses.total, previousPlData.expenses.total))}`}>
                  {formatPercentage(Math.abs(calculateChange(plData.expenses.total, previousPlData.expenses.total)))}
                </span>
                <span className="ml-1">vs previous period</span>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Income</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${plData.profitLoss.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatCurrency(plData.profitLoss.netIncome)}
            </div>
            {previousPlData && (
              <div className="flex items-center text-xs text-muted-foreground">
                {getChangeIcon(calculateChange(plData.profitLoss.netIncome, previousPlData.profitLoss.netIncome))}
                <span className={`ml-1 ${getChangeColor(calculateChange(plData.profitLoss.netIncome, previousPlData.profitLoss.netIncome))}`}>
                  {formatPercentage(Math.abs(calculateChange(plData.profitLoss.netIncome, previousPlData.profitLoss.netIncome)))}
                </span>
                <span className="ml-1">vs previous period</span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Detailed P&L Statement */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Statement</CardTitle>
          <CardDescription>
            Period: {format(new Date(startDate), 'MMM dd, yyyy')} - {format(new Date(endDate), 'MMM dd, yyyy')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            {/* Revenue Section */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-green-700 border-b border-green-200 pb-2">
                REVENUE
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Rent Revenue</span>
                  <div className="text-right">
                    <span className="font-medium">{formatCurrency(plData.revenue.rent)}</span>
                    {previousPlData && (
                      <div className="text-xs text-gray-500">
                        {formatPercentage(Math.abs(calculateChange(plData.revenue.rent, previousPlData.revenue.rent)))}
                        {calculateChange(plData.revenue.rent, previousPlData.revenue.rent) >= 0 ? ' ↑' : ' ↓'}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Deposit Revenue</span>
                  <div className="text-right">
                    <span className="font-medium">{formatCurrency(plData.revenue.deposits)}</span>
                    {previousPlData && (
                      <div className="text-xs text-gray-500">
                        {formatPercentage(Math.abs(calculateChange(plData.revenue.deposits, previousPlData.revenue.deposits)))}
                        {calculateChange(plData.revenue.deposits, previousPlData.revenue.deposits) >= 0 ? ' ↑' : ' ↓'}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Maintenance Revenue</span>
                  <div className="text-right">
                    <span className="font-medium">{formatCurrency(plData.revenue.maintenance)}</span>
                    {previousPlData && (
                      <div className="text-xs text-gray-500">
                        {formatPercentage(Math.abs(calculateChange(plData.revenue.maintenance, previousPlData.revenue.maintenance)))}
                        {calculateChange(plData.revenue.maintenance, previousPlData.revenue.maintenance) >= 0 ? ' ↑' : ' ↓'}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Other Revenue</span>
                  <div className="text-right">
                    <span className="font-medium">{formatCurrency(plData.revenue.other)}</span>
                    {previousPlData && (
                      <div className="text-xs text-gray-500">
                        {formatPercentage(Math.abs(calculateChange(plData.revenue.other, previousPlData.revenue.other)))}
                        {calculateChange(plData.revenue.other, previousPlData.revenue.other) >= 0 ? ' ↑' : ' ↓'}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="border-t-2 border-green-200 pt-3 flex justify-between font-semibold text-lg">
                  <span>Total Revenue</span>
                  <span className="text-green-600">{formatCurrency(plData.revenue.total)}</span>
                </div>
              </div>
            </div>

            {/* Expenses Section */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-red-700 border-b border-red-200 pb-2">
                EXPENSES
              </h3>
              <div className="space-y-3">
                {Object.entries(plData.expenses.byCategory).map(([category, amount]) => (
                  amount > 0 && (
                    <div key={category} className="flex justify-between items-center">
                      <span className="text-gray-700 capitalize">{category} Expenses</span>
                      <div className="text-right">
                        <span className="font-medium">{formatCurrency(amount)}</span>
                        {previousPlData && previousPlData.expenses.byCategory[category as keyof typeof previousPlData.expenses.byCategory] && (
                          <div className="text-xs text-gray-500">
                            {formatPercentage(Math.abs(calculateChange(amount, previousPlData.expenses.byCategory[category as keyof typeof previousPlData.expenses.byCategory])))}
                            {calculateChange(amount, previousPlData.expenses.byCategory[category as keyof typeof previousPlData.expenses.byCategory]) >= 0 ? ' ↑' : ' ↓'}
                          </div>
                        )}
                      </div>
                    </div>
                  )
                ))}
                
                <div className="border-t-2 border-red-200 pt-3 flex justify-between font-semibold text-lg">
                  <span>Total Expenses</span>
                  <span className="text-red-600">{formatCurrency(plData.expenses.total)}</span>
                </div>
              </div>
            </div>

            {/* Profit & Loss Section */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4 text-blue-700 border-b border-blue-200 pb-2">
                PROFIT & LOSS
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Gross Profit</span>
                  <span className={`font-semibold text-lg ${plData.profitLoss.grossProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatCurrency(plData.profitLoss.grossProfit)}
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Gross Margin</span>
                  <span className="font-medium">
                    {formatPercentage(plData.profitLoss.grossMargin)}
                  </span>
                </div>
                
                <div className="border-t-2 border-blue-200 pt-4 flex justify-between items-center">
                  <span className="text-xl font-bold">Net Income</span>
                  <span className={`text-2xl font-bold ${plData.profitLoss.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatCurrency(plData.profitLoss.netIncome)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};