import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { LeaseForm } from '../LeaseForm';

// Mock Convex hooks
vi.mock('convex/react', () => ({
  useMutation: vi.fn(() => vi.fn()),
  useQuery: vi.fn(() => []),
}));

// Mock API
vi.mock('../../../../convex/_generated/api', () => ({
  api: {
    leases: {
      createLease: 'createLease',
    },
    properties: {
      getProperties: 'getProperties',
    },
    units: {
      getUnits: 'getUnits',
    },
    users: {
      getUsersByRole: 'getUsersByRole',
    },
  },
}));

describe('LeaseForm', () => {
  const mockOnSuccess = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders lease form with required fields', () => {
    render(
      <LeaseForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText('Create New Lease')).toBeInTheDocument();
    expect(screen.getByLabelText(/property/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/unit/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/tenant/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/start date/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/end date/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/monthly rent/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/security deposit/i)).toBeInTheDocument();
  });

  it('shows validation errors for empty required fields', async () => {
    render(
      <LeaseForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    );

    const submitButton = screen.getByRole('button', { name: /create lease/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Property is required')).toBeInTheDocument();
      expect(screen.getByText('Unit is required')).toBeInTheDocument();
      expect(screen.getByText('Tenant is required')).toBeInTheDocument();
      expect(screen.getByText('Start date is required')).toBeInTheDocument();
      expect(screen.getByText('End date is required')).toBeInTheDocument();
      expect(screen.getByText('Monthly rent is required')).toBeInTheDocument();
      expect(screen.getByText('Deposit is required')).toBeInTheDocument();
    });
  });

  it('validates date range', async () => {
    render(
      <LeaseForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    );

    const startDateInput = screen.getByLabelText(/start date/i);
    const endDateInput = screen.getByLabelText(/end date/i);

    fireEvent.change(startDateInput, { target: { value: '2024-12-31' } });
    fireEvent.change(endDateInput, { target: { value: '2024-01-01' } });

    const submitButton = screen.getByRole('button', { name: /create lease/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('End date must be after start date')).toBeInTheDocument();
    });
  });

  it('validates numeric fields', async () => {
    render(
      <LeaseForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    );

    const rentInput = screen.getByLabelText(/monthly rent/i);
    const depositInput = screen.getByLabelText(/security deposit/i);

    fireEvent.change(rentInput, { target: { value: '-100' } });
    fireEvent.change(depositInput, { target: { value: '-50' } });

    const submitButton = screen.getByRole('button', { name: /create lease/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Monthly rent must be a positive number')).toBeInTheDocument();
      expect(screen.getByText('Deposit must be a non-negative number')).toBeInTheDocument();
    });
  });

  it('calls onCancel when cancel button is clicked', () => {
    render(
      <LeaseForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    );

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalledTimes(1);
  });
});