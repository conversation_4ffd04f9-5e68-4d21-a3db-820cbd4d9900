import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { UnitManagement } from '../units/UnitManagement';
import { PropertyAnalyticsDashboard } from '../analytics/PropertyAnalyticsDashboard';

interface PropertyDetailsProps {
  propertyId: Id<"properties">;
  onEdit?: () => void;
  onBack?: () => void;
}

export const PropertyDetails: React.FC<PropertyDetailsProps> = ({
  propertyId,
  onEdit,
  onBack,
}) => {
  const property = useQuery(api.properties.getPropertyById, { id: propertyId });
  const analytics = useQuery(api.properties.getPropertyAnalytics, { propertyId });
  const deleteProperty = useMutation(api.properties.deleteProperty);
  
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'units' | 'analytics'>('overview');

  const handleDelete = async () => {
    if (!showDeleteConfirm) {
      setShowDeleteConfirm(true);
      return;
    }

    setIsDeleting(true);
    try {
      await deleteProperty({ id: propertyId });
      onBack?.();
    } catch (error) {
      console.error('Error deleting property:', error);
      alert('Failed to delete property. It may have active leases.');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  if (!property) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  const formatAddress = (address: any) => {
    return `${address.street}, ${address.city}, ${address.state}, ${address.country} ${address.postalCode}`;
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'residential':
        return 'bg-blue-100 text-blue-800';
      case 'commercial':
        return 'bg-green-100 text-green-800';
      case 'mixed':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button variant="outline" onClick={onBack}>
              ← Back
            </Button>
          )}
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{property.name}</h1>
            <p className="text-gray-600">{formatAddress(property.address)}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span
            className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full capitalize ${getTypeColor(
              property.type
            )}`}
          >
            {property.type}
          </span>
          <span
            className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
              property.isActive
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}
          >
            {property.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'overview'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('units')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'units'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Units ({analytics?.totalUnits || 0})
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'analytics'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Analytics
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <>
          {/* Analytics Cards */}
          {analytics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Units</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.totalUnits}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Occupancy Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {analytics.occupancyRate.toFixed(1)}%
                  </div>
                  <p className="text-sm text-gray-600">
                    {analytics.occupiedUnits} of {analytics.totalUnits} occupied
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Monthly Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">
                    {property.settings?.currency || 'KES'} {analytics.totalMonthlyRevenue.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">
                    {analytics.activeLeases} active leases
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Open Tickets</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-600">
                    {analytics.openMaintenanceTickets}
                  </div>
                  <p className="text-sm text-gray-600">Maintenance requests</p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Property Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold text-gray-900">Property Type</h4>
              <p className="text-gray-600 capitalize">{property.type}</p>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900">Address</h4>
              <p className="text-gray-600">{formatAddress(property.address)}</p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900">Status</h4>
              <p className={`font-medium ${property.isActive ? 'text-green-600' : 'text-red-600'}`}>
                {property.isActive ? 'Active' : 'Inactive'}
              </p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900">Created</h4>
              <p className="text-gray-600">
                {new Date(property.createdAt).toLocaleDateString()} at{' '}
                {new Date(property.createdAt).toLocaleTimeString()}
              </p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900">Last Updated</h4>
              <p className="text-gray-600">
                {new Date(property.updatedAt).toLocaleDateString()} at{' '}
                {new Date(property.updatedAt).toLocaleTimeString()}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold text-gray-900">Currency</h4>
              <p className="text-gray-600">{property.settings?.currency || 'KES'}</p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900">Timezone</h4>
              <p className="text-gray-600">{property.settings?.timezone || 'Africa/Nairobi'}</p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900">Language</h4>
              <p className="text-gray-600">{property.settings?.language || 'English'}</p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900">Maintenance SLA</h4>
              <p className="text-gray-600">{property.settings?.maintenanceSLA || 24} hours</p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900">Auto Rent Reminders</h4>
              <p className={`font-medium ${
                property.settings?.autoRentReminders ? 'text-green-600' : 'text-red-600'
              }`}>
                {property.settings?.autoRentReminders ? 'Enabled' : 'Disabled'}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Branding */}
        <Card>
          <CardHeader>
            <CardTitle>Branding</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-4">
              <div>
                <h4 className="font-semibold text-gray-900">Primary Color</h4>
                <div className="flex items-center space-x-2">
                  <div
                    className="w-6 h-6 rounded border"
                    style={{ backgroundColor: property.branding?.primaryColor || '#3b82f6' }}
                  ></div>
                  <span className="text-gray-600">
                    {property.branding?.primaryColor || '#3b82f6'}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div>
                <h4 className="font-semibold text-gray-900">Secondary Color</h4>
                <div className="flex items-center space-x-2">
                  <div
                    className="w-6 h-6 rounded border"
                    style={{ backgroundColor: property.branding?.secondaryColor || '#1e40af' }}
                  ></div>
                  <span className="text-gray-600">
                    {property.branding?.secondaryColor || '#1e40af'}
                  </span>
                </div>
              </div>
            </div>

            {property.branding?.customDomain && (
              <div>
                <h4 className="font-semibold text-gray-900">Custom Domain</h4>
                <p className="text-blue-600 underline">
                  {property.branding.customDomain}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {onEdit && (
              <Button onClick={onEdit} className="w-full">
                Edit Property
              </Button>
            )}
            
            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirm(!showDeleteConfirm)}
              className="w-full text-red-600 border-red-300 hover:bg-red-50"
              disabled={isDeleting}
            >
              {showDeleteConfirm ? 'Cancel Delete' : 'Delete Property'}
            </Button>

            {showDeleteConfirm && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-800 mb-3">
                  Are you sure you want to delete this property? This action cannot be undone.
                  Properties with active leases cannot be deleted.
                </p>
                <Button
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="w-full bg-red-600 hover:bg-red-700"
                >
                  {isDeleting ? 'Deleting...' : 'Confirm Delete'}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
          </div>
        </>
      )}

      {activeTab === 'units' && (
        <UnitManagement propertyId={propertyId} />
      )}

      {activeTab === 'analytics' && (
        <PropertyAnalyticsDashboard propertyId={propertyId} />
      )}
    </div>
  );
};