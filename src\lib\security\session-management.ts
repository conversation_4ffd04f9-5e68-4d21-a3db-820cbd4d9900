import { jwtDecode } from 'jwt-decode';

// Session configuration
export interface SessionConfig {
  maxAge: number; // Session duration in milliseconds
  renewalThreshold: number; // Renew session when this much time is left
  maxConcurrentSessions: number; // Maximum concurrent sessions per user
  requireReauth: string[]; // Actions that require re-authentication
}

// Default session configuration
export const DEFAULT_SESSION_CONFIG: SessionConfig = {
  maxAge: 8 * 60 * 60 * 1000, // 8 hours
  renewalThreshold: 30 * 60 * 1000, // 30 minutes
  maxConcurrentSessions: 3,
  requireReauth: ['change_password', 'delete_account', 'financial_operations']
};

// JWT token payload interface
export interface TokenPayload {
  sub: string; // User ID
  email: string;
  role: string;
  permissions: string[];
  sessionId: string;
  iat: number; // Issued at
  exp: number; // Expires at
  jti: string; // JWT ID
}

// Session data interface
export interface SessionData {
  sessionId: string;
  userId: string;
  email: string;
  role: string;
  permissions: string[];
  createdAt: number;
  lastActivity: number;
  expiresAt: number;
  deviceInfo: DeviceInfo;
  ipAddress: string;
  isActive: boolean;
}

// Device information
export interface DeviceInfo {
  userAgent: string;
  platform: string;
  browser: string;
  fingerprint: string;
}

// Secure session manager
export class SessionManager {
  private static instance: SessionManager;
  private config: SessionConfig;
  private sessions = new Map<string, SessionData>();
  private userSessions = new Map<string, Set<string>>(); // userId -> sessionIds
  private refreshTokens = new Map<string, string>(); // sessionId -> refreshToken

  constructor(config: SessionConfig = DEFAULT_SESSION_CONFIG) {
    this.config = config;
    this.startCleanupTimer();
  }

  static getInstance(config?: SessionConfig): SessionManager {
    if (!this.instance) {
      this.instance = new SessionManager(config);
    }
    return this.instance;
  }

  /**
   * Create a new session
   */
  async createSession(
    userId: string,
    email: string,
    role: string,
    permissions: string[],
    deviceInfo: DeviceInfo,
    ipAddress: string
  ): Promise<{ accessToken: string; refreshToken: string; sessionId: string }> {
    // Check concurrent session limit
    const userSessionIds = this.userSessions.get(userId) || new Set();
    if (userSessionIds.size >= this.config.maxConcurrentSessions) {
      // Remove oldest session
      const oldestSessionId = Array.from(userSessionIds)[0];
      await this.terminateSession(oldestSessionId);
    }

    // Generate session ID and tokens
    const sessionId = this.generateSecureId();
    const accessToken = await this.generateAccessToken(userId, email, role, permissions, sessionId);
    const refreshToken = this.generateSecureId(64);

    // Create session data
    const now = Date.now();
    const sessionData: SessionData = {
      sessionId,
      userId,
      email,
      role,
      permissions,
      createdAt: now,
      lastActivity: now,
      expiresAt: now + this.config.maxAge,
      deviceInfo,
      ipAddress,
      isActive: true
    };

    // Store session
    this.sessions.set(sessionId, sessionData);
    this.refreshTokens.set(sessionId, refreshToken);

    // Update user sessions
    if (!this.userSessions.has(userId)) {
      this.userSessions.set(userId, new Set());
    }
    this.userSessions.get(userId)!.add(sessionId);

    return { accessToken, refreshToken, sessionId };
  }

  /**
   * Validate and refresh session
   */
  async validateSession(accessToken: string): Promise<SessionData | null> {
    try {
      const payload = jwtDecode<TokenPayload>(accessToken);
      const session = this.sessions.get(payload.sessionId);

      if (!session || !session.isActive) {
        return null;
      }

      // Check expiration
      if (Date.now() > session.expiresAt) {
        await this.terminateSession(session.sessionId);
        return null;
      }

      // Update last activity
      session.lastActivity = Date.now();

      return session;
    } catch (error) {
      console.error('Token validation failed:', error);
      return null;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(refreshToken: string, sessionId: string): Promise<string | null> {
    const storedRefreshToken = this.refreshTokens.get(sessionId);
    const session = this.sessions.get(sessionId);

    if (!storedRefreshToken || !session || storedRefreshToken !== refreshToken) {
      return null;
    }

    if (!session.isActive || Date.now() > session.expiresAt) {
      await this.terminateSession(sessionId);
      return null;
    }

    // Generate new access token
    const newAccessToken = await this.generateAccessToken(
      session.userId,
      session.email,
      session.role,
      session.permissions,
      sessionId
    );

    // Update session activity
    session.lastActivity = Date.now();

    return newAccessToken;
  }

  /**
   * Check if session needs renewal
   */
  needsRenewal(sessionData: SessionData): boolean {
    const timeLeft = sessionData.expiresAt - Date.now();
    return timeLeft < this.config.renewalThreshold;
  }

  /**
   * Extend session expiration
   */
  async extendSession(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId);
    if (!session || !session.isActive) {
      return false;
    }

    session.expiresAt = Date.now() + this.config.maxAge;
    session.lastActivity = Date.now();
    return true;
  }

  /**
   * Terminate specific session
   */
  async terminateSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.isActive = false;
      
      // Remove from user sessions
      const userSessions = this.userSessions.get(session.userId);
      if (userSessions) {
        userSessions.delete(sessionId);
        if (userSessions.size === 0) {
          this.userSessions.delete(session.userId);
        }
      }
    }

    this.sessions.delete(sessionId);
    this.refreshTokens.delete(sessionId);
  }

  /**
   * Terminate all sessions for a user
   */
  async terminateAllUserSessions(userId: string): Promise<void> {
    const userSessions = this.userSessions.get(userId);
    if (userSessions) {
      for (const sessionId of userSessions) {
        await this.terminateSession(sessionId);
      }
    }
  }

  /**
   * Get active sessions for user
   */
  getUserSessions(userId: string): SessionData[] {
    const sessionIds = this.userSessions.get(userId) || new Set();
    return Array.from(sessionIds)
      .map(id => this.sessions.get(id))
      .filter((session): session is SessionData => session !== undefined && session.isActive);
  }

  /**
   * Check if action requires re-authentication
   */
  requiresReauth(action: string): boolean {
    return this.config.requireReauth.includes(action);
  }

  /**
   * Verify recent authentication for sensitive actions
   */
  verifyRecentAuth(sessionId: string, maxAge: number = 5 * 60 * 1000): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    return (Date.now() - session.lastActivity) < maxAge;
  }

  /**
   * Generate secure access token
   */
  private async generateAccessToken(
    userId: string,
    email: string,
    role: string,
    permissions: string[],
    sessionId: string
  ): Promise<string> {
    const payload: TokenPayload = {
      sub: userId,
      email,
      role,
      permissions,
      sessionId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor((Date.now() + this.config.maxAge) / 1000),
      jti: this.generateSecureId()
    };

    // Using base64 encoding - should be replaced with proper JWT signing in production
    return btoa(JSON.stringify(payload));
  }

  /**
   * Generate cryptographically secure ID
   */
  private generateSecureId(length: number = 32): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Start cleanup timer for expired sessions
   */
  private startCleanupTimer(): void {
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 5 * 60 * 1000); // Cleanup every 5 minutes
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.sessions.entries()) {
      if (now > session.expiresAt || !session.isActive) {
        expiredSessions.push(sessionId);
      }
    }

    expiredSessions.forEach(sessionId => {
      this.terminateSession(sessionId);
    });

    console.log(`Cleaned up ${expiredSessions.length} expired sessions`);
  }
}

// Token security utilities
export class TokenSecurity {
  /**
   * Validate token format and structure
   */
  static validateTokenFormat(token: string): boolean {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) return false;

      // Validate base64 encoding
      parts.forEach(part => {
        atob(part.replace(/-/g, '+').replace(/_/g, '/'));
      });

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if token is expired
   */
  static isTokenExpired(token: string): boolean {
    try {
      const payload = jwtDecode<TokenPayload>(token);
      return Date.now() >= payload.exp * 1000;
    } catch {
      return true;
    }
  }

  /**
   * Get token expiration time
   */
  static getTokenExpiration(token: string): number | null {
    try {
      const payload = jwtDecode<TokenPayload>(token);
      return payload.exp * 1000;
    } catch {
      return null;
    }
  }

  /**
   * Extract user ID from token
   */
  static getUserIdFromToken(token: string): string | null {
    try {
      const payload = jwtDecode<TokenPayload>(token);
      return payload.sub;
    } catch {
      return null;
    }
  }

  /**
   * Secure token storage in localStorage with encryption
   */
  static storeToken(key: string, token: string): void {
    try {
      // TODO: Implement proper token encryption for production
      const encrypted = btoa(token);
      localStorage.setItem(key, encrypted);
    } catch (error) {
      console.error('Failed to store token:', error);
    }
  }

  /**
   * Retrieve and decrypt token from localStorage
   */
  static retrieveToken(key: string): string | null {
    try {
      const encrypted = localStorage.getItem(key);
      if (!encrypted) return null;
      
      // Decrypt token
      return atob(encrypted);
    } catch (error) {
      console.error('Failed to retrieve token:', error);
      return null;
    }
  }

  /**
   * Clear stored tokens
   */
  static clearTokens(): void {
    const tokenKeys = ['accessToken', 'refreshToken', 'sessionId'];
    tokenKeys.forEach(key => {
      localStorage.removeItem(key);
    });
  }
}

// Security event logging
export class SecurityEventLogger {
  private static events: SecurityEvent[] = [];
  private static readonly MAX_EVENTS = 1000;

  /**
   * Log security event
   */
  static logEvent(event: Omit<SecurityEvent, 'timestamp' | 'id'>): void {
    const securityEvent: SecurityEvent = {
      ...event,
      id: crypto.randomUUID(),
      timestamp: Date.now()
    };

    this.events.unshift(securityEvent);
    
    // Keep only recent events
    if (this.events.length > this.MAX_EVENTS) {
      this.events = this.events.slice(0, this.MAX_EVENTS);
    }

    // Log to console for development
    console.log('Security Event:', securityEvent);
  }

  /**
   * Get security events
   */
  static getEvents(filter?: Partial<SecurityEvent>): SecurityEvent[] {
    if (!filter) return [...this.events];

    return this.events.filter(event => {
      return Object.entries(filter).every(([key, value]) => {
        return event[key as keyof SecurityEvent] === value;
      });
    });
  }

  /**
   * Get events by user
   */
  static getUserEvents(userId: string): SecurityEvent[] {
    return this.getEvents({ userId });
  }

  /**
   * Get events by type
   */
  static getEventsByType(type: SecurityEventType): SecurityEvent[] {
    return this.getEvents({ type });
  }

  /**
   * Clear old events
   */
  static clearOldEvents(maxAge: number = 30 * 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge;
    this.events = this.events.filter(event => event.timestamp > cutoff);
  }
}

// Types
export enum SecurityEventType {
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILURE = 'login_failure',
  LOGOUT = 'logout',
  SESSION_EXPIRED = 'session_expired',
  TOKEN_REFRESH = 'token_refresh',
  PERMISSION_DENIED = 'permission_denied',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  INVALID_TOKEN = 'invalid_token',
  SESSION_HIJACK_ATTEMPT = 'session_hijack_attempt'
}

export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  userId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  details: Record<string, any>;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
}