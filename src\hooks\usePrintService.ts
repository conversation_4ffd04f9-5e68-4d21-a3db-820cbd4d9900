import { useCallback } from 'react'

export interface PrintOptions {
  silent?: boolean
  printBackground?: boolean
  color?: boolean
  landscape?: boolean
  pagesPerSheet?: number
  collate?: boolean
  copies?: number
  margins?: {
    marginType?: 'default' | 'none' | 'printableArea' | 'custom'
    top?: number
    bottom?: number
    left?: number
    right?: number
  }
}

export interface PDFOptions {
  marginsType?: number
  pageSize?: 'A3' | 'A4' | 'A5' | 'Legal' | 'Letter' | 'Tabloid'
  printBackground?: boolean
  printSelectionOnly?: boolean
  landscape?: boolean
}

export const usePrintService = () => {
  const printDocument = useCallback(async (html: string, options: PrintOptions = {}) => {
    if (!window.electronAPI) {
      // Fallback to browser print for web environment
      const printWindow = window.open('', '_blank')
      if (printWindow) {
        printWindow.document.write(html)
        printWindow.document.close()
        printWindow.print()
        printWindow.close()
        return { success: true }
      }
      return { success: false, error: 'Could not open print window' }
    }

    return await window.electronAPI.printDocument(html, options)
  }, [])

  const printToPDF = useCallback(async (html: string, options: PDFOptions = {}) => {
    if (!window.electronAPI) {
      throw new Error('PDF generation not available in web environment')
    }

    const result = await window.electronAPI.printToPDF(html, options)
    
    if (result.success && result.buffer) {
      // Convert buffer back to Uint8Array for download
      const uint8Array = new Uint8Array(result.buffer)
      const blob = new Blob([uint8Array], { type: 'application/pdf' })
      return { success: true, blob }
    }
    
    return result
  }, [])

  const downloadPDF = useCallback(async (html: string, filename: string, options: PDFOptions = {}) => {
    const result = await printToPDF(html, options)
    
    if (result.success && result.blob) {
      const url = URL.createObjectURL(result.blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      return { success: true }
    }
    
    return result
  }, [printToPDF])

  // Predefined print templates for common reports
  const printPropertyReport = useCallback(async (propertyData: any, options: PrintOptions = {}) => {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Property Report - ${propertyData.name}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .property-info { margin-bottom: 20px; }
            .section { margin-bottom: 25px; }
            .section h3 { border-bottom: 2px solid #333; padding-bottom: 5px; }
            table { width: 100%; border-collapse: collapse; margin-top: 10px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Property Report</h1>
            <h2>${propertyData.name}</h2>
            <p>Generated on ${new Date().toLocaleDateString()}</p>
          </div>
          
          <div class="property-info">
            <h3>Property Information</h3>
            <p><strong>Address:</strong> ${propertyData.address}</p>
            <p><strong>Type:</strong> ${propertyData.type}</p>
            <p><strong>Total Units:</strong> ${propertyData.totalUnits}</p>
            <p><strong>Occupied Units:</strong> ${propertyData.occupiedUnits}</p>
            <p><strong>Occupancy Rate:</strong> ${((propertyData.occupiedUnits / propertyData.totalUnits) * 100).toFixed(1)}%</p>
          </div>
          
          <div class="section">
            <h3>Financial Summary</h3>
            <table>
              <tr><th>Metric</th><th>Amount</th></tr>
              <tr><td>Monthly Revenue</td><td>$${propertyData.monthlyRevenue?.toLocaleString() || 'N/A'}</td></tr>
              <tr><td>Annual Revenue</td><td>$${propertyData.annualRevenue?.toLocaleString() || 'N/A'}</td></tr>
              <tr><td>Outstanding Payments</td><td>$${propertyData.outstandingPayments?.toLocaleString() || 'N/A'}</td></tr>
            </table>
          </div>
          
          <div class="footer">
            <p>EstatePulse Property Management System</p>
          </div>
        </body>
      </html>
    `
    
    return await printDocument(html, options)
  }, [printDocument])

  const printLeaseAgreement = useCallback(async (leaseData: any, options: PrintOptions = {}) => {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Lease Agreement - ${leaseData.tenantName}</title>
          <style>
            body { font-family: 'Times New Roman', serif; margin: 40px; line-height: 1.6; }
            .header { text-align: center; margin-bottom: 40px; }
            .section { margin-bottom: 30px; }
            .signature-section { margin-top: 50px; }
            .signature-line { border-bottom: 1px solid #000; width: 200px; display: inline-block; margin: 0 20px; }
            .terms { margin-left: 20px; }
            .terms li { margin-bottom: 10px; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>LEASE AGREEMENT</h1>
            <p>Property: ${leaseData.propertyName}</p>
            <p>Unit: ${leaseData.unitNumber}</p>
          </div>
          
          <div class="section">
            <h3>PARTIES</h3>
            <p><strong>Landlord:</strong> ${leaseData.landlordName}</p>
            <p><strong>Tenant:</strong> ${leaseData.tenantName}</p>
          </div>
          
          <div class="section">
            <h3>LEASE TERMS</h3>
            <p><strong>Lease Start Date:</strong> ${new Date(leaseData.startDate).toLocaleDateString()}</p>
            <p><strong>Lease End Date:</strong> ${new Date(leaseData.endDate).toLocaleDateString()}</p>
            <p><strong>Monthly Rent:</strong> $${leaseData.monthlyRent.toLocaleString()}</p>
            <p><strong>Security Deposit:</strong> $${leaseData.deposit.toLocaleString()}</p>
          </div>
          
          <div class="section">
            <h3>TERMS AND CONDITIONS</h3>
            <ol class="terms">
              <li>The tenant agrees to pay rent on or before the 1st day of each month.</li>
              <li>The security deposit will be returned within 30 days of lease termination, subject to property condition.</li>
              <li>The tenant is responsible for maintaining the property in good condition.</li>
              <li>No pets are allowed without prior written consent from the landlord.</li>
              <li>The tenant must provide 30 days written notice before vacating the property.</li>
            </ol>
          </div>
          
          <div class="signature-section">
            <p>Landlord Signature: <span class="signature-line"></span> Date: <span class="signature-line"></span></p>
            <br>
            <p>Tenant Signature: <span class="signature-line"></span> Date: <span class="signature-line"></span></p>
          </div>
        </body>
      </html>
    `
    
    return await printDocument(html, options)
  }, [printDocument])

  const printMaintenanceReport = useCallback(async (tickets: any[], options: PrintOptions = {}) => {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Maintenance Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            table { width: 100%; border-collapse: collapse; margin-top: 10px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
            th { background-color: #f2f2f2; }
            .priority-high { background-color: #ffebee; }
            .priority-emergency { background-color: #ffcdd2; }
            .status-completed { color: #4caf50; font-weight: bold; }
            .status-pending { color: #ff9800; font-weight: bold; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Maintenance Report</h1>
            <p>Generated on ${new Date().toLocaleDateString()}</p>
            <p>Total Tickets: ${tickets.length}</p>
          </div>
          
          <table>
            <thead>
              <tr>
                <th>Ticket ID</th>
                <th>Property</th>
                <th>Unit</th>
                <th>Description</th>
                <th>Priority</th>
                <th>Status</th>
                <th>Created Date</th>
                <th>Assigned Vendor</th>
              </tr>
            </thead>
            <tbody>
              ${tickets.map(ticket => `
                <tr class="${ticket.priority === 'high' ? 'priority-high' : ticket.priority === 'emergency' ? 'priority-emergency' : ''}">
                  <td>${ticket.id}</td>
                  <td>${ticket.propertyName}</td>
                  <td>${ticket.unitNumber || 'N/A'}</td>
                  <td>${ticket.description}</td>
                  <td>${ticket.priority}</td>
                  <td class="status-${ticket.status}">${ticket.status}</td>
                  <td>${new Date(ticket.createdAt).toLocaleDateString()}</td>
                  <td>${ticket.vendorName || 'Unassigned'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </body>
      </html>
    `
    
    return await printDocument(html, options)
  }, [printDocument])

  return {
    printDocument,
    printToPDF,
    downloadPDF,
    // Predefined print functions
    printPropertyReport,
    printLeaseAgreement,
    printMaintenanceReport
  }
}