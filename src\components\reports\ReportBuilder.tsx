import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Checkbox } from '../ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Calendar, Clock, Mail, Users } from 'lucide-react';
import { Id } from '../../../convex/_generated/dataModel';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  sections: string[];
}

interface ReportBuilderProps {
  templates: ReportTemplate[];
  onClose: () => void;
  onSave: () => void;
}

export const ReportBuilder: React.FC<ReportBuilderProps> = ({ templates, onClose, onSave }) => {
  const [currentTab, setCurrentTab] = useState('basic');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    templateId: '',
    sections: [] as string[],
    propertyIds: [] as Id<"properties">[],
    dateRangeType: 'monthly' as 'monthly' | 'quarterly' | 'annual' | 'custom',
    customStart: undefined as number | undefined,
    customEnd: undefined as number | undefined,
    scheduleEnabled: false,
    frequency: 'monthly' as 'weekly' | 'monthly' | 'quarterly',
    dayOfWeek: undefined as number | undefined,
    dayOfMonth: undefined as number | undefined,
    recipients: [] as string[],
    recipientEmail: '',
  });

  const properties = useQuery(api.properties.list);
  const saveReportConfig = useMutation(api.executiveReports.saveReportConfiguration);

  const selectedTemplate = templates.find(t => t.id === formData.templateId);

  const handleSectionToggle = (sectionId: string) => {
    setFormData(prev => ({
      ...prev,
      sections: prev.sections.includes(sectionId)
        ? prev.sections.filter(s => s !== sectionId)
        : [...prev.sections, sectionId]
    }));
  };

  const handlePropertyToggle = (propertyId: Id<"properties">) => {
    setFormData(prev => ({
      ...prev,
      propertyIds: prev.propertyIds.includes(propertyId)
        ? prev.propertyIds.filter(p => p !== propertyId)
        : [...prev.propertyIds, propertyId]
    }));
  };

  const addRecipient = () => {
    if (formData.recipientEmail && !formData.recipients.includes(formData.recipientEmail)) {
      setFormData(prev => ({
        ...prev,
        recipients: [...prev.recipients, prev.recipientEmail],
        recipientEmail: ''
      }));
    }
  };

  const removeRecipient = (email: string) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.filter(r => r !== email)
    }));
  };

  const handleSave = async () => {
    try {
      await saveReportConfig({
        name: formData.name,
        description: formData.description,
        templateId: formData.templateId,
        sections: formData.sections,
        filters: {
          propertyIds: formData.propertyIds.length > 0 ? formData.propertyIds : undefined,
          dateRange: {
            type: formData.dateRangeType,
            customStart: formData.customStart,
            customEnd: formData.customEnd,
          },
        },
        schedule: formData.scheduleEnabled ? {
          frequency: formData.frequency,
          dayOfWeek: formData.dayOfWeek,
          dayOfMonth: formData.dayOfMonth,
          recipients: formData.recipients,
        } : undefined,
      });
      onSave();
    } catch (error) {
      console.error('Failed to save report configuration:', error);
    }
  };

  const isFormValid = formData.name && formData.templateId && formData.sections.length > 0;

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Custom Report</DialogTitle>
          <DialogDescription>
            Configure a custom report with automated generation and distribution
          </DialogDescription>
        </DialogHeader>

        <Tabs value={currentTab} onValueChange={setCurrentTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="template">Template & Sections</TabsTrigger>
            <TabsTrigger value="filters">Filters</TabsTrigger>
            <TabsTrigger value="schedule">Schedule</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Report Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Monthly Executive Summary"
                />
              </div>
              
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Brief description of what this report covers"
                  rows={3}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="template" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label>Select Template</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                  {templates.map((template) => (
                    <Card 
                      key={template.id}
                      className={`cursor-pointer transition-colors ${
                        formData.templateId === template.id ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => setFormData(prev => ({ 
                        ...prev, 
                        templateId: template.id,
                        sections: template.sections 
                      }))}
                    >
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">{template.name}</CardTitle>
                        <CardDescription className="text-xs">{template.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="text-xs text-gray-600">
                          {template.sections.length} sections included
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {selectedTemplate && (
                <div>
                  <Label>Report Sections</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                    {selectedTemplate.sections.map((section) => (
                      <div key={section} className="flex items-center space-x-2">
                        <Checkbox
                          id={section}
                          checked={formData.sections.includes(section)}
                          onCheckedChange={() => handleSectionToggle(section)}
                        />
                        <Label htmlFor={section} className="text-sm capitalize">
                          {section.replace(/-/g, ' ')}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="filters" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label>Date Range</Label>
                <Select 
                  value={formData.dateRangeType} 
                  onValueChange={(value: 'monthly' | 'quarterly' | 'annual' | 'custom') => 
                    setFormData(prev => ({ ...prev, dateRangeType: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="annual">Annual</SelectItem>
                    <SelectItem value="custom">Custom Range</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.dateRangeType === 'custom' && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="customStart">Start Date</Label>
                    <Input
                      id="customStart"
                      type="date"
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        customStart: new Date(e.target.value).getTime() 
                      }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="customEnd">End Date</Label>
                    <Input
                      id="customEnd"
                      type="date"
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        customEnd: new Date(e.target.value).getTime() 
                      }))}
                    />
                  </div>
                </div>
              )}

              <div>
                <Label>Properties (leave empty for all)</Label>
                <div className="max-h-40 overflow-y-auto border rounded-md p-3 mt-2">
                  {properties?.map((property) => (
                    <div key={property._id} className="flex items-center space-x-2 py-1">
                      <Checkbox
                        id={property._id}
                        checked={formData.propertyIds.includes(property._id)}
                        onCheckedChange={() => handlePropertyToggle(property._id)}
                      />
                      <Label htmlFor={property._id} className="text-sm">
                        {property.name}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="scheduleEnabled"
                  checked={formData.scheduleEnabled}
                  onCheckedChange={(checked) => setFormData(prev => ({ 
                    ...prev, 
                    scheduleEnabled: checked as boolean 
                  }))}
                />
                <Label htmlFor="scheduleEnabled">Enable Automated Generation</Label>
              </div>

              {formData.scheduleEnabled && (
                <>
                  <div>
                    <Label>Frequency</Label>
                    <Select 
                      value={formData.frequency} 
                      onValueChange={(value: 'weekly' | 'monthly' | 'quarterly') => 
                        setFormData(prev => ({ ...prev, frequency: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {formData.frequency === 'weekly' && (
                    <div>
                      <Label>Day of Week</Label>
                      <Select 
                        value={formData.dayOfWeek?.toString()} 
                        onValueChange={(value) => setFormData(prev => ({ 
                          ...prev, 
                          dayOfWeek: parseInt(value) 
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select day" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">Sunday</SelectItem>
                          <SelectItem value="1">Monday</SelectItem>
                          <SelectItem value="2">Tuesday</SelectItem>
                          <SelectItem value="3">Wednesday</SelectItem>
                          <SelectItem value="4">Thursday</SelectItem>
                          <SelectItem value="5">Friday</SelectItem>
                          <SelectItem value="6">Saturday</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {formData.frequency === 'monthly' && (
                    <div>
                      <Label>Day of Month</Label>
                      <Input
                        type="number"
                        min="1"
                        max="28"
                        value={formData.dayOfMonth || ''}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          dayOfMonth: parseInt(e.target.value) || undefined 
                        }))}
                        placeholder="1-28"
                      />
                    </div>
                  )}

                  <div>
                    <Label>Email Recipients</Label>
                    <div className="flex gap-2 mt-2">
                      <Input
                        type="email"
                        value={formData.recipientEmail}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          recipientEmail: e.target.value 
                        }))}
                        placeholder="Enter email address"
                      />
                      <Button type="button" onClick={addRecipient} variant="outline">
                        Add
                      </Button>
                    </div>
                    
                    {formData.recipients.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {formData.recipients.map((email) => (
                          <Badge key={email} variant="secondary" className="flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {email}
                            <button
                              onClick={() => removeRecipient(email)}
                              className="ml-1 text-xs hover:text-red-600"
                            >
                              ×
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={!isFormValid}>
            Save Report Configuration
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
