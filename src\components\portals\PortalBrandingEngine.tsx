import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { useToast } from '../ui/use-toast';
import { Palette, Monitor, Settings, Globe, Eye, Save } from 'lucide-react';

interface PortalBrandingEngineProps {
  propertyId: Id<"properties">;
}

interface BrandingData {
  logo?: string;
  favicon?: string;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  fontFamily: string;
  customCSS?: string;
}

interface ThemeData {
  layout: 'modern' | 'classic' | 'minimal';
  headerStyle: 'fixed' | 'static' | 'transparent';
  sidebarStyle: 'expanded' | 'collapsed' | 'overlay';
  cardStyle: 'elevated' | 'outlined' | 'flat';
  borderRadius: 'none' | 'small' | 'medium' | 'large';
}

const defaultBranding: BrandingData = {
  primaryColor: '#3b82f6',
  secondaryColor: '#64748b',
  accentColor: '#10b981',
  backgroundColor: '#ffffff',
  textColor: '#1f2937',
  fontFamily: 'Inter, sans-serif',
};

const defaultTheme: ThemeData = {
  layout: 'modern',
  headerStyle: 'fixed',
  sidebarStyle: 'expanded',
  cardStyle: 'elevated',
  borderRadius: 'medium',
};

const fontOptions = [
  'Inter, sans-serif',
  'Roboto, sans-serif',
  'Open Sans, sans-serif',
  'Lato, sans-serif',
  'Montserrat, sans-serif',
  'Poppins, sans-serif',
  'Source Sans Pro, sans-serif',
  'Nunito, sans-serif',
];

export const PortalBrandingEngine: React.FC<PortalBrandingEngineProps> = ({ propertyId }) => {
  const { toast } = useToast();
  const [branding, setBranding] = useState<BrandingData>(defaultBranding);
  const [theme, setTheme] = useState<ThemeData>(defaultTheme);
  const [subdomain, setSubdomain] = useState('');
  const [customDomain, setCustomDomain] = useState('');
  const [portalName, setPortalName] = useState('');
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const portal = useQuery(api.portals.getPortalByProperty, { propertyId });
  const property = useQuery(api.properties.getById, { id: propertyId });
  const portalCSS = useQuery(api.portals.generatePortalCSS, 
    portal ? { portalId: portal._id } : 'skip'
  );

  const createPortal = useMutation(api.portals.createPortal);
  const updateBranding = useMutation(api.portals.updatePortalBranding);
  const updateTheme = useMutation(api.portals.updatePortalTheme);
  const updateCustomDomain = useMutation(api.portals.updateCustomDomain);
  const checkSubdomainAvailability = useQuery(api.portals.checkSubdomainAvailability, 
    subdomain ? { subdomain } : 'skip'
  );

  useEffect(() => {
    if (portal) {
      setBranding(portal.branding);
      setTheme(portal.theme);
      setSubdomain(portal.subdomain);
      setCustomDomain(portal.customDomain || '');
      setPortalName(portal.name);
    } else if (property) {
      setPortalName(`${property.name} Portal`);
      setSubdomain(property.name.toLowerCase().replace(/[^a-z0-9]/g, '-'));
    }
  }, [portal, property]);

  const handleSaveBranding = async () => {
    try {
      if (portal) {
        await updateBranding({
          portalId: portal._id,
          branding,
        });
        toast({
          title: "Branding Updated",
          description: "Portal branding has been successfully updated.",
        });
      } else {
        // Create new portal
        await createPortal({
          propertyId,
          name: portalName,
          subdomain,
          customDomain: customDomain || undefined,
          branding,
          theme,
        });
        toast({
          title: "Portal Created",
          description: "Portal has been successfully created with your branding.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save branding",
        variant: "destructive",
      });
    }
  };

  const handleSaveTheme = async () => {
    try {
      if (portal) {
        await updateTheme({
          portalId: portal._id,
          theme,
        });
        toast({
          title: "Theme Updated",
          description: "Portal theme has been successfully updated.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save theme",
        variant: "destructive",
      });
    }
  };

  const handleSaveDomain = async () => {
    try {
      if (portal) {
        await updateCustomDomain({
          portalId: portal._id,
          customDomain: customDomain || undefined,
        });
        toast({
          title: "Domain Updated",
          description: "Custom domain has been successfully updated.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save domain",
        variant: "destructive",
      });
    }
  };

  const ColorPicker: React.FC<{ 
    label: string; 
    value: string; 
    onChange: (value: string) => void;
    description?: string;
  }> = ({ label, value, onChange, description }) => (
    <div className="space-y-2">
      <Label htmlFor={label}>{label}</Label>
      <div className="flex items-center space-x-2">
        <Input
          id={label}
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-16 h-10 p-1 border rounded"
        />
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="#000000"
          className="flex-1"
        />
      </div>
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
    </div>
  );

  const PreviewCard: React.FC = () => (
    <Card className="w-full max-w-md mx-auto" style={{
      backgroundColor: branding.backgroundColor,
      color: branding.textColor,
      fontFamily: branding.fontFamily,
      borderRadius: theme.borderRadius === 'none' ? '0' : 
                   theme.borderRadius === 'small' ? '4px' : 
                   theme.borderRadius === 'medium' ? '8px' : '12px',
      boxShadow: theme.cardStyle === 'elevated' ? '0 4px 6px rgba(0, 0, 0, 0.1)' : 'none',
      border: theme.cardStyle === 'outlined' ? '1px solid rgba(0, 0, 0, 0.1)' : 'none',
    }}>
      <CardHeader style={{ backgroundColor: branding.primaryColor, color: 'white' }}>
        <CardTitle>{portalName}</CardTitle>
        <CardDescription style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
          Welcome to your tenant portal
        </CardDescription>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-3">
          <Button 
            style={{ 
              backgroundColor: branding.primaryColor, 
              borderColor: branding.primaryColor 
            }}
            className="w-full"
          >
            Primary Action
          </Button>
          <Button 
            variant="outline"
            style={{ 
              borderColor: branding.secondaryColor,
              color: branding.secondaryColor
            }}
            className="w-full"
          >
            Secondary Action
          </Button>
          <div 
            className="p-3 rounded"
            style={{ backgroundColor: branding.accentColor, color: 'white' }}
          >
            Accent Color Example
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Portal Branding & Customization</h2>
          <p className="text-muted-foreground">
            Customize your tenant portal's appearance and branding
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setIsPreviewMode(!isPreviewMode)}
          >
            <Eye className="w-4 h-4 mr-2" />
            {isPreviewMode ? 'Edit' : 'Preview'}
          </Button>
          {portal && (
            <Badge variant={portal.isPublished ? "default" : "secondary"}>
              {portal.isPublished ? 'Published' : 'Draft'}
            </Badge>
          )}
        </div>
      </div>

      {isPreviewMode ? (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Portal Preview</CardTitle>
              <CardDescription>
                This is how your portal will look with the current settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PreviewCard />
            </CardContent>
          </Card>
          
          {portalCSS && (
            <Card>
              <CardHeader>
                <CardTitle>Generated CSS</CardTitle>
                <CardDescription>
                  CSS variables and styles generated from your branding
                </CardDescription>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded text-sm overflow-auto max-h-96">
                  {portalCSS}
                </pre>
              </CardContent>
            </Card>
          )}
        </div>
      ) : (
        <Tabs defaultValue="branding" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="branding">
              <Palette className="w-4 h-4 mr-2" />
              Branding
            </TabsTrigger>
            <TabsTrigger value="theme">
              <Monitor className="w-4 h-4 mr-2" />
              Theme
            </TabsTrigger>
            <TabsTrigger value="domain">
              <Globe className="w-4 h-4 mr-2" />
              Domain
            </TabsTrigger>
            <TabsTrigger value="advanced">
              <Settings className="w-4 h-4 mr-2" />
              Advanced
            </TabsTrigger>
          </TabsList>

          <TabsContent value="branding" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Brand Colors</CardTitle>
                  <CardDescription>
                    Define your portal's color scheme
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ColorPicker
                    label="Primary Color"
                    value={branding.primaryColor}
                    onChange={(value) => setBranding(prev => ({ ...prev, primaryColor: value }))}
                    description="Main brand color for buttons and headers"
                  />
                  <ColorPicker
                    label="Secondary Color"
                    value={branding.secondaryColor}
                    onChange={(value) => setBranding(prev => ({ ...prev, secondaryColor: value }))}
                    description="Secondary color for subtle elements"
                  />
                  <ColorPicker
                    label="Accent Color"
                    value={branding.accentColor}
                    onChange={(value) => setBranding(prev => ({ ...prev, accentColor: value }))}
                    description="Accent color for highlights and notifications"
                  />
                  <ColorPicker
                    label="Background Color"
                    value={branding.backgroundColor}
                    onChange={(value) => setBranding(prev => ({ ...prev, backgroundColor: value }))}
                    description="Main background color"
                  />
                  <ColorPicker
                    label="Text Color"
                    value={branding.textColor}
                    onChange={(value) => setBranding(prev => ({ ...prev, textColor: value }))}
                    description="Primary text color"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Typography & Assets</CardTitle>
                  <CardDescription>
                    Configure fonts and upload brand assets
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="fontFamily">Font Family</Label>
                    <Select
                      value={branding.fontFamily}
                      onValueChange={(value) => setBranding(prev => ({ ...prev, fontFamily: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {fontOptions.map((font) => (
                          <SelectItem key={font} value={font}>
                            <span style={{ fontFamily: font }}>{font.split(',')[0]}</span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="logo">Logo URL</Label>
                    <Input
                      id="logo"
                      value={branding.logo || ''}
                      onChange={(e) => setBranding(prev => ({ ...prev, logo: e.target.value }))}
                      placeholder="https://example.com/logo.png"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="favicon">Favicon URL</Label>
                    <Input
                      id="favicon"
                      value={branding.favicon || ''}
                      onChange={(e) => setBranding(prev => ({ ...prev, favicon: e.target.value }))}
                      placeholder="https://example.com/favicon.ico"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex justify-end">
              <Button onClick={handleSaveBranding}>
                <Save className="w-4 h-4 mr-2" />
                Save Branding
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="theme" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Layout & Theme Settings</CardTitle>
                <CardDescription>
                  Configure the visual layout and styling of your portal
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label>Layout Style</Label>
                    <Select
                      value={theme.layout}
                      onValueChange={(value: 'modern' | 'classic' | 'minimal') => 
                        setTheme(prev => ({ ...prev, layout: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="modern">Modern</SelectItem>
                        <SelectItem value="classic">Classic</SelectItem>
                        <SelectItem value="minimal">Minimal</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Header Style</Label>
                    <Select
                      value={theme.headerStyle}
                      onValueChange={(value: 'fixed' | 'static' | 'transparent') => 
                        setTheme(prev => ({ ...prev, headerStyle: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fixed">Fixed</SelectItem>
                        <SelectItem value="static">Static</SelectItem>
                        <SelectItem value="transparent">Transparent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Sidebar Style</Label>
                    <Select
                      value={theme.sidebarStyle}
                      onValueChange={(value: 'expanded' | 'collapsed' | 'overlay') => 
                        setTheme(prev => ({ ...prev, sidebarStyle: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="expanded">Expanded</SelectItem>
                        <SelectItem value="collapsed">Collapsed</SelectItem>
                        <SelectItem value="overlay">Overlay</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Card Style</Label>
                    <Select
                      value={theme.cardStyle}
                      onValueChange={(value: 'elevated' | 'outlined' | 'flat') => 
                        setTheme(prev => ({ ...prev, cardStyle: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="elevated">Elevated (Shadow)</SelectItem>
                        <SelectItem value="outlined">Outlined (Border)</SelectItem>
                        <SelectItem value="flat">Flat (No Border)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Border Radius</Label>
                    <Select
                      value={theme.borderRadius}
                      onValueChange={(value: 'none' | 'small' | 'medium' | 'large') => 
                        setTheme(prev => ({ ...prev, borderRadius: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None (0px)</SelectItem>
                        <SelectItem value="small">Small (4px)</SelectItem>
                        <SelectItem value="medium">Medium (8px)</SelectItem>
                        <SelectItem value="large">Large (12px)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Separator />

                <div className="flex justify-end">
                  <Button onClick={handleSaveTheme}>
                    <Save className="w-4 h-4 mr-2" />
                    Save Theme
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="domain" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Domain Configuration</CardTitle>
                <CardDescription>
                  Set up your portal's subdomain and custom domain
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="portalName">Portal Name</Label>
                  <Input
                    id="portalName"
                    value={portalName}
                    onChange={(e) => setPortalName(e.target.value)}
                    placeholder="My Property Portal"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subdomain">Subdomain</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="subdomain"
                      value={subdomain}
                      onChange={(e) => setSubdomain(e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, ''))}
                      placeholder="my-property"
                      className="flex-1"
                    />
                    <span className="text-muted-foreground">.estatepulse.com</span>
                  </div>
                  {subdomain && checkSubdomainAvailability !== undefined && (
                    <p className={`text-sm ${checkSubdomainAvailability ? 'text-green-600' : 'text-red-600'}`}>
                      {checkSubdomainAvailability ? '✓ Available' : '✗ Not available'}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="customDomain">Custom Domain (Optional)</Label>
                  <Input
                    id="customDomain"
                    value={customDomain}
                    onChange={(e) => setCustomDomain(e.target.value)}
                    placeholder="portal.mycompany.com"
                  />
                  <p className="text-sm text-muted-foreground">
                    You'll need to configure DNS settings to point to our servers
                  </p>
                </div>

                <Separator />

                <div className="flex justify-end">
                  <Button onClick={handleSaveDomain}>
                    <Save className="w-4 h-4 mr-2" />
                    Save Domain Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Custom CSS</CardTitle>
                <CardDescription>
                  Add custom CSS to further customize your portal's appearance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="customCSS">Custom CSS</Label>
                  <Textarea
                    id="customCSS"
                    value={branding.customCSS || ''}
                    onChange={(e) => setBranding(prev => ({ ...prev, customCSS: e.target.value }))}
                    placeholder="/* Add your custom CSS here */
.custom-class {
  /* Your styles */
}"
                    rows={10}
                    className="font-mono text-sm"
                  />
                  <p className="text-sm text-muted-foreground">
                    Use CSS custom properties like var(--primary-color) to reference your brand colors
                  </p>
                </div>

                <Separator className="my-4" />

                <div className="flex justify-end">
                  <Button onClick={handleSaveBranding}>
                    <Save className="w-4 h-4 mr-2" />
                    Save Custom CSS
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};