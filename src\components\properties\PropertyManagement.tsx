import React, { useState } from 'react';
import { Id } from '../../../convex/_generated/dataModel';
import { PropertyList } from './PropertyList';
import { PropertyForm } from './PropertyForm';
import { PropertyDetails } from './PropertyDetails';

type ViewMode = 'list' | 'create' | 'edit' | 'details';

export const PropertyManagement: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedPropertyId, setSelectedPropertyId] = useState<Id<"properties"> | null>(null);

  const handleCreateNew = () => {
    setSelectedPropertyId(null);
    setViewMode('create');
  };

  const handleEditProperty = (propertyId: Id<"properties">) => {
    setSelectedPropertyId(propertyId);
    setViewMode('edit');
  };

  const handlePropertySelect = (propertyId: Id<"properties">) => {
    setSelectedPropertyId(propertyId);
    setViewMode('details');
  };

  const handleFormSuccess = () => {
    setViewMode('list');
    setSelectedPropertyId(null);
  };

  const handleFormCancel = () => {
    setViewMode('list');
    setSelectedPropertyId(null);
  };

  const handleBackToList = () => {
    setViewMode('list');
    setSelectedPropertyId(null);
  };

  const handleEditFromDetails = () => {
    if (selectedPropertyId) {
      setViewMode('edit');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {viewMode === 'list' && (
          <PropertyList
            onPropertySelect={handlePropertySelect}
            onCreateNew={handleCreateNew}
            onEditProperty={handleEditProperty}
          />
        )}

        {(viewMode === 'create' || viewMode === 'edit') && (
          <PropertyForm
            propertyId={viewMode === 'edit' ? selectedPropertyId || undefined : undefined}
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
          />
        )}

        {viewMode === 'details' && selectedPropertyId && (
          <PropertyDetails
            propertyId={selectedPropertyId}
            onEdit={handleEditFromDetails}
            onBack={handleBackToList}
          />
        )}
      </div>
    </div>
  );
};