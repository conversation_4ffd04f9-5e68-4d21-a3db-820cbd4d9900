import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import { Separator } from '../ui/separator'
import { 
  FileText, 
  Bell, 
  Printer, 
  Download, 
  Upload, 
  Keyboard, 
  Monitor,
  Minimize2,
  Maximize2,
  X,
  Eye,
  EyeOff
} from 'lucide-react'
import { useNativeFileSystem } from '../../hooks/useNativeFileSystem'
import { useNativeNotifications } from '../../hooks/useNativeNotifications'
import { usePrintService } from '../../hooks/usePrintService'
import { useNativeMenuActions } from '../../hooks/useNativeMenuActions'
import { useToast } from '../ui/use-toast'

export const NativeDesktopManager: React.FC = () => {
  const [isElectron, setIsElectron] = useState(false)
  const [platform, setPlatform] = useState<string>('')
  const [version, setVersion] = useState<string>('')
  const [notificationPermission, setNotificationPermission] = useState<string>('default')
  
  const { toast } = useToast()
  const fileSystem = useNativeFileSystem()
  const notifications = useNativeNotifications()
  const printService = usePrintService()

  useNativeMenuActions({
    onImportDocuments: handleImportDocuments,
    onExportReport: handleExportReport,
    onShowAbout: () => toast({ title: 'About EstatePulse', description: `Version ${version} - Cross-platform property management` }),
    onShowHelp: () => toast({ title: 'Help', description: 'Press F1 for keyboard shortcuts' }),
    onShowShortcuts: handleShowShortcuts,
    onQuickNew: () => toast({ title: 'Quick New', description: 'Choose what to create: Property, Lease, or Ticket' }),
    onGlobalSearch: () => toast({ title: 'Global Search', description: 'Search functionality activated' })
  })

  useEffect(() => {
    const checkElectronEnvironment = async () => {
      if (window.electronAPI) {
        setIsElectron(true)
        try {
          const [platformResult, versionResult] = await Promise.all([
            window.electronAPI.getPlatform(),
            window.electronAPI.getVersion()
          ])
          setPlatform(platformResult)
          setVersion(versionResult)
        } catch (error) {
          console.error('Failed to get app info:', error)
        }
      }
      
      setNotificationPermission(notifications.getPermission())
    }

    checkElectronEnvironment()
  }, [notifications])

  async function handleImportDocuments() {
    try {
      const files = await fileSystem.importDocuments()
      if (files.length > 0) {
        toast({
          title: 'Documents Imported',
          description: `Successfully imported ${files.length} document(s)`
        })
      } else {
        toast({
          title: 'Import Cancelled',
          description: 'No documents were selected'
        })
      }
    } catch (error) {
      toast({
        title: 'Import Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive'
      })
    }
  }

  async function handleExportReport() {
    try {
      const sampleReport = `EstatePulse Report
Generated: ${new Date().toLocaleString()}

Property Summary:
- Total Properties: 5
- Total Units: 150
- Occupancy Rate: 92%
- Monthly Revenue: $45,000

Recent Activity:
- New leases signed: 3
- Maintenance tickets: 7
- Payments processed: 142
`
      
      const result = await fileSystem.exportReport(sampleReport, 'estate-pulse-report.txt')
      if (result.success) {
        toast({
          title: 'Report Exported',
          description: 'Report has been saved successfully'
        })
      } else {
        toast({
          title: 'Export Failed',
          description: result.error || 'Unknown error occurred',
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: 'Export Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive'
      })
    }
  }

  async function handleTestNotification() {
    const success = await notifications.showNotification({
      title: 'EstatePulse Notification',
      body: 'This is a test notification from the desktop application',
      icon: '/electron-vite.svg'
    })
    
    if (success) {
      toast({
        title: 'Notification Sent',
        description: 'Test notification was displayed successfully'
      })
    } else {
      toast({
        title: 'Notification Failed',
        description: 'Could not display notification. Check permissions.',
        variant: 'destructive'
      })
    }
  }

  async function handlePrintTest() {
    const testHtml = `
      <html>
        <head><title>EstatePulse Test Print</title></head>
        <body style="font-family: Arial, sans-serif; padding: 20px;">
          <h1>EstatePulse Test Print</h1>
          <p>This is a test print from the EstatePulse desktop application.</p>
          <p>Generated on: ${new Date().toLocaleString()}</p>
          <p>Platform: ${platform}</p>
          <p>Version: ${version}</p>
        </body>
      </html>
    `
    
    try {
      const result = await printService.printDocument(testHtml)
      if (result.success) {
        toast({
          title: 'Print Successful',
          description: 'Test document was sent to printer'
        })
      } else {
        toast({
          title: 'Print Failed',
          description: result.error || 'Unknown error occurred',
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: 'Print Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive'
      })
    }
  }

  async function handlePrintToPDF() {
    const testHtml = `
      <html>
        <head><title>EstatePulse PDF Export</title></head>
        <body style="font-family: Arial, sans-serif; padding: 20px;">
          <h1>EstatePulse PDF Export</h1>
          <p>This is a test PDF export from the EstatePulse desktop application.</p>
          <p>Generated on: ${new Date().toLocaleString()}</p>
          <p>Platform: ${platform}</p>
          <p>Version: ${version}</p>
          <h2>Features Tested:</h2>
          <ul>
            <li>Native file system access</li>
            <li>System notifications</li>
            <li>Print functionality</li>
            <li>PDF generation</li>
            <li>Menu integration</li>
            <li>Keyboard shortcuts</li>
          </ul>
        </body>
      </html>
    `
    
    try {
      const result = await printService.downloadPDF(testHtml, 'estate-pulse-test.pdf')
      if (result.success) {
        toast({
          title: 'PDF Generated',
          description: 'Test PDF was generated and downloaded'
        })
      } else {
        toast({
          title: 'PDF Generation Failed',
          description: result.error || 'Unknown error occurred',
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: 'PDF Generation Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive'
      })
    }
  }

  function handleShowShortcuts() {
    const shortcuts = [
      'Ctrl/Cmd + N: New Property',
      'Ctrl/Cmd + Shift + L: New Lease',
      'Ctrl/Cmd + Shift + M: New Maintenance Ticket',
      'Ctrl/Cmd + I: Import Documents',
      'Ctrl/Cmd + E: Export Report',
      'Ctrl/Cmd + K: Global Search',
      'Ctrl/Cmd + 1-5: Navigate to sections',
      'Ctrl/Cmd + /: Show shortcuts',
      'F1: Show help'
    ]
    
    toast({
      title: 'Keyboard Shortcuts',
      description: shortcuts.join('\n')
    })
  }

  async function handleWindowAction(action: 'minimize' | 'maximize' | 'hide' | 'show') {
    if (!window.electronAPI) {
      toast({
        title: 'Not Available',
        description: 'Window controls are only available in desktop mode',
        variant: 'destructive'
      })
      return
    }

    try {
      switch (action) {
        case 'minimize':
          await window.electronAPI.minimizeWindow()
          break
        case 'maximize':
          await window.electronAPI.maximizeWindow()
          break
        case 'hide':
          await window.electronAPI.hideWindow()
          break
        case 'show':
          await window.electronAPI.showWindow()
          break
      }
      
      toast({
        title: 'Window Action',
        description: `Window ${action} executed successfully`
      })
    } catch (error) {
      toast({
        title: 'Window Action Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            Native Desktop Integration
          </CardTitle>
          <CardDescription>
            Test and manage native desktop features and integrations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <Badge variant={isElectron ? 'default' : 'secondary'}>
              {isElectron ? 'Desktop Mode' : 'Web Mode'}
            </Badge>
            {platform && <Badge variant="outline">Platform: {platform}</Badge>}
            {version && <Badge variant="outline">Version: {version}</Badge>}
          </div>
          
          <Separator />
          
          {/* File System Access */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <FileText className="h-4 w-4" />
              File System Access
            </h3>
            <div className="flex gap-2">
              <Button onClick={handleImportDocuments} variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import Documents
              </Button>
              <Button onClick={handleExportReport} variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
            </div>
          </div>
          
          <Separator />
          
          {/* System Notifications */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Bell className="h-4 w-4" />
              System Notifications
            </h3>
            <div className="flex items-center gap-4">
              <Badge variant={notificationPermission === 'granted' ? 'default' : 'destructive'}>
                Permission: {notificationPermission}
              </Badge>
              <Button onClick={handleTestNotification} variant="outline" size="sm">
                Test Notification
              </Button>
            </div>
          </div>
          
          <Separator />
          
          {/* Print Functionality */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Printer className="h-4 w-4" />
              Print Functionality
            </h3>
            <div className="flex gap-2">
              <Button onClick={handlePrintTest} variant="outline" size="sm">
                Test Print
              </Button>
              <Button onClick={handlePrintToPDF} variant="outline" size="sm">
                Generate PDF
              </Button>
            </div>
          </div>
          
          <Separator />
          
          {/* Window Management */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Monitor className="h-4 w-4" />
              Window Management
            </h3>
            <div className="flex gap-2">
              <Button 
                onClick={() => handleWindowAction('minimize')} 
                variant="outline" 
                size="sm"
                disabled={!isElectron}
              >
                <Minimize2 className="h-4 w-4 mr-2" />
                Minimize
              </Button>
              <Button 
                onClick={() => handleWindowAction('maximize')} 
                variant="outline" 
                size="sm"
                disabled={!isElectron}
              >
                <Maximize2 className="h-4 w-4 mr-2" />
                Maximize
              </Button>
              <Button 
                onClick={() => handleWindowAction('hide')} 
                variant="outline" 
                size="sm"
                disabled={!isElectron}
              >
                <EyeOff className="h-4 w-4 mr-2" />
                Hide
              </Button>
              <Button 
                onClick={() => handleWindowAction('show')} 
                variant="outline" 
                size="sm"
                disabled={!isElectron}
              >
                <Eye className="h-4 w-4 mr-2" />
                Show
              </Button>
            </div>
          </div>
          
          <Separator />
          
          {/* Keyboard Shortcuts */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Keyboard className="h-4 w-4" />
              Keyboard Shortcuts
            </h3>
            <Button onClick={handleShowShortcuts} variant="outline" size="sm">
              Show All Shortcuts
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}