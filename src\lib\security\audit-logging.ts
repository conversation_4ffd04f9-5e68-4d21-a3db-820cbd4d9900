// Audit logging system for tracking sensitive operations
export enum AuditEventType {
  // Authentication events
  USER_LOGIN = 'user_login',
  USER_LOGOUT = 'user_logout',
  PASSWORD_CHANGE = 'password_change',
  PASSWORD_RESET = 'password_reset',
  ACCOUNT_LOCKED = 'account_locked',
  ACCOUNT_UNLOCKED = 'account_unlocked',
  
  // User management
  USER_CREATED = 'user_created',
  USER_UPDATED = 'user_updated',
  USER_DELETED = 'user_deleted',
  ROLE_ASSIGNED = 'role_assigned',
  ROLE_REVOKED = 'role_revoked',
  PERMISSIONS_CHANGED = 'permissions_changed',
  
  // Property management
  PROPERTY_CREATED = 'property_created',
  PROPERTY_UPDATED = 'property_updated',
  PROPERTY_DELETED = 'property_deleted',
  UNIT_CREATED = 'unit_created',
  UNIT_UPDATED = 'unit_updated',
  UNIT_DELETED = 'unit_deleted',
  
  // Financial operations
  PAYMENT_PROCESSED = 'payment_processed',
  PAYMENT_REFUNDED = 'payment_refunded',
  INVOICE_CREATED = 'invoice_created',
  INVOICE_UPDATED = 'invoice_updated',
  INVOICE_DELETED = 'invoice_deleted',
  RENT_ROLL_GENERATED = 'rent_roll_generated',
  
  // Lease management
  LEASE_CREATED = 'lease_created',
  LEASE_UPDATED = 'lease_updated',
  LEASE_TERMINATED = 'lease_terminated',
  LEASE_RENEWED = 'lease_renewed',
  ESIGNATURE_SENT = 'esignature_sent',
  ESIGNATURE_COMPLETED = 'esignature_completed',
  
  // Document management
  DOCUMENT_UPLOADED = 'document_uploaded',
  DOCUMENT_DOWNLOADED = 'document_downloaded',
  DOCUMENT_DELETED = 'document_deleted',
  DOCUMENT_SHARED = 'document_shared',
  
  // Compliance and KYC
  KYC_VERIFICATION_STARTED = 'kyc_verification_started',
  KYC_VERIFICATION_COMPLETED = 'kyc_verification_completed',
  KYC_VERIFICATION_FAILED = 'kyc_verification_failed',
  COMPLIANCE_REPORT_GENERATED = 'compliance_report_generated',
  
  // System administration
  SYSTEM_BACKUP_CREATED = 'system_backup_created',
  SYSTEM_RESTORE_PERFORMED = 'system_restore_performed',
  CONFIGURATION_CHANGED = 'configuration_changed',
  BULK_OPERATION_PERFORMED = 'bulk_operation_performed',
  
  // Security events
  SECURITY_BREACH_DETECTED = 'security_breach_detected',
  SUSPICIOUS_LOGIN_ATTEMPT = 'suspicious_login_attempt',
  DATA_EXPORT_PERFORMED = 'data_export_performed',
  PRIVACY_SETTINGS_CHANGED = 'privacy_settings_changed',
  
  // Communication
  BULK_MESSAGE_SENT = 'bulk_message_sent',
  NOTIFICATION_SENT = 'notification_sent',
  EMAIL_SENT = 'email_sent',
  SMS_SENT = 'sms_sent'
}

export enum AuditSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface AuditEvent {
  id: string;
  type: AuditEventType;
  severity: AuditSeverity;
  timestamp: number;
  userId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  resource?: string; // What was affected (property ID, user ID, etc.)
  resourceType?: string; // Type of resource (property, user, lease, etc.)
  action: string; // Human-readable action description
  details: Record<string, any>; // Additional context
  oldValues?: Record<string, any>; // Previous values for updates
  newValues?: Record<string, any>; // New values for updates
  success: boolean;
  errorMessage?: string;
  metadata?: Record<string, any>; // Additional metadata
}

export interface AuditFilter {
  userId?: string;
  sessionId?: string;
  type?: AuditEventType;
  severity?: AuditSeverity;
  resource?: string;
  resourceType?: string;
  startDate?: number;
  endDate?: number;
  success?: boolean;
  limit?: number;
  offset?: number;
}

export class AuditLogger {
  private static instance: AuditLogger;
  private events: AuditEvent[] = [];
  private readonly maxEvents = 10000; // Keep last 10k events in memory
  private readonly persistenceKey = 'audit_events';

  constructor() {
    this.loadPersistedEvents();
    this.startPeriodicPersistence();
  }

  static getInstance(): AuditLogger {
    if (!this.instance) {
      this.instance = new AuditLogger();
    }
    return this.instance;
  }

  /**
   * Log an audit event
   */
  async logEvent(event: Omit<AuditEvent, 'id' | 'timestamp'>): Promise<void> {
    const auditEvent: AuditEvent = {
      ...event,
      id: crypto.randomUUID(),
      timestamp: Date.now()
    };

    // Add to in-memory storage
    this.events.unshift(auditEvent);

    // Maintain size limit
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(0, this.maxEvents);
    }

    // Log to console for development
    console.log('Audit Event:', auditEvent);

    // In production, you would also send to external audit service
    await this.sendToExternalAuditService(auditEvent);
  }

  /**
   * Log user authentication event
   */
  async logAuthEvent(
    type: AuditEventType,
    userId: string,
    sessionId: string,
    ipAddress: string,
    userAgent: string,
    success: boolean,
    details: Record<string, any> = {},
    errorMessage?: string
  ): Promise<void> {
    await this.logEvent({
      type,
      severity: success ? AuditSeverity.LOW : AuditSeverity.MEDIUM,
      userId,
      sessionId,
      ipAddress,
      userAgent,
      action: this.getActionDescription(type),
      details,
      success,
      errorMessage
    });
  }

  /**
   * Log data modification event
   */
  async logDataModification(
    type: AuditEventType,
    userId: string,
    sessionId: string,
    resource: string,
    resourceType: string,
    action: string,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logEvent({
      type,
      severity: this.getSeverityForDataModification(type),
      userId,
      sessionId,
      resource,
      resourceType,
      action,
      oldValues,
      newValues,
      details,
      success: true
    });
  }

  /**
   * Log financial operation
   */
  async logFinancialOperation(
    type: AuditEventType,
    userId: string,
    sessionId: string,
    amount: number,
    currency: string,
    resource: string,
    action: string,
    success: boolean,
    details: Record<string, any> = {},
    errorMessage?: string
  ): Promise<void> {
    await this.logEvent({
      type,
      severity: AuditSeverity.HIGH,
      userId,
      sessionId,
      resource,
      resourceType: 'financial',
      action,
      details: {
        ...details,
        amount,
        currency
      },
      success,
      errorMessage
    });
  }

  /**
   * Log security event
   */
  async logSecurityEvent(
    type: AuditEventType,
    severity: AuditSeverity,
    userId: string | undefined,
    sessionId: string | undefined,
    ipAddress: string,
    action: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logEvent({
      type,
      severity,
      userId,
      sessionId,
      ipAddress,
      action,
      details,
      success: false // Security events are typically failures/alerts
    });
  }

  /**
   * Log compliance event
   */
  async logComplianceEvent(
    type: AuditEventType,
    userId: string,
    sessionId: string,
    resource: string,
    action: string,
    complianceType: string,
    success: boolean,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logEvent({
      type,
      severity: AuditSeverity.MEDIUM,
      userId,
      sessionId,
      resource,
      resourceType: 'compliance',
      action,
      details: {
        ...details,
        complianceType
      },
      success
    });
  }

  /**
   * Get audit events with filtering
   */
  getEvents(filter: AuditFilter = {}): AuditEvent[] {
    let filteredEvents = [...this.events];

    // Apply filters
    if (filter.userId) {
      filteredEvents = filteredEvents.filter(event => event.userId === filter.userId);
    }

    if (filter.sessionId) {
      filteredEvents = filteredEvents.filter(event => event.sessionId === filter.sessionId);
    }

    if (filter.type) {
      filteredEvents = filteredEvents.filter(event => event.type === filter.type);
    }

    if (filter.severity) {
      filteredEvents = filteredEvents.filter(event => event.severity === filter.severity);
    }

    if (filter.resource) {
      filteredEvents = filteredEvents.filter(event => event.resource === filter.resource);
    }

    if (filter.resourceType) {
      filteredEvents = filteredEvents.filter(event => event.resourceType === filter.resourceType);
    }

    if (filter.startDate) {
      filteredEvents = filteredEvents.filter(event => event.timestamp >= filter.startDate!);
    }

    if (filter.endDate) {
      filteredEvents = filteredEvents.filter(event => event.timestamp <= filter.endDate!);
    }

    if (filter.success !== undefined) {
      filteredEvents = filteredEvents.filter(event => event.success === filter.success);
    }

    // Apply pagination
    const offset = filter.offset || 0;
    const limit = filter.limit || 100;
    
    return filteredEvents.slice(offset, offset + limit);
  }

  /**
   * Get audit trail for specific resource
   */
  getResourceAuditTrail(resource: string, resourceType?: string): AuditEvent[] {
    return this.getEvents({ resource, resourceType });
  }

  /**
   * Get user activity log
   */
  getUserActivityLog(userId: string, limit: number = 100): AuditEvent[] {
    return this.getEvents({ userId, limit });
  }

  /**
   * Get security events
   */
  getSecurityEvents(severity?: AuditSeverity): AuditEvent[] {
    const securityTypes = [
      AuditEventType.SECURITY_BREACH_DETECTED,
      AuditEventType.SUSPICIOUS_LOGIN_ATTEMPT,
      AuditEventType.ACCOUNT_LOCKED,
      AuditEventType.PASSWORD_RESET
    ];

    return this.events.filter(event => {
      const isSecurityEvent = securityTypes.includes(event.type);
      const matchesSeverity = !severity || event.severity === severity;
      return isSecurityEvent && matchesSeverity;
    });
  }

  /**
   * Generate audit report
   */
  generateAuditReport(filter: AuditFilter = {}): AuditReport {
    const events = this.getEvents(filter);
    
    const report: AuditReport = {
      generatedAt: Date.now(),
      filter,
      totalEvents: events.length,
      eventsByType: this.groupEventsByType(events),
      eventsBySeverity: this.groupEventsBySeverity(events),
      eventsByUser: this.groupEventsByUser(events),
      failedOperations: events.filter(e => !e.success).length,
      securityEvents: this.getSecurityEvents().length,
      timeRange: {
        start: Math.min(...events.map(e => e.timestamp)),
        end: Math.max(...events.map(e => e.timestamp))
      }
    };

    return report;
  }

  /**
   * Export audit events
   */
  exportEvents(filter: AuditFilter = {}, format: 'json' | 'csv' = 'json'): string {
    const events = this.getEvents(filter);
    
    if (format === 'csv') {
      return this.exportToCsv(events);
    }
    
    return JSON.stringify(events, null, 2);
  }

  /**
   * Clear old audit events
   */
  clearOldEvents(maxAge: number = 90 * 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge;
    this.events = this.events.filter(event => event.timestamp > cutoff);
    this.persistEvents();
  }

  /**
   * Get action description for audit event type
   */
  private getActionDescription(type: AuditEventType): string {
    const descriptions: Record<AuditEventType, string> = {
      [AuditEventType.USER_LOGIN]: 'User logged in',
      [AuditEventType.USER_LOGOUT]: 'User logged out',
      [AuditEventType.PASSWORD_CHANGE]: 'Password changed',
      [AuditEventType.PASSWORD_RESET]: 'Password reset requested',
      [AuditEventType.ACCOUNT_LOCKED]: 'Account locked',
      [AuditEventType.ACCOUNT_UNLOCKED]: 'Account unlocked',
      [AuditEventType.USER_CREATED]: 'User account created',
      [AuditEventType.USER_UPDATED]: 'User account updated',
      [AuditEventType.USER_DELETED]: 'User account deleted',
      [AuditEventType.ROLE_ASSIGNED]: 'Role assigned to user',
      [AuditEventType.ROLE_REVOKED]: 'Role revoked from user',
      [AuditEventType.PERMISSIONS_CHANGED]: 'User permissions changed',
      [AuditEventType.PROPERTY_CREATED]: 'Property created',
      [AuditEventType.PROPERTY_UPDATED]: 'Property updated',
      [AuditEventType.PROPERTY_DELETED]: 'Property deleted',
      [AuditEventType.UNIT_CREATED]: 'Unit created',
      [AuditEventType.UNIT_UPDATED]: 'Unit updated',
      [AuditEventType.UNIT_DELETED]: 'Unit deleted',
      [AuditEventType.PAYMENT_PROCESSED]: 'Payment processed',
      [AuditEventType.PAYMENT_REFUNDED]: 'Payment refunded',
      [AuditEventType.INVOICE_CREATED]: 'Invoice created',
      [AuditEventType.INVOICE_UPDATED]: 'Invoice updated',
      [AuditEventType.INVOICE_DELETED]: 'Invoice deleted',
      [AuditEventType.RENT_ROLL_GENERATED]: 'Rent roll generated',
      [AuditEventType.LEASE_CREATED]: 'Lease created',
      [AuditEventType.LEASE_UPDATED]: 'Lease updated',
      [AuditEventType.LEASE_TERMINATED]: 'Lease terminated',
      [AuditEventType.LEASE_RENEWED]: 'Lease renewed',
      [AuditEventType.ESIGNATURE_SENT]: 'E-signature request sent',
      [AuditEventType.ESIGNATURE_COMPLETED]: 'E-signature completed',
      [AuditEventType.DOCUMENT_UPLOADED]: 'Document uploaded',
      [AuditEventType.DOCUMENT_DOWNLOADED]: 'Document downloaded',
      [AuditEventType.DOCUMENT_DELETED]: 'Document deleted',
      [AuditEventType.DOCUMENT_SHARED]: 'Document shared',
      [AuditEventType.KYC_VERIFICATION_STARTED]: 'KYC verification started',
      [AuditEventType.KYC_VERIFICATION_COMPLETED]: 'KYC verification completed',
      [AuditEventType.KYC_VERIFICATION_FAILED]: 'KYC verification failed',
      [AuditEventType.COMPLIANCE_REPORT_GENERATED]: 'Compliance report generated',
      [AuditEventType.SYSTEM_BACKUP_CREATED]: 'System backup created',
      [AuditEventType.SYSTEM_RESTORE_PERFORMED]: 'System restore performed',
      [AuditEventType.CONFIGURATION_CHANGED]: 'System configuration changed',
      [AuditEventType.BULK_OPERATION_PERFORMED]: 'Bulk operation performed',
      [AuditEventType.SECURITY_BREACH_DETECTED]: 'Security breach detected',
      [AuditEventType.SUSPICIOUS_LOGIN_ATTEMPT]: 'Suspicious login attempt',
      [AuditEventType.DATA_EXPORT_PERFORMED]: 'Data export performed',
      [AuditEventType.PRIVACY_SETTINGS_CHANGED]: 'Privacy settings changed',
      [AuditEventType.BULK_MESSAGE_SENT]: 'Bulk message sent',
      [AuditEventType.NOTIFICATION_SENT]: 'Notification sent',
      [AuditEventType.EMAIL_SENT]: 'Email sent',
      [AuditEventType.SMS_SENT]: 'SMS sent'
    };

    return descriptions[type] || 'Unknown action';
  }

  /**
   * Get severity for data modification events
   */
  private getSeverityForDataModification(type: AuditEventType): AuditSeverity {
    const highSeverityEvents = [
      AuditEventType.USER_DELETED,
      AuditEventType.PROPERTY_DELETED,
      AuditEventType.LEASE_TERMINATED,
      AuditEventType.PAYMENT_REFUNDED,
      AuditEventType.DOCUMENT_DELETED
    ];

    const mediumSeverityEvents = [
      AuditEventType.USER_CREATED,
      AuditEventType.PROPERTY_CREATED,
      AuditEventType.LEASE_CREATED,
      AuditEventType.PAYMENT_PROCESSED,
      AuditEventType.ROLE_ASSIGNED,
      AuditEventType.PERMISSIONS_CHANGED
    ];

    if (highSeverityEvents.includes(type)) {
      return AuditSeverity.HIGH;
    } else if (mediumSeverityEvents.includes(type)) {
      return AuditSeverity.MEDIUM;
    }

    return AuditSeverity.LOW;
  }

  /**
   * Group events by type
   */
  private groupEventsByType(events: AuditEvent[]): Record<string, number> {
    return events.reduce((acc, event) => {
      acc[event.type] = (acc[event.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Group events by severity
   */
  private groupEventsBySeverity(events: AuditEvent[]): Record<string, number> {
    return events.reduce((acc, event) => {
      acc[event.severity] = (acc[event.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Group events by user
   */
  private groupEventsByUser(events: AuditEvent[]): Record<string, number> {
    return events.reduce((acc, event) => {
      if (event.userId) {
        acc[event.userId] = (acc[event.userId] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Export events to CSV format
   */
  private exportToCsv(events: AuditEvent[]): string {
    const headers = [
      'ID', 'Type', 'Severity', 'Timestamp', 'User ID', 'Session ID',
      'IP Address', 'Resource', 'Resource Type', 'Action', 'Success', 'Error Message'
    ];

    const rows = events.map(event => [
      event.id,
      event.type,
      event.severity,
      new Date(event.timestamp).toISOString(),
      event.userId || '',
      event.sessionId || '',
      event.ipAddress || '',
      event.resource || '',
      event.resourceType || '',
      event.action,
      event.success.toString(),
      event.errorMessage || ''
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  /**
   * Load persisted events from storage
   */
  private loadPersistedEvents(): void {
    try {
      const stored = localStorage.getItem(this.persistenceKey);
      if (stored) {
        this.events = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load persisted audit events:', error);
    }
  }

  /**
   * Persist events to storage
   */
  private persistEvents(): void {
    try {
      localStorage.setItem(this.persistenceKey, JSON.stringify(this.events));
    } catch (error) {
      console.error('Failed to persist audit events:', error);
    }
  }

  /**
   * Start periodic persistence
   */
  private startPeriodicPersistence(): void {
    setInterval(() => {
      this.persistEvents();
    }, 60 * 1000); // Persist every minute
  }

  /**
   * Send to external audit service (placeholder)
   */
  private async sendToExternalAuditService(event: AuditEvent): Promise<void> {
    // In production, this would send to external audit/SIEM system
    // For now, we'll just log critical events
    if (event.severity === AuditSeverity.CRITICAL) {
      console.warn('CRITICAL AUDIT EVENT:', event);
    }
  }
}

// Audit report interface
export interface AuditReport {
  generatedAt: number;
  filter: AuditFilter;
  totalEvents: number;
  eventsByType: Record<string, number>;
  eventsBySeverity: Record<string, number>;
  eventsByUser: Record<string, number>;
  failedOperations: number;
  securityEvents: number;
  timeRange: {
    start: number;
    end: number;
  };
}

// Convenience function to get audit logger instance
export const auditLogger = AuditLogger.getInstance();