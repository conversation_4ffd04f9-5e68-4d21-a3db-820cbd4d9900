import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { validateUnitData, sanitizeUnitData } from "./lib/validation";

// Get all units with optional filters
export const getUnits = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    status: v.optional(v.union(v.literal("vacant"), v.literal("occupied"), v.literal("maintenance"))),
    type: v.optional(v.union(v.literal("apartment"), v.literal("office"), v.literal("retail"), v.literal("parking"))),
  },
  handler: async (ctx, args) => {
    let units;

    if (args.propertyId) {
      units = await ctx.db
        .query("units")
        .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId!))
        .collect();
    } else {
      units = await ctx.db.query("units").collect();
    }

    if (args.status) {
      units = units.filter(unit => unit.status === args.status);
    }

    if (args.type) {
      units = units.filter(unit => unit.type === args.type);
    }

    return units;
  },
});

// Get unit by ID
export const getUnitById = query({
  args: { id: v.id("units") },
  handler: async (ctx, args) => {
    const unit = await ctx.db.get(args.id);
    if (!unit) {
      throw new Error("Unit not found");
    }
    return unit;
  },
});

// Alias for getUnitById for consistency
export const getById = getUnitById;

// Get units by property
export const getUnitsByProperty = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("units")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();
  },
});

// Create a new unit
export const createUnit = mutation({
  args: {
    propertyId: v.id("properties"),
    unitNumber: v.string(),
    type: v.union(v.literal("apartment"), v.literal("office"), v.literal("retail"), v.literal("parking")),
    size: v.number(),
    bedrooms: v.optional(v.number()),
    bathrooms: v.optional(v.number()),
    rent: v.number(),
    deposit: v.number(),
    amenities: v.optional(v.array(v.string())),
    description: v.optional(v.string()),
    images: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    // Validate and sanitize input
    const sanitizedData = sanitizeUnitData(args);
    validateUnitData(sanitizedData);

    // Check if property exists
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Property not found");
    }

    // Check if unit number already exists in this property
    const existingUnit = await ctx.db
      .query("units")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .filter((q) => q.eq(q.field("unitNumber"), sanitizedData.unitNumber))
      .first();

    if (existingUnit) {
      throw new Error("Unit number already exists in this property");
    }

    const now = Date.now();

    const unitId = await ctx.db.insert("units", {
      propertyId: sanitizedData.propertyId,
      unitNumber: sanitizedData.unitNumber,
      type: sanitizedData.type,
      size: sanitizedData.size,
      bedrooms: sanitizedData.bedrooms,
      bathrooms: sanitizedData.bathrooms,
      rent: sanitizedData.rent,
      deposit: sanitizedData.deposit,
      status: "vacant",
      amenities: sanitizedData.amenities || [],
      description: sanitizedData.description,
      images: sanitizedData.images || [],
      createdAt: now,
      updatedAt: now,
    });

    return unitId;
  },
});

// Update unit
export const updateUnit = mutation({
  args: {
    id: v.id("units"),
    unitNumber: v.optional(v.string()),
    type: v.optional(v.union(v.literal("apartment"), v.literal("office"), v.literal("retail"), v.literal("parking"))),
    size: v.optional(v.number()),
    bedrooms: v.optional(v.number()),
    bathrooms: v.optional(v.number()),
    rent: v.optional(v.number()),
    deposit: v.optional(v.number()),
    status: v.optional(v.union(v.literal("vacant"), v.literal("occupied"), v.literal("maintenance"))),
    amenities: v.optional(v.array(v.string())),
    description: v.optional(v.string()),
    images: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;

    // Check if unit exists
    const existingUnit = await ctx.db.get(id);
    if (!existingUnit) {
      throw new Error("Unit not found");
    }

    // Validate updates if provided
    if (Object.keys(updates).length > 0) {
      const dataToValidate = {
        unitNumber: updates.unitNumber || existingUnit.unitNumber,
        type: updates.type || existingUnit.type,
        size: updates.size || existingUnit.size,
        rent: updates.rent || existingUnit.rent,
        deposit: updates.deposit || existingUnit.deposit,
      };
      validateUnitData(dataToValidate);
    }

    // Check if unit number is being changed and doesn't conflict
    if (updates.unitNumber && updates.unitNumber !== existingUnit.unitNumber) {
      const conflictingUnit = await ctx.db
        .query("units")
        .withIndex("by_property", (q) => q.eq("propertyId", existingUnit.propertyId))
        .filter((q) => q.eq(q.field("unitNumber"), updates.unitNumber))
        .first();

      if (conflictingUnit) {
        throw new Error("Unit number already exists in this property");
      }
    }

    // Sanitize updates
    const sanitizedUpdates = sanitizeUnitData(updates);

    await ctx.db.patch(id, {
      ...sanitizedUpdates,
      updatedAt: Date.now(),
    });

    return id;
  },
});

// Update unit status
export const updateUnitStatus = mutation({
  args: {
    id: v.id("units"),
    status: v.union(v.literal("vacant"), v.literal("occupied"), v.literal("maintenance")),
  },
  handler: async (ctx, args) => {
    const unit = await ctx.db.get(args.id);
    if (!unit) {
      throw new Error("Unit not found");
    }

    // Additional validation for status changes
    if (args.status === "occupied") {
      // Check if there's an active lease for this unit
      const activeLease = await ctx.db
        .query("leases")
        .withIndex("by_unit", (q) => q.eq("unitId", args.id))
        .filter((q) => q.eq(q.field("status"), "active"))
        .first();

      if (!activeLease) {
        throw new Error("Cannot mark unit as occupied without an active lease");
      }
    }

    await ctx.db.patch(args.id, {
      status: args.status,
      updatedAt: Date.now(),
    });

    return args.id;
  },
});

// Delete unit
export const deleteUnit = mutation({
  args: { id: v.id("units") },
  handler: async (ctx, args) => {
    const unit = await ctx.db.get(args.id);
    if (!unit) {
      throw new Error("Unit not found");
    }

    // Check if unit has active leases
    const activeLeases = await ctx.db
      .query("leases")
      .withIndex("by_unit", (q) => q.eq("unitId", args.id))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    if (activeLeases.length > 0) {
      throw new Error("Cannot delete unit with active leases");
    }

    // Check if unit has open maintenance tickets
    const openTickets = await ctx.db
      .query("maintenanceTickets")
      .filter((q) => q.and(
        q.eq(q.field("unitId"), args.id),
        q.neq(q.field("status"), "closed")
      ))
      .collect();

    if (openTickets.length > 0) {
      throw new Error("Cannot delete unit with open maintenance tickets");
    }

    await ctx.db.delete(args.id);
    return args.id;
  },
});

// Get unit occupancy analytics
export const getUnitOccupancyAnalytics = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    const units = await ctx.db
      .query("units")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();

    const totalUnits = units.length;
    const occupiedUnits = units.filter(unit => unit.status === "occupied").length;
    const vacantUnits = units.filter(unit => unit.status === "vacant").length;
    const maintenanceUnits = units.filter(unit => unit.status === "maintenance").length;

    const occupancyRate = totalUnits > 0 ? (occupiedUnits / totalUnits) * 100 : 0;

    // Group by unit type
    const unitsByType = units.reduce((acc, unit) => {
      if (!acc[unit.type]) {
        acc[unit.type] = { total: 0, occupied: 0, vacant: 0, maintenance: 0 };
      }
      acc[unit.type].total++;
      acc[unit.type][unit.status]++;
      return acc;
    }, {} as Record<string, any>);

    return {
      totalUnits,
      occupiedUnits,
      vacantUnits,
      maintenanceUnits,
      occupancyRate,
      unitsByType,
    };
  },
});

// Get available units for lease
export const getAvailableUnits = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("units")
      .withIndex("by_property_status", (q) =>
        q.eq("propertyId", args.propertyId).eq("status", "vacant")
      )
      .collect();
  },
});

// Bulk update unit statuses
export const bulkUpdateUnitStatus = mutation({
  args: {
    unitIds: v.array(v.id("units")),
    status: v.union(v.literal("vacant"), v.literal("occupied"), v.literal("maintenance")),
  },
  handler: async (ctx, args) => {
    const results = [];

    for (const unitId of args.unitIds) {
      try {
        const unit = await ctx.db.get(unitId);
        if (!unit) {
          results.push({ unitId, success: false, error: "Unit not found" });
          continue;
        }

        await ctx.db.patch(unitId, {
          status: args.status,
          updatedAt: Date.now(),
        });

        results.push({ unitId, success: true });
      } catch (error) {
        results.push({
          unitId,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error"
        });
      }
    }

    return results;
  },
});