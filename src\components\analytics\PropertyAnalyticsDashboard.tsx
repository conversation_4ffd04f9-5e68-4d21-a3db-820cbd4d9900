import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Select } from '../ui/select';

interface PropertyAnalyticsDashboardProps {
  propertyId: Id<"properties">;
}

export const PropertyAnalyticsDashboard: React.FC<PropertyAnalyticsDashboardProps> = ({
  propertyId,
}) => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  
  const property = useQuery(api.properties.getPropertyById, { id: propertyId });
  const analytics = useQuery(api.properties.getPropertyAnalytics, { propertyId });
  const unitAnalytics = useQuery(api.units.getUnitOccupancyAnalytics, { propertyId });

  if (!property || !analytics || !unitAnalytics) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: property.settings?.currency || 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const calculateRevenuePotential = () => {
    const totalPotentialRevenue = Object.values(unitAnalytics.unitsByType).reduce(
      (total: number, typeData: any) => {
        return total + (typeData.total * (analytics.totalMonthlyRevenue / analytics.activeLeases || 0));
      },
      0
    );
    return totalPotentialRevenue;
  };

  const getOccupancyTrend = () => {
    // This would typically come from historical data
    // For now, we'll simulate some trend data
    return analytics.occupancyRate > 80 ? 'up' : analytics.occupancyRate > 60 ? 'stable' : 'down';
  };

  const getRevenueTrend = () => {
    // This would typically come from historical data
    // For now, we'll simulate based on occupancy
    return analytics.occupancyRate > 75 ? 'up' : 'stable';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Property Analytics</h2>
          <p className="text-gray-600">{property.name} Performance Dashboard</p>
        </div>
        <div>
          <Select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </Select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Occupancy Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-3xl font-bold text-green-600">
                {analytics.occupancyRate.toFixed(1)}%
              </div>
              <div className={`text-sm ${
                getOccupancyTrend() === 'up' ? 'text-green-600' : 
                getOccupancyTrend() === 'down' ? 'text-red-600' : 'text-gray-600'
              }`}>
                {getOccupancyTrend() === 'up' ? '↗' : getOccupancyTrend() === 'down' ? '↘' : '→'}
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              {analytics.occupiedUnits} of {analytics.totalUnits} units occupied
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Monthly Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-3xl font-bold text-blue-600">
                {formatCurrency(analytics.totalMonthlyRevenue)}
              </div>
              <div className={`text-sm ${
                getRevenueTrend() === 'up' ? 'text-green-600' : 'text-gray-600'
              }`}>
                {getRevenueTrend() === 'up' ? '↗' : '→'}
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              From {analytics.activeLeases} active leases
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Revenue Potential</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-600">
              {formatCurrency(calculateRevenuePotential())}
            </div>
            <p className="text-sm text-gray-600 mt-1">
              At 100% occupancy
            </p>
            <p className="text-xs text-purple-600 mt-1">
              +{formatCurrency(calculateRevenuePotential() - analytics.totalMonthlyRevenue)} potential
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Avg. Rent per Sq Ft</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-indigo-600">
              {analytics.totalUnits > 0 
                ? formatCurrency(analytics.totalMonthlyRevenue / analytics.totalUnits / 100)
                : formatCurrency(0)
              }
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Per square foot
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Unit Type Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Unit Type Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(unitAnalytics.unitsByType).map(([type, data]: [string, any]) => (
                <div key={type} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium capitalize">{type}</span>
                    <span className="text-sm text-gray-600">
                      {data.occupied}/{data.total} occupied
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{
                        width: `${data.total > 0 ? (data.occupied / data.total) * 100 : 0}%`,
                      }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{data.total > 0 ? ((data.occupied / data.total) * 100).toFixed(1) : 0}% occupied</span>
                    <span>{data.vacant} vacant, {data.maintenance} maintenance</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Unit Status Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-green-800">Occupied Units</p>
                  <p className="text-2xl font-bold text-green-600">{unitAnalytics.occupiedUnits}</p>
                </div>
                <div className="text-green-600">
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>

              <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-yellow-800">Vacant Units</p>
                  <p className="text-2xl font-bold text-yellow-600">{unitAnalytics.vacantUnits}</p>
                </div>
                <div className="text-yellow-600">
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>

              <div className="flex items-center justify-between p-4 bg-orange-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-orange-800">Maintenance Units</p>
                  <p className="text-2xl font-bold text-orange-600">{unitAnalytics.maintenanceUnits}</p>
                </div>
                <div className="text-orange-600">
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Financial Performance</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Current Monthly Revenue</span>
              <span className="font-semibold">{formatCurrency(analytics.totalMonthlyRevenue)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Potential Monthly Revenue</span>
              <span className="font-semibold">{formatCurrency(calculateRevenuePotential())}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Revenue Efficiency</span>
              <span className="font-semibold text-blue-600">
                {calculateRevenuePotential() > 0 
                  ? ((analytics.totalMonthlyRevenue / calculateRevenuePotential()) * 100).toFixed(1)
                  : 0
                }%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Avg. Revenue per Unit</span>
              <span className="font-semibold">
                {analytics.totalUnits > 0 
                  ? formatCurrency(analytics.totalMonthlyRevenue / analytics.totalUnits)
                  : formatCurrency(0)
                }
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Operational Metrics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Units</span>
              <span className="font-semibold">{analytics.totalUnits}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Active Leases</span>
              <span className="font-semibold">{analytics.activeLeases}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Lease-to-Unit Ratio</span>
              <span className="font-semibold">
                {analytics.totalUnits > 0 
                  ? ((analytics.activeLeases / analytics.totalUnits) * 100).toFixed(1)
                  : 0
                }%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Open Maintenance Tickets</span>
              <span className={`font-semibold ${
                analytics.openMaintenanceTickets > 5 ? 'text-red-600' : 
                analytics.openMaintenanceTickets > 2 ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {analytics.openMaintenanceTickets}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Property Health Score</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">
                {(() => {
                  const occupancyScore = analytics.occupancyRate;
                  const maintenanceScore = Math.max(0, 100 - (analytics.openMaintenanceTickets * 10));
                  const overallScore = (occupancyScore + maintenanceScore) / 2;
                  return Math.round(overallScore);
                })()}
              </div>
              <div className="text-sm text-gray-600 mb-4">Overall Health Score</div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Occupancy</span>
                  <span className="font-medium">{analytics.occupancyRate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Maintenance</span>
                  <span className={`font-medium ${
                    analytics.openMaintenanceTickets <= 2 ? 'text-green-600' : 
                    analytics.openMaintenanceTickets <= 5 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {analytics.openMaintenanceTickets <= 2 ? 'Good' : 
                     analytics.openMaintenanceTickets <= 5 ? 'Fair' : 'Poor'}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.occupancyRate < 80 && (
              <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg">
                <div className="text-yellow-600 mt-0.5">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium text-yellow-800">Improve Occupancy Rate</p>
                  <p className="text-sm text-yellow-700">
                    Your occupancy rate is {analytics.occupancyRate.toFixed(1)}%. Consider marketing campaigns or rent adjustments to attract tenants.
                  </p>
                </div>
              </div>
            )}

            {analytics.openMaintenanceTickets > 5 && (
              <div className="flex items-start space-x-3 p-3 bg-red-50 rounded-lg">
                <div className="text-red-600 mt-0.5">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium text-red-800">Address Maintenance Backlog</p>
                  <p className="text-sm text-red-700">
                    You have {analytics.openMaintenanceTickets} open maintenance tickets. Consider prioritizing urgent repairs to maintain tenant satisfaction.
                  </p>
                </div>
              </div>
            )}

            {analytics.occupancyRate >= 90 && analytics.openMaintenanceTickets <= 2 && (
              <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
                <div className="text-green-600 mt-0.5">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium text-green-800">Excellent Performance</p>
                  <p className="text-sm text-green-700">
                    Your property is performing exceptionally well with high occupancy and low maintenance issues. Consider expanding your portfolio.
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};