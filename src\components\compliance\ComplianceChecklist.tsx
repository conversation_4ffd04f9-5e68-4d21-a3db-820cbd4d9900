import { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Textarea } from '../ui/textarea';
import { Switch } from '../ui/switch';
import { Progress } from '../ui/progress';
import { 
  CheckCircle, 
  Plus, 
  Edit, 
  Trash2, 
  Eye,
  AlertTriangle,
  Clock,
  XCircle,
  FileText,
  Shield,
  Building,
  Users,
  DollarSign,
  Scale,
  Home,
  AlertCircle
} from 'lucide-react';
import { Id } from '../../../convex/_generated/dataModel';

interface ComplianceChecklistProps {
  propertyId?: string;
  entityType?: 'tenant' | 'vendor' | 'property' | 'lease';
  entityId?: string;
}

export function ComplianceChecklist({ propertyId, entityType, entityId }: ComplianceChecklistProps) {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedChecklist, setSelectedChecklist] = useState<string>('');
  const [checklistForm, setChecklistForm] = useState({
    name: '',
    description: '',
    entityType: entityType || 'tenant' as 'tenant' | 'vendor' | 'property' | 'lease',
    requirements: [] as Array<{
      id: string;
      title: string;
      description: string;
      category: 'kyc' | 'legal' | 'financial' | 'regulatory' | 'safety' | 'insurance';
      priority: 'low' | 'medium' | 'high' | 'critical';
      requiredDocuments: string[];
      isRequired: boolean;
      automationRules?: {
        autoCheck: boolean;
        checkFrequency?: 'daily' | 'weekly' | 'monthly';
        conditions?: string;
      };
    }>
  });

  // Fetch data
  const complianceChecklists = useQuery(
    api.compliance.getComplianceChecklists,
    propertyId ? { propertyId: propertyId as Id<"properties"> } : {}
  );

  const complianceStatus = useQuery(
    api.compliance.getComplianceStatus,
    entityId && entityType ? {
      entityId,
      entityType,
    } : "skip"
  );

  // Mutations
  const createChecklist = useMutation(api.compliance.createComplianceChecklist);
  const updateComplianceStatus = useMutation(api.compliance.updateComplianceStatus);
  const runComplianceCheck = useMutation(api.compliance.runComplianceCheck);

  const documentTypes = [
    'national_id', 'passport', 'drivers_license', 'birth_certificate',
    'proof_of_income', 'bank_statement', 'employment_letter',
    'business_registration', 'tax_certificate', 'insurance_certificate',
    'lease_agreement', 'property_title', 'other'
  ];

  const categories = [
    { value: 'kyc', label: 'KYC', icon: Users, color: 'text-blue-600' },
    { value: 'legal', label: 'Legal', icon: Scale, color: 'text-purple-600' },
    { value: 'financial', label: 'Financial', icon: DollarSign, color: 'text-green-600' },
    { value: 'regulatory', label: 'Regulatory', icon: Shield, color: 'text-orange-600' },
    { value: 'safety', label: 'Safety', icon: AlertTriangle, color: 'text-red-600' },
    { value: 'insurance', label: 'Insurance', icon: Home, color: 'text-indigo-600' }
  ];

  const priorities = [
    { value: 'low', label: 'Low', color: 'text-gray-600 bg-gray-100' },
    { value: 'medium', label: 'Medium', color: 'text-blue-600 bg-blue-100' },
    { value: 'high', label: 'High', color: 'text-orange-600 bg-orange-100' },
    { value: 'critical', label: 'Critical', color: 'text-red-600 bg-red-100' }
  ];

  const handleCreateChecklist = async () => {
    if (!checklistForm.name || !checklistForm.description) return;

    try {
      await createChecklist({
        ...checklistForm,
        propertyId: propertyId as Id<"properties">,
        createdBy: "current-user" as Id<"users">, // Replace with actual user ID
      });

      setShowCreateForm(false);
      resetForm();
    } catch (error) {
      console.error('Failed to create checklist:', error);
    }
  };

  const resetForm = () => {
    setChecklistForm({
      name: '',
      description: '',
      entityType: entityType || 'tenant',
      requirements: []
    });
  };

  const addRequirement = () => {
    const newRequirement = {
      id: `req_${Date.now()}`,
      title: '',
      description: '',
      category: 'kyc' as const,
      priority: 'medium' as const,
      requiredDocuments: [],
      isRequired: true,
      automationRules: {
        autoCheck: false,
        checkFrequency: 'monthly' as const,
        conditions: ''
      }
    };

    setChecklistForm({
      ...checklistForm,
      requirements: [...checklistForm.requirements, newRequirement]
    });
  };

  const updateRequirement = (index: number, field: string, value: any) => {
    const updatedRequirements = [...checklistForm.requirements];
    updatedRequirements[index] = {
      ...updatedRequirements[index],
      [field]: value
    };
    setChecklistForm({
      ...checklistForm,
      requirements: updatedRequirements
    });
  };

  const removeRequirement = (index: number) => {
    setChecklistForm({
      ...checklistForm,
      requirements: checklistForm.requirements.filter((_, i) => i !== index)
    });
  };

  const handleRunCheck = async (checklistId: string) => {
    if (!entityId || !entityType) return;

    try {
      await runComplianceCheck({
        entityId,
        entityType,
        checklistId: checklistId as Id<"complianceChecklists">,
        propertyId: propertyId as Id<"properties">,
        triggeredBy: "current-user" as Id<"users">, // Replace with actual user ID
      });
    } catch (error) {
      console.error('Failed to run compliance check:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'non_compliant': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'in_review': return <Eye className="h-4 w-4 text-blue-500" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    const cat = categories.find(c => c.value === category);
    if (!cat) return FileText;
    return cat.icon;
  };

  const getCategoryColor = (category: string) => {
    const cat = categories.find(c => c.value === category);
    return cat?.color || 'text-gray-600';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-blue-600" />
            Compliance Checklists
          </h2>
          <p className="text-gray-600 mt-1">
            Manage compliance requirements and track status
          </p>
        </div>
        <Button 
          onClick={() => setShowCreateForm(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Checklist
        </Button>
      </div>

      {/* Entity Status (if viewing specific entity) */}
      {entityId && entityType && complianceStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(complianceStatus[0]?.overallStatus || 'pending')}
              Compliance Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            {complianceStatus.map((status) => (
              <div key={status._id} className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Overall Compliance Score</span>
                  <span className="text-lg font-bold">{Math.round(status.overallScore)}%</span>
                </div>
                <Progress value={status.overallScore} className="w-full" />
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Status</p>
                    <Badge className={`${status.overallStatus === 'compliant' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {status.overallStatus}
                    </Badge>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Last Review</p>
                    <p className="text-sm font-medium">
                      {new Date(status.lastReviewDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Next Review</p>
                    <p className="text-sm font-medium">
                      {new Date(status.nextReviewDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Alerts</p>
                    <p className="text-sm font-medium text-red-600">
                      {status.alerts?.length || 0}
                    </p>
                  </div>
                </div>

                {status.alerts && status.alerts.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Active Alerts</h4>
                    {status.alerts.map((alert, index) => (
                      <div key={index} className="p-2 bg-red-50 border border-red-200 rounded text-sm">
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                          {alert.message}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Checklists */}
      <div className="grid gap-4">
        {complianceChecklists?.map((checklist) => (
          <Card key={checklist._id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">{checklist.name}</CardTitle>
                  <CardDescription>{checklist.description}</CardDescription>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="secondary">{checklist.entityType}</Badge>
                    <span className="text-xs text-gray-500">
                      {checklist.requirements.length} requirements
                    </span>
                  </div>
                </div>
                <div className="flex gap-2">
                  {entityId && entityType && (
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleRunCheck(checklist._id)}
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Run Check
                    </Button>
                  )}
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {checklist.requirements.map((requirement) => {
                  const CategoryIcon = getCategoryIcon(requirement.category);
                  return (
                    <div key={requirement.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <CategoryIcon className={`h-5 w-5 ${getCategoryColor(requirement.category)}`} />
                        <div>
                          <p className="font-medium text-sm">{requirement.title}</p>
                          <p className="text-xs text-gray-500">{requirement.description}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge 
                              variant="outline" 
                              className={priorities.find(p => p.value === requirement.priority)?.color}
                            >
                              {requirement.priority}
                            </Badge>
                            {requirement.automationRules?.autoCheck && (
                              <Badge variant="outline" className="text-green-600 bg-green-50">
                                Auto-check
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      {requirement.isRequired && (
                        <Badge variant="destructive" className="text-xs">
                          Required
                        </Badge>
                      )}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )) || (
          <Card>
            <CardContent className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No compliance checklists found</p>
              <Button 
                variant="outline" 
                className="mt-2"
                onClick={() => setShowCreateForm(true)}
              >
                Create Your First Checklist
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Create Checklist Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">Create Compliance Checklist</h2>
              <p className="text-sm text-gray-600">Define compliance requirements for entities</p>
            </div>
            
            <div className="p-6 space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Checklist Name</Label>
                  <Input
                    id="name"
                    value={checklistForm.name}
                    onChange={(e) => setChecklistForm({...checklistForm, name: e.target.value})}
                    placeholder="e.g., Tenant KYC Requirements"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="entityType">Entity Type</Label>
                  <Select 
                    value={checklistForm.entityType} 
                    onValueChange={(value: 'tenant' | 'vendor' | 'property' | 'lease') => 
                      setChecklistForm({...checklistForm, entityType: value})
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tenant">Tenant</SelectItem>
                      <SelectItem value="vendor">Vendor</SelectItem>
                      <SelectItem value="property">Property</SelectItem>
                      <SelectItem value="lease">Lease</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={checklistForm.description}
                  onChange={(e) => setChecklistForm({...checklistForm, description: e.target.value})}
                  placeholder="Describe the purpose and scope of this checklist"
                />
              </div>

              {/* Requirements */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Requirements</Label>
                  <Button onClick={addRequirement} size="sm" variant="outline">
                    <Plus className="h-4 w-4 mr-1" />
                    Add Requirement
                  </Button>
                </div>

                {checklistForm.requirements.map((requirement, index) => (
                  <Card key={requirement.id} className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">Requirement {index + 1}</h4>
                        <Button 
                          size="sm" 
                          variant="ghost" 
                          onClick={() => removeRequirement(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Title</Label>
                          <Input
                            value={requirement.title}
                            onChange={(e) => updateRequirement(index, 'title', e.target.value)}
                            placeholder="Requirement title"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Category</Label>
                          <Select 
                            value={requirement.category} 
                            onValueChange={(value) => updateRequirement(index, 'category', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {categories.map((cat) => (
                                <SelectItem key={cat.value} value={cat.value}>
                                  {cat.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Description</Label>
                        <Textarea
                          value={requirement.description}
                          onChange={(e) => updateRequirement(index, 'description', e.target.value)}
                          placeholder="Detailed description of the requirement"
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Priority</Label>
                          <Select 
                            value={requirement.priority} 
                            onValueChange={(value) => updateRequirement(index, 'priority', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {priorities.map((priority) => (
                                <SelectItem key={priority.value} value={priority.value}>
                                  {priority.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex items-center space-x-2 pt-6">
                          <Switch
                            checked={requirement.isRequired}
                            onCheckedChange={(checked) => updateRequirement(index, 'isRequired', checked)}
                          />
                          <Label>Required</Label>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Required Documents</Label>
                        <div className="grid grid-cols-3 gap-2 max-h-32 overflow-y-auto">
                          {documentTypes.map((docType) => (
                            <label key={docType} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                checked={requirement.requiredDocuments.includes(docType)}
                                onChange={(e) => {
                                  const docs = requirement.requiredDocuments;
                                  if (e.target.checked) {
                                    updateRequirement(index, 'requiredDocuments', [...docs, docType]);
                                  } else {
                                    updateRequirement(index, 'requiredDocuments', docs.filter(d => d !== docType));
                                  }
                                }}
                              />
                              <span className="text-sm">{docType.replace('_', ' ')}</span>
                            </label>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={requirement.automationRules?.autoCheck || false}
                            onCheckedChange={(checked) => updateRequirement(index, 'automationRules', {
                              ...requirement.automationRules,
                              autoCheck: checked
                            })}
                          />
                          <Label>Enable Automatic Checking</Label>
                        </div>
                        
                        {requirement.automationRules?.autoCheck && (
                          <div className="grid grid-cols-2 gap-4 ml-6">
                            <div className="space-y-2">
                              <Label>Check Frequency</Label>
                              <Select 
                                value={requirement.automationRules.checkFrequency || 'monthly'} 
                                onValueChange={(value) => updateRequirement(index, 'automationRules', {
                                  ...requirement.automationRules,
                                  checkFrequency: value
                                })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="daily">Daily</SelectItem>
                                  <SelectItem value="weekly">Weekly</SelectItem>
                                  <SelectItem value="monthly">Monthly</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Conditions</Label>
                              <Input
                                value={requirement.automationRules.conditions || ''}
                                onChange={(e) => updateRequirement(index, 'automationRules', {
                                  ...requirement.automationRules,
                                  conditions: e.target.value
                                })}
                                placeholder="e.g., status == 'active'"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            <div className="p-6 border-t flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateChecklist}>
                <CheckCircle className="h-4 w-4 mr-2" />
                Create Checklist
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}