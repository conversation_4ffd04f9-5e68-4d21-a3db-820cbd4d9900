import { Id } from '../../convex/_generated/dataModel';

// E-signature service types
export interface ESignatureProvider {
  name: string;
  apiKey: string;
  baseUrl: string;
}

export interface SignatureRequest {
  documentId: string;
  documentUrl: string;
  signers: Signer[];
  title: string;
  message?: string;
  expiresAt?: Date;
}

export interface Signer {
  email: string;
  name: string;
  role: 'tenant' | 'landlord' | 'witness';
  order: number;
}

export interface SignatureStatus {
  requestId: string;
  status: 'pending' | 'signed' | 'declined' | 'expired' | 'error';
  signedAt?: Date;
  signedDocumentUrl?: string;
  signers: SignerStatus[];
}

export interface SignerStatus {
  email: string;
  status: 'pending' | 'signed' | 'declined';
  signedAt?: Date;
  ipAddress?: string;
}

export interface ESignatureWebhookPayload {
  eventType: 'signature_request_signed' | 'signature_request_declined' | 'signature_request_expired';
  requestId: string;
  documentId: string;
  signer: {
    email: string;
    name: string;
    signedAt: string;
    ipAddress: string;
  };
  signedDocumentUrl?: string;
}

// Mock e-signature service implementation
// In production, this would integrate with services like DocuSign, HelloSign, or Adobe Sign
export class ESignatureService {
  // private provider: ESignatureProvider;

  constructor(_provider: ESignatureProvider) {
    // this.provider = provider;
  }

  async createSignatureRequest(request: SignatureRequest): Promise<string> {
    // Mock implementation - in production this would call the actual API
    console.log('Creating signature request:', request);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Return mock request ID
    const requestId = `sig_req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // In production, this would:
    // 1. Upload the document to the e-signature service
    // 2. Create signature fields and positions
    // 3. Send signature requests to all signers
    // 4. Set up webhook notifications
    
    return requestId;
  }

  async getSignatureStatus(requestId: string): Promise<SignatureStatus> {
    // Mock implementation
    console.log('Getting signature status for:', requestId);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Return mock status - in production this would query the actual service
    return {
      requestId,
      status: 'pending',
      signers: [
        {
          email: '<EMAIL>',
          status: 'pending',
        },
        {
          email: '<EMAIL>',
          status: 'pending',
        }
      ]
    };
  }

  async cancelSignatureRequest(requestId: string): Promise<void> {
    // Mock implementation
    console.log('Cancelling signature request:', requestId);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // In production, this would cancel the signature request
  }

  async downloadSignedDocument(requestId: string): Promise<string> {
    // Mock implementation
    console.log('Downloading signed document for:', requestId);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Return mock signed document URL
    return `https://example.com/signed-documents/${requestId}.pdf`;
  }

  async resendSignatureRequest(_requestId: string, signerEmail: string): Promise<void> {
    // Mock implementation
    console.log('Resending signature request to:', signerEmail);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // In production, this would resend the signature request
  }

  // Webhook handler for processing signature events
  async handleWebhook(payload: ESignatureWebhookPayload): Promise<void> {
    console.log('Processing e-signature webhook:', payload);
    
    switch (payload.eventType) {
      case 'signature_request_signed':
        // Handle successful signature
        await this.handleSignatureCompleted(payload);
        break;
      
      case 'signature_request_declined':
        // Handle declined signature
        await this.handleSignatureDeclined(payload);
        break;
      
      case 'signature_request_expired':
        // Handle expired signature request
        await this.handleSignatureExpired(payload);
        break;
      
      default:
        console.warn('Unknown webhook event type:', payload.eventType);
    }
  }

  private async handleSignatureCompleted(payload: ESignatureWebhookPayload): Promise<void> {
    // In production, this would:
    // 1. Update the lease status in the database
    // 2. Download and store the signed document
    // 3. Send notifications to relevant parties
    // 4. Trigger any post-signature workflows
    
    console.log('Signature completed for document:', payload.documentId);
  }

  private async handleSignatureDeclined(payload: ESignatureWebhookPayload): Promise<void> {
    // In production, this would:
    // 1. Update the lease status to declined
    // 2. Notify the property manager
    // 3. Potentially trigger a new signature request
    
    console.log('Signature declined for document:', payload.documentId);
  }

  private async handleSignatureExpired(payload: ESignatureWebhookPayload): Promise<void> {
    // In production, this would:
    // 1. Update the lease status to expired
    // 2. Notify relevant parties
    // 3. Potentially create a new signature request
    
    console.log('Signature request expired for document:', payload.documentId);
  }
}

// Factory function to create e-signature service instance
export function createESignatureService(): ESignatureService {
  // In production, this would read from environment variables
  const provider: ESignatureProvider = {
    name: 'HelloSign', // or 'DocuSign', 'Adobe Sign', etc.
    apiKey: process.env.ESIGNATURE_API_KEY || 'mock-api-key',
    baseUrl: process.env.ESIGNATURE_BASE_URL || 'https://api.hellosign.com/v3',
  };

  return new ESignatureService(provider);
}

// Utility functions for e-signature integration
export const ESignatureUtils = {
  // Generate signature positions for different document types
  generateSignatureFields: (documentType: 'lease' | 'addendum' | 'notice') => {
    const baseFields = [
      {
        type: 'signature',
        page: 1,
        x: 100,
        y: 700,
        width: 200,
        height: 50,
        signer: 0, // Tenant
        required: true,
      },
      {
        type: 'date',
        page: 1,
        x: 320,
        y: 700,
        width: 100,
        height: 30,
        signer: 0,
        required: true,
      },
      {
        type: 'signature',
        page: 1,
        x: 100,
        y: 600,
        width: 200,
        height: 50,
        signer: 1, // Landlord
        required: true,
      },
      {
        type: 'date',
        page: 1,
        x: 320,
        y: 600,
        width: 100,
        height: 30,
        signer: 1,
        required: true,
      },
    ];

    // Add document-specific fields
    switch (documentType) {
      case 'lease':
        return [
          ...baseFields,
          {
            type: 'initial',
            page: 2,
            x: 50,
            y: 750,
            width: 50,
            height: 30,
            signer: 0,
            required: true,
          },
        ];
      default:
        return baseFields;
    }
  },

  // Validate signer information
  validateSigners: (signers: Signer[]): string[] => {
    const errors: string[] = [];

    if (signers.length === 0) {
      errors.push('At least one signer is required');
    }

    signers.forEach((signer, index) => {
      if (!signer.email || !signer.email.includes('@')) {
        errors.push(`Invalid email for signer ${index + 1}`);
      }
      if (!signer.name || signer.name.trim().length === 0) {
        errors.push(`Name is required for signer ${index + 1}`);
      }
      if (signer.order < 1) {
        errors.push(`Invalid signing order for signer ${index + 1}`);
      }
    });

    // Check for duplicate emails
    const emails = signers.map(s => s.email.toLowerCase());
    const duplicates = emails.filter((email, index) => emails.indexOf(email) !== index);
    if (duplicates.length > 0) {
      errors.push('Duplicate signer emails are not allowed');
    }

    return errors;
  },

  // Generate signature request message
  generateSignatureMessage: (_leaseId: Id<"leases">, propertyName: string, unitNumber: string) => {
    return `Please review and sign the lease agreement for ${propertyName}, Unit ${unitNumber}. 

This document contains the terms and conditions of your lease agreement. Please read through all sections carefully before signing.

If you have any questions, please contact the property management team.

Thank you!`;
  },

  // Calculate signature request expiration date
  calculateExpirationDate: (daysFromNow: number = 7): Date => {
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + daysFromNow);
    return expirationDate;
  },
};