/**
 * Contextual help tooltip component
 */
import React, { useState, useRef } from 'react';
import { useAccessibility } from '../../contexts/AccessibilityContext';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip';
import { Button } from '../ui/button';
import { useKeyboardNavigation, KEYBOARD_KEYS } from '../../lib/accessibility';

interface HelpTooltipProps {
  content: string;
  title?: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  children?: React.ReactNode;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function HelpTooltip({
  content,
  title,
  position = 'top',
  children,
  showIcon = true,
  size = 'sm',
  className = '',
}: HelpTooltipProps) {
  const { announceToScreenReader } = useAccessibility();
  const [isOpen, setIsOpen] = useState(false);
  const triggerRef = useRef<HTMLButtonElement>(null);

  const handleOpen = () => {
    setIsOpen(true);
    if (title) {
      announceToScreenReader(`Help: ${title}. ${content}`, 'polite');
    } else {
      announceToScreenReader(`Help: ${content}`, 'polite');
    }
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === KEYBOARD_KEYS.ESCAPE) {
      handleClose();
      triggerRef.current?.focus();
    }
  };

  const iconSize = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  }[size];

  return (
    <TooltipProvider>
      <Tooltip open={isOpen} onOpenChange={setIsOpen}>
        <TooltipTrigger asChild>
          {children || (
            <Button
              ref={triggerRef}
              variant="ghost"
              size="sm"
              className={`${iconSize} p-0 text-muted-foreground hover:text-foreground ${className}`}
              onClick={handleOpen}
              onKeyDown={handleKeyDown}
              aria-label={title ? `Help: ${title}` : 'Help'}
              aria-describedby={isOpen ? 'help-tooltip-content' : undefined}
            >
              {showIcon && (
                <svg
                  className={iconSize}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              )}
            </Button>
          )}
        </TooltipTrigger>
        
        <TooltipContent
          side={position}
          className="max-w-xs p-3"
          onKeyDown={handleKeyDown}
          id="help-tooltip-content"
          role="tooltip"
        >
          {title && (
            <div className="font-semibold text-sm mb-1">{title}</div>
          )}
          <div className="text-sm text-muted-foreground">{content}</div>
          <div className="text-xs text-muted-foreground mt-2 opacity-75">
            Press Escape to close
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Predefined help tooltips for common UI elements
export const PropertyHelpTooltips = {
  addProperty: (
    <HelpTooltip
      title="Add Property"
      content="Create a new property in your portfolio. You'll need to provide basic information like name, address, and property type. You can add units and upload images later."
    />
  ),
  
  propertyType: (
    <HelpTooltip
      title="Property Type"
      content="Select the type of property: Residential (apartments, houses), Commercial (offices, retail), or Mixed (combination of both)."
    />
  ),
  
  occupancyRate: (
    <HelpTooltip
      title="Occupancy Rate"
      content="The percentage of units that are currently occupied. This is calculated automatically based on active leases."
    />
  ),
  
  monthlyRevenue: (
    <HelpTooltip
      title="Monthly Revenue"
      content="Total rental income generated by this property per month, including all occupied units."
    />
  ),
};

export const LeaseHelpTooltips = {
  leaseStatus: (
    <HelpTooltip
      title="Lease Status"
      content="Active: Currently valid lease. Expired: Past end date. Terminated: Ended early by tenant or landlord."
    />
  ),
  
  securityDeposit: (
    <HelpTooltip
      title="Security Deposit"
      content="Amount held as security against damages or unpaid rent. Typically 1-3 months' rent depending on local regulations."
    />
  ),
  
  eSignature: (
    <HelpTooltip
      title="E-Signature"
      content="Digital signature status for the lease agreement. Pending: Awaiting signature. Signed: Completed by all parties. Expired: Signature request timed out."
    />
  ),
};

export const MaintenanceHelpTooltips = {
  slaDeadline: (
    <HelpTooltip
      title="SLA Deadline"
      content="Service Level Agreement deadline for completing this maintenance request. Based on priority level and property policies."
    />
  ),
  
  ticketPriority: (
    <HelpTooltip
      title="Priority Level"
      content="Low: Non-urgent issues. Medium: Important but not critical. High: Urgent repairs needed. Emergency: Immediate safety concerns."
    />
  ),
  
  vendorAssignment: (
    <HelpTooltip
      title="Vendor Assignment"
      content="Assign this ticket to a qualified vendor. The system will notify them and track their progress against SLA requirements."
    />
  ),
};

export const PaymentHelpTooltips = {
  mpesaPayment: (
    <HelpTooltip
      title="M-PESA Payment"
      content="Pay using M-PESA mobile money. You'll receive an STK push notification on your phone to complete the payment."
    />
  ),
  
  paymentHistory: (
    <HelpTooltip
      title="Payment History"
      content="View all your past payments, receipts, and transaction details. You can download receipts for your records."
    />
  ),
  
  lateFeesPolicy: (
    <HelpTooltip
      title="Late Fees"
      content="Late fees are automatically calculated based on your lease agreement. Fees typically apply after a grace period (usually 5-10 days)."
    />
  ),
};

export const AccessibilityHelpTooltips = {
  keyboardNavigation: (
    <HelpTooltip
      title="Keyboard Navigation"
      content="Use Tab to move forward, Shift+Tab to move backward. Press Enter or Space to activate buttons. Use arrow keys in menus and lists."
    />
  ),
  
  screenReader: (
    <HelpTooltip
      title="Screen Reader Support"
      content="This application is compatible with screen readers like NVDA, JAWS, and VoiceOver. Important changes are announced automatically."
    />
  ),
  
  highContrast: (
    <HelpTooltip
      title="High Contrast Theme"
      content="Increases contrast between text and background colors for better visibility. Useful for users with visual impairments."
    />
  ),
  
  colorBlindFriendly: (
    <HelpTooltip
      title="Color-Blind Friendly Themes"
      content="Alternative color schemes designed for users with different types of color vision deficiency (deuteranopia, protanopia, tritanopia)."
    />
  ),
};