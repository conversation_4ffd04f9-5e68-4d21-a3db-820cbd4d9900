import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ConvexReactClient } from 'convex/react';
import { AuthService, RBACService, User } from './auth';

interface AuthContextType {
  authService: AuthService;
  rbacService: RBACService;
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name: string, role: "owner" | "manager" | "vendor" | "tenant", phone?: string) => Promise<void>;
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
  convex: ConvexReactClient;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children, convex }) => {
  const [authService] = useState(() => new AuthService(convex));
  const [rbacService] = useState(() => new RBACService(convex, authService));
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = user !== null;

  // Initialize auth state
  useEffect(() => {
    const initAuth = async () => {
      setIsLoading(true);
      try {
        const currentUser = authService.getCurrentUser();
        if (currentUser) {
          // Verify session is still valid
          const isValid = await authService.verifySession();
          if (isValid) {
            setUser(authService.getCurrentUser());
          } else {
            setUser(null);
          }
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, [authService]);

  // Set up token refresh interval
  useEffect(() => {
    if (!isAuthenticated) return;

    const refreshInterval = setInterval(async () => {
      try {
        await authService.refreshToken();
      } catch (error) {
        console.error('Error refreshing token:', error);
        setUser(null);
      }
    }, 15 * 60 * 1000); // Refresh every 15 minutes

    return () => clearInterval(refreshInterval);
  }, [isAuthenticated, authService]);

  const signIn = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const session = await authService.signIn(email, password);
      setUser(session.user);
    } catch (error) {
      setUser(null);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (
    email: string,
    password: string,
    name: string,
    role: "owner" | "manager" | "vendor" | "tenant",
    phone?: string
  ) => {
    setIsLoading(true);
    try {
      const session = await authService.signUp(email, password, name, role, phone);
      setUser(session.user);
    } catch (error) {
      setUser(null);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    setIsLoading(true);
    try {
      await authService.signOut();
      setUser(null);
    } catch (error) {
      console.error('Error signing out:', error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshAuth = async () => {
    try {
      const isValid = await authService.verifySession();
      if (isValid) {
        setUser(authService.getCurrentUser());
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Error refreshing auth:', error);
      setUser(null);
    }
  };

  const value: AuthContextType = {
    authService,
    rbacService,
    user,
    isAuthenticated,
    isLoading,
    signIn,
    signUp,
    signOut,
    refreshAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Higher-order component for protecting routes
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  requiredRoles?: ("owner" | "manager" | "vendor" | "tenant")[]
) => {
  return (props: P) => {
    const { user, isAuthenticated, isLoading } = useAuth();

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
        </div>
      );
    }

    if (!isAuthenticated) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Authentication Required</h2>
            <p className="text-gray-600">Please sign in to access this page.</p>
          </div>
        </div>
      );
    }

    if (requiredRoles && user && !requiredRoles.includes(user.role)) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
            <p className="text-gray-600">You don't have permission to access this page.</p>
          </div>
        </div>
      );
    }

    return <Component {...props} />;
  };
};

// Hook for checking permissions
export const usePermissions = () => {
  const { rbacService, user } = useAuth();

  const canCreate = (resource: string) => rbacService.canPerform(resource, "create");
  const canRead = (resource: string) => rbacService.canPerform(resource, "read");
  const canUpdate = (resource: string) => rbacService.canPerform(resource, "update");
  const canDelete = (resource: string) => rbacService.canPerform(resource, "delete");

  const hasRole = (role: "owner" | "manager" | "vendor" | "tenant") => user?.role === role;
  const hasAnyRole = (roles: ("owner" | "manager" | "vendor" | "tenant")[]) => 
    user ? roles.includes(user.role) : false;

  return {
    user,
    canCreate,
    canRead,
    canUpdate,
    canDelete,
    hasRole,
    hasAnyRole,
  };
};