import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Textarea } from '../ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { CalendarIcon, DollarSign, FileText, User } from 'lucide-react';

interface LeaseFormProps {
  propertyId?: Id<"properties">;
  unitId?: Id<"units">;
  tenantId?: Id<"users">;
  onSuccess?: (leaseId: Id<"leases">) => void;
  onCancel?: () => void;
}

interface LeaseFormData {
  propertyId: string;
  unitId: string;
  tenantId: string;
  startDate: string;
  endDate: string;
  monthlyRent: string;
  deposit: string;
  noticePeriod: string;
  lateFeePercentage: string;
  gracePeriod: string;
  renewalOption: boolean;
}

export function LeaseForm({ 
  propertyId, 
  unitId, 
  tenantId, 
  onSuccess, 
  onCancel 
}: LeaseFormProps) {
  const createLease = useMutation(api.leases.createLease);
  const properties = useQuery(api.properties.getProperties, {});
  const units = useQuery(api.units.getUnits, propertyId ? { propertyId } : {});
  const tenants = useQuery(api.users.getUsersByRole, { 
    sessionToken: "temp-session", // TODO: Replace with actual session token
    role: "tenant" 
  });

  const [formData, setFormData] = useState<LeaseFormData>({
    propertyId: propertyId || '',
    unitId: unitId || '',
    tenantId: tenantId || '',
    startDate: '',
    endDate: '',
    monthlyRent: '',
    deposit: '',
    noticePeriod: '30',
    lateFeePercentage: '5',
    gracePeriod: '5',
    renewalOption: true,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.propertyId) newErrors.propertyId = 'Property is required';
    if (!formData.unitId) newErrors.unitId = 'Unit is required';
    if (!formData.tenantId) newErrors.tenantId = 'Tenant is required';
    if (!formData.startDate) newErrors.startDate = 'Start date is required';
    if (!formData.endDate) newErrors.endDate = 'End date is required';
    if (!formData.monthlyRent) newErrors.monthlyRent = 'Monthly rent is required';
    if (!formData.deposit) newErrors.deposit = 'Deposit is required';

    // Validate dates
    if (formData.startDate && formData.endDate) {
      const startDate = new Date(formData.startDate);
      const endDate = new Date(formData.endDate);
      if (endDate <= startDate) {
        newErrors.endDate = 'End date must be after start date';
      }
    }

    // Validate numbers
    if (formData.monthlyRent && (isNaN(Number(formData.monthlyRent)) || Number(formData.monthlyRent) <= 0)) {
      newErrors.monthlyRent = 'Monthly rent must be a positive number';
    }
    if (formData.deposit && (isNaN(Number(formData.deposit)) || Number(formData.deposit) < 0)) {
      newErrors.deposit = 'Deposit must be a non-negative number';
    }
    if (formData.noticePeriod && (isNaN(Number(formData.noticePeriod)) || Number(formData.noticePeriod) < 0)) {
      newErrors.noticePeriod = 'Notice period must be a non-negative number';
    }
    if (formData.lateFeePercentage && (isNaN(Number(formData.lateFeePercentage)) || Number(formData.lateFeePercentage) < 0 || Number(formData.lateFeePercentage) > 100)) {
      newErrors.lateFeePercentage = 'Late fee percentage must be between 0 and 100';
    }
    if (formData.gracePeriod && (isNaN(Number(formData.gracePeriod)) || Number(formData.gracePeriod) < 0)) {
      newErrors.gracePeriod = 'Grace period must be a non-negative number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const leaseId = await createLease({
        propertyId: formData.propertyId as Id<"properties">,
        unitId: formData.unitId as Id<"units">,
        tenantId: formData.tenantId as Id<"users">,
        startDate: new Date(formData.startDate).getTime(),
        endDate: new Date(formData.endDate).getTime(),
        monthlyRent: Number(formData.monthlyRent),
        deposit: Number(formData.deposit),
        terms: {
          noticePeriod: Number(formData.noticePeriod),
          lateFeePercentage: Number(formData.lateFeePercentage),
          gracePeriod: Number(formData.gracePeriod),
          renewalOption: formData.renewalOption,
        },
      });

      onSuccess?.(leaseId);
    } catch (error) {
      console.error('Error creating lease:', error);
      setErrors({ submit: error instanceof Error ? error.message : 'Failed to create lease' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof LeaseFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Filter available units based on selected property
  const availableUnits = units?.filter(unit => 
    unit.propertyId === formData.propertyId && unit.status === 'vacant'
  ) || [];

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Create New Lease
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Property and Unit Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="property">Property *</Label>
              <Select
                value={formData.propertyId}
                onValueChange={(value) => {
                  handleInputChange('propertyId', value);
                  handleInputChange('unitId', ''); // Reset unit when property changes
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select property" />
                </SelectTrigger>
                <SelectContent>
                  {properties?.map((property) => (
                    <SelectItem key={property._id} value={property._id}>
                      {property.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.propertyId && <p className="text-sm text-red-500">{errors.propertyId}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="unit">Unit *</Label>
              <Select
                value={formData.unitId}
                onValueChange={(value) => handleInputChange('unitId', value)}
                disabled={!formData.propertyId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select unit" />
                </SelectTrigger>
                <SelectContent>
                  {availableUnits.map((unit) => (
                    <SelectItem key={unit._id} value={unit._id}>
                      {unit.unitNumber} - ${unit.rent}/month
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.unitId && <p className="text-sm text-red-500">{errors.unitId}</p>}
            </div>
          </div>

          {/* Tenant Selection */}
          <div className="space-y-2">
            <Label htmlFor="tenant">Tenant *</Label>
            <Select
              value={formData.tenantId}
              onValueChange={(value) => handleInputChange('tenantId', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select tenant" />
              </SelectTrigger>
              <SelectContent>
                {tenants?.map((tenant) => (
                  <SelectItem key={tenant._id} value={tenant._id}>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      {tenant.name} ({tenant.email})
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.tenantId && <p className="text-sm text-red-500">{errors.tenantId}</p>}
          </div>

          {/* Lease Dates */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date *</Label>
              <div className="relative">
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                  className="pl-10"
                />
                <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              </div>
              {errors.startDate && <p className="text-sm text-red-500">{errors.startDate}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">End Date *</Label>
              <div className="relative">
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleInputChange('endDate', e.target.value)}
                  className="pl-10"
                />
                <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              </div>
              {errors.endDate && <p className="text-sm text-red-500">{errors.endDate}</p>}
            </div>
          </div>

          {/* Financial Terms */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="monthlyRent">Monthly Rent *</Label>
              <div className="relative">
                <Input
                  id="monthlyRent"
                  type="number"
                  step="0.01"
                  value={formData.monthlyRent}
                  onChange={(e) => handleInputChange('monthlyRent', e.target.value)}
                  className="pl-10"
                  placeholder="0.00"
                />
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              </div>
              {errors.monthlyRent && <p className="text-sm text-red-500">{errors.monthlyRent}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="deposit">Security Deposit *</Label>
              <div className="relative">
                <Input
                  id="deposit"
                  type="number"
                  step="0.01"
                  value={formData.deposit}
                  onChange={(e) => handleInputChange('deposit', e.target.value)}
                  className="pl-10"
                  placeholder="0.00"
                />
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              </div>
              {errors.deposit && <p className="text-sm text-red-500">{errors.deposit}</p>}
            </div>
          </div>

          {/* Lease Terms */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Lease Terms</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="noticePeriod">Notice Period (days)</Label>
                <Input
                  id="noticePeriod"
                  type="number"
                  value={formData.noticePeriod}
                  onChange={(e) => handleInputChange('noticePeriod', e.target.value)}
                  placeholder="30"
                />
                {errors.noticePeriod && <p className="text-sm text-red-500">{errors.noticePeriod}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lateFeePercentage">Late Fee (%)</Label>
                <Input
                  id="lateFeePercentage"
                  type="number"
                  step="0.1"
                  value={formData.lateFeePercentage}
                  onChange={(e) => handleInputChange('lateFeePercentage', e.target.value)}
                  placeholder="5"
                />
                {errors.lateFeePercentage && <p className="text-sm text-red-500">{errors.lateFeePercentage}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="gracePeriod">Grace Period (days)</Label>
                <Input
                  id="gracePeriod"
                  type="number"
                  value={formData.gracePeriod}
                  onChange={(e) => handleInputChange('gracePeriod', e.target.value)}
                  placeholder="5"
                />
                {errors.gracePeriod && <p className="text-sm text-red-500">{errors.gracePeriod}</p>}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                id="renewalOption"
                type="checkbox"
                checked={formData.renewalOption}
                onChange={(e) => handleInputChange('renewalOption', e.target.checked)}
                className="rounded border-gray-300"
              />
              <Label htmlFor="renewalOption">Allow lease renewal</Label>
            </div>
          </div>

          {/* Error Display */}
          {errors.submit && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Lease'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}