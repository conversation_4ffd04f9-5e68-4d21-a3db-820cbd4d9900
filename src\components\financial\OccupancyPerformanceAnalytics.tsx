import React, { useState, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '../ui/tabs';
import { But<PERSON> } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Home, 
  Calendar,
  Target,
  CheckCircle
} from 'lucide-react';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  ScatterChart,
  Scatter
} from 'recharts';
// import { format, subMonths } from 'date-fns';
import { Id } from '../../../convex/_generated/dataModel';

interface OccupancyPerformanceAnalyticsProps {
  propertyId?: Id<"properties">;
}

const HEATMAP_COLORS = {
  high: '#10b981', // Green for high occupancy
  medium: '#f59e0b', // Yellow for medium occupancy
  low: '#ef4444', // Red for low occupancy
  vacant: '#6b7280', // Gray for vacant
};

export const OccupancyPerformanceAnalytics: React.FC<OccupancyPerformanceAnalyticsProps> = ({ propertyId }) => {
  const [selectedTab, setSelectedTab] = useState('occupancy');
  const [timeRange, setTimeRange] = useState<number>(12);

  // Fetch analytics data
  const occupancyData = useQuery(api.financialAnalytics.getOccupancyAnalytics, {
    propertyId,
    months: timeRange,
  });

  const retentionData = useQuery(api.financialAnalytics.getTenantRetentionAnalytics, {
    propertyId,
    months: timeRange,
  });

  const rentOptimization = useQuery(api.financialAnalytics.getRentOptimizationRecommendations, {
    propertyId,
  });

  const marketBenchmarking = useQuery(api.financialAnalytics.getMarketBenchmarking, {
    propertyId,
  });

  const isLoading = !occupancyData || !retentionData || !rentOptimization || !marketBenchmarking;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading analytics data...</p>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Prepare occupancy heatmap data
  const getOccupancyColor = (occupancyRate: number) => {
    if (occupancyRate >= 90) return HEATMAP_COLORS.high;
    if (occupancyRate >= 70) return HEATMAP_COLORS.medium;
    if (occupancyRate > 0) return HEATMAP_COLORS.low;
    return HEATMAP_COLORS.vacant;
  };

  // Get the first property's data for display
  const primaryProperty = occupancyData.properties[0];
  const primaryRetention = retentionData;
  const primaryOptimization = rentOptimization.recommendations[0];
  const primaryBenchmark = marketBenchmarking.properties[0];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Occupancy & Performance Analytics</h1>
          <p className="text-gray-600">
            Comprehensive occupancy trends, tenant retention, and optimization insights
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={timeRange.toString()} onValueChange={(value) => setTimeRange(parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="6">6 Months</SelectItem>
              <SelectItem value="12">12 Months</SelectItem>
              <SelectItem value="24">24 Months</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Key Metrics Cards */}
      {primaryProperty && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Occupancy</CardTitle>
              <Home className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatPercentage(primaryProperty.currentOccupancy.occupancyRate)}
              </div>
              <div className="text-xs text-muted-foreground">
                {primaryProperty.currentOccupancy.occupiedUnits} of {primaryProperty.currentOccupancy.totalUnits} units
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tenant Retention</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatPercentage(primaryRetention.summary.retentionRate)}
              </div>
              <div className="text-xs text-muted-foreground">
                {primaryRetention.summary.activeTenants} active tenants
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Lease Duration</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(primaryRetention.summary.averageLeaseDuration)} days
              </div>
              <div className="text-xs text-muted-foreground">
                Average tenant stay
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Rent Optimization</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(primaryOptimization?.propertyRecommendations.totalPotentialIncrease || 0)}
              </div>
              <div className="text-xs text-muted-foreground">
                Potential monthly increase
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="occupancy">Occupancy Heatmap</TabsTrigger>
          <TabsTrigger value="retention">Tenant Retention</TabsTrigger>
          <TabsTrigger value="optimization">Rent Optimization</TabsTrigger>
          <TabsTrigger value="benchmarking">Market Benchmarking</TabsTrigger>
        </TabsList>

        <TabsContent value="occupancy" className="space-y-4">
          {primaryProperty && (
            <>
              {/* Occupancy Trends */}
              <Card>
                <CardHeader>
                  <CardTitle>Occupancy Trends</CardTitle>
                  <CardDescription>Monthly occupancy rates and lease activity</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={primaryProperty.monthlyTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="monthName" />
                      <YAxis domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
                      <Tooltip 
                        formatter={(value, name) => [
                          name === 'occupancyRate' ? `${Number(value).toFixed(1)}%` : value,
                          name === 'occupancyRate' ? 'Occupancy Rate' : 
                          name === 'newLeases' ? 'New Leases' :
                          name === 'terminatedLeases' ? 'Terminated Leases' : name
                        ]}
                      />
                      <Area 
                        type="monotone" 
                        dataKey="occupancyRate" 
                        stroke="#3b82f6" 
                        fill="#3b82f6" 
                        fillOpacity={0.3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Unit Heatmap */}
              <Card>
                <CardHeader>
                  <CardTitle>Unit Occupancy Heatmap</CardTitle>
                  <CardDescription>Visual representation of unit performance over the last year</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-8 md:grid-cols-12 lg:grid-cols-16 gap-2 mb-4">
                    {primaryProperty.heatmapData.map((unit) => (
                      <div
                        key={unit.unitId}
                        className="aspect-square rounded-md flex items-center justify-center text-xs font-medium text-white cursor-pointer hover:opacity-80 transition-opacity"
                        style={{ 
                          backgroundColor: getOccupancyColor((unit.occupancyDays / 365) * 100)
                        }}
                        title={`Unit ${unit.unitNumber}: ${Math.round((unit.occupancyDays / 365) * 100)}% occupied (${unit.occupancyDays} days)`}
                      >
                        {unit.unitNumber}
                      </div>
                    ))}
                  </div>
                  
                  {/* Legend */}
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded" style={{ backgroundColor: HEATMAP_COLORS.high }}></div>
                      <span>High (90%+)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded" style={{ backgroundColor: HEATMAP_COLORS.medium }}></div>
                      <span>Medium (70-89%)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded" style={{ backgroundColor: HEATMAP_COLORS.low }}></div>
                      <span>Low (1-69%)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded" style={{ backgroundColor: HEATMAP_COLORS.vacant }}></div>
                      <span>Vacant (0%)</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Lease Activity */}
              <Card>
                <CardHeader>
                  <CardTitle>Lease Activity</CardTitle>
                  <CardDescription>Monthly new leases, terminations, and renewals</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={primaryProperty.monthlyTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="monthName" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="newLeases" fill="#10b981" name="New Leases" />
                      <Bar dataKey="terminatedLeases" fill="#ef4444" name="Terminated" />
                      <Bar dataKey="renewedLeases" fill="#3b82f6" name="Renewed" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="retention" className="space-y-4">
          {/* Retention Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Retention Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">
                  {formatPercentage(primaryRetention.summary.retentionRate)}
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {primaryRetention.summary.activeTenants} of {primaryRetention.summary.totalTenants} tenants retained
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Turnover Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-red-600">
                  {formatPercentage(primaryRetention.summary.turnoverRate)}
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {primaryRetention.summary.terminatedLeases + primaryRetention.summary.expiredLeases} lease endings
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Avg Lease Duration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">
                  {Math.round(primaryRetention.summary.averageLeaseDuration)} days
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Average tenant stay period
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Retention Trends */}
          <Card>
            <CardHeader>
              <CardTitle>Retention Trends</CardTitle>
              <CardDescription>Monthly tenant retention and turnover rates</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={primaryRetention.monthlyTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="monthName" />
                  <YAxis domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
                  <Tooltip formatter={(value) => `${Number(value).toFixed(1)}%`} />
                  <Line 
                    type="monotone" 
                    dataKey="retentionRate" 
                    stroke="#10b981" 
                    strokeWidth={2}
                    name="Retention Rate"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="turnoverRate" 
                    stroke="#ef4444" 
                    strokeWidth={2}
                    name="Turnover Rate"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Top Tenants by Duration */}
          <Card>
            <CardHeader>
              <CardTitle>Tenant Lifecycle Analysis</CardTitle>
              <CardDescription>Top tenants by total duration and lease activity</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {primaryRetention.tenantLifecycle.slice(0, 10).map((tenant, index) => (
                  <div key={tenant.tenantId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium">Tenant {tenant.tenantId.slice(-6)}</div>
                        <div className="text-sm text-gray-600">
                          {tenant.totalLeases} lease{tenant.totalLeases !== 1 ? 's' : ''} • {tenant.renewalCount} renewal{tenant.renewalCount !== 1 ? 's' : ''}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{Math.round(tenant.totalDuration)} days</div>
                      <Badge variant={tenant.isActive ? "default" : "secondary"}>
                        {tenant.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimization" className="space-y-4">
          {primaryOptimization && (
            <>
              {/* Optimization Summary */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Potential Increase</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">
                      {formatCurrency(primaryOptimization.propertyRecommendations.totalPotentialIncrease)}
                    </div>
                    <p className="text-sm text-gray-600">Monthly revenue increase</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">High Performers</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">
                      {primaryOptimization.propertyRecommendations.highPerformingUnits}
                    </div>
                    <p className="text-sm text-gray-600">Units with 90%+ occupancy</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Underperforming</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-red-600">
                      {primaryOptimization.propertyRecommendations.underperformingUnits}
                    </div>
                    <p className="text-sm text-gray-600">Units below 70% occupancy</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Vacancy Risk</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-yellow-600">
                      {formatPercentage(primaryOptimization.propertyRecommendations.vacancyRisk)}
                    </div>
                    <p className="text-sm text-gray-600">Units at risk</p>
                  </CardContent>
                </Card>
              </div>

              {/* Unit Recommendations */}
              <Card>
                <CardHeader>
                  <CardTitle>Unit-Level Recommendations</CardTitle>
                  <CardDescription>Rent optimization suggestions for individual units</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {primaryOptimization.unitAnalysis
                      .filter(unit => Math.abs(unit.potentialIncrease) > 1000) // Show only significant changes
                      .slice(0, 10)
                      .map((unit) => (
                      <div key={unit.unitId} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-4">
                          <div className="font-medium">Unit {unit.unitNumber}</div>
                          <Badge 
                            variant={
                              unit.riskLevel === 'low' ? 'default' : 
                              unit.riskLevel === 'medium' ? 'secondary' : 'destructive'
                            }
                          >
                            {unit.riskLevel} risk
                          </Badge>
                          <div className="text-sm text-gray-600">
                            {formatPercentage(unit.occupancyHistory.occupancyRate)} occupied
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-600">
                              {formatCurrency(unit.currentRent)} →
                            </span>
                            <span className="font-medium">
                              {formatCurrency(unit.recommendedRent)}
                            </span>
                            <span className={`text-sm ${unit.potentialIncrease >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              ({unit.potentialIncrease >= 0 ? '+' : ''}{formatCurrency(unit.potentialIncrease)})
                            </span>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {unit.reasoning}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Rent vs Occupancy Scatter */}
              <Card>
                <CardHeader>
                  <CardTitle>Rent vs Occupancy Analysis</CardTitle>
                  <CardDescription>Relationship between rent levels and occupancy rates</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <ScatterChart data={primaryOptimization.unitAnalysis}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="currentRent" 
                        type="number" 
                        domain={['dataMin', 'dataMax']}
                        tickFormatter={(value) => formatCurrency(value)}
                      />
                      <YAxis 
                        dataKey="occupancyHistory.occupancyRate" 
                        type="number" 
                        domain={[0, 100]}
                        tickFormatter={(value) => `${value}%`}
                      />
                      <Tooltip 
                        formatter={(value, name) => [
                          name === 'currentRent' ? formatCurrency(Number(value)) : `${Number(value).toFixed(1)}%`,
                          name === 'currentRent' ? 'Current Rent' : 'Occupancy Rate'
                        ]}
                      />
                      <Scatter dataKey="currentRent" fill="#3b82f6" />
                    </ScatterChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="benchmarking" className="space-y-4">
          {primaryBenchmark && (
            <>
              {/* Benchmark Overview */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Overall Score</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">
                      {primaryBenchmark.benchmarkComparison.overallScore.toFixed(1)}
                    </div>
                    <p className="text-sm text-gray-600">
                      {formatPercentage(primaryBenchmark.benchmarkComparison.percentile)} percentile
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Occupancy Rank</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      #{primaryBenchmark.benchmarkComparison.occupancyRanking}
                    </div>
                    <p className="text-sm text-gray-600">
                      {formatPercentage(primaryBenchmark.metrics.occupancyRate)} occupancy
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Rent Rank</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      #{primaryBenchmark.benchmarkComparison.rentRanking}
                    </div>
                    <p className="text-sm text-gray-600">
                      {formatCurrency(primaryBenchmark.metrics.averageRent)} avg rent
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Profitability Rank</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      #{primaryBenchmark.benchmarkComparison.profitabilityRanking}
                    </div>
                    <p className="text-sm text-gray-600">
                      {formatPercentage(primaryBenchmark.metrics.profitMargin)} margin
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Market Comparison */}
              <Card>
                <CardHeader>
                  <CardTitle>Market Comparison</CardTitle>
                  <CardDescription>How your property compares to market averages</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { 
                        label: 'Occupancy Rate', 
                        current: primaryBenchmark.metrics.occupancyRate, 
                        market: marketBenchmarking.marketAverages.occupancyRate,
                        format: formatPercentage
                      },
                      { 
                        label: 'Average Rent', 
                        current: primaryBenchmark.metrics.averageRent, 
                        market: marketBenchmarking.marketAverages.averageRent,
                        format: formatCurrency
                      },
                      { 
                        label: 'Revenue per Unit', 
                        current: primaryBenchmark.metrics.revenuePerUnit, 
                        market: marketBenchmarking.marketAverages.revenuePerUnit,
                        format: formatCurrency
                      },
                      { 
                        label: 'Profit Margin', 
                        current: primaryBenchmark.metrics.profitMargin, 
                        market: marketBenchmarking.marketAverages.profitMargin,
                        format: formatPercentage
                      },
                      { 
                        label: 'Tenant Retention', 
                        current: primaryBenchmark.metrics.tenantRetentionRate, 
                        market: marketBenchmarking.marketAverages.tenantRetentionRate,
                        format: formatPercentage
                      },
                    ].map((metric) => {
                      const isAboveMarket = metric.current > metric.market;
                      const difference = ((metric.current - metric.market) / metric.market) * 100;
                      
                      return (
                        <div key={metric.label} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="font-medium">{metric.label}</div>
                            {isAboveMarket ? (
                              <TrendingUp className="h-4 w-4 text-green-600" />
                            ) : (
                              <TrendingDown className="h-4 w-4 text-red-600" />
                            )}
                          </div>
                          <div className="text-right">
                            <div className="font-medium">{metric.format(metric.current)}</div>
                            <div className={`text-sm ${isAboveMarket ? 'text-green-600' : 'text-red-600'}`}>
                              {isAboveMarket ? '+' : ''}{difference.toFixed(1)}% vs market
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Recommendations */}
              <Card>
                <CardHeader>
                  <CardTitle>Improvement Recommendations</CardTitle>
                  <CardDescription>Actionable insights to improve performance</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {primaryBenchmark.recommendations.map((recommendation, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                        <p className="text-sm">{recommendation}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};