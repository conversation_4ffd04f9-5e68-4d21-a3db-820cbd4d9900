import React, { useState, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Building, 
  Users, 
  DollarSign,
  BarChart3,
  Award,
  AlertTriangle
} from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';
import { subMonths, subYears } from 'date-fns';
import { Id } from '../../../convex/_generated/dataModel';

interface ComparativeAnalysisProps {
  propertyIds: Id<"properties">[];
}

export const ComparativeAnalysis: React.FC<ComparativeAnalysisProps> = ({ propertyIds }) => {
  const [dateRange, setDateRange] = useState<'quarter' | 'year'>('quarter');
  const [sortBy, setSortBy] = useState<'revenue' | 'profitMargin' | 'occupancy'>('revenue');

  // Calculate date range
  const { startDate, endDate } = useMemo(() => {
    const now = new Date();
    const start = dateRange === 'year' ? subYears(now, 1) : subMonths(now, 3);
    
    return {
      startDate: start.getTime(),
      endDate: now.getTime(),
    };
  }, [dateRange]);

  // Fetch comparative data
  const comparativeData = useQuery(api.financialAnalytics.getComparativeAnalysis, {
    propertyIds,
    startDate,
    endDate,
  });

  if (!comparativeData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading comparative analysis...</p>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Sort properties based on selected criteria
  const sortedProperties = useMemo(() => {
    return [...comparativeData.properties].sort((a, b) => {
      switch (sortBy) {
        case 'revenue':
          return b.metrics.totalRevenue - a.metrics.totalRevenue;
        case 'profitMargin':
          return b.metrics.profitMargin - a.metrics.profitMargin;
        case 'occupancy':
          return b.metrics.occupancyRate - a.metrics.occupancyRate;
        default:
          return 0;
      }
    });
  }, [comparativeData.properties, sortBy]);

  // Prepare chart data
  const chartData = sortedProperties.map(property => ({
    name: property.propertyName.length > 15 
      ? property.propertyName.substring(0, 15) + '...' 
      : property.propertyName,
    fullName: property.propertyName,
    revenue: property.metrics.totalRevenue,
    netIncome: property.metrics.netIncome,
    occupancyRate: property.metrics.occupancyRate,
    profitMargin: property.metrics.profitMargin,
    collectionRate: property.metrics.collectionRate,
    revenuePerUnit: property.metrics.revenuePerUnit,
  }));

  // Prepare radar chart data for top 3 properties
  const radarData = [
    { metric: 'Occupancy Rate', ...Object.fromEntries(sortedProperties.slice(0, 3).map(p => [p.propertyName, p.metrics.occupancyRate])) },
    { metric: 'Profit Margin', ...Object.fromEntries(sortedProperties.slice(0, 3).map(p => [p.propertyName, p.metrics.profitMargin])) },
    { metric: 'Collection Rate', ...Object.fromEntries(sortedProperties.slice(0, 3).map(p => [p.propertyName, p.metrics.collectionRate])) },
  ];

  const getPerformanceBadge = (value: number, benchmark: number, isPercentage = false) => {
    const threshold = isPercentage ? 5 : benchmark * 0.1;
    if (value >= benchmark + threshold) {
      return <Badge className="bg-green-100 text-green-800">Above Average</Badge>;
    } else if (value <= benchmark - threshold) {
      return <Badge variant="destructive">Below Average</Badge>;
    } else {
      return <Badge variant="secondary">Average</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Comparative Analysis</h1>
          <p className="text-gray-600">
            Compare performance across {propertyIds.length} properties
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={dateRange} onValueChange={(value: 'quarter' | 'year') => setDateRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="quarter">Last Quarter</SelectItem>
              <SelectItem value="year">Last Year</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={sortBy} onValueChange={(value: 'revenue' | 'profitMargin' | 'occupancy') => setSortBy(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="revenue">Sort by Revenue</SelectItem>
              <SelectItem value="profitMargin">Sort by Profit</SelectItem>
              <SelectItem value="occupancy">Sort by Occupancy</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Benchmarks Overview */}
      {comparativeData.benchmarks && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Occupancy</CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatPercentage(comparativeData.benchmarks.averageOccupancyRate)}
              </div>
              <p className="text-xs text-muted-foreground">
                Across all properties
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Profit Margin</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatPercentage(comparativeData.benchmarks.averageProfitMargin)}
              </div>
              <p className="text-xs text-muted-foreground">
                Portfolio average
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Collection Rate</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatPercentage(comparativeData.benchmarks.averageCollectionRate)}
              </div>
              <p className="text-xs text-muted-foreground">
                Payment efficiency
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Revenue/Unit</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(comparativeData.benchmarks.averageRevenuePerUnit)}
              </div>
              <p className="text-xs text-muted-foreground">
                Per unit performance
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Top Performers */}
      {comparativeData.benchmarks && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-yellow-500" />
                Top Revenue
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg font-semibold">
                {comparativeData.benchmarks.topPerformer.byRevenue.propertyName}
              </div>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(comparativeData.benchmarks.topPerformer.byRevenue.metrics.totalRevenue)}
              </div>
              <p className="text-sm text-gray-600">
                {comparativeData.benchmarks.topPerformer.byRevenue.metrics.totalUnits} units
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-green-500" />
                Best Profit Margin
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg font-semibold">
                {comparativeData.benchmarks.topPerformer.byProfitMargin.propertyName}
              </div>
              <div className="text-2xl font-bold text-green-600">
                {formatPercentage(comparativeData.benchmarks.topPerformer.byProfitMargin.metrics.profitMargin)}
              </div>
              <p className="text-sm text-gray-600">
                {formatCurrency(comparativeData.benchmarks.topPerformer.byProfitMargin.metrics.netIncome)} net income
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-blue-500" />
                Highest Occupancy
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg font-semibold">
                {comparativeData.benchmarks.topPerformer.byOccupancy.propertyName}
              </div>
              <div className="text-2xl font-bold text-blue-600">
                {formatPercentage(comparativeData.benchmarks.topPerformer.byOccupancy.metrics.occupancyRate)}
              </div>
              <p className="text-sm text-gray-600">
                {comparativeData.benchmarks.topPerformer.byOccupancy.metrics.occupiedUnits} of {comparativeData.benchmarks.topPerformer.byOccupancy.metrics.totalUnits} units
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Comparative Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Comparison */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue Comparison</CardTitle>
            <CardDescription>Total revenue by property</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" tickFormatter={(value) => formatCurrency(value)} />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip 
                  formatter={(value) => formatCurrency(Number(value))}
                  labelFormatter={(label) => chartData.find(d => d.name === label)?.fullName || label}
                />
                <Bar dataKey="revenue" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Performance Radar */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Radar</CardTitle>
            <CardDescription>Multi-metric comparison (top 3 properties)</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RadarChart data={radarData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="metric" />
                <PolarRadiusAxis angle={90} domain={[0, 100]} />
                {sortedProperties.slice(0, 3).map((property, index) => (
                  <Radar
                    key={property.propertyId}
                    name={property.propertyName}
                    dataKey={property.propertyName}
                    stroke={['#3b82f6', '#10b981', '#f59e0b'][index]}
                    fill={['#3b82f6', '#10b981', '#f59e0b'][index]}
                    fillOpacity={0.1}
                  />
                ))}
                <Tooltip />
              </RadarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Property Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Property Comparison</CardTitle>
          <CardDescription>Comprehensive metrics for all properties</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Property</th>
                  <th className="text-right p-2">Units</th>
                  <th className="text-right p-2">Occupancy</th>
                  <th className="text-right p-2">Revenue</th>
                  <th className="text-right p-2">Net Income</th>
                  <th className="text-right p-2">Profit Margin</th>
                  <th className="text-right p-2">Collection Rate</th>
                  <th className="text-right p-2">Revenue/Unit</th>
                  <th className="text-center p-2">Performance</th>
                </tr>
              </thead>
              <tbody>
                {sortedProperties.map((property, index) => (
                  <tr key={property.propertyId} className="border-b hover:bg-gray-50">
                    <td className="p-2">
                      <div>
                        <div className="font-medium">{property.propertyName}</div>
                        <div className="text-sm text-gray-500 capitalize">{property.propertyType}</div>
                      </div>
                    </td>
                    <td className="text-right p-2">
                      <div>{property.metrics.totalUnits}</div>
                      <div className="text-sm text-gray-500">
                        {property.metrics.occupiedUnits} occupied
                      </div>
                    </td>
                    <td className="text-right p-2">
                      <div className="font-medium">
                        {formatPercentage(property.metrics.occupancyRate)}
                      </div>
                      {comparativeData.benchmarks && getPerformanceBadge(
                        property.metrics.occupancyRate, 
                        comparativeData.benchmarks.averageOccupancyRate,
                        true
                      )}
                    </td>
                    <td className="text-right p-2">
                      <div className="font-medium">
                        {formatCurrency(property.metrics.totalRevenue)}
                      </div>
                    </td>
                    <td className="text-right p-2">
                      <div className={`font-medium ${property.metrics.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCurrency(property.metrics.netIncome)}
                      </div>
                    </td>
                    <td className="text-right p-2">
                      <div className="font-medium">
                        {formatPercentage(property.metrics.profitMargin)}
                      </div>
                      {comparativeData.benchmarks && getPerformanceBadge(
                        property.metrics.profitMargin, 
                        comparativeData.benchmarks.averageProfitMargin,
                        true
                      )}
                    </td>
                    <td className="text-right p-2">
                      <div className="font-medium">
                        {formatPercentage(property.metrics.collectionRate)}
                      </div>
                    </td>
                    <td className="text-right p-2">
                      <div className="font-medium">
                        {formatCurrency(property.metrics.revenuePerUnit)}
                      </div>
                    </td>
                    <td className="text-center p-2">
                      {index === 0 && (
                        <div className="flex items-center justify-center">
                          <TrendingUp className="h-4 w-4 text-green-600" />
                        </div>
                      )}
                      {index === sortedProperties.length - 1 && sortedProperties.length > 1 && (
                        <div className="flex items-center justify-center">
                          <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};