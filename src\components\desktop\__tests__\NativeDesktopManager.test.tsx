import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { NativeDesktopManager } from '../NativeDesktopManager'

// Mock the hooks
vi.mock('../../hooks/useNativeFileSystem', () => ({
  useNativeFileSystem: () => ({
    importDocuments: vi.fn().mockResolvedValue([
      { path: '/test/file.pdf', name: 'file.pdf', content: 'test content' }
    ]),
    exportReport: vi.fn().mockResolvedValue({ success: true })
  })
}))

vi.mock('../../hooks/useNativeNotifications', () => ({
  useNativeNotifications: () => ({
    showNotification: vi.fn().mockResolvedValue(true),
    getPermission: vi.fn().mockReturnValue('granted')
  })
}))

vi.mock('../../hooks/usePrintService', () => ({
  usePrintService: () => ({
    printDocument: vi.fn().mockResolvedValue({ success: true }),
    downloadPDF: vi.fn().mockResolvedValue({ success: true })
  })
}))

vi.mock('../../hooks/useNativeMenuActions', () => ({
  useNativeMenuActions: vi.fn()
}))

vi.mock('../../ui/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}))

// Mock window.electronAPI
const mockElectronAPI = {
  getPlatform: vi.fn().mockResolvedValue('win32'),
  getVersion: vi.fn().mockResolvedValue('1.0.0'),
  minimizeWindow: vi.fn().mockResolvedValue(undefined),
  maximizeWindow: vi.fn().mockResolvedValue(undefined),
  hideWindow: vi.fn().mockResolvedValue(undefined),
  showWindow: vi.fn().mockResolvedValue(undefined)
}

beforeEach(() => {
  // @ts-ignore
  global.window.electronAPI = mockElectronAPI
  vi.clearAllMocks()
})

describe('NativeDesktopManager', () => {
  it('renders desktop integration interface', () => {
    render(<NativeDesktopManager />)
    
    expect(screen.getByText('Native Desktop Integration')).toBeInTheDocument()
    expect(screen.getByText('File System Access')).toBeInTheDocument()
    expect(screen.getByText('System Notifications')).toBeInTheDocument()
    expect(screen.getByText('Print Functionality')).toBeInTheDocument()
    expect(screen.getByText('Window Management')).toBeInTheDocument()
    expect(screen.getByText('Keyboard Shortcuts')).toBeInTheDocument()
  })

  it('shows desktop mode badge when in Electron', async () => {
    render(<NativeDesktopManager />)
    
    await waitFor(() => {
      expect(screen.getByText('Desktop Mode')).toBeInTheDocument()
      expect(screen.getByText('Platform: win32')).toBeInTheDocument()
      expect(screen.getByText('Version: 1.0.0')).toBeInTheDocument()
    })
  })

  it('handles file import action', async () => {
    render(<NativeDesktopManager />)
    
    const importButton = screen.getByText('Import Documents')
    fireEvent.click(importButton)
    
    // The actual import functionality is tested in the hook tests
    expect(importButton).toBeInTheDocument()
  })

  it('handles export report action', async () => {
    render(<NativeDesktopManager />)
    
    const exportButton = screen.getByText('Export Report')
    fireEvent.click(exportButton)
    
    // The actual export functionality is tested in the hook tests
    expect(exportButton).toBeInTheDocument()
  })

  it('handles notification test', async () => {
    render(<NativeDesktopManager />)
    
    const notificationButton = screen.getByText('Test Notification')
    fireEvent.click(notificationButton)
    
    expect(notificationButton).toBeInTheDocument()
  })

  it('handles print test', async () => {
    render(<NativeDesktopManager />)
    
    const printButton = screen.getByText('Test Print')
    fireEvent.click(printButton)
    
    expect(printButton).toBeInTheDocument()
  })

  it('handles PDF generation', async () => {
    render(<NativeDesktopManager />)
    
    const pdfButton = screen.getByText('Generate PDF')
    fireEvent.click(pdfButton)
    
    expect(pdfButton).toBeInTheDocument()
  })

  it('handles window management actions', async () => {
    render(<NativeDesktopManager />)
    
    const minimizeButton = screen.getByText('Minimize')
    const maximizeButton = screen.getByText('Maximize')
    const hideButton = screen.getByText('Hide')
    const showButton = screen.getByText('Show')
    
    fireEvent.click(minimizeButton)
    fireEvent.click(maximizeButton)
    fireEvent.click(hideButton)
    fireEvent.click(showButton)
    
    expect(minimizeButton).toBeInTheDocument()
    expect(maximizeButton).toBeInTheDocument()
    expect(hideButton).toBeInTheDocument()
    expect(showButton).toBeInTheDocument()
  })

  it('shows shortcuts when requested', async () => {
    render(<NativeDesktopManager />)
    
    const shortcutsButton = screen.getByText('Show All Shortcuts')
    fireEvent.click(shortcutsButton)
    
    expect(shortcutsButton).toBeInTheDocument()
  })

  it('disables window management buttons in web mode', () => {
    // @ts-ignore
    global.window.electronAPI = undefined
    
    render(<NativeDesktopManager />)
    
    const minimizeButton = screen.getByText('Minimize')
    const maximizeButton = screen.getByText('Maximize')
    const hideButton = screen.getByText('Hide')
    const showButton = screen.getByText('Show')
    
    expect(minimizeButton).toBeDisabled()
    expect(maximizeButton).toBeDisabled()
    expect(hideButton).toBeDisabled()
    expect(showButton).toBeDisabled()
  })
})