/**
 * Real-time Sync Status Indicator Component
 * Shows connection status and pending operations
 */

import React from 'react';
import { useRealtimeSyncStatus } from '../../hooks/useRealtimeData';
import { useConnectionStatus } from '../../lib/realtime-notifications';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { 
  Wifi, 
  WifiOff, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Activity
} from 'lucide-react';

interface SyncStatusIndicatorProps {
  showDetails?: boolean;
  className?: string;
}

export function SyncStatusIndicator({ showDetails = false, className }: SyncStatusIndicatorProps) {
  const { 
    isOnline, 
    lastSyncTime, 
    pendingOperations, 
    lastHeartbeat, 
    isConnected 
  } = useRealtimeSyncStatus();
  
  const { 
    isConnected: networkConnected, 
    lastConnected, 
    reconnectAttempts, 
    attemptReconnect 
  } = useConnectionStatus();

  const getStatusColor = () => {
    if (!networkConnected || !isConnected) return 'destructive';
    if (pendingOperations > 0) return 'warning';
    return 'success';
  };

  const getStatusIcon = () => {
    if (!networkConnected) return <WifiOff className="h-4 w-4" />;
    if (!isConnected) return <AlertCircle className="h-4 w-4" />;
    if (pendingOperations > 0) return <Clock className="h-4 w-4" />;
    return <CheckCircle className="h-4 w-4" />;
  };

  const getStatusText = () => {
    if (!networkConnected) return 'Offline';
    if (!isConnected) return 'Disconnected';
    if (pendingOperations > 0) return `${pendingOperations} pending`;
    return 'Connected';
  };

  const formatTime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return new Date(timestamp).toLocaleDateString();
  };

  if (!showDetails) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Badge variant={getStatusColor()} className="flex items-center gap-1">
          {getStatusIcon()}
          {getStatusText()}
        </Badge>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Activity className="h-4 w-4" />
          Sync Status
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Connection Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Connection</span>
          <Badge variant={getStatusColor()} className="flex items-center gap-1">
            {getStatusIcon()}
            {getStatusText()}
          </Badge>
        </div>

        {/* Last Sync Time */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Last Sync</span>
          <span className="text-sm">{formatTime(lastSyncTime)}</span>
        </div>

        {/* Pending Operations */}
        {pendingOperations > 0 && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Pending</span>
            <Badge variant="warning">{pendingOperations} operations</Badge>
          </div>
        )}

        {/* Heartbeat Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Heartbeat</span>
          <span className="text-sm">{formatTime(lastHeartbeat)}</span>
        </div>

        {/* Network Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Network</span>
          <div className="flex items-center gap-2">
            {networkConnected ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <span className="text-sm">
              {networkConnected ? 'Online' : 'Offline'}
            </span>
          </div>
        </div>

        {/* Reconnect Attempts */}
        {reconnectAttempts > 0 && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Reconnect Attempts</span>
            <span className="text-sm">{reconnectAttempts}</span>
          </div>
        )}

        {/* Last Connected */}
        {lastConnected && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Last Connected</span>
            <span className="text-sm">{formatTime(lastConnected.getTime())}</span>
          </div>
        )}

        {/* Reconnect Button */}
        {!isConnected && (
          <Button 
            onClick={attemptReconnect} 
            variant="outline" 
            size="sm" 
            className="w-full"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reconnect
          </Button>
        )}

        {/* Status Messages */}
        <div className="text-xs text-muted-foreground">
          {!networkConnected && (
            <p className="text-red-600">No network connection detected</p>
          )}
          {networkConnected && !isConnected && (
            <p className="text-yellow-600">Server connection lost</p>
          )}
          {pendingOperations > 0 && (
            <p className="text-blue-600">Changes will sync when connection is restored</p>
          )}
          {networkConnected && isConnected && pendingOperations === 0 && (
            <p className="text-green-600">All changes synchronized</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Compact sync status for header/toolbar
export function CompactSyncStatus({ className }: { className?: string }) {
  const { isOnline, pendingOperations, isConnected } = useRealtimeSyncStatus();
  const { isConnected: networkConnected } = useConnectionStatus();

  const getStatusColor = () => {
    if (!networkConnected || !isConnected) return 'text-red-500';
    if (pendingOperations > 0) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getStatusIcon = () => {
    if (!networkConnected) return <WifiOff className="h-3 w-3" />;
    if (!isConnected) return <AlertCircle className="h-3 w-3" />;
    if (pendingOperations > 0) return <Clock className="h-3 w-3" />;
    return <CheckCircle className="h-3 w-3" />;
  };

  return (
    <div className={`flex items-center gap-1 ${getStatusColor()} ${className}`}>
      {getStatusIcon()}
      {pendingOperations > 0 && (
        <span className="text-xs">{pendingOperations}</span>
      )}
    </div>
  );
}

// Real-time notification toast
export function RealtimeNotificationToast() {
  const { isOnline, pendingOperations } = useRealtimeSyncStatus();
  const [showToast, setShowToast] = React.useState(false);
  const [lastOnlineState, setLastOnlineState] = React.useState(isOnline);

  React.useEffect(() => {
    if (isOnline !== lastOnlineState) {
      setShowToast(true);
      setLastOnlineState(isOnline);
      
      // Auto-hide toast after 3 seconds
      const timeout = setTimeout(() => setShowToast(false), 3000);
      return () => clearTimeout(timeout);
    }
  }, [isOnline, lastOnlineState]);

  if (!showToast) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className="w-80">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            {isOnline ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <WifiOff className="h-5 w-5 text-red-500" />
            )}
            <div>
              <p className="font-medium">
                {isOnline ? 'Back Online' : 'Connection Lost'}
              </p>
              <p className="text-sm text-muted-foreground">
                {isOnline 
                  ? pendingOperations > 0 
                    ? `Syncing ${pendingOperations} pending changes...`
                    : 'All data synchronized'
                  : 'Changes will be saved locally'
                }
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}