import { auditLogger, AuditEventType, AuditSeverity } from './audit-logging';
import { consentManager, ConsentType } from './consent-management';
import { DataMasking, DataClassification } from './data-encryption';

// Privacy control types
export enum PrivacyControlType {
  DATA_EXPORT = 'data_export',
  DATA_DELETION = 'data_deletion',
  DATA_RECTIFICATION = 'data_rectification',
  DATA_PORTABILITY = 'data_portability',
  PROCESSING_RESTRICTION = 'processing_restriction',
  OBJECTION_TO_PROCESSING = 'objection_to_processing',
  AUTOMATED_DECISION_OBJECTION = 'automated_decision_objection'
}

export enum RequestStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled'
}

// Privacy request interface
export interface PrivacyRequest {
  id: string;
  userId: string;
  type: PrivacyControlType;
  status: RequestStatus;
  description: string;
  requestedAt: number;
  processedAt?: number;
  completedAt?: number;
  processedBy?: string;
  rejectionReason?: string;
  metadata?: Record<string, any>;
  dataCategories?: string[];
  timeRange?: {
    start: number;
    end: number;
  };
}

// Data export format
export enum ExportFormat {
  JSON = 'json',
  CSV = 'csv',
  XML = 'xml',
  PDF = 'pdf'
}

// Data category definitions
export interface DataCategory {
  id: string;
  name: string;
  description: string;
  classification: DataClassification;
  tables: string[];
  fields: string[];
  retentionPeriod?: number;
  isExportable: boolean;
  isDeletable: boolean;
}

// Privacy controls manager
export class PrivacyControlsManager {
  private static instance: PrivacyControlsManager;
  private requests = new Map<string, PrivacyRequest>();
  private dataCategories = new Map<string, DataCategory>();
  private readonly REQUESTS_KEY = 'privacy_requests';
  private readonly CATEGORIES_KEY = 'data_categories';

  constructor() {
    this.loadRequests();
    this.loadDataCategories();
    this.initializeDefaultCategories();
  }

  static getInstance(): PrivacyControlsManager {
    if (!this.instance) {
      this.instance = new PrivacyControlsManager();
    }
    return this.instance;
  }

  /**
   * Submit a privacy request
   */
  async submitRequest(
    userId: string,
    type: PrivacyControlType,
    description: string,
    dataCategories?: string[],
    timeRange?: { start: number; end: number },
    metadata?: Record<string, any>
  ): Promise<string> {
    const requestId = crypto.randomUUID();
    const now = Date.now();

    const request: PrivacyRequest = {
      id: requestId,
      userId,
      type,
      status: RequestStatus.PENDING,
      description,
      requestedAt: now,
      dataCategories,
      timeRange,
      metadata
    };

    this.requests.set(requestId, request);
    await this.saveRequests();

    // Log privacy request
    await auditLogger.logEvent({
      type: AuditEventType.PRIVACY_SETTINGS_CHANGED,
      severity: AuditSeverity.HIGH,
      userId,
      action: `Privacy request submitted: ${type}`,
      details: {
        requestId,
        requestType: type,
        dataCategories,
        timeRange
      },
      success: true
    });

    // Auto-process certain types of requests
    if (this.canAutoProcess(type)) {
      await this.processRequest(requestId, 'system');
    }

    return requestId;
  }

  /**
   * Process a privacy request
   */
  async processRequest(requestId: string, processedBy: string): Promise<void> {
    const request = this.requests.get(requestId);
    if (!request) {
      throw new Error(`Privacy request ${requestId} not found`);
    }

    if (request.status !== RequestStatus.PENDING) {
      throw new Error(`Request ${requestId} is not in pending status`);
    }

    try {
      // Update status to in progress
      request.status = RequestStatus.IN_PROGRESS;
      request.processedAt = Date.now();
      request.processedBy = processedBy;
      this.requests.set(requestId, request);
      await this.saveRequests();

      // Process based on request type
      let result: any;
      switch (request.type) {
        case PrivacyControlType.DATA_EXPORT:
          result = await this.processDataExport(request);
          break;
        case PrivacyControlType.DATA_DELETION:
          result = await this.processDataDeletion(request);
          break;
        case PrivacyControlType.DATA_RECTIFICATION:
          result = await this.processDataRectification(request);
          break;
        case PrivacyControlType.DATA_PORTABILITY:
          result = await this.processDataPortability(request);
          break;
        case PrivacyControlType.PROCESSING_RESTRICTION:
          result = await this.processProcessingRestriction(request);
          break;
        case PrivacyControlType.OBJECTION_TO_PROCESSING:
          result = await this.processObjectionToProcessing(request);
          break;
        case PrivacyControlType.AUTOMATED_DECISION_OBJECTION:
          result = await this.processAutomatedDecisionObjection(request);
          break;
        default:
          throw new Error(`Unknown request type: ${request.type}`);
      }

      // Mark as completed
      request.status = RequestStatus.COMPLETED;
      request.completedAt = Date.now();
      request.metadata = { ...request.metadata, result };
      this.requests.set(requestId, request);
      await this.saveRequests();

      // Log completion
      await auditLogger.logEvent({
        type: AuditEventType.PRIVACY_SETTINGS_CHANGED,
        severity: AuditSeverity.HIGH,
        userId: request.userId,
        action: `Privacy request completed: ${request.type}`,
        details: {
          requestId,
          processedBy,
          completedAt: request.completedAt
        },
        success: true
      });

    } catch (error) {
      // Mark as rejected
      request.status = RequestStatus.REJECTED;
      request.rejectionReason = error instanceof Error ? error.message : 'Unknown error';
      this.requests.set(requestId, request);
      await this.saveRequests();

      // Log failure
      await auditLogger.logEvent({
        type: AuditEventType.PRIVACY_SETTINGS_CHANGED,
        severity: AuditSeverity.CRITICAL,
        userId: request.userId,
        action: `Privacy request failed: ${request.type}`,
        details: {
          requestId,
          error: request.rejectionReason
        },
        success: false,
        errorMessage: request.rejectionReason
      });

      throw error;
    }
  }

  /**
   * Export user data
   */
  async exportUserData(
    userId: string,
    format: ExportFormat = ExportFormat.JSON,
    categories?: string[],
    timeRange?: { start: number; end: number }
  ): Promise<UserDataExport> {
    // Check consent for data export
    if (!consentManager.hasConsent(userId, ConsentType.DATA_PROCESSING)) {
      throw new Error('User has not consented to data processing');
    }

    const exportData: UserDataExport = {
      userId,
      exportedAt: Date.now(),
      format,
      categories: categories || Array.from(this.dataCategories.keys()),
      timeRange,
      data: {}
    };

    // Get data for each category
    for (const categoryId of exportData.categories) {
      const category = this.dataCategories.get(categoryId);
      if (!category || !category.isExportable) {
        continue;
      }

      try {
        const categoryData = await this.getCategoryData(userId, category, timeRange);
        exportData.data[categoryId] = categoryData;
      } catch (error) {
        console.error(`Failed to export data for category ${categoryId}:`, error);
        exportData.data[categoryId] = { error: 'Export failed' };
      }
    }

    // Log data export
    await auditLogger.logEvent({
      type: AuditEventType.DATA_EXPORT_PERFORMED,
      severity: AuditSeverity.HIGH,
      userId,
      action: 'User data exported',
      details: {
        format,
        categories: exportData.categories,
        timeRange,
        dataSize: JSON.stringify(exportData.data).length
      },
      success: true
    });

    return exportData;
  }

  /**
   * Delete user data
   */
  async deleteUserData(
    userId: string,
    categories?: string[],
    timeRange?: { start: number; end: number }
  ): Promise<DataDeletionResult> {
    const deletionResult: DataDeletionResult = {
      userId,
      deletedAt: Date.now(),
      categories: categories || Array.from(this.dataCategories.keys()),
      timeRange,
      deletedRecords: {},
      errors: {}
    };

    // Delete data for each category
    for (const categoryId of deletionResult.categories) {
      const category = this.dataCategories.get(categoryId);
      if (!category || !category.isDeletable) {
        deletionResult.errors[categoryId] = 'Category not deletable';
        continue;
      }

      try {
        const deletedCount = await this.deleteCategoryData(userId, category, timeRange);
        deletionResult.deletedRecords[categoryId] = deletedCount;
      } catch (error) {
        console.error(`Failed to delete data for category ${categoryId}:`, error);
        deletionResult.errors[categoryId] = error instanceof Error ? error.message : 'Deletion failed';
      }
    }

    // Log data deletion
    await auditLogger.logEvent({
      type: AuditEventType.USER_DELETED,
      severity: AuditSeverity.CRITICAL,
      userId,
      action: 'User data deleted',
      details: {
        categories: deletionResult.categories,
        timeRange,
        deletedRecords: deletionResult.deletedRecords,
        errors: deletionResult.errors
      },
      success: Object.keys(deletionResult.errors).length === 0
    });

    return deletionResult;
  }

  /**
   * Get user privacy dashboard data
   */
  async getPrivacyDashboard(userId: string): Promise<PrivacyDashboard> {
    const userRequests = Array.from(this.requests.values())
      .filter(request => request.userId === userId);

    const consentSummary = consentManager.getConsentSummary(userId);
    
    const dataCategories = Array.from(this.dataCategories.values()).map(category => ({
      id: category.id,
      name: category.name,
      description: category.description,
      classification: category.classification,
      isExportable: category.isExportable,
      isDeletable: category.isDeletable,
      recordCount: 0 // Would be populated from actual data
    }));

    return {
      userId,
      generatedAt: Date.now(),
      consentSummary,
      dataCategories,
      recentRequests: userRequests.slice(-10),
      privacyScore: this.calculatePrivacyScore(userId),
      recommendations: this.getPrivacyRecommendations(userId)
    };
  }

  /**
   * Get privacy request by ID
   */
  getRequest(requestId: string): PrivacyRequest | null {
    return this.requests.get(requestId) || null;
  }

  /**
   * Get user's privacy requests
   */
  getUserRequests(userId: string): PrivacyRequest[] {
    return Array.from(this.requests.values())
      .filter(request => request.userId === userId)
      .sort((a, b) => b.requestedAt - a.requestedAt);
  }

  /**
   * Cancel privacy request
   */
  async cancelRequest(requestId: string, userId: string): Promise<void> {
    const request = this.requests.get(requestId);
    if (!request) {
      throw new Error(`Request ${requestId} not found`);
    }

    if (request.userId !== userId) {
      throw new Error('Unauthorized to cancel this request');
    }

    if (request.status !== RequestStatus.PENDING) {
      throw new Error('Can only cancel pending requests');
    }

    request.status = RequestStatus.CANCELLED;
    this.requests.set(requestId, request);
    await this.saveRequests();

    // Log cancellation
    await auditLogger.logEvent({
      type: AuditEventType.PRIVACY_SETTINGS_CHANGED,
      severity: AuditSeverity.MEDIUM,
      userId,
      action: `Privacy request cancelled: ${request.type}`,
      details: { requestId },
      success: true
    });
  }

  /**
   * Check if request type can be auto-processed
   */
  private canAutoProcess(type: PrivacyControlType): boolean {
    const autoProcessTypes = [
      PrivacyControlType.DATA_EXPORT,
      PrivacyControlType.DATA_PORTABILITY
    ];
    return autoProcessTypes.includes(type);
  }

  /**
   * Process data export request
   */
  private async processDataExport(request: PrivacyRequest): Promise<any> {
    const exportData = await this.exportUserData(
      request.userId,
      ExportFormat.JSON,
      request.dataCategories,
      request.timeRange
    );

    // In a real implementation, this would generate a download link
    return {
      exportId: crypto.randomUUID(),
      downloadUrl: `/api/privacy/export/${exportData.userId}`,
      expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days
    };
  }

  /**
   * Process data deletion request
   */
  private async processDataDeletion(request: PrivacyRequest): Promise<any> {
    const deletionResult = await this.deleteUserData(
      request.userId,
      request.dataCategories,
      request.timeRange
    );

    return deletionResult;
  }

  /**
   * Process data rectification request
   */
  private async processDataRectification(request: PrivacyRequest): Promise<any> {
    // In a real implementation, this would update the user's data
    console.log(`Processing data rectification for user ${request.userId}`);
    return { message: 'Data rectification completed' };
  }

  /**
   * Process data portability request
   */
  private async processDataPortability(request: PrivacyRequest): Promise<any> {
    // Similar to export but in a structured, machine-readable format
    return await this.processDataExport(request);
  }

  /**
   * Process processing restriction request
   */
  private async processProcessingRestriction(request: PrivacyRequest): Promise<any> {
    // In a real implementation, this would restrict processing of user's data
    console.log(`Processing restriction request for user ${request.userId}`);
    return { message: 'Processing restriction applied' };
  }

  /**
   * Process objection to processing request
   */
  private async processObjectionToProcessing(request: PrivacyRequest): Promise<any> {
    // In a real implementation, this would stop certain types of processing
    console.log(`Processing objection for user ${request.userId}`);
    return { message: 'Objection to processing recorded' };
  }

  /**
   * Process automated decision objection request
   */
  private async processAutomatedDecisionObjection(request: PrivacyRequest): Promise<any> {
    // In a real implementation, this would opt user out of automated decisions
    console.log(`Processing automated decision objection for user ${request.userId}`);
    return { message: 'Automated decision objection recorded' };
  }

  /**
   * Get category data for user
   */
  private async getCategoryData(
    userId: string,
    category: DataCategory,
    timeRange?: { start: number; end: number }
  ): Promise<any> {
    // This should be implemented with your actual database queries
    throw new Error('getCategoryData must be implemented with actual database integration');
  }

  /**
   * Delete category data for user
   */
  private async deleteCategoryData(
    userId: string,
    category: DataCategory,
    timeRange?: { start: number; end: number }
  ): Promise<number> {
    // In a real implementation, this would delete from the actual database
    console.log(`Deleting ${category.name} data for user ${userId}`);
    return 0; // Return number of deleted records
  }

  /**
   * Calculate privacy score for user
   */
  private calculatePrivacyScore(userId: string): number {
    const consentSummary = consentManager.getConsentSummary(userId);
    const userRequests = this.getUserRequests(userId);

    let score = 0;

    // Base score for having consents configured
    if (consentSummary.totalConsents > 0) {
      score += 30;
    }

    // Score for granted consents
    const consentRatio = consentSummary.grantedConsents / consentSummary.totalConsents;
    score += consentRatio * 40;

    // Score for privacy activity
    if (userRequests.length > 0) {
      score += 20;
    }

    // Score for recent activity
    const recentActivity = userRequests.some(r => 
      Date.now() - r.requestedAt < 30 * 24 * 60 * 60 * 1000
    );
    if (recentActivity) {
      score += 10;
    }

    return Math.min(100, Math.max(0, score));
  }

  /**
   * Get privacy recommendations for user
   */
  private getPrivacyRecommendations(userId: string): string[] {
    const recommendations: string[] = [];
    const consentSummary = consentManager.getConsentSummary(userId);

    if (consentSummary.pendingConsents > 0) {
      recommendations.push('Review and update your consent preferences');
    }

    if (consentSummary.expiredConsents > 0) {
      recommendations.push('Renew expired consents to continue using all features');
    }

    const userRequests = this.getUserRequests(userId);
    const hasRecentExport = userRequests.some(r => 
      r.type === PrivacyControlType.DATA_EXPORT &&
      Date.now() - r.requestedAt < 365 * 24 * 60 * 60 * 1000
    );

    if (!hasRecentExport) {
      recommendations.push('Consider exporting your data to see what information we have');
    }

    return recommendations;
  }

  /**
   * Initialize default data categories
   */
  private initializeDefaultCategories(): void {
    if (this.dataCategories.size === 0) {
      DEFAULT_DATA_CATEGORIES.forEach(category => {
        this.dataCategories.set(category.id, category);
      });
      this.saveDataCategories();
    }
  }

  /**
   * Load requests from storage
   */
  private loadRequests(): void {
    try {
      const stored = localStorage.getItem(this.REQUESTS_KEY);
      if (stored) {
        const requests = JSON.parse(stored);
        requests.forEach((request: PrivacyRequest) => {
          this.requests.set(request.id, request);
        });
      }
    } catch (error) {
      console.error('Failed to load privacy requests:', error);
    }
  }

  /**
   * Save requests to storage
   */
  private async saveRequests(): Promise<void> {
    try {
      const requests = Array.from(this.requests.values());
      localStorage.setItem(this.REQUESTS_KEY, JSON.stringify(requests));
    } catch (error) {
      console.error('Failed to save privacy requests:', error);
    }
  }

  /**
   * Load data categories from storage
   */
  private loadDataCategories(): void {
    try {
      const stored = localStorage.getItem(this.CATEGORIES_KEY);
      if (stored) {
        const categories = JSON.parse(stored);
        categories.forEach((category: DataCategory) => {
          this.dataCategories.set(category.id, category);
        });
      }
    } catch (error) {
      console.error('Failed to load data categories:', error);
    }
  }

  /**
   * Save data categories to storage
   */
  private async saveDataCategories(): Promise<void> {
    try {
      const categories = Array.from(this.dataCategories.values());
      localStorage.setItem(this.CATEGORIES_KEY, JSON.stringify(categories));
    } catch (error) {
      console.error('Failed to save data categories:', error);
    }
  }
}

// Types and interfaces
export interface UserDataExport {
  userId: string;
  exportedAt: number;
  format: ExportFormat;
  categories: string[];
  timeRange?: { start: number; end: number };
  data: Record<string, any>;
}

export interface DataDeletionResult {
  userId: string;
  deletedAt: number;
  categories: string[];
  timeRange?: { start: number; end: number };
  deletedRecords: Record<string, number>;
  errors: Record<string, string>;
}

export interface PrivacyDashboard {
  userId: string;
  generatedAt: number;
  consentSummary: any;
  dataCategories: Array<{
    id: string;
    name: string;
    description: string;
    classification: DataClassification;
    isExportable: boolean;
    isDeletable: boolean;
    recordCount: number;
  }>;
  recentRequests: PrivacyRequest[];
  privacyScore: number;
  recommendations: string[];
}

// Default data categories
export const DEFAULT_DATA_CATEGORIES: DataCategory[] = [
  {
    id: 'profile',
    name: 'Profile Information',
    description: 'Basic user profile data including name, email, and contact information',
    classification: DataClassification.CONFIDENTIAL,
    tables: ['users'],
    fields: ['name', 'email', 'phone', 'address'],
    isExportable: true,
    isDeletable: true
  },
  {
    id: 'financial',
    name: 'Financial Data',
    description: 'Payment history, invoices, and financial transactions',
    classification: DataClassification.RESTRICTED,
    tables: ['payments', 'invoices'],
    fields: ['amount', 'paymentMethod', 'transactionId'],
    retentionPeriod: 2555, // 7 years
    isExportable: true,
    isDeletable: false // Financial records must be retained
  },
  {
    id: 'property',
    name: 'Property Data',
    description: 'Property and lease information',
    classification: DataClassification.INTERNAL,
    tables: ['properties', 'leases', 'units'],
    fields: ['propertyName', 'unitNumber', 'leaseTerms'],
    isExportable: true,
    isDeletable: true
  },
  {
    id: 'communication',
    name: 'Communication History',
    description: 'SMS, email, and notification history',
    classification: DataClassification.CONFIDENTIAL,
    tables: ['communications', 'notifications'],
    fields: ['message', 'recipient', 'sentAt'],
    retentionPeriod: 365, // 1 year
    isExportable: true,
    isDeletable: true
  },
  {
    id: 'maintenance',
    name: 'Maintenance Records',
    description: 'Maintenance requests and service history',
    classification: DataClassification.INTERNAL,
    tables: ['maintenance_tickets'],
    fields: ['description', 'status', 'assignedVendor'],
    isExportable: true,
    isDeletable: true
  },
  {
    id: 'analytics',
    name: 'Usage Analytics',
    description: 'Application usage patterns and analytics data',
    classification: DataClassification.INTERNAL,
    tables: ['analytics_events'],
    fields: ['eventType', 'timestamp', 'metadata'],
    retentionPeriod: 730, // 2 years
    isExportable: true,
    isDeletable: true
  }
];

// Convenience function to get privacy controls manager instance
export const privacyControlsManager = PrivacyControlsManager.getInstance();