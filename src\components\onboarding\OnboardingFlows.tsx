/**
 * Predefined onboarding flows for different user roles
 */
import { OnboardingFlow } from './OnboardingProvider';

export const propertyManagerOnboarding: OnboardingFlow = {
  id: 'property-manager-intro',
  name: 'Property Manager Introduction',
  description: 'Learn the basics of managing properties in EstatePulse',
  role: 'manager',
  autoStart: true,
  completionKey: 'onboarding-property-manager-completed',
  steps: [
    {
      id: 'welcome',
      title: 'Welcome to EstatePulse!',
      content: 'Welcome to your comprehensive property management solution. This quick tour will help you get started with the key features.',
      position: 'center',
      skippable: false,
    },
    {
      id: 'navigation',
      title: 'Navigation Menu',
      content: 'Use this navigation menu to access different sections of the application. You can manage properties, leases, maintenance, and more.',
      target: 'nav[role="navigation"]',
      position: 'bottom',
    },
    {
      id: 'properties',
      title: 'Properties Section',
      content: 'Click here to manage your properties. You can add new properties, view existing ones, and track occupancy rates.',
      target: 'button:contains("Properties"), a[href*="properties"]',
      position: 'bottom',
      action: {
        type: 'navigate',
        url: '/properties',
      },
    },
    {
      id: 'add-property',
      title: 'Add New Property',
      content: 'Use this button to add a new property to your portfolio. You\'ll be able to enter all the property details and upload images.',
      target: 'button:contains("Add Property"), [aria-label*="Add Property"]',
      position: 'bottom',
    },
    {
      id: 'property-list',
      title: 'Property List',
      content: 'Here you can see all your properties at a glance. Click on any property to view detailed information or make changes.',
      target: '[role="table"], .property-list, .properties-grid',
      position: 'top',
    },
    {
      id: 'accessibility',
      title: 'Accessibility Settings',
      content: 'EstatePulse is designed to be accessible. You can customize your experience in the accessibility settings.',
      target: 'button:contains("Accessibility"), a[href*="accessibility"]',
      position: 'bottom',
    },
    {
      id: 'help',
      title: 'Getting Help',
      content: 'Press Shift+? anytime to see keyboard shortcuts, or look for help tooltips throughout the application.',
      position: 'center',
    },
  ],
};

export const tenantOnboarding: OnboardingFlow = {
  id: 'tenant-intro',
  name: 'Tenant Portal Introduction',
  description: 'Learn how to use your tenant portal effectively',
  role: 'tenant',
  autoStart: true,
  completionKey: 'onboarding-tenant-completed',
  steps: [
    {
      id: 'welcome',
      title: 'Welcome to Your Tenant Portal!',
      content: 'This is your personal portal where you can manage your lease, make payments, and submit maintenance requests.',
      position: 'center',
      skippable: false,
    },
    {
      id: 'dashboard',
      title: 'Your Dashboard',
      content: 'Your dashboard shows important information about your lease, upcoming payments, and recent activity.',
      target: '.tenant-dashboard, [role="main"]',
      position: 'center',
    },
    {
      id: 'payments',
      title: 'Make Payments',
      content: 'Click here to make rent payments. You can pay using M-PESA or credit card, and view your payment history.',
      target: 'button:contains("Pay"), a[href*="payment"]',
      position: 'bottom',
    },
    {
      id: 'maintenance',
      title: 'Maintenance Requests',
      content: 'Submit maintenance requests here. You can track the status of your requests and communicate with maintenance staff.',
      target: 'button:contains("Maintenance"), a[href*="maintenance"]',
      position: 'bottom',
    },
    {
      id: 'documents',
      title: 'Documents',
      content: 'Access your lease documents, receipts, and other important files in this section.',
      target: 'button:contains("Documents"), a[href*="documents"]',
      position: 'bottom',
    },
  ],
};

export const ownerOnboarding: OnboardingFlow = {
  id: 'owner-intro',
  name: 'Property Owner Introduction',
  description: 'Learn how to monitor and manage your property investments',
  role: 'owner',
  autoStart: true,
  completionKey: 'onboarding-owner-completed',
  steps: [
    {
      id: 'welcome',
      title: 'Welcome, Property Owner!',
      content: 'As a property owner, you have access to comprehensive analytics and reports about your investments.',
      position: 'center',
      skippable: false,
    },
    {
      id: 'financial-dashboard',
      title: 'Financial Dashboard',
      content: 'Monitor your property performance with detailed financial analytics, occupancy rates, and revenue tracking.',
      target: 'button:contains("Financial"), a[href*="financial"]',
      position: 'bottom',
    },
    {
      id: 'reports',
      title: 'Executive Reports',
      content: 'Access comprehensive reports including P&L statements, occupancy analysis, and market comparisons.',
      target: 'button:contains("Reports"), a[href*="reports"]',
      position: 'bottom',
    },
    {
      id: 'analytics',
      title: 'Property Analytics',
      content: 'Dive deep into property performance with advanced analytics and forecasting tools.',
      target: 'button:contains("Analytics"), a[href*="analytics"]',
      position: 'bottom',
    },
    {
      id: 'compliance',
      title: 'Compliance Monitoring',
      content: 'Stay compliant with regulations and track KYC status, document verification, and regulatory reporting.',
      target: 'button:contains("Compliance"), a[href*="compliance"]',
      position: 'bottom',
    },
  ],
};

export const vendorOnboarding: OnboardingFlow = {
  id: 'vendor-intro',
  name: 'Vendor Introduction',
  description: 'Learn how to manage maintenance requests and track your work',
  role: 'vendor',
  autoStart: true,
  completionKey: 'onboarding-vendor-completed',
  steps: [
    {
      id: 'welcome',
      title: 'Welcome, Vendor!',
      content: 'As a vendor, you can view assigned maintenance tickets, update work progress, and manage your service requests.',
      position: 'center',
      skippable: false,
    },
    {
      id: 'tickets',
      title: 'Assigned Tickets',
      content: 'View all maintenance tickets assigned to you. You can see priority levels, SLA deadlines, and property details.',
      target: '.ticket-list, [role="table"]',
      position: 'top',
    },
    {
      id: 'update-status',
      title: 'Update Work Status',
      content: 'Keep property managers informed by updating the status of your work and adding progress notes.',
      target: 'button:contains("Update"), .status-update',
      position: 'bottom',
    },
    {
      id: 'sla-tracking',
      title: 'SLA Tracking',
      content: 'Monitor your SLA compliance and deadlines to maintain good vendor ratings and relationships.',
      target: '.sla-tracker, .deadline-indicator',
      position: 'top',
    },
  ],
};

export const accessibilityOnboarding: OnboardingFlow = {
  id: 'accessibility-intro',
  name: 'Accessibility Features Tour',
  description: 'Learn about the accessibility features available in EstatePulse',
  autoStart: false,
  completionKey: 'onboarding-accessibility-completed',
  steps: [
    {
      id: 'welcome',
      title: 'Accessibility in EstatePulse',
      content: 'EstatePulse is designed to be accessible to everyone. Let\'s explore the accessibility features available to you.',
      position: 'center',
      skippable: false,
    },
    {
      id: 'keyboard-navigation',
      title: 'Keyboard Navigation',
      content: 'You can navigate the entire application using just your keyboard. Press Tab to move between elements, and use arrow keys in menus.',
      position: 'center',
    },
    {
      id: 'skip-links',
      title: 'Skip Links',
      content: 'Press Tab when the page loads to reveal skip links that help you jump to main content areas quickly.',
      position: 'center',
    },
    {
      id: 'keyboard-shortcuts',
      title: 'Keyboard Shortcuts',
      content: 'Press Shift+? to see all available keyboard shortcuts. These can help you work more efficiently.',
      position: 'center',
    },
    {
      id: 'themes',
      title: 'Accessibility Themes',
      content: 'Choose from high contrast themes and color-blind friendly options in the accessibility settings.',
      target: 'button:contains("Accessibility"), a[href*="accessibility"]',
      position: 'bottom',
    },
    {
      id: 'screen-reader',
      title: 'Screen Reader Support',
      content: 'EstatePulse works with screen readers and provides live announcements for important changes.',
      position: 'center',
    },
    {
      id: 'font-settings',
      title: 'Font and Display Settings',
      content: 'Adjust font size, weight, and reduce motion in the accessibility settings to suit your needs.',
      position: 'center',
    },
  ],
};

export const allOnboardingFlows = [
  propertyManagerOnboarding,
  tenantOnboarding,
  ownerOnboarding,
  vendorOnboarding,
  accessibilityOnboarding,
];