# EstatePulse Testing Suite

This directory contains comprehensive tests for the EstatePulse property management application, covering unit tests, integration tests, end-to-end tests, and performance tests.

## Test Structure

```
tests/
├── e2e/                    # End-to-end tests with <PERSON><PERSON>
│   ├── auth.spec.ts        # Authentication flow tests
│   ├── property-management.spec.ts  # Property management tests
│   ├── payment-flow.spec.ts         # Payment processing tests
│   ├── maintenance-workflow.spec.ts # Maintenance ticket tests
│   ├── cross-platform.spec.ts      # Cross-platform compatibility
│   └── setup.ts            # Test setup and authentication
├── integration/            # Integration tests
│   └── api-endpoints.test.ts        # API integration tests
├── performance/            # Performance tests with k6
│   ├── load-test.js        # Load testing scenarios
│   └── stress-test.js      # Stress testing scenarios
├── test-config.ts          # Test configuration and utilities
└── README.md              # This file
```

## Test Types

### 1. Unit Tests

Located in `src/**/__tests__/` directories alongside the source code.

**Coverage:**
- Business logic functions
- React components
- Utility functions
- Validation logic
- Convex functions
- Service integrations

**Technologies:**
- Vitest for test runner
- React Testing Library for component testing
- convex-test for Convex function testing

**Running Unit Tests:**
```bash
npm run test:unit          # Run all unit tests
npm run test              # Run tests in watch mode
```

### 2. Integration Tests

Located in `tests/integration/` directory.

**Coverage:**
- API endpoint integration
- Database operations
- Real-time synchronization
- Payment flow integration
- Maintenance workflow integration

**Technologies:**
- Vitest for test runner
- convex-test for database integration

**Running Integration Tests:**
```bash
npm run test:integration
```

### 3. End-to-End Tests

Located in `tests/e2e/` directory.

**Coverage:**
- Complete user workflows
- Authentication flows
- Property management operations
- Payment processing
- Maintenance ticket lifecycle
- Cross-platform compatibility
- Mobile responsiveness

**Technologies:**
- Playwright for browser automation
- Multiple browser support (Chrome, Firefox, Safari)
- Mobile device simulation

**Running E2E Tests:**
```bash
npm run test:e2e           # Run all e2e tests
npm run test:e2e:headed    # Run with browser UI
npm run test:e2e:debug     # Run in debug mode
```

### 4. Performance Tests

Located in `tests/performance/` directory.

**Coverage:**
- Load testing with concurrent users
- Stress testing to find breaking points
- API response time validation
- Database performance under load
- Real-time synchronization performance

**Technologies:**
- k6 for performance testing
- Custom metrics and thresholds

**Running Performance Tests:**
```bash
npm run test:performance   # Run load tests
npm run test:stress       # Run stress tests
```

## Test Configuration

### Environment Variables

Create a `.env.test` file for test-specific configuration:

```env
VITE_API_URL=http://localhost:5173
CONVEX_URL=https://test-convex-url.convex.cloud
PLAYWRIGHT_BROWSERS=chromium,firefox,webkit
```

### Test Data

Tests use generated test data to avoid conflicts:
- Unique email addresses with timestamps
- Random property names and addresses
- Generated phone numbers for M-PESA testing
- Mock payment tokens for Stripe testing

### Mock Services

Tests include comprehensive mocking for:
- Authentication APIs
- Payment processing (M-PESA, Stripe)
- SMS/WhatsApp services
- File upload services
- Real-time synchronization

## Test Scenarios

### Authentication Tests
- Login with valid credentials
- Login with invalid credentials
- Sign up flow
- Password reset
- Session management
- Role-based access control

### Property Management Tests
- Create, read, update, delete properties
- Property search and filtering
- Unit management within properties
- Property analytics and reporting
- Image upload and management

### Payment Flow Tests
- M-PESA STK push integration
- Stripe payment processing
- Payment status tracking
- Payment history and receipts
- Recurring payment setup
- Payment failure handling

### Maintenance Workflow Tests
- Ticket creation by tenants
- Vendor assignment by managers
- SLA tracking and escalation
- Ticket status updates
- Comment system
- Analytics and reporting

### Cross-Platform Tests
- Desktop browsers (Chrome, Firefox, Safari)
- Mobile devices (iOS, Android)
- Tablet devices
- Responsive design validation
- Touch interaction testing
- Keyboard navigation

## Performance Benchmarks

### Load Testing Thresholds
- **Concurrent Users:** Up to 20 users
- **Response Time:** 95% of requests < 500ms
- **Error Rate:** < 10%
- **Throughput:** > 100 requests/second

### Stress Testing Thresholds
- **Peak Load:** Up to 400 concurrent users
- **Response Time:** 95% of requests < 2000ms
- **Error Rate:** < 30%
- **Breaking Point:** Identify system limits

## CI/CD Integration

### GitHub Actions Workflow

```yaml
name: Test Suite
on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:unit

  integration-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npx playwright install
      - run: npm run test:e2e

  performance-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:performance
```

## Test Data Management

### Test Database
- Separate test database for integration tests
- Automatic cleanup after each test suite
- Seeded with consistent test data

### Mock Data Generation
- Realistic property data for Nairobi market
- Valid Kenyan phone numbers for M-PESA testing
- Proper address formats and postal codes
- Realistic rent amounts and property sizes

## Debugging Tests

### Unit Tests
```bash
# Run specific test file
npm run test src/components/auth/__tests__/LoginForm.test.tsx

# Run tests in debug mode
npm run test -- --inspect-brk
```

### E2E Tests
```bash
# Run specific test file
npx playwright test tests/e2e/auth.spec.ts

# Run in headed mode (see browser)
npx playwright test --headed

# Run in debug mode
npx playwright test --debug

# Generate test report
npx playwright show-report
```

### Performance Tests
```bash
# Run with custom options
k6 run --vus 10 --duration 30s tests/performance/load-test.js

# Run with environment variables
K6_VUS=50 K6_DURATION=2m k6 run tests/performance/stress-test.js
```

## Test Coverage

### Coverage Reports
```bash
# Generate coverage report
npm run test:unit -- --coverage

# View coverage in browser
npm run test:unit -- --coverage --reporter=html
```

### Coverage Targets
- **Unit Tests:** > 80% code coverage
- **Integration Tests:** > 70% API endpoint coverage
- **E2E Tests:** > 90% user workflow coverage

## Best Practices

### Writing Tests
1. **Descriptive Names:** Use clear, descriptive test names
2. **Arrange-Act-Assert:** Follow AAA pattern
3. **Independent Tests:** Each test should be independent
4. **Mock External Dependencies:** Mock APIs, databases, etc.
5. **Test Edge Cases:** Include error conditions and edge cases

### Test Data
1. **Generate Unique Data:** Use timestamps or UUIDs
2. **Clean Up:** Remove test data after tests
3. **Realistic Data:** Use realistic test data
4. **Avoid Hard-coding:** Use configuration for test data

### Performance
1. **Parallel Execution:** Run tests in parallel when possible
2. **Selective Testing:** Run only relevant tests during development
3. **Optimize Setup:** Minimize test setup time
4. **Resource Management:** Clean up resources after tests

## Troubleshooting

### Common Issues

**Tests Failing Locally:**
- Check environment variables
- Ensure test database is running
- Clear browser cache for e2e tests
- Update dependencies

**Flaky Tests:**
- Add proper wait conditions
- Increase timeouts for slow operations
- Check for race conditions
- Use deterministic test data

**Performance Test Issues:**
- Check system resources
- Verify network connectivity
- Adjust load test parameters
- Monitor application logs

### Getting Help

1. Check test logs and error messages
2. Review test configuration
3. Consult team documentation
4. Ask in team chat or create issue

## Maintenance

### Regular Tasks
- Update test dependencies monthly
- Review and update test data
- Monitor test execution times
- Update browser versions for e2e tests
- Review and update performance thresholds

### Test Metrics
- Track test execution time trends
- Monitor test failure rates
- Measure code coverage trends
- Performance benchmark tracking