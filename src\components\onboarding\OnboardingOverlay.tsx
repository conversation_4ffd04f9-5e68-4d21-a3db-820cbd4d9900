/**
 * Onboarding overlay component that highlights elements and shows step content
 */
import React, { useEffect, useRef, useState } from 'react';
import { useOnboarding } from './OnboardingProvider';
import { useAccessibility } from '../../contexts/AccessibilityContext';
import { AccessibleButton } from '../accessibility/AccessibleButton';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { useKeyboardNavigation, KEYBOARD_KEYS } from '../../lib/accessibility';

interface ElementPosition {
  top: number;
  left: number;
  width: number;
  height: number;
}

export function OnboardingOverlay() {
  const {
    currentFlow,
    currentStep,
    isActive,
    nextStep,
    previousStep,
    skipStep,
    completeFlow,
    dismissFlow,
  } = useOnboarding();
  
  const { announceToScreenReader } = useAccessibility();
  const overlayRef = useRef<HTMLDivElement>(null);
  const [targetPosition, setTargetPosition] = useState<ElementPosition | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState<{ top: number; left: number }>({ top: 0, left: 0 });

  // Enable keyboard navigation for the overlay
  useKeyboardNavigation(overlayRef, {
    onEscape: dismissFlow,
    trapFocus: true,
    autoFocus: true,
  });

  // Calculate positions for target element and tooltip
  useEffect(() => {
    if (!isActive || !currentFlow) return;

    const step = currentFlow.steps[currentStep];
    if (!step.target) {
      setTargetPosition(null);
      return;
    }

    const targetElement = document.querySelector(step.target) as HTMLElement;
    if (!targetElement) {
      console.warn(`Onboarding target element "${step.target}" not found`);
      setTargetPosition(null);
      return;
    }

    const rect = targetElement.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    const position: ElementPosition = {
      top: rect.top + scrollTop,
      left: rect.left + scrollLeft,
      width: rect.width,
      height: rect.height,
    };

    setTargetPosition(position);

    // Calculate tooltip position based on step position preference
    const tooltipWidth = 320;
    const tooltipHeight = 200;
    const margin = 16;

    let tooltipTop = position.top;
    let tooltipLeft = position.left;

    switch (step.position) {
      case 'top':
        tooltipTop = position.top - tooltipHeight - margin;
        tooltipLeft = position.left + (position.width - tooltipWidth) / 2;
        break;
      case 'bottom':
        tooltipTop = position.top + position.height + margin;
        tooltipLeft = position.left + (position.width - tooltipWidth) / 2;
        break;
      case 'left':
        tooltipTop = position.top + (position.height - tooltipHeight) / 2;
        tooltipLeft = position.left - tooltipWidth - margin;
        break;
      case 'right':
        tooltipTop = position.top + (position.height - tooltipHeight) / 2;
        tooltipLeft = position.left + position.width + margin;
        break;
      case 'center':
      default:
        tooltipTop = window.innerHeight / 2 - tooltipHeight / 2 + scrollTop;
        tooltipLeft = window.innerWidth / 2 - tooltipWidth / 2 + scrollLeft;
        break;
    }

    // Ensure tooltip stays within viewport
    const maxTop = window.innerHeight + scrollTop - tooltipHeight - margin;
    const maxLeft = window.innerWidth + scrollLeft - tooltipWidth - margin;
    
    tooltipTop = Math.max(margin + scrollTop, Math.min(tooltipTop, maxTop));
    tooltipLeft = Math.max(margin + scrollLeft, Math.min(tooltipLeft, maxLeft));

    setTooltipPosition({ top: tooltipTop, left: tooltipLeft });

    // Scroll target element into view if needed
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'center',
    });
  }, [isActive, currentFlow, currentStep]);

  // Handle keyboard shortcuts
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!isActive || !currentFlow) return;

    switch (event.key) {
      case KEYBOARD_KEYS.ARROW_RIGHT:
      case KEYBOARD_KEYS.SPACE:
      case KEYBOARD_KEYS.ENTER:
        event.preventDefault();
        nextStep();
        break;
      case KEYBOARD_KEYS.ARROW_LEFT:
        event.preventDefault();
        if (currentStep > 0) {
          previousStep();
        }
        break;
      case 'S':
      case 's':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          skipStep();
        }
        break;
    }
  };

  if (!isActive || !currentFlow) {
    return null;
  }

  const step = currentFlow.steps[currentStep];
  const isLastStep = currentStep === currentFlow.steps.length - 1;
  const isFirstStep = currentStep === 0;

  return (
    <div
      ref={overlayRef}
      className="fixed inset-0 z-50 bg-black/50"
      onKeyDown={handleKeyDown}
      role="dialog"
      aria-modal="true"
      aria-labelledby="onboarding-title"
      aria-describedby="onboarding-description"
    >
      {/* Highlight overlay for target element */}
      {targetPosition && (
        <>
          {/* Top overlay */}
          <div
            className="absolute bg-black/50"
            style={{
              top: 0,
              left: 0,
              right: 0,
              height: targetPosition.top,
            }}
          />
          
          {/* Bottom overlay */}
          <div
            className="absolute bg-black/50"
            style={{
              top: targetPosition.top + targetPosition.height,
              left: 0,
              right: 0,
              bottom: 0,
            }}
          />
          
          {/* Left overlay */}
          <div
            className="absolute bg-black/50"
            style={{
              top: targetPosition.top,
              left: 0,
              width: targetPosition.left,
              height: targetPosition.height,
            }}
          />
          
          {/* Right overlay */}
          <div
            className="absolute bg-black/50"
            style={{
              top: targetPosition.top,
              left: targetPosition.left + targetPosition.width,
              right: 0,
              height: targetPosition.height,
            }}
          />
          
          {/* Highlight border */}
          <div
            className="absolute border-2 border-primary rounded-md pointer-events-none"
            style={{
              top: targetPosition.top - 2,
              left: targetPosition.left - 2,
              width: targetPosition.width + 4,
              height: targetPosition.height + 4,
            }}
          />
        </>
      )}

      {/* Tooltip */}
      <Card
        className="absolute w-80 max-w-sm shadow-lg"
        style={{
          top: tooltipPosition.top,
          left: tooltipPosition.left,
        }}
      >
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Badge variant="secondary" className="text-xs">
              Step {currentStep + 1} of {currentFlow.steps.length}
            </Badge>
            <AccessibleButton
              variant="ghost"
              size="sm"
              onClick={dismissFlow}
              aria-label="Close onboarding"
              className="h-6 w-6 p-0"
            >
              ×
            </AccessibleButton>
          </div>
          <CardTitle id="onboarding-title" className="text-lg">
            {step.title}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <CardDescription id="onboarding-description" className="text-sm">
            {step.content}
          </CardDescription>
          
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              {!isFirstStep && (
                <AccessibleButton
                  variant="outline"
                  size="sm"
                  onClick={previousStep}
                  shortcut="←"
                >
                  Previous
                </AccessibleButton>
              )}
              
              {step.skippable !== false && (
                <AccessibleButton
                  variant="ghost"
                  size="sm"
                  onClick={skipStep}
                  shortcut="Ctrl+S"
                >
                  Skip
                </AccessibleButton>
              )}
            </div>
            
            <AccessibleButton
              onClick={isLastStep ? completeFlow : nextStep}
              size="sm"
              shortcut={isLastStep ? "Enter" : "→ or Space"}
            >
              {isLastStep ? 'Complete' : 'Next'}
            </AccessibleButton>
          </div>
          
          <div className="text-xs text-muted-foreground">
            Use arrow keys to navigate, Escape to close
          </div>
        </CardContent>
      </Card>
    </div>
  );
}