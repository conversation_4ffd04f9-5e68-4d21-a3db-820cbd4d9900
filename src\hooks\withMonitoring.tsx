import React, { useEffect, useRef } from 'react';
import { useMonitoring } from './useMonitoring';

// Higher-order component for automatic component monitoring
export function withMonitoring<P extends object>(
    WrappedComponent: React.ComponentType<P>,
    componentName?: string
): React.ComponentType<P> {
    const MonitoredComponent = (props: P) => {
        const { trackComponentMount, trackComponentUnmount } = useMonitoring();
        const mountTimeRef = useRef<number>(Date.now());
        const name = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Unknown';

        useEffect(() => {
            trackComponentMount(name, props as Record<string, any>);

            return () => {
                const duration = Date.now() - mountTimeRef.current;
                trackComponentUnmount(name, duration);
            };
        }, [name, props, trackComponentMount, trackComponentUnmount]);

        return <WrappedComponent {...props} />;
    };

    MonitoredComponent.displayName = `withMonitoring(${componentName || WrappedComponent.displayName || WrappedComponent.name})`;

    return MonitoredComponent;
}

export default withMonitoring;