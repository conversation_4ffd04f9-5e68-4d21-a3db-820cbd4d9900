import React from 'react';
import { ConvexProvider, ConvexReactClient } from 'convex/react';
import { AuthProvider, useAuth } from './lib/auth-context';
import { AuthPage } from './components/auth/AuthPage';
import { UserProfile } from './components/auth/UserProfile';
import { PropertyManagement } from './components/properties/PropertyManagement';
import { ExecutiveReports } from './components/reports/ExecutiveReports';
import { OccupancyPerformanceAnalytics } from './components/analytics/OccupancyPerformanceAnalytics';
import { NativeDesktopManager } from './components/desktop/NativeDesktopManager';
import { UpdateManager } from './components/desktop/UpdateManager';
import { UpdateNotification } from './components/desktop/UpdateNotification';
import { PerformanceDashboard } from './components/monitoring/PerformanceDashboard';
import { Button } from './components/ui/button';
import { useNativeMenuActions } from './hooks/useNativeMenuActions';
import { useNativeNotifications } from './hooks/useNativeNotifications';
import { Toaster } from './components/ui/toaster';
import { AccessibilityProvider } from './contexts/AccessibilityContext';
import { AccessibilitySettings } from './components/accessibility/AccessibilitySettings';
import { SkipLinks, MainContent, NavigationLandmark, FooterLandmark } from './components/accessibility/SkipLinks';
import { KeyboardShortcutsButton } from './components/accessibility/KeyboardShortcutsHelp';
import { useKeyboardShortcuts, commonShortcuts, propertyManagementShortcuts } from './hooks/useKeyboardShortcuts';
import { OnboardingProvider } from './components/onboarding/OnboardingProvider';
import { OnboardingOverlay } from './components/onboarding/OnboardingOverlay';
import { OnboardingSetup } from './components/onboarding/OnboardingSetup';
import { HelpCenter } from './components/help/HelpCenter';
import { TutorialLauncher } from './components/onboarding/InteractiveTutorial';
import { MonitoringService } from './lib/monitoring';

const convex = new ConvexReactClient((import.meta as any).env.VITE_CONVEX_URL as string);

const AppContent: React.FC = () => {
  const { user, isAuthenticated, isLoading, signOut } = useAuth();
  const [currentView, setCurrentView] = React.useState<'dashboard' | 'properties' | 'profile' | 'reports' | 'analytics' | 'desktop' | 'updates' | 'accessibility' | 'help' | 'monitoring'>('dashboard');
  const [showUpdateNotification, setShowUpdateNotification] = React.useState(false);
  const notifications = useNativeNotifications();

  // Initialize monitoring when user is authenticated
  React.useEffect(() => {
    if (isAuthenticated && user) {
      const monitoring = MonitoringService.getInstance();
      
      // Initialize monitoring services
      monitoring.initialize({
        sentry: {
          dsn: import.meta.env.VITE_SENTRY_DSN,
          environment: import.meta.env.NODE_ENV || 'development',
          tracesSampleRate: import.meta.env.NODE_ENV === 'production' ? 0.1 : 1.0,
          profilesSampleRate: import.meta.env.NODE_ENV === 'production' ? 0.1 : 1.0,
        },
        posthog: {
          apiKey: import.meta.env.VITE_POSTHOG_API_KEY,
          apiHost: import.meta.env.VITE_POSTHOG_API_HOST || 'https://app.posthog.com',
          enableSessionRecording: import.meta.env.NODE_ENV === 'production',
          enableHeatmaps: import.meta.env.NODE_ENV === 'production',
        },
        enablePerformanceMonitoring: true,
        enableDatabaseMonitoring: true,
      });

      // Identify user for analytics
      monitoring.identifyUser(user._id, {
        email: user.email,
        name: user.name,
        role: user.role,
        kycStatus: user.kycStatus,
        isActive: user.isActive,
      });

      // Track user login
      monitoring.trackEvent('user_login', {
        role: user.role,
        kycStatus: user.kycStatus,
      });
    }
  }, [isAuthenticated, user]);

  // Enable keyboard shortcuts
  useKeyboardShortcuts({
    shortcuts: [...commonShortcuts, ...propertyManagementShortcuts],
  });

  // Handle native menu actions
  useNativeMenuActions({
    onNewProperty: () => setCurrentView('properties'),
    onNewLease: () => setCurrentView('properties'), // Could navigate to lease creation
    onNewTicket: () => setCurrentView('properties'), // Could navigate to maintenance
    onImportDocuments: async () => {
      // This will be handled by the NativeDesktopManager component
      setCurrentView('desktop');
    },
    onExportReport: async () => {
      // This will be handled by the NativeDesktopManager component
      setCurrentView('desktop');
    },
    onShowAbout: () => {
      notifications.showNotification({
        title: 'About EstatePulse',
        body: 'Cross-platform property management desktop application',
        icon: '/electron-vite.svg'
      });
    },
    onShowHelp: () => {
      notifications.showNotification({
        title: 'Help',
        body: 'Use keyboard shortcuts for quick navigation. Press Ctrl/Cmd + / to see all shortcuts.',
        icon: '/electron-vite.svg'
      });
    },
    onQuickNew: () => {
      notifications.showNotification({
        title: 'Quick New',
        body: 'Use Ctrl/Cmd + N for new property, Ctrl/Cmd + Shift + L for new lease',
        icon: '/electron-vite.svg'
      });
    },
    onGlobalSearch: () => {
      notifications.showNotification({
        title: 'Global Search',
        body: 'Global search functionality - coming soon!',
        icon: '/electron-vite.svg'
      });
    }
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <AuthPage />;
  }

  const renderContent = () => {
    switch (currentView) {
      case 'properties':
        return <PropertyManagement />;
      case 'reports':
        return (
          <div className="max-w-7xl mx-auto py-6 px-4">
            <ExecutiveReports />
          </div>
        );
      case 'analytics':
        return (
          <div className="max-w-7xl mx-auto py-6 px-4">
            <OccupancyPerformanceAnalytics />
          </div>
        );
      case 'profile':
        return (
          <div className="max-w-4xl mx-auto py-6 px-4">
            <UserProfile />
          </div>
        );
      case 'desktop':
        return (
          <div className="max-w-7xl mx-auto py-6 px-4">
            <NativeDesktopManager />
          </div>
        );
      case 'updates':
        return (
          <div className="max-w-7xl mx-auto py-6 px-4">
            <UpdateManager />
          </div>
        );
      case 'accessibility':
        return (
          <div className="max-w-7xl mx-auto py-6 px-4">
            <AccessibilitySettings />
          </div>
        );
      case 'help':
        return (
          <div className="max-w-7xl mx-auto py-6 px-4">
            <HelpCenter />
          </div>
        );
      case 'monitoring':
        return (
          <div className="max-w-7xl mx-auto py-6 px-4">
            <PerformanceDashboard />
          </div>
        );
      default:
        return (
          <div className="max-w-7xl mx-auto py-6 px-4">
            <div className="border-4 border-dashed border-gray-200 rounded-lg p-8">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Welcome to EstatePulse
                </h2>
                <p className="text-gray-600 mb-8">
                  Your comprehensive property management solution.
                </p>
                
                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-900">Account Status</h3>
                    <p className={`text-2xl font-bold mt-2 ${
                      user?.isActive ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {user?.isActive ? 'Active' : 'Inactive'}
                    </p>
                  </div>
                  
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-900">KYC Status</h3>
                    <p className={`text-2xl font-bold mt-2 capitalize ${
                      user?.kycStatus === 'verified' 
                        ? 'text-green-600'
                        : user?.kycStatus === 'rejected'
                        ? 'text-red-600'
                        : 'text-yellow-600'
                    }`}>
                      {user?.kycStatus}
                    </p>
                  </div>
                  
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-semibold text-gray-900">Properties Access</h3>
                    <p className="text-2xl font-bold text-blue-600 mt-2">
                      {user?.propertyAccess?.length || 0}
                    </p>
                  </div>
                </div>

                <div className="flex justify-center space-x-4">
                  <Button onClick={() => setCurrentView('properties')}>
                    Manage Properties
                  </Button>
                  <Button variant="outline" onClick={() => setCurrentView('profile')}>
                    View Profile
                  </Button>
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <SkipLinks />
      {/* Header */}
      <NavigationLandmark className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-8">
              <h1 
                className="text-xl font-semibold text-gray-900 cursor-pointer"
                onClick={() => setCurrentView('dashboard')}
              >
                EstatePulse
              </h1>
              
              <nav className="flex space-x-4">
                <button
                  onClick={() => setCurrentView('dashboard')}
                  className={`px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    currentView === 'dashboard'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Dashboard
                </button>
                <button
                  onClick={() => setCurrentView('properties')}
                  className={`px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    currentView === 'properties'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Properties
                </button>
                <button
                  onClick={() => setCurrentView('reports')}
                  className={`px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    currentView === 'reports'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Reports
                </button>
                <button
                  onClick={() => setCurrentView('analytics')}
                  className={`px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    currentView === 'analytics'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Analytics
                </button>
                <button
                  onClick={() => setCurrentView('desktop')}
                  className={`px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    currentView === 'desktop'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Desktop
                </button>
                <button
                  onClick={() => setCurrentView('updates')}
                  className={`px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    currentView === 'updates'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Updates
                </button>
                <button
                  onClick={() => setCurrentView('accessibility')}
                  className={`px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    currentView === 'accessibility'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  aria-label="Accessibility settings"
                >
                  Accessibility
                </button>
                <button
                  onClick={() => setCurrentView('help')}
                  className={`px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    currentView === 'help'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  aria-label="Help center"
                >
                  Help
                </button>
                <button
                  onClick={() => setCurrentView('monitoring')}
                  className={`px-3 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    currentView === 'monitoring'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  aria-label="Performance monitoring"
                >
                  Monitoring
                </button>
              </nav>
            </div>
            
            <div className="flex items-center space-x-4">
              <TutorialLauncher />
              <span className="text-sm text-gray-700">
                Welcome, {user?.name}
              </span>
              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 capitalize">
                {user?.role}
              </span>
              <Button
                onClick={() => setCurrentView('profile')}
                variant="outline"
                size="sm"
              >
                Profile
              </Button>
              <Button
                onClick={signOut}
                variant="outline"
                size="sm"
              >
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </NavigationLandmark>

      {/* Main Content */}
      <MainContent>
        {renderContent()}
      </MainContent>
      
      <FooterLandmark className="border-t mt-8 py-4">
        <div className="max-w-7xl mx-auto px-4 text-center text-sm text-gray-600">
          <p>&copy; 2024 EstatePulse. All rights reserved.</p>
        </div>
      </FooterLandmark>

      {/* Toast notifications */}
      <Toaster />
      
      {/* Update notification dialog */}
      <UpdateNotification 
        open={showUpdateNotification} 
        onOpenChange={setShowUpdateNotification} 
      />
      
      {/* Keyboard shortcuts help */}
      <KeyboardShortcutsButton />
      
      {/* Onboarding setup and overlay */}
      <OnboardingSetup />
      <OnboardingOverlay />
    </div>
  );
};

const App: React.FC = () => {
  return (
    <ConvexProvider client={convex}>
      <AccessibilityProvider>
        <OnboardingProvider>
          <AuthProvider convex={convex}>
            <AppContent />
          </AuthProvider>
        </OnboardingProvider>
      </AccessibilityProvider>
    </ConvexProvider>
  );
};

export default App;