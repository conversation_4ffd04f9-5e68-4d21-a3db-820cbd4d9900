import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../../convex/_generated/api';
import { Id } from '../../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Input } from '../../ui/input';
import { Badge } from '../../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs';
import { useToast } from '../../ui/use-toast';
import { 
  FileText, 
  Download, 
  Search,
  Eye,
  Calendar,
  User,
  File,
  Image,
  Archive,
  Shield
} from 'lucide-react';

interface TenantDocumentPortalProps {
  tenantId: Id<"users">;
  portalId: Id<"portals">;
}

export const TenantDocumentPortal: React.FC<TenantDocumentPortalProps> = ({ tenantId, portalId }) => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');

  const portal = useQuery(api.portals.getPortalById, { portalId });
  const tenant = useQuery(api.users.getById, { id: tenantId });
  const activeLease = useQuery(api.leases.getActiveLease, { tenantId });
  const leaseDocuments = useQuery(api.documents.getLeaseDocuments, 
    activeLease ? { leaseId: activeLease._id } : 'skip'
  );
  const propertyDocuments = useQuery(api.documents.getPropertyDocuments, 
    activeLease ? { propertyId: activeLease.propertyId } : 'skip'
  );
  const tenantDocuments = useQuery(api.documents.getTenantDocuments, { tenantId });

  const handleDownload = (document: any) => {
    // In a real implementation, this would handle secure document download
    window.open(document.url, '_blank');
    toast({
      title: "Download Started",
      description: `Downloading ${document.name}`,
    });
  };

  const handleView = (document: any) => {
    // In a real implementation, this would open a document viewer
    window.open(document.url, '_blank');
  };

  const getFileIcon = (type: string) => {
    if (type.includes('image')) return <Image className="w-5 h-5" />;
    if (type.includes('pdf')) return <FileText className="w-5 h-5" />;
    if (type.includes('zip') || type.includes('archive')) return <Archive className="w-5 h-5" />;
    return <File className="w-5 h-5" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const DocumentCard: React.FC<{ 
    document: any; 
    category?: string;
    showCategory?: boolean;
  }> = ({ document, category, showCategory = false }) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 text-primary rounded-lg">
              {getFileIcon(document.type)}
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-sm">{document.name}</h3>
              <p className="text-xs text-muted-foreground">
                {formatFileSize(document.size)} • {document.type}
              </p>
              {showCategory && category && (
                <Badge variant="outline" className="mt-1 text-xs">
                  {category}
                </Badge>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleView(document)}
            >
              <Eye className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDownload(document)}
            >
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
          <div className="flex items-center space-x-1">
            <Calendar className="w-3 h-3" />
            <span>Uploaded: {new Date(document.createdAt).toLocaleDateString()}</span>
          </div>
          <div className="flex items-center space-x-1">
            <User className="w-3 h-3" />
            <span>By: Management</span>
          </div>
        </div>

        {document.tags && document.tags.length > 0 && (
          <div className="mt-3 pt-3 border-t">
            <div className="flex flex-wrap gap-1">
              {document.tags.map((tag: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  const filterDocuments = (documents: any[]) => {
    if (!searchQuery) return documents;
    return documents.filter(doc => 
      doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.tags?.some((tag: string) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  };

  if (!portal || !tenant) {
    return <div>Loading...</div>;
  }

  const allDocuments = [
    ...(leaseDocuments || []).map((doc: any) => ({ ...doc, category: 'Lease' })),
    ...(propertyDocuments || []).map((doc: any) => ({ ...doc, category: 'Property' })),
    ...(tenantDocuments || []).map((doc: any) => ({ ...doc, category: 'Personal' })),
  ];

  const filteredAllDocuments = filterDocuments(allDocuments);
  const filteredLeaseDocuments = filterDocuments(leaseDocuments || []);
  const filteredPropertyDocuments = filterDocuments(propertyDocuments || []);
  const filteredTenantDocuments = filterDocuments(tenantDocuments || []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Documents</h1>
          <p className="text-muted-foreground">
            Access your lease documents, property information, and personal files
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Shield className="w-5 h-5 text-green-600" />
          <span className="text-sm text-muted-foreground">Secure Access</span>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search documents by name or tag..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="all" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all" className="flex items-center">
            <FileText className="w-4 h-4 mr-2" />
            All ({filteredAllDocuments.length})
          </TabsTrigger>
          <TabsTrigger value="lease" className="flex items-center">
            Lease ({filteredLeaseDocuments.length})
          </TabsTrigger>
          <TabsTrigger value="property" className="flex items-center">
            Property ({filteredPropertyDocuments.length})
          </TabsTrigger>
          <TabsTrigger value="personal" className="flex items-center">
            Personal ({filteredTenantDocuments.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {filteredAllDocuments.length > 0 ? (
            <div className="grid gap-4">
              {filteredAllDocuments.map((document) => (
                <DocumentCard 
                  key={document._id} 
                  document={document} 
                  category={document.category}
                  showCategory={true}
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">
                  {searchQuery ? 'No Documents Found' : 'No Documents Available'}
                </h3>
                <p className="text-muted-foreground">
                  {searchQuery 
                    ? 'Try adjusting your search terms'
                    : 'Documents will appear here when they are uploaded by management'
                  }
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="lease" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Lease Documents</CardTitle>
              <CardDescription>
                Your lease agreement and related legal documents
              </CardDescription>
            </CardHeader>
          </Card>
          
          {filteredLeaseDocuments.length > 0 ? (
            <div className="grid gap-4">
              {filteredLeaseDocuments.map((document) => (
                <DocumentCard key={document._id} document={document} />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No Lease Documents</h3>
                <p className="text-muted-foreground">
                  Your lease documents will appear here once they are processed
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="property" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Property Information</CardTitle>
              <CardDescription>
                Building rules, amenity information, and property-related documents
              </CardDescription>
            </CardHeader>
          </Card>
          
          {filteredPropertyDocuments.length > 0 ? (
            <div className="grid gap-4">
              {filteredPropertyDocuments.map((document) => (
                <DocumentCard key={document._id} document={document} />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No Property Documents</h3>
                <p className="text-muted-foreground">
                  Property information and building documents will appear here
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="personal" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Personal Documents</CardTitle>
              <CardDescription>
                Documents you've uploaded or that are specific to your tenancy
              </CardDescription>
            </CardHeader>
          </Card>
          
          {filteredTenantDocuments.length > 0 ? (
            <div className="grid gap-4">
              {filteredTenantDocuments.map((document) => (
                <DocumentCard key={document._id} document={document} />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No Personal Documents</h3>
                <p className="text-muted-foreground">
                  Your personal documents and uploads will appear here
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Document Security Notice */}
      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <Shield className="w-5 h-5 text-green-600" />
            <div>
              <h4 className="font-medium text-green-900">Secure Document Access</h4>
              <p className="text-sm text-green-700">
                All documents are encrypted and access is logged for security. 
                Only you and authorized property management staff can access your documents.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};