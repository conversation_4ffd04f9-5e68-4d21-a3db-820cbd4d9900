import * as Sentry from '@sentry/react';

export interface SentryConfig {
  dsn: string;
  environment: 'development' | 'staging' | 'production';
  tracesSampleRate: number;
  profilesSampleRate: number;
  enablePerformanceMonitoring: boolean;
}

const defaultConfig: SentryConfig = {
  dsn: process.env.VITE_SENTRY_DSN || '',
  environment: (process.env.NODE_ENV as any) || 'development',
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  profilesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  enablePerformanceMonitoring: true,
};

export class SentryMonitoring {
  private static instance: SentryMonitoring;
  private config: SentryConfig;
  private initialized = false;

  private constructor(config: Partial<SentryConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  static getInstance(config?: Partial<SentryConfig>): SentryMonitoring {
    if (!SentryMonitoring.instance) {
      SentryMonitoring.instance = new SentryMonitoring(config);
    }
    return SentryMonitoring.instance;
  }

  initialize(): void {
    if (this.initialized || !this.config.dsn) {
      return;
    }

    Sentry.init({
      dsn: this.config.dsn,
      environment: this.config.environment,
      integrations: [],
      tracesSampleRate: this.config.tracesSampleRate,
      profilesSampleRate: this.config.profilesSampleRate,
      beforeSend: (event) => {
        // Filter out development errors in production
        if (this.config.environment === 'production') {
          if (event.exception?.values?.[0]?.value?.includes('ResizeObserver')) {
            return null;
          }
        }
        return event;
      },
    });

    this.initialized = true;
  }

  // Performance monitoring methods
  startTransaction(name: string, op: string): any {
    // Use newer Sentry API with spans
    return Sentry.startSpan({ name, op }, (span) => span);
  }

  measureFunction<T>(name: string, fn: () => T): T {
    return Sentry.withScope(() => {
      try {
        const result = fn();
        return result;
      } catch (error) {
        Sentry.captureException(error);
        throw error;
      }
    });
  }

  async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    return Sentry.withScope(async () => {
      try {
        const result = await fn();
        return result;
      } catch (error) {
        Sentry.captureException(error);
        throw error;
      }
    });
  }

  // Custom performance metrics
  recordMetric(name: string, value: number, unit: string = 'millisecond'): void {
    Sentry.addBreadcrumb({
      category: 'performance',
      message: `${name}: ${value}${unit}`,
      level: 'info',
      data: { name, value, unit },
    });
  }

  // Database query monitoring
  recordDatabaseQuery(query: string, duration: number, success: boolean): void {
    Sentry.addBreadcrumb({
      category: 'database',
      message: `Query executed in ${duration}ms`,
      level: success ? 'info' : 'error',
      data: {
        query: query.substring(0, 100), // Truncate long queries
        duration,
        success,
      },
    });

    if (duration > 1000) { // Slow query threshold
      Sentry.captureMessage(`Slow database query detected: ${duration}ms`, 'warning');
    }
  }

  // Memory usage monitoring
  recordMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.recordMetric('memory.used', memory.usedJSHeapSize, 'bytes');
      this.recordMetric('memory.total', memory.totalJSHeapSize, 'bytes');
      this.recordMetric('memory.limit', memory.jsHeapSizeLimit, 'bytes');
    }
  }

  // Error reporting
  captureError(error: Error, context?: Record<string, any>): void {
    Sentry.withScope((scope) => {
      if (context) {
        scope.setContext('additional', context);
      }
      Sentry.captureException(error);
    });
  }

  // User context
  setUser(user: { id: string; email?: string; role?: string }): void {
    Sentry.setUser(user);
  }

  // Performance budgets
  checkPerformanceBudget(metric: string, value: number, budget: number): void {
    if (value > budget) {
      Sentry.captureMessage(
        `Performance budget exceeded for ${metric}: ${value} > ${budget}`,
        'warning'
      );
    }
  }
}

// React integration
export const withSentryProfiling = Sentry.withProfiler;
export const SentryErrorBoundary = Sentry.ErrorBoundary;

// Performance budgets configuration
export const PERFORMANCE_BUDGETS = {
  // Page load times (milliseconds)
  pageLoad: 3000,
  firstContentfulPaint: 1500,
  largestContentfulPaint: 2500,
  
  // Bundle sizes (bytes)
  mainBundle: 500 * 1024, // 500KB
  chunkBundle: 200 * 1024, // 200KB
  
  // Database queries (milliseconds)
  databaseQuery: 500,
  complexQuery: 1000,
  
  // Memory usage (bytes)
  memoryUsage: 100 * 1024 * 1024, // 100MB
} as const;

export default SentryMonitoring;