import { test, expect, devices } from '@playwright/test';

// Test across different platforms and browsers
const platforms = [
  { name: 'Desktop Chrome', ...devices['Desktop Chrome'] },
  { name: 'Desktop Firefox', ...devices['Desktop Firefox'] },
  { name: 'Desktop Safari', ...devices['Desktop Safari'] },
  { name: 'Mobile Chrome', ...devices['Pixel 5'] },
  { name: 'Mobile Safari', ...devices['iPhone 12'] },
  { name: 'Tablet', ...devices['iPad Pro'] },
];

platforms.forEach(platform => {
  test.describe(`Cross-Platform Tests - ${platform.name}`, () => {
    test.use(platform);

    test.beforeEach(async ({ page }) => {
      // Mock authentication
      await page.route('**/api/auth/verify', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            isAuthenticated: true,
            user: {
              _id: 'user1',
              email: '<EMAIL>',
              name: 'Test User',
              role: 'owner'
            }
          })
        });
      });

      await page.goto('/dashboard');
    });

    test('should display responsive navigation', async ({ page }) => {
      // Check if navigation is visible and accessible
      const navigation = page.getByRole('navigation');
      await expect(navigation).toBeVisible();

      // On mobile, navigation might be in a hamburger menu
      if (platform.name.includes('Mobile')) {
        const menuButton = page.getByRole('button', { name: /menu/i });
        if (await menuButton.isVisible()) {
          await menuButton.click();
        }
      }

      // Check main navigation items
      await expect(page.getByRole('link', { name: /dashboard/i })).toBeVisible();
      await expect(page.getByRole('link', { name: /properties/i })).toBeVisible();
      await expect(page.getByRole('link', { name: /payments/i })).toBeVisible();
    });

    test('should handle touch interactions on mobile', async ({ page }) => {
      if (!platform.name.includes('Mobile')) {
        test.skip('Touch test only for mobile devices');
      }

      // Test touch scrolling
      await page.goto('/dashboard/properties');
      
      // Simulate touch scroll
      await page.touchscreen.tap(200, 300);
      await page.mouse.wheel(0, 500);
      
      // Should still be able to interact with elements
      await expect(page.getByRole('heading', { name: /properties/i })).toBeVisible();
    });

    test('should display data tables responsively', async ({ page }) => {
      await page.goto('/dashboard/properties');
      
      const table = page.getByRole('table').first();
      await expect(table).toBeVisible();

      // On mobile, table might be horizontally scrollable or stacked
      if (platform.name.includes('Mobile')) {
        // Check if table is scrollable or if data is stacked
        const tableContainer = table.locator('..');
        const isScrollable = await tableContainer.evaluate(el => 
          el.scrollWidth > el.clientWidth
        );
        
        // Either scrollable or responsive layout should work
        expect(isScrollable || await page.getByText('Property Name').isVisible()).toBeTruthy();
      } else {
        // Desktop should show full table
        await expect(page.getByText('Property Name')).toBeVisible();
        await expect(page.getByText('Type')).toBeVisible();
        await expect(page.getByText('Occupancy')).toBeVisible();
      }
    });

    test('should handle form inputs correctly', async ({ page }) => {
      await page.goto('/dashboard/properties');
      await page.getByRole('button', { name: /add property/i }).click();

      const dialog = page.getByRole('dialog');
      await expect(dialog).toBeVisible();

      // Test form inputs
      const nameInput = page.getByLabel(/property name/i);
      await nameInput.fill('Test Property');
      await expect(nameInput).toHaveValue('Test Property');

      // Test select dropdown
      const typeSelect = page.getByLabel(/property type/i);
      await typeSelect.click();
      await page.getByText('Residential').click();

      // Test textarea
      const descriptionTextarea = page.getByLabel(/description/i);
      if (await descriptionTextarea.isVisible()) {
        await descriptionTextarea.fill('Test description');
        await expect(descriptionTextarea).toHaveValue('Test description');
      }
    });

    test('should display modals and dialogs correctly', async ({ page }) => {
      await page.goto('/dashboard/properties');
      await page.getByRole('button', { name: /add property/i }).click();

      const dialog = page.getByRole('dialog');
      await expect(dialog).toBeVisible();

      // Check if dialog is properly sized for the viewport
      const dialogBox = await dialog.boundingBox();
      const viewportSize = page.viewportSize();
      
      if (viewportSize) {
        // Dialog should not exceed viewport bounds
        expect(dialogBox?.width).toBeLessThanOrEqual(viewportSize.width);
        expect(dialogBox?.height).toBeLessThanOrEqual(viewportSize.height);
      }

      // Close dialog
      await page.getByRole('button', { name: /cancel/i }).click();
      await expect(dialog).not.toBeVisible();
    });

    test('should handle keyboard navigation', async ({ page }) => {
      if (platform.name.includes('Mobile')) {
        test.skip('Keyboard navigation test only for desktop');
      }

      await page.goto('/dashboard/properties');

      // Test tab navigation
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');

      // Should be able to activate focused element with Enter
      await page.keyboard.press('Enter');
      
      // Should not cause any errors
      await expect(page).not.toHaveURL(/error/);
    });

    test('should load and display images correctly', async ({ page }) => {
      await page.goto('/dashboard/properties');

      // Check if property images load (if any)
      const images = page.getByRole('img');
      const imageCount = await images.count();

      for (let i = 0; i < imageCount; i++) {
        const image = images.nth(i);
        await expect(image).toBeVisible();
        
        // Check if image loaded successfully
        const naturalWidth = await image.evaluate((img: HTMLImageElement) => img.naturalWidth);
        expect(naturalWidth).toBeGreaterThan(0);
      }
    });

    test('should handle network errors gracefully', async ({ page }) => {
      // Simulate network failure
      await page.route('**/api/**', route => route.abort());

      await page.goto('/dashboard/properties');

      // Should show error state, not crash
      await expect(page.getByText(/error/i).or(page.getByText(/failed/i))).toBeVisible();
      
      // Should still show basic UI structure
      await expect(page.getByRole('navigation')).toBeVisible();
    });

    test('should maintain performance across platforms', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/dashboard');
      
      // Wait for main content to load
      await expect(page.getByRole('main')).toBeVisible();
      
      const loadTime = Date.now() - startTime;
      
      // Performance expectations (more lenient for mobile)
      const maxLoadTime = platform.name.includes('Mobile') ? 5000 : 3000;
      expect(loadTime).toBeLessThan(maxLoadTime);
    });

    test('should support accessibility features', async ({ page }) => {
      await page.goto('/dashboard');

      // Check for proper heading structure
      const h1 = page.getByRole('heading', { level: 1 });
      await expect(h1).toBeVisible();

      // Check for proper button labels
      const buttons = page.getByRole('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i);
        const accessibleName = await button.getAttribute('aria-label') || 
                              await button.textContent();
        expect(accessibleName).toBeTruthy();
      }

      // Check for proper form labels
      const inputs = page.getByRole('textbox');
      const inputCount = await inputs.count();
      
      for (let i = 0; i < inputCount; i++) {
        const input = inputs.nth(i);
        const hasLabel = await input.getAttribute('aria-label') ||
                        await input.getAttribute('aria-labelledby') ||
                        await page.locator(`label[for="${await input.getAttribute('id')}"]`).count() > 0;
        expect(hasLabel).toBeTruthy();
      }
    });

    test('should handle orientation changes on mobile', async ({ page }) => {
      if (!platform.name.includes('Mobile')) {
        test.skip('Orientation test only for mobile devices');
      }

      await page.goto('/dashboard/properties');
      
      // Test portrait orientation
      await expect(page.getByRole('heading', { name: /properties/i })).toBeVisible();
      
      // Simulate orientation change to landscape
      await page.setViewportSize({ width: 812, height: 375 });
      
      // Should still be functional in landscape
      await expect(page.getByRole('heading', { name: /properties/i })).toBeVisible();
      
      // Navigation should still work
      const navigation = page.getByRole('navigation');
      await expect(navigation).toBeVisible();
    });
  });
});

// Browser-specific tests
test.describe('Browser-Specific Features', () => {
  test('should work with Chrome extensions', async ({ page, browserName }) => {
    test.skip(browserName !== 'chromium', 'Chrome-specific test');
    
    await page.goto('/dashboard');
    
    // Test that the app works even with potential extension interference
    await expect(page.getByRole('main')).toBeVisible();
    
    // Check console for extension-related errors
    const logs: string[] = [];
    page.on('console', msg => logs.push(msg.text()));
    
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Should not have critical errors
    const criticalErrors = logs.filter(log => 
      log.includes('error') && !log.includes('extension')
    );
    expect(criticalErrors.length).toBe(0);
  });

  test('should handle Firefox-specific behaviors', async ({ page, browserName }) => {
    test.skip(browserName !== 'firefox', 'Firefox-specific test');
    
    await page.goto('/dashboard');
    
    // Test file upload (Firefox handles differently)
    await page.goto('/dashboard/properties');
    await page.getByRole('button', { name: /add property/i }).click();
    
    // If there's a file upload field, test it
    const fileInput = page.locator('input[type="file"]');
    if (await fileInput.count() > 0) {
      await fileInput.setInputFiles({
        name: 'test.jpg',
        mimeType: 'image/jpeg',
        buffer: Buffer.from('fake image data')
      });
      
      // Should handle file upload without errors
      await expect(page).not.toHaveURL(/error/);
    }
  });

  test('should work with Safari restrictions', async ({ page, browserName }) => {
    test.skip(browserName !== 'webkit', 'Safari-specific test');
    
    await page.goto('/dashboard');
    
    // Test localStorage (Safari has restrictions)
    const localStorageWorks = await page.evaluate(() => {
      try {
        localStorage.setItem('test', 'value');
        const value = localStorage.getItem('test');
        localStorage.removeItem('test');
        return value === 'value';
      } catch {
        return false;
      }
    });
    
    // App should work even if localStorage is restricted
    await expect(page.getByRole('main')).toBeVisible();
    
    if (!localStorageWorks) {
      console.log('localStorage restricted in Safari - app should handle gracefully');
    }
  });
});