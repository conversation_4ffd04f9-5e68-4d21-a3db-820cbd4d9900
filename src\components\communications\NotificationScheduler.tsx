import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Switch } from '../ui/switch';
import { Badge } from '../ui/badge';
import { useToast } from '../ui/use-toast';
import { 
  Calendar, 
  Clock, 
  Send,
  MessageSquare,
  Phone,
  Mail,
  Bell
} from 'lucide-react';

interface NotificationSchedulerProps {
  userId: Id<"users">;
  propertyId?: Id<"properties">;
  onScheduled?: () => void;
}

interface ScheduleForm {
  type: 'sms' | 'whatsapp' | 'email' | 'in_app';
  recipients: 'all_tenants' | 'specific_users' | 'property_managers';
  specificUserIds: Id<"users">[];
  subject: string;
  content: string;
  scheduledFor: string; // ISO string
  isRecurring: boolean;
  recurringPattern: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number;
    endDate?: string;
    endAfterOccurrences?: number;
  };
  priority: 'low' | 'medium' | 'high' | 'urgent';
  respectQuietHours: boolean;
}

export function NotificationScheduler({ userId, propertyId, onScheduled }: NotificationSchedulerProps) {
  const { toast } = useToast();
  const [isScheduling, setIsScheduling] = useState(false);
  const [form, setForm] = useState<ScheduleForm>({
    type: 'sms',
    recipients: 'all_tenants',
    specificUserIds: [],
    subject: '',
    content: '',
    scheduledFor: '',
    isRecurring: false,
    recurringPattern: {
      frequency: 'weekly',
      interval: 1,
    },
    priority: 'medium',
    respectQuietHours: true,
  });

  // Queries
  const users = useQuery(api.users.getUsers, propertyId ? { propertyId } : {});
  const messageTemplates = useQuery(api.communications.getMessageTemplates, {
    propertyId,
    type: form.type,
  });

  // Mutations
  const scheduleNotification = useMutation(api.communications.scheduleNotification);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!form.content.trim()) {
      toast({
        title: "Error",
        description: "Message content is required",
        variant: "destructive",
      });
      return;
    }

    if (!form.scheduledFor) {
      toast({
        title: "Error",
        description: "Scheduled time is required",
        variant: "destructive",
      });
      return;
    }

    const scheduledTime = new Date(form.scheduledFor).getTime();
    if (scheduledTime <= Date.now()) {
      toast({
        title: "Error",
        description: "Scheduled time must be in the future",
        variant: "destructive",
      });
      return;
    }

    setIsScheduling(true);

    try {
      // Determine recipient user IDs
      let recipientIds: Id<"users">[] = [];
      
      if (form.recipients === 'specific_users') {
        recipientIds = form.specificUserIds;
      } else if (form.recipients === 'all_tenants' && users) {
        recipientIds = users.filter(user => user.role === 'tenant').map(user => user._id);
      } else if (form.recipients === 'property_managers' && users) {
        recipientIds = users.filter(user => user.role === 'manager').map(user => user._id);
      }

      if (recipientIds.length === 0) {
        toast({
          title: "Error",
          description: "No recipients selected",
          variant: "destructive",
        });
        return;
      }

      // Schedule notification for each recipient
      const schedulePromises = recipientIds.map(recipientId =>
        scheduleNotification({
          userId: recipientId,
          propertyId,
          type: form.type,
          subject: form.subject || undefined,
          content: form.content,
          scheduledFor: scheduledTime,
          priority: form.priority,
          recurringPattern: form.isRecurring ? form.recurringPattern : undefined,
          respectQuietHours: form.respectQuietHours,
          createdBy: userId,
        })
      );

      await Promise.all(schedulePromises);

      toast({
        title: "Notifications Scheduled",
        description: `Successfully scheduled ${recipientIds.length} notification(s)`,
      });

      // Reset form
      setForm({
        ...form,
        subject: '',
        content: '',
        scheduledFor: '',
        specificUserIds: [],
      });

      onScheduled?.();

    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsScheduling(false);
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = messageTemplates?.find(t => t._id === templateId);
    if (template) {
      setForm({
        ...form,
        subject: template.subject || '',
        content: template.content,
      });
    }
  };

  const getChannelIcon = (type: string) => {
    switch (type) {
      case 'sms':
        return <Phone className="h-4 w-4" />;
      case 'whatsapp':
        return <MessageSquare className="h-4 w-4" />;
      case 'email':
        return <Mail className="h-4 w-4" />;
      case 'in_app':
        return <Bell className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  // Set minimum datetime to current time + 5 minutes
  const minDateTime = new Date(Date.now() + 5 * 60 * 1000).toISOString().slice(0, 16);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="h-5 w-5" />
          <span>Schedule Notification</span>
        </CardTitle>
        <CardDescription>
          Schedule notifications to be sent at a specific time or on a recurring basis
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Channel Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Notification Channel</label>
            <div className="grid grid-cols-4 gap-2">
              {(['sms', 'whatsapp', 'email', 'in_app'] as const).map((type) => (
                <Button
                  key={type}
                  type="button"
                  variant={form.type === type ? 'default' : 'outline'}
                  className="flex items-center space-x-2"
                  onClick={() => setForm({ ...form, type })}
                >
                  {getChannelIcon(type)}
                  <span className="capitalize">{type === 'in_app' ? 'In-App' : type}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Recipients */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Recipients</label>
            <Select 
              value={form.recipients} 
              onValueChange={(value: any) => setForm({ ...form, recipients: value, specificUserIds: [] })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all_tenants">All Tenants</SelectItem>
                <SelectItem value="property_managers">Property Managers</SelectItem>
                <SelectItem value="specific_users">Specific Users</SelectItem>
              </SelectContent>
            </Select>
            
            {form.recipients === 'specific_users' && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Select Users</label>
                <div className="max-h-32 overflow-y-auto border rounded-md p-2 space-y-1">
                  {users?.map((user) => (
                    <div key={user._id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={user._id}
                        checked={form.specificUserIds.includes(user._id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setForm({
                              ...form,
                              specificUserIds: [...form.specificUserIds, user._id]
                            });
                          } else {
                            setForm({
                              ...form,
                              specificUserIds: form.specificUserIds.filter(id => id !== user._id)
                            });
                          }
                        }}
                      />
                      <label htmlFor={user._id} className="text-sm">
                        {user.name} ({user.email}) - {user.role}
                      </label>
                    </div>
                  ))}
                </div>
                {form.specificUserIds.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {form.specificUserIds.map((userId) => {
                      const user = users?.find(u => u._id === userId);
                      return user ? (
                        <Badge key={userId} variant="secondary">
                          {user.name}
                        </Badge>
                      ) : null;
                    })}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Template Selection */}
          {messageTemplates && messageTemplates.length > 0 && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Use Template (Optional)</label>
              <Select onValueChange={handleTemplateSelect}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a template..." />
                </SelectTrigger>
                <SelectContent>
                  {messageTemplates.map((template) => (
                    <SelectItem key={template._id} value={template._id}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Subject (for email and in-app) */}
          {(form.type === 'email' || form.type === 'in_app') && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Subject</label>
              <Input
                value={form.subject}
                onChange={(e) => setForm({ ...form, subject: e.target.value })}
                placeholder="Enter notification subject..."
              />
            </div>
          )}

          {/* Content */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Message Content</label>
            <Textarea
              value={form.content}
              onChange={(e) => setForm({ ...form, content: e.target.value })}
              placeholder="Enter your message..."
              rows={4}
              required
            />
            <div className="text-xs text-gray-500">
              {form.content.length}/1000 characters
            </div>
          </div>

          {/* Scheduled Time */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Scheduled Time</label>
            <Input
              type="datetime-local"
              value={form.scheduledFor}
              onChange={(e) => setForm({ ...form, scheduledFor: e.target.value })}
              min={minDateTime}
              required
            />
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Priority</label>
            <Select 
              value={form.priority} 
              onValueChange={(value: any) => setForm({ ...form, priority: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Recurring Options */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                checked={form.isRecurring}
                onCheckedChange={(checked) => setForm({ ...form, isRecurring: checked })}
              />
              <label className="text-sm font-medium">Recurring Notification</label>
            </div>

            {form.isRecurring && (
              <div className="space-y-4 pl-6 border-l-2 border-gray-200">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Frequency</label>
                    <Select 
                      value={form.recurringPattern.frequency} 
                      onValueChange={(value: any) => setForm({ 
                        ...form, 
                        recurringPattern: { ...form.recurringPattern, frequency: value }
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Interval</label>
                    <Input
                      type="number"
                      min="1"
                      value={form.recurringPattern.interval}
                      onChange={(e) => setForm({ 
                        ...form, 
                        recurringPattern: { 
                          ...form.recurringPattern, 
                          interval: parseInt(e.target.value) || 1 
                        }
                      })}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">End Date (Optional)</label>
                  <Input
                    type="date"
                    value={form.recurringPattern.endDate || ''}
                    onChange={(e) => setForm({ 
                      ...form, 
                      recurringPattern: { 
                        ...form.recurringPattern, 
                        endDate: e.target.value || undefined 
                      }
                    })}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Or End After Occurrences</label>
                  <Input
                    type="number"
                    min="1"
                    placeholder="Number of occurrences"
                    value={form.recurringPattern.endAfterOccurrences || ''}
                    onChange={(e) => setForm({ 
                      ...form, 
                      recurringPattern: { 
                        ...form.recurringPattern, 
                        endAfterOccurrences: e.target.value ? parseInt(e.target.value) : undefined 
                      }
                    })}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Respect Quiet Hours */}
          <div className="flex items-center space-x-2">
            <Switch
              checked={form.respectQuietHours}
              onCheckedChange={(checked) => setForm({ ...form, respectQuietHours: checked })}
            />
            <label className="text-sm font-medium">Respect Quiet Hours</label>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-2">
            <Button type="submit" disabled={isScheduling}>
              {isScheduling ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Scheduling...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Schedule Notification
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
