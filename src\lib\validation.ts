// Validation utilities for EstatePulse

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface PropertyData {
  name: string;
  type: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
}

export interface UnitData {
  unitNumber: string;
  type: string;
  size: number;
  rent: number;
  status: string;
  amenities: string[];
}

export interface LeaseData {
  tenantEmail: string;
  startDate: number;
  endDate: number;
  monthlyRent: number;
  deposit: number;
}

export interface PaymentData {
  method: string;
  amount: number;
  phoneNumber?: string;
  cardToken?: string;
  reference: string;
}

export interface MaintenanceTicketData {
  title: string;
  description: string;
  priority: string;
  category: string;
}

export interface FileUploadOptions {
  maxSize: number;
  allowedTypes: string[];
}

const VALID_PROPERTY_TYPES = ['residential', 'commercial', 'mixed'];
const VALID_UNIT_TYPES = ['apartment', 'office', 'retail', 'parking'];
const VALID_UNIT_STATUSES = ['vacant', 'occupied', 'maintenance'];
const VALID_PRIORITIES = ['low', 'medium', 'high', 'emergency'];
const VALID_CATEGORIES = ['plumbing', 'electrical', 'hvac', 'general', 'security'];

export function validatePropertyData(data: PropertyData): ValidationResult {
  const errors: string[] = [];

  if (!data.name || data.name.trim().length === 0) {
    errors.push('Property name is required');
  }

  if (!VALID_PROPERTY_TYPES.includes(data.type)) {
    errors.push('Invalid property type');
  }

  if (!data.address.street || data.address.street.trim().length === 0) {
    errors.push('Street address is required');
  }

  if (!data.address.city || data.address.city.trim().length === 0) {
    errors.push('City is required');
  }

  if (!data.address.state || data.address.state.trim().length === 0) {
    errors.push('State is required');
  }

  if (!data.address.postalCode || data.address.postalCode.trim().length === 0) {
    errors.push('Postal code is required');
  } else if (!/^\d{5}$/.test(data.address.postalCode)) {
    errors.push('Invalid postal code format');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateUnitData(data: UnitData): ValidationResult {
  const errors: string[] = [];

  if (!data.unitNumber || data.unitNumber.trim().length === 0) {
    errors.push('Unit number is required');
  } else if (!/^[A-Z0-9]+$/.test(data.unitNumber)) {
    errors.push('Invalid unit number format');
  }

  if (!VALID_UNIT_TYPES.includes(data.type)) {
    errors.push('Invalid unit type');
  }

  if (data.size <= 0) {
    errors.push('Size must be positive');
  }

  if (data.rent <= 0) {
    errors.push('Rent must be positive');
  }

  if (!VALID_UNIT_STATUSES.includes(data.status)) {
    errors.push('Invalid unit status');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateLeaseData(data: LeaseData): ValidationResult {
  const errors: string[] = [];

  if (!validateEmail(data.tenantEmail)) {
    errors.push('Invalid email format');
  }

  if (data.endDate <= data.startDate) {
    errors.push('End date must be after start date');
  }

  if (data.monthlyRent <= 0) {
    errors.push('Monthly rent must be positive');
  }

  if (data.deposit <= 0) {
    errors.push('Deposit must be positive');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validatePaymentData(data: PaymentData): ValidationResult {
  const errors: string[] = [];

  if (data.amount <= 0) {
    errors.push('Amount must be positive');
  }

  if (data.method === 'mpesa' && !data.phoneNumber) {
    errors.push('Phone number is required for M-PESA payments');
  }

  if (data.method === 'stripe' && !data.cardToken) {
    errors.push('Card token is required for Stripe payments');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateMaintenanceTicketData(data: MaintenanceTicketData): ValidationResult {
  const errors: string[] = [];

  if (!data.title || data.title.trim().length === 0) {
    errors.push('Title is required');
  }

  if (!data.description || data.description.trim().length === 0) {
    errors.push('Description is required');
  } else if (data.description.length < 10) {
    errors.push('Description must be at least 10 characters');
  }

  if (!VALID_PRIORITIES.includes(data.priority)) {
    errors.push('Invalid priority level');
  }

  if (!VALID_CATEGORIES.includes(data.category)) {
    errors.push('Invalid category');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function sanitizeInput(input: any): string {
  if (input === null || input === undefined) {
    return '';
  }

  const str = String(input);
  
  // Remove HTML tags
  const withoutHtml = str.replace(/<[^>]*>/g, '');
  
  // Trim whitespace
  return withoutHtml.trim();
}

export function validateEmail(email: string): boolean {
  if (!email) return false;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePhone(phone: string): boolean {
  if (!phone) return false;
  
  // Support Kenyan and international formats
  const phoneRegex = /^(\+254|0)?[17]\d{8}$|^\+\d{10,15}$/;
  return phoneRegex.test(phone);
}

export function validateFileUpload(file: File, options: FileUploadOptions): ValidationResult {
  const errors: string[] = [];

  if (file.size > options.maxSize) {
    errors.push('File size exceeds maximum allowed size');
  }

  if (!options.allowedTypes.includes(file.type)) {
    errors.push('File type not allowed');
  }

  // Check for suspicious file extensions
  const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
  const fileName = file.name.toLowerCase();
  
  if (suspiciousExtensions.some(ext => fileName.endsWith(ext))) {
    errors.push('File extension not allowed');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}