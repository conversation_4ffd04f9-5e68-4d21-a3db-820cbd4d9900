import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Authentication credentials table
  authCredentials: defineTable({
    userId: v.id("users"),
    passwordHash: v.string(),
    createdAt: v.number(),
  }).index("by_user_id", ["userId"]),
  users: defineTable({
    email: v.string(),
    name: v.string(),
    role: v.union(v.literal("owner"), v.literal("manager"), v.literal("vendor"), v.literal("tenant")),
    propertyAccess: v.array(v.id("properties")),
    kycStatus: v.union(v.literal("pending"), v.literal("verified"), v.literal("rejected")),
    phone: v.optional(v.string()),
    avatar: v.optional(v.string()),
    isActive: v.boolean(),
    lastLogin: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_email", ["email"])
    .index("by_role", ["role"]),

  properties: defineTable({
    name: v.string(),
    type: v.union(v.literal("residential"), v.literal("commercial"), v.literal("mixed")),
    address: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      country: v.string(),
      postalCode: v.string(),
      coordinates: v.optional(v.object({
        lat: v.number(),
        lng: v.number(),
      })),
    }),
    ownerId: v.id("users"),
    managerId: v.optional(v.id("users")),
    branding: v.object({
      logo: v.optional(v.string()),
      primaryColor: v.string(),
      secondaryColor: v.string(),
      customDomain: v.optional(v.string()),
    }),
    settings: v.object({
      currency: v.string(),
      timezone: v.string(),
      language: v.string(),
      autoRentReminders: v.boolean(),
      maintenanceSLA: v.number(), // hours
    }),
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_owner", ["ownerId"])
    .index("by_manager", ["managerId"])
    .index("by_type", ["type"]),

  units: defineTable({
    propertyId: v.id("properties"),
    unitNumber: v.string(),
    type: v.union(v.literal("apartment"), v.literal("office"), v.literal("retail"), v.literal("parking")),
    size: v.number(), // square feet/meters
    bedrooms: v.optional(v.number()),
    bathrooms: v.optional(v.number()),
    rent: v.number(),
    deposit: v.number(),
    status: v.union(v.literal("vacant"), v.literal("occupied"), v.literal("maintenance")),
    amenities: v.array(v.string()),
    description: v.optional(v.string()),
    images: v.array(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_property", ["propertyId"])
    .index("by_status", ["status"])
    .index("by_property_status", ["propertyId", "status"]),

  leases: defineTable({
    propertyId: v.id("properties"),
    unitId: v.id("units"),
    tenantId: v.id("users"),
    startDate: v.number(),
    endDate: v.number(),
    monthlyRent: v.number(),
    deposit: v.number(),
    status: v.union(v.literal("active"), v.literal("expired"), v.literal("terminated"), v.literal("pending")),
    terms: v.object({
      noticePeriod: v.number(), // days
      lateFeePercentage: v.number(),
      gracePeriod: v.number(), // days
      renewalOption: v.boolean(),
    }),
    eSignatureStatus: v.union(v.literal("pending"), v.literal("signed"), v.literal("expired")),
    documentUrl: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_property", ["propertyId"])
    .index("by_tenant", ["tenantId"])
    .index("by_unit", ["unitId"])
    .index("by_status", ["status"]),

  maintenanceTickets: defineTable({
    propertyId: v.id("properties"),
    unitId: v.optional(v.id("units")),
    tenantId: v.id("users"),
    vendorId: v.optional(v.id("users")),
    assignedBy: v.optional(v.id("users")),
    title: v.string(),
    description: v.string(),
    category: v.union(
      v.literal("plumbing"),
      v.literal("electrical"),
      v.literal("hvac"),
      v.literal("appliance"),
      v.literal("structural"),
      v.literal("cleaning"),
      v.literal("security"),
      v.literal("other")
    ),
    priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("emergency")),
    status: v.union(
      v.literal("open"),
      v.literal("assigned"),
      v.literal("in_progress"),
      v.literal("completed"),
      v.literal("closed"),
      v.literal("escalated")
    ),
    slaDeadline: v.number(),
    escalationDeadline: v.optional(v.number()),
    estimatedCost: v.optional(v.number()),
    actualCost: v.optional(v.number()),
    estimatedDuration: v.optional(v.number()), // hours
    actualDuration: v.optional(v.number()), // hours
    images: v.array(v.string()),
    attachments: v.array(v.id("documents")),
    notes: v.array(v.object({
      userId: v.id("users"),
      message: v.string(),
      timestamp: v.number(),
      type: v.union(v.literal("comment"), v.literal("status_change"), v.literal("assignment"), v.literal("escalation")),
    })),
    escalationHistory: v.array(v.object({
      escalatedBy: v.id("users"),
      escalatedTo: v.id("users"),
      reason: v.string(),
      timestamp: v.number(),
    })),
    completionDetails: v.optional(v.object({
      completedBy: v.id("users"),
      completionNotes: v.string(),
      satisfactionRating: v.optional(v.number()), // 1-5 scale
      completedAt: v.number(),
    })),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_property", ["propertyId"])
    .index("by_tenant", ["tenantId"])
    .index("by_vendor", ["vendorId"])
    .index("by_assigned_by", ["assignedBy"])
    .index("by_status", ["status"])
    .index("by_priority", ["priority"])
    .index("by_category", ["category"])
    .index("by_sla_deadline", ["slaDeadline"])
    .index("by_property_status", ["propertyId", "status"])
    .index("by_vendor_status", ["vendorId", "status"]),

  invoices: defineTable({
    leaseId: v.id("leases"),
    propertyId: v.id("properties"),
    tenantId: v.id("users"),
    amount: v.number(),
    dueDate: v.number(),
    status: v.union(v.literal("pending"), v.literal("paid"), v.literal("overdue"), v.literal("cancelled")),
    type: v.union(v.literal("rent"), v.literal("deposit"), v.literal("maintenance"), v.literal("other")),
    items: v.array(v.object({
      description: v.string(),
      amount: v.number(),
      quantity: v.number(),
    })),
    paymentMethod: v.optional(v.union(v.literal("mpesa"), v.literal("stripe"), v.literal("bank_transfer"))),
    paidAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_lease", ["leaseId"])
    .index("by_tenant", ["tenantId"])
    .index("by_status", ["status"])
    .index("by_due_date", ["dueDate"]),

  signatureRequests: defineTable({
    leaseId: v.id("leases"),
    documentUrl: v.string(),
    status: v.union(v.literal("pending"), v.literal("signed"), v.literal("declined"), v.literal("expired")),
    signers: v.array(v.object({
      email: v.string(),
      name: v.string(),
      role: v.union(v.literal("tenant"), v.literal("landlord"), v.literal("witness")),
      order: v.number(),
      status: v.union(v.literal("pending"), v.literal("signed"), v.literal("declined")),
      signedAt: v.optional(v.number()),
      ipAddress: v.optional(v.string()),
    })),
    externalRequestId: v.string(), // ID from e-signature service
    signedDocumentUrl: v.optional(v.string()),
    expiresAt: v.number(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_lease", ["leaseId"])
    .index("by_status", ["status"])
    .index("by_external_id", ["externalRequestId"]),

  payments: defineTable({
    invoiceId: v.id("invoices"),
    amount: v.number(),
    method: v.union(v.literal("mpesa"), v.literal("stripe"), v.literal("bank_transfer")),
    transactionId: v.string(),
    status: v.union(v.literal("pending"), v.literal("completed"), v.literal("failed"), v.literal("refunded")),
    metadata: v.object({
      mpesaReceiptNumber: v.optional(v.string()),
      stripePaymentIntentId: v.optional(v.string()),
      phoneNumber: v.optional(v.string()),
    }),
    processedAt: v.number(),
    createdAt: v.number(),
  })
    .index("by_invoice", ["invoiceId"])
    .index("by_transaction_id", ["transactionId"])
    .index("by_status", ["status"]),

  documents: defineTable({
    name: v.string(),
    type: v.string(),
    url: v.string(),
    size: v.number(),
    uploadedBy: v.id("users"),
    relatedTo: v.object({
      type: v.union(v.literal("property"), v.literal("lease"), v.literal("tenant"), v.literal("maintenance")),
      id: v.string(),
    }),
    tags: v.array(v.string()),
    isPublic: v.boolean(),
    createdAt: v.number(),
  })
    .index("by_uploader", ["uploadedBy"])
    .index("by_related", ["relatedTo.type", "relatedTo.id"]),

  vendors: defineTable({
    userId: v.id("users"), // Links to user with vendor role
    companyName: v.string(),
    contactPerson: v.string(),
    phone: v.string(),
    email: v.string(),
    address: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      country: v.string(),
      postalCode: v.string(),
    }),
    specialties: v.array(v.union(
      v.literal("plumbing"),
      v.literal("electrical"),
      v.literal("hvac"),
      v.literal("appliance"),
      v.literal("structural"),
      v.literal("cleaning"),
      v.literal("security"),
      v.literal("other")
    )),
    serviceAreas: v.array(v.id("properties")), // Properties they can service
    availability: v.object({
      workingHours: v.object({
        start: v.string(), // "09:00"
        end: v.string(), // "17:00"
      }),
      workingDays: v.array(v.union(
        v.literal("monday"),
        v.literal("tuesday"),
        v.literal("wednesday"),
        v.literal("thursday"),
        v.literal("friday"),
        v.literal("saturday"),
        v.literal("sunday")
      )),
      emergencyAvailable: v.boolean(),
    }),
    pricing: v.object({
      hourlyRate: v.optional(v.number()),
      calloutFee: v.optional(v.number()),
      emergencyRate: v.optional(v.number()),
    }),
    performance: v.object({
      averageRating: v.number(),
      totalJobs: v.number(),
      completedJobs: v.number(),
      averageResponseTime: v.number(), // hours
      averageCompletionTime: v.number(), // hours
      slaCompliance: v.number(), // percentage
    }),
    documents: v.array(v.object({
      type: v.union(v.literal("license"), v.literal("insurance"), v.literal("certification")),
      documentId: v.id("documents"),
      expiryDate: v.optional(v.number()),
    })),
    isActive: v.boolean(),
    isVerified: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_specialty", ["specialties"])
    .index("by_service_area", ["serviceAreas"])
    .index("by_active", ["isActive"])
    .index("by_verified", ["isVerified"]),

  maintenanceEscalations: defineTable({
    ticketId: v.id("maintenanceTickets"),
    propertyId: v.id("properties"),
    escalationLevel: v.number(), // 1, 2, 3, etc.
    escalatedFrom: v.optional(v.id("users")),
    escalatedTo: v.id("users"),
    reason: v.union(
      v.literal("sla_breach"),
      v.literal("vendor_unavailable"),
      v.literal("complexity"),
      v.literal("cost_approval"),
      v.literal("tenant_complaint"),
      v.literal("manual")
    ),
    escalationRules: v.object({
      triggerCondition: v.string(),
      autoEscalate: v.boolean(),
      notificationSent: v.boolean(),
    }),
    resolvedAt: v.optional(v.number()),
    resolution: v.optional(v.string()),
    createdAt: v.number(),
  })
    .index("by_ticket", ["ticketId"])
    .index("by_property", ["propertyId"])
    .index("by_escalated_to", ["escalatedTo"])
    .index("by_level", ["escalationLevel"])
    .index("by_reason", ["reason"]),

  maintenanceSchedules: defineTable({
    propertyId: v.id("properties"),
    unitId: v.optional(v.id("units")),
    title: v.string(),
    description: v.string(),
    category: v.union(
      v.literal("plumbing"),
      v.literal("electrical"),
      v.literal("hvac"),
      v.literal("appliance"),
      v.literal("structural"),
      v.literal("cleaning"),
      v.literal("security"),
      v.literal("other")
    ),
    frequency: v.union(
      v.literal("weekly"),
      v.literal("monthly"),
      v.literal("quarterly"),
      v.literal("semi_annual"),
      v.literal("annual")
    ),
    preferredVendorId: v.optional(v.id("users")),
    nextDueDate: v.number(),
    lastCompletedDate: v.optional(v.number()),
    estimatedCost: v.optional(v.number()),
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_property", ["propertyId"])
    .index("by_unit", ["unitId"])
    .index("by_due_date", ["nextDueDate"])
    .index("by_category", ["category"])
    .index("by_vendor", ["preferredVendorId"]),

  notifications: defineTable({
    userId: v.id("users"),
    title: v.string(),
    message: v.string(),
    type: v.union(
      v.literal("payment_reminder"),
      v.literal("maintenance_update"),
      v.literal("maintenance_assigned"),
      v.literal("maintenance_escalated"),
      v.literal("maintenance_completed"),
      v.literal("sla_warning"),
      v.literal("lease_expiry"),
      v.literal("system"),
      v.literal("report_generated"),
      v.literal("system_alert"),
      v.literal("maintenance_due")
    ),
    priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("urgent")),
    isRead: v.boolean(),
    actionUrl: v.optional(v.string()),
    metadata: v.optional(v.object({
      invoiceId: v.optional(v.id("invoices")),
      ticketId: v.optional(v.id("maintenanceTickets")),
      leaseId: v.optional(v.id("leases")),
      escalationId: v.optional(v.id("maintenanceEscalations")),
    })),
    scheduledFor: v.optional(v.number()),
    sentAt: v.optional(v.number()),
    createdAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_user_read", ["userId", "isRead"])
    .index("by_type", ["type"])
    .index("by_priority", ["priority"])
    .index("by_scheduled", ["scheduledFor"]),

  portals: defineTable({
    propertyId: v.id("properties"),
    name: v.string(),
    subdomain: v.string(), // e.g., "property-name" for property-name.estatepulse.com
    customDomain: v.optional(v.string()), // e.g., "portal.propertyname.com"
    branding: v.object({
      logo: v.optional(v.string()), // URL to logo image
      favicon: v.optional(v.string()), // URL to favicon
      primaryColor: v.string(), // Hex color code
      secondaryColor: v.string(), // Hex color code
      accentColor: v.string(), // Hex color code
      backgroundColor: v.string(), // Hex color code
      textColor: v.string(), // Hex color code
      fontFamily: v.string(), // Font family name
      customCSS: v.optional(v.string()), // Custom CSS overrides
    }),
    theme: v.object({
      layout: v.union(v.literal("modern"), v.literal("classic"), v.literal("minimal")),
      headerStyle: v.union(v.literal("fixed"), v.literal("static"), v.literal("transparent")),
      sidebarStyle: v.union(v.literal("expanded"), v.literal("collapsed"), v.literal("overlay")),
      cardStyle: v.union(v.literal("elevated"), v.literal("outlined"), v.literal("flat")),
      borderRadius: v.union(v.literal("none"), v.literal("small"), v.literal("medium"), v.literal("large")),
    }),
    features: v.object({
      paymentPortal: v.boolean(),
      maintenanceRequests: v.boolean(),
      documentAccess: v.boolean(),
      leaseInformation: v.boolean(),
      communicationCenter: v.boolean(),
      announcementsBoard: v.boolean(),
    }),
    customization: v.object({
      welcomeMessage: v.optional(v.string()),
      footerText: v.optional(v.string()),
      contactInfo: v.optional(v.object({
        phone: v.string(),
        email: v.string(),
        address: v.string(),
        hours: v.string(),
      })),
      socialLinks: v.optional(v.object({
        facebook: v.optional(v.string()),
        twitter: v.optional(v.string()),
        instagram: v.optional(v.string()),
        linkedin: v.optional(v.string()),
      })),
      customPages: v.array(v.object({
        title: v.string(),
        slug: v.string(),
        content: v.string(),
        isPublished: v.boolean(),
      })),
    }),
    seoSettings: v.object({
      title: v.string(),
      description: v.string(),
      keywords: v.array(v.string()),
      ogImage: v.optional(v.string()),
    }),
    analytics: v.object({
      googleAnalyticsId: v.optional(v.string()),
      facebookPixelId: v.optional(v.string()),
      customTrackingCode: v.optional(v.string()),
    }),
    isActive: v.boolean(),
    isPublished: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_property", ["propertyId"])
    .index("by_subdomain", ["subdomain"])
    .index("by_custom_domain", ["customDomain"])
    .index("by_active", ["isActive"]),

  portalAnalytics: defineTable({
    portalId: v.id("portals"),
    date: v.string(), // YYYY-MM-DD format
    metrics: v.object({
      pageViews: v.number(),
      uniqueVisitors: v.number(),
      sessions: v.number(),
      averageSessionDuration: v.number(), // seconds
      bounceRate: v.number(), // percentage
      topPages: v.array(v.object({
        path: v.string(),
        views: v.number(),
      })),
      deviceBreakdown: v.object({
        desktop: v.number(),
        mobile: v.number(),
        tablet: v.number(),
      }),
      browserBreakdown: v.array(v.object({
        browser: v.string(),
        count: v.number(),
      })),
    }),
    tenantEngagement: v.object({
      activeUsers: v.number(),
      paymentTransactions: v.number(),
      maintenanceRequests: v.number(),
      documentDownloads: v.number(),
      messagesSent: v.number(),
    }),
    createdAt: v.number(),
  })
    .index("by_portal", ["portalId"])
    .index("by_date", ["date"])
    .index("by_portal_date", ["portalId", "date"]),

  // Communication and Notification System Tables
  messageTemplates: defineTable({
    name: v.string(),
    type: v.union(v.literal("sms"), v.literal("whatsapp"), v.literal("email")),
    category: v.union(
      v.literal("payment_reminder"),
      v.literal("maintenance_update"),
      v.literal("lease_expiry"),
      v.literal("welcome"),
      v.literal("general"),
      v.literal("emergency")
    ),
    subject: v.optional(v.string()), // For email templates
    content: v.string(),
    variables: v.array(v.string()), // Available template variables like {tenant_name}, {amount}
    isActive: v.boolean(),
    propertyId: v.optional(v.id("properties")), // Property-specific templates
    createdBy: v.id("users"),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_type", ["type"])
    .index("by_category", ["category"])
    .index("by_property", ["propertyId"])
    .index("by_active", ["isActive"]),

  communicationLogs: defineTable({
    recipientId: v.id("users"),
    recipientPhone: v.string(),
    recipientEmail: v.optional(v.string()),
    senderId: v.id("users"),
    propertyId: v.optional(v.id("properties")),
    type: v.union(v.literal("sms"), v.literal("whatsapp"), v.literal("email")),
    templateId: v.optional(v.id("messageTemplates")),
    subject: v.optional(v.string()),
    content: v.string(),
    status: v.union(
      v.literal("pending"),
      v.literal("sent"),
      v.literal("delivered"),
      v.literal("failed"),
      v.literal("read")
    ),
    externalId: v.optional(v.string()), // ID from external service (Twilio, etc.)
    errorMessage: v.optional(v.string()),
    cost: v.optional(v.number()), // Cost in cents/smallest currency unit
    metadata: v.optional(v.object({
      twilioSid: v.optional(v.string()),
      whatsappStatus: v.optional(v.string()),
      deliveryTime: v.optional(v.number()),
      readTime: v.optional(v.number()),
    })),
    scheduledFor: v.optional(v.number()), // For scheduled messages
    sentAt: v.optional(v.number()),
    deliveredAt: v.optional(v.number()),
    readAt: v.optional(v.number()),
    createdAt: v.number(),
  })
    .index("by_recipient", ["recipientId"])
    .index("by_sender", ["senderId"])
    .index("by_property", ["propertyId"])
    .index("by_type", ["type"])
    .index("by_status", ["status"])
    .index("by_scheduled", ["scheduledFor"])
    .index("by_external_id", ["externalId"]),

  bulkMessages: defineTable({
    name: v.string(),
    senderId: v.id("users"),
    propertyId: v.optional(v.id("properties")),
    type: v.union(v.literal("sms"), v.literal("whatsapp"), v.literal("email")),
    templateId: v.optional(v.id("messageTemplates")),
    subject: v.optional(v.string()),
    content: v.string(),
    recipientFilter: v.object({
      roles: v.optional(v.array(v.union(v.literal("tenant"), v.literal("vendor"), v.literal("manager")))),
      properties: v.optional(v.array(v.id("properties"))),
      units: v.optional(v.array(v.id("units"))),
      customRecipients: v.optional(v.array(v.object({
        userId: v.id("users"),
        phone: v.string(),
        email: v.optional(v.string()),
      }))),
    }),
    status: v.union(
      v.literal("draft"),
      v.literal("scheduled"),
      v.literal("sending"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("cancelled")
    ),
    totalRecipients: v.number(),
    sentCount: v.number(),
    deliveredCount: v.number(),
    failedCount: v.number(),
    estimatedCost: v.optional(v.number()),
    actualCost: v.optional(v.number()),
    scheduledFor: v.optional(v.number()),
    startedAt: v.optional(v.number()),
    completedAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_sender", ["senderId"])
    .index("by_property", ["propertyId"])
    .index("by_status", ["status"])
    .index("by_scheduled", ["scheduledFor"]),

  notificationPreferences: defineTable({
    userId: v.id("users"),
    propertyId: v.optional(v.id("properties")),
    preferences: v.object({
      sms: v.object({
        enabled: v.boolean(),
        paymentReminders: v.boolean(),
        maintenanceUpdates: v.boolean(),
        leaseNotifications: v.boolean(),
        emergencyAlerts: v.boolean(),
        generalAnnouncements: v.boolean(),
      }),
      whatsapp: v.object({
        enabled: v.boolean(),
        paymentReminders: v.boolean(),
        maintenanceUpdates: v.boolean(),
        leaseNotifications: v.boolean(),
        emergencyAlerts: v.boolean(),
        generalAnnouncements: v.boolean(),
      }),
      email: v.object({
        enabled: v.boolean(),
        paymentReminders: v.boolean(),
        maintenanceUpdates: v.boolean(),
        leaseNotifications: v.boolean(),
        emergencyAlerts: v.boolean(),
        generalAnnouncements: v.boolean(),
        weeklyReports: v.boolean(),
        monthlyStatements: v.boolean(),
      }),
      inApp: v.object({
        enabled: v.boolean(),
        paymentReminders: v.boolean(),
        maintenanceUpdates: v.boolean(),
        leaseNotifications: v.boolean(),
        emergencyAlerts: v.boolean(),
        generalAnnouncements: v.boolean(),
      }),
    }),
    quietHours: v.object({
      enabled: v.boolean(),
      startTime: v.string(), // "22:00"
      endTime: v.string(), // "08:00"
      timezone: v.string(),
    }),
    language: v.string(), // ISO language code
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_property", ["propertyId"])
    .index("by_user_property", ["userId", "propertyId"]),




  // Notification triggers for automated notifications
  notificationTriggers: defineTable({
    name: v.string(),
    type: v.union(
      v.literal("payment_due"),
      v.literal("payment_overdue"),
      v.literal("lease_expiry"),
      v.literal("maintenance_assigned"),
      v.literal("maintenance_escalated"),
      v.literal("maintenance_completed"),
      v.literal("sla_warning"),
      v.literal("welcome_tenant"),
      v.literal("scheduled_reminder")
    ),
    propertyId: v.optional(v.id("properties")),
    conditions: v.object({
      entityType: v.union(v.literal("invoice"), v.literal("lease"), v.literal("maintenance"), v.literal("user")),
      triggerEvent: v.string(), // e.g., "status_change", "date_reached", "created"
      conditionExpression: v.string(), // JSON string with condition logic
    }),
    notificationConfig: v.object({
      channels: v.array(v.union(v.literal("sms"), v.literal("whatsapp"), v.literal("email"), v.literal("in_app"))),
      templateId: v.optional(v.id("messageTemplates")),
      customMessage: v.optional(v.string()),
      priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("urgent")),
      respectQuietHours: v.boolean(),
    }),
    timing: v.object({
      delay: v.optional(v.number()), // Minutes to delay after trigger
      schedule: v.optional(v.object({
        frequency: v.union(v.literal("once"), v.literal("daily"), v.literal("weekly"), v.literal("monthly")),
        interval: v.optional(v.number()),
        endCondition: v.optional(v.string()),
      })),
    }),
    isActive: v.boolean(),
    createdBy: v.id("users"),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_type", ["type"])
    .index("by_property", ["propertyId"])
    .index("by_active", ["isActive"])
    .index("by_created_by", ["createdBy"]),

  // Executive reporting configurations
  reportConfigurations: defineTable({
    name: v.string(),
    description: v.string(),
    templateId: v.string(),
    sections: v.array(v.string()),
    filters: v.object({
      propertyIds: v.optional(v.array(v.id("properties"))),
      dateRange: v.object({
        type: v.union(v.literal("monthly"), v.literal("quarterly"), v.literal("annual"), v.literal("custom")),
        customStart: v.optional(v.number()),
        customEnd: v.optional(v.number()),
      }),
    }),
    schedule: v.optional(v.object({
      frequency: v.union(v.literal("weekly"), v.literal("monthly"), v.literal("quarterly")),
      dayOfWeek: v.optional(v.number()),
      dayOfMonth: v.optional(v.number()),
      recipients: v.array(v.string()),
    })),
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_template", ["templateId"])
    .index("by_active", ["isActive"]),

  // Compliance and KYC Management Tables
  kycDocuments: defineTable({
    userId: v.id("users"),
    documentType: v.union(
      v.literal("national_id"),
      v.literal("passport"),
      v.literal("drivers_license"),
      v.literal("birth_certificate"),
      v.literal("proof_of_income"),
      v.literal("bank_statement"),
      v.literal("employment_letter"),
      v.literal("business_registration"),
      v.literal("tax_certificate"),
      v.literal("other")
    ),
    documentNumber: v.optional(v.string()),
    documentUrl: v.string(),
    fileName: v.string(),
    fileSize: v.number(),
    mimeType: v.string(),
    verificationStatus: v.union(
      v.literal("pending"),
      v.literal("in_review"),
      v.literal("verified"),
      v.literal("rejected"),
      v.literal("expired")
    ),
    verificationNotes: v.optional(v.string()),
    verifiedBy: v.optional(v.id("users")),
    verifiedAt: v.optional(v.number()),
    expiryDate: v.optional(v.number()),
    extractedData: v.optional(v.object({
      fullName: v.optional(v.string()),
      idNumber: v.optional(v.string()),
      dateOfBirth: v.optional(v.string()),
      nationality: v.optional(v.string()),
      address: v.optional(v.string()),
    })),
    tags: v.array(v.string()),
    version: v.number(),
    previousVersionId: v.optional(v.id("kycDocuments")),
    isActive: v.boolean(),
    uploadedBy: v.id("users"),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_type", ["documentType"])
    .index("by_status", ["verificationStatus"])
    .index("by_verified_by", ["verifiedBy"])
    .index("by_user_type", ["userId", "documentType"])
    .index("by_expiry", ["expiryDate"])
    .index("by_version", ["version"])
    .index("by_active", ["isActive"]),

  complianceChecklists: defineTable({
    name: v.string(),
    description: v.string(),
    entityType: v.union(v.literal("tenant"), v.literal("vendor"), v.literal("property"), v.literal("lease")),
    propertyId: v.optional(v.id("properties")),
    requirements: v.array(v.object({
      id: v.string(),
      title: v.string(),
      description: v.string(),
      category: v.union(
        v.literal("kyc"),
        v.literal("legal"),
        v.literal("financial"),
        v.literal("regulatory"),
        v.literal("safety"),
        v.literal("insurance")
      ),
      priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("critical")),
      requiredDocuments: v.array(v.string()),
      isRequired: v.boolean(),
      automationRules: v.optional(v.object({
        autoCheck: v.boolean(),
        checkFrequency: v.optional(v.union(v.literal("daily"), v.literal("weekly"), v.literal("monthly"))),
        conditions: v.optional(v.string()),
      })),
    })),
    isActive: v.boolean(),
    createdBy: v.id("users"),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_entity_type", ["entityType"])
    .index("by_property", ["propertyId"])
    .index("by_active", ["isActive"])
    .index("by_created_by", ["createdBy"]),

  complianceStatus: defineTable({
    entityId: v.string(), // ID of the entity being checked (user, property, lease, etc.)
    entityType: v.union(v.literal("tenant"), v.literal("vendor"), v.literal("property"), v.literal("lease")),
    checklistId: v.id("complianceChecklists"),
    propertyId: v.optional(v.id("properties")),
    overallStatus: v.union(
      v.literal("compliant"),
      v.literal("non_compliant"),
      v.literal("pending"),
      v.literal("in_review"),
      v.literal("expired")
    ),
    overallScore: v.number(), // Percentage 0-100
    requirementStatuses: v.array(v.object({
      requirementId: v.string(),
      status: v.union(
        v.literal("compliant"),
        v.literal("non_compliant"),
        v.literal("pending"),
        v.literal("not_applicable")
      ),
      completedAt: v.optional(v.number()),
      notes: v.optional(v.string()),
      documentIds: v.array(v.id("kycDocuments")),
      reviewedBy: v.optional(v.id("users")),
    })),
    lastReviewDate: v.optional(v.number()),
    nextReviewDate: v.optional(v.number()),
    reviewedBy: v.optional(v.id("users")),
    alerts: v.array(v.object({
      type: v.union(v.literal("missing_document"), v.literal("expired_document"), v.literal("review_required")),
      message: v.string(),
      severity: v.union(v.literal("info"), v.literal("warning"), v.literal("error"), v.literal("critical")),
      createdAt: v.number(),
      resolvedAt: v.optional(v.number()),
    })),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_entity", ["entityId", "entityType"])
    .index("by_checklist", ["checklistId"])
    .index("by_property", ["propertyId"])
    .index("by_status", ["overallStatus"])
    .index("by_score", ["overallScore"])
    .index("by_next_review", ["nextReviewDate"]),

  auditTrails: defineTable({
    entityId: v.string(),
    entityType: v.union(
      v.literal("kyc_document"),
      v.literal("compliance_status"),
      v.literal("user"),
      v.literal("property"),
      v.literal("lease")
    ),
    action: v.union(
      v.literal("created"),
      v.literal("updated"),
      v.literal("deleted"),
      v.literal("verified"),
      v.literal("rejected"),
      v.literal("reviewed"),
      v.literal("accessed"),
      v.literal("downloaded")
    ),
    performedBy: v.id("users"),
    propertyId: v.optional(v.id("properties")),
    details: v.object({
      description: v.string(),
      oldValues: v.optional(v.any()),
      newValues: v.optional(v.any()),
      ipAddress: v.optional(v.string()),
      userAgent: v.optional(v.string()),
      sessionId: v.optional(v.string()),
    }),
    metadata: v.optional(v.object({
      documentType: v.optional(v.string()),
      verificationResult: v.optional(v.string()),
      complianceScore: v.optional(v.number()),
      riskLevel: v.optional(v.string()),
    })),
    timestamp: v.number(),
  })
    .index("by_entity", ["entityId", "entityType"])
    .index("by_performer", ["performedBy"])
    .index("by_property", ["propertyId"])
    .index("by_action", ["action"])
    .index("by_timestamp", ["timestamp"])
    .index("by_entity_timestamp", ["entityId", "timestamp"]),

  regulatoryReports: defineTable({
    name: v.string(),
    reportType: v.union(
      v.literal("etims_vat"),
      v.literal("etims_income_tax"),
      v.literal("rental_income"),
      v.literal("tenant_registry"),
      v.literal("compliance_summary"),
      v.literal("kyc_status"),
      v.literal("custom")
    ),
    propertyIds: v.array(v.id("properties")),
    reportPeriod: v.object({
      startDate: v.number(),
      endDate: v.number(),
      frequency: v.union(v.literal("monthly"), v.literal("quarterly"), v.literal("annual")),
    }),
    generatedBy: v.id("users"),
    status: v.union(
      v.literal("generating"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("scheduled")
    ),
    reportData: v.optional(v.object({
      summary: v.any(),
      details: v.any(),
      totals: v.any(),
      complianceMetrics: v.optional(v.any()),
    })),
    fileUrl: v.optional(v.string()),
    fileFormat: v.union(v.literal("pdf"), v.literal("excel"), v.literal("csv"), v.literal("xml")),
    scheduledFor: v.optional(v.number()),
    completedAt: v.optional(v.number()),
    errorMessage: v.optional(v.string()),
    recipients: v.array(v.object({
      email: v.string(),
      name: v.string(),
      role: v.string(),
    })),
    isAutoGenerated: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_type", ["reportType"])
    .index("by_property", ["propertyIds"])
    .index("by_generator", ["generatedBy"])
    .index("by_status", ["status"])
    .index("by_scheduled", ["scheduledFor"])
    .index("by_period", ["reportPeriod.startDate", "reportPeriod.endDate"]),

  documentCategories: defineTable({
    name: v.string(),
    description: v.string(),
    parentCategoryId: v.optional(v.id("documentCategories")),
    entityTypes: v.array(v.union(v.literal("tenant"), v.literal("vendor"), v.literal("property"), v.literal("lease"))),
    requiredFields: v.array(v.object({
      fieldName: v.string(),
      fieldType: v.union(v.literal("text"), v.literal("number"), v.literal("date"), v.literal("boolean")),
      isRequired: v.boolean(),
      validationRules: v.optional(v.string()),
    })),
    retentionPolicy: v.object({
      retentionPeriod: v.number(), // months
      autoDelete: v.boolean(),
      archiveAfter: v.optional(v.number()), // months
    }),
    accessRoles: v.array(v.union(v.literal("owner"), v.literal("manager"), v.literal("vendor"), v.literal("tenant"))),
    isActive: v.boolean(),
    createdBy: v.id("users"),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_parent", ["parentCategoryId"])
    .index("by_entity_type", ["entityTypes"])
    .index("by_active", ["isActive"])
    .index("by_created_by", ["createdBy"]),

  // Generated reports storage
  generatedReports: defineTable({
    configId: v.id("reportConfigurations"),
    name: v.string(),
    generatedAt: v.number(),
    period: v.object({
      startDate: v.number(),
      endDate: v.number(),
    }),
    data: v.any(), // Report data JSON
    status: v.union(v.literal("generating"), v.literal("generated"), v.literal("failed")),
    recipients: v.array(v.string()),
    downloadCount: v.optional(v.number()),
    lastDownloaded: v.optional(v.number()),
  })
    .index("by_config", ["configId"])
    .index("by_status", ["status"])
    .index("by_generated_at", ["generatedAt"]),


});