/**
 * Convex functions for real-time synchronization
 * Handles conflict resolution and data synchronization
 */

import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Sync metadata table for tracking changes
export const syncMetadata = {
  entityType: v.string(),
  entityId: v.string(),
  version: v.number(),
  lastModified: v.number(),
  lastModifiedBy: v.id("users"),
  checksum: v.string(),
};

// Get entity version for conflict detection
export const getEntityVersion = query({
  args: {
    entityType: v.string(),
    entityId: v.string(),
  },
  handler: async (ctx, args) => {
    // This would check the actual entity's updatedAt field
    // For now, we'll simulate version checking
    
    switch (args.entityType) {
      case "properties":
        const property = await ctx.db.get(args.entityId as Id<"properties">);
        return property ? {
          version: property.updatedAt,
          lastModified: property.updatedAt,
          exists: true
        } : { version: 0, lastModified: 0, exists: false };
        
      case "units":
        const unit = await ctx.db.get(args.entityId as Id<"units">);
        return unit ? {
          version: unit.updatedAt,
          lastModified: unit.updatedAt,
          exists: true
        } : { version: 0, lastModified: 0, exists: false };
        
      case "leases":
        const lease = await ctx.db.get(args.entityId as Id<"leases">);
        return lease ? {
          version: lease.updatedAt,
          lastModified: lease.updatedAt,
          exists: true
        } : { version: 0, lastModified: 0, exists: false };
        
      case "maintenanceTickets":
        const ticket = await ctx.db.get(args.entityId as Id<"maintenanceTickets">);
        return ticket ? {
          version: ticket.updatedAt,
          lastModified: ticket.updatedAt,
          exists: true
        } : { version: 0, lastModified: 0, exists: false };
        
      default:
        throw new Error(`Unsupported entity type: ${args.entityType}`);
    }
  },
});

// Batch sync operation for multiple entities
export const batchSync = mutation({
  args: {
    operations: v.array(v.object({
      type: v.union(v.literal("create"), v.literal("update"), v.literal("delete")),
      entityType: v.string(),
      entityId: v.optional(v.string()),
      data: v.any(),
      clientVersion: v.number(),
      timestamp: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    const results = [];
    const conflicts = [];
    
    for (const operation of args.operations) {
      try {
        // Check for conflicts
        const serverVersion = await getEntityVersion(ctx, {
          entityType: operation.entityType,
          entityId: operation.entityId || "",
        });

        if (operation.type === "update" && serverVersion.exists) {
          if (serverVersion.version > operation.clientVersion) {
            // Conflict detected
            conflicts.push({
              operationId: operation.entityId,
              entityType: operation.entityType,
              entityId: operation.entityId,
              clientVersion: operation.clientVersion,
              serverVersion: serverVersion.version,
              conflictType: "version_mismatch",
            });
            continue;
          }
        }

        // Execute operation
        const result = await executeOperation(ctx, operation);
        results.push({
          operationId: operation.entityId,
          success: true,
          result,
        });

      } catch (error) {
        results.push({
          operationId: operation.entityId,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return {
      results,
      conflicts,
      timestamp: Date.now(),
    };
  },
});

// Execute a single sync operation
async function executeOperation(ctx: any, operation: any) {
  const now = Date.now();
  
  switch (operation.entityType) {
    case "properties":
      if (operation.type === "create") {
        return await ctx.db.insert("properties", {
          ...operation.data,
          createdAt: now,
          updatedAt: now,
        });
      } else if (operation.type === "update") {
        await ctx.db.patch(operation.entityId as Id<"properties">, {
          ...operation.data,
          updatedAt: now,
        });
        return operation.entityId;
      } else if (operation.type === "delete") {
        await ctx.db.delete(operation.entityId as Id<"properties">);
        return operation.entityId;
      }
      break;

    case "units":
      if (operation.type === "create") {
        return await ctx.db.insert("units", {
          ...operation.data,
          createdAt: now,
          updatedAt: now,
        });
      } else if (operation.type === "update") {
        await ctx.db.patch(operation.entityId as Id<"units">, {
          ...operation.data,
          updatedAt: now,
        });
        return operation.entityId;
      } else if (operation.type === "delete") {
        await ctx.db.delete(operation.entityId as Id<"units">);
        return operation.entityId;
      }
      break;

    case "leases":
      if (operation.type === "create") {
        return await ctx.db.insert("leases", {
          ...operation.data,
          createdAt: now,
          updatedAt: now,
        });
      } else if (operation.type === "update") {
        await ctx.db.patch(operation.entityId as Id<"leases">, {
          ...operation.data,
          updatedAt: now,
        });
        return operation.entityId;
      } else if (operation.type === "delete") {
        await ctx.db.delete(operation.entityId as Id<"leases">);
        return operation.entityId;
      }
      break;

    case "maintenanceTickets":
      if (operation.type === "create") {
        return await ctx.db.insert("maintenanceTickets", {
          ...operation.data,
          createdAt: now,
          updatedAt: now,
        });
      } else if (operation.type === "update") {
        await ctx.db.patch(operation.entityId as Id<"maintenanceTickets">, {
          ...operation.data,
          updatedAt: now,
        });
        return operation.entityId;
      } else if (operation.type === "delete") {
        await ctx.db.delete(operation.entityId as Id<"maintenanceTickets">);
        return operation.entityId;
      }
      break;

    default:
      throw new Error(`Unsupported entity type: ${operation.entityType}`);
  }
}

// Resolve conflicts using specified strategy
export const resolveConflict = mutation({
  args: {
    entityType: v.string(),
    entityId: v.string(),
    resolution: v.union(v.literal("local"), v.literal("server"), v.literal("merge")),
    localData: v.any(),
    serverData: v.any(),
    mergedData: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    let finalData;
    
    switch (args.resolution) {
      case "local":
        finalData = args.localData;
        break;
      case "server":
        finalData = args.serverData;
        break;
      case "merge":
        finalData = args.mergedData || args.serverData;
        break;
    }

    // Update the entity with resolved data
    const operation = {
      type: "update" as const,
      entityType: args.entityType,
      entityId: args.entityId,
      data: { ...finalData, updatedAt: now },
      clientVersion: now,
      timestamp: now,
    };

    return await executeOperation(ctx, operation);
  },
});

// Get changes since last sync
export const getChangesSince = query({
  args: {
    entityType: v.string(),
    lastSyncTime: v.number(),
    propertyId: v.optional(v.id("properties")),
  },
  handler: async (ctx, args) => {
    const changes = [];
    
    switch (args.entityType) {
      case "properties":
        const properties = await ctx.db
          .query("properties")
          .filter((q) => q.gt(q.field("updatedAt"), args.lastSyncTime))
          .collect();
        
        changes.push(...properties.map(p => ({
          entityType: "properties",
          entityId: p._id,
          changeType: "updated" as const,
          data: p,
          timestamp: p.updatedAt,
        })));
        break;

      case "units":
        let unitsQuery = ctx.db
          .query("units")
          .filter((q) => q.gt(q.field("updatedAt"), args.lastSyncTime));
        
        if (args.propertyId) {
          unitsQuery = unitsQuery.filter((q) => q.eq(q.field("propertyId"), args.propertyId));
        }
        
        const units = await unitsQuery.collect();
        changes.push(...units.map(u => ({
          entityType: "units",
          entityId: u._id,
          changeType: "updated" as const,
          data: u,
          timestamp: u.updatedAt,
        })));
        break;

      case "leases":
        let leasesQuery = ctx.db
          .query("leases")
          .filter((q) => q.gt(q.field("updatedAt"), args.lastSyncTime));
        
        if (args.propertyId) {
          leasesQuery = leasesQuery.filter((q) => q.eq(q.field("propertyId"), args.propertyId));
        }
        
        const leases = await leasesQuery.collect();
        changes.push(...leases.map(l => ({
          entityType: "leases",
          entityId: l._id,
          changeType: "updated" as const,
          data: l,
          timestamp: l.updatedAt,
        })));
        break;

      case "maintenanceTickets":
        let ticketsQuery = ctx.db
          .query("maintenanceTickets")
          .filter((q) => q.gt(q.field("updatedAt"), args.lastSyncTime));
        
        if (args.propertyId) {
          ticketsQuery = ticketsQuery.filter((q) => q.eq(q.field("propertyId"), args.propertyId));
        }
        
        const tickets = await ticketsQuery.collect();
        changes.push(...tickets.map(t => ({
          entityType: "maintenanceTickets",
          entityId: t._id,
          changeType: "updated" as const,
          data: t,
          timestamp: t.updatedAt,
        })));
        break;
    }

    return {
      changes,
      timestamp: Date.now(),
    };
  },
});

// Subscribe to real-time changes for specific entities
export const subscribeToChanges = query({
  args: {
    entityTypes: v.array(v.string()),
    propertyId: v.optional(v.id("properties")),
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    // This query will be used by the client to establish real-time subscriptions
    // The actual real-time updates will be handled by Convex's built-in reactivity
    
    const subscriptionInfo = {
      entityTypes: args.entityTypes,
      propertyId: args.propertyId,
      userId: args.userId,
      timestamp: Date.now(),
    };

    return subscriptionInfo;
  },
});

// Heartbeat function to check connection status
export const heartbeat = query({
  args: {},
  handler: async (ctx, args) => {
    return {
      timestamp: Date.now(),
      status: "connected",
    };
  },
});