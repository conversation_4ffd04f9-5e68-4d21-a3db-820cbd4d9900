/**
 * Example component demonstrating offline sync reconciliation usage
 */

import React, { useState, useEffect } from 'react';
import { useOfflineSync, useOfflineData } from '../../hooks/useOfflineSync';
import { useOfflineReconciliation } from '../../hooks/useOfflineReconciliation';
import { useConvex } from 'convex/react';
import { SyncStatusBadge, CompactSyncStatus } from '../sync/SyncStatusBadge';
import { ReconciliationManager } from '../sync/ReconciliationManager';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Alert, AlertDescription } from '../ui/alert';
import { Badge } from '../ui/badge';
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  Plus, 
  Edit, 
  Trash2,
  Alert<PERSON>riangle,
  CheckCircle
} from 'lucide-react';

interface Property {
  id: string;
  name: string;
  address: string;
  type: string;
  updatedAt: number;
}

export function OfflineSyncExample() {
  const convex = useConvex();
  const [showReconciliation, setShowReconciliation] = useState(false);
  const [newPropertyName, setNewPropertyName] = useState('');
  const [editingProperty, setEditingProperty] = useState<string | null>(null);

  // Offline sync hooks
  const {
    status,
    isOnline,
    isSyncing,
    hasPendingOperations,
    hasConflicts,
    sync,
    lastSyncText
  } = useOfflineSync({
    autoSync: true,
    syncInterval: 30000
  });

  // Offline data management for properties
  const {
    data: properties,
    loading,
    error,
    createData: createProperty,
    updateData: updateProperty,
    deleteData: deleteProperty
  } = useOfflineData<Property>('properties');

  // Reconciliation management
  const {
    state: reconciliationState,
    pendingOperationsCount,
    isReconciliationNeeded,
    autoReconcile,
    getStatusSummary
  } = useOfflineReconciliation(convex);

  const statusSummary = getStatusSummary();

  // Handle creating a new property
  const handleCreateProperty = async () => {
    if (!newPropertyName.trim()) return;

    try {
      await createProperty({
        name: newPropertyName,
        address: '123 Example St',
        type: 'Residential',
        updatedAt: Date.now()
      });
      setNewPropertyName('');
    } catch (error) {
      console.error('Failed to create property:', error);
    }
  };

  // Handle updating a property
  const handleUpdateProperty = async (id: string, updates: Partial<Property>) => {
    try {
      await updateProperty({ ...updates, updatedAt: Date.now() }, id);
      setEditingProperty(null);
    } catch (error) {
      console.error('Failed to update property:', error);
    }
  };

  // Handle deleting a property
  const handleDeleteProperty = async (id: string) => {
    try {
      await deleteProperty(id);
    } catch (error) {
      console.error('Failed to delete property:', error);
    }
  };

  // Auto-reconcile when coming back online
  useEffect(() => {
    if (isOnline && hasPendingOperations && !isSyncing) {
      const timer = setTimeout(() => {
        autoReconcile();
      }, 2000); // Wait 2 seconds after coming online

      return () => clearTimeout(timer);
    }
  }, [isOnline, hasPendingOperations, isSyncing, autoReconcile]);

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header with sync status */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Offline Sync Example</h1>
          <p className="text-gray-600">Demonstrating offline-first property management</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <CompactSyncStatus />
          <SyncStatusBadge />
        </div>
      </div>

      {/* Connection and sync status */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              {isOnline ? (
                <Wifi className="h-4 w-4 text-green-500 mr-2" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500 mr-2" />
              )}
              Connection
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-semibold">
              {isOnline ? 'Online' : 'Offline'}
            </div>
            <p className="text-xs text-gray-600">{lastSyncText}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Pending Operations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingOperationsCount}</div>
            <p className="text-xs text-gray-600">Waiting to sync</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Conflicts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reconciliationState.conflicts.length}</div>
            <p className="text-xs text-gray-600">Need resolution</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {statusSummary.status === 'synced' && (
                <CheckCircle className="h-4 w-4 text-green-500" />
              )}
              {statusSummary.status === 'pending' && (
                <RefreshCw className="h-4 w-4 text-yellow-500" />
              )}
              {statusSummary.status === 'conflicts' && (
                <AlertTriangle className="h-4 w-4 text-orange-500" />
              )}
              <span className="text-sm font-medium">{statusSummary.message}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sync actions */}
      <div className="flex items-center space-x-4">
        <Button
          onClick={sync}
          disabled={!isOnline || isSyncing}
          variant="outline"
        >
          {isSyncing ? (
            <RefreshCw className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Manual Sync
        </Button>

        <Button
          onClick={() => setShowReconciliation(true)}
          disabled={!isReconciliationNeeded}
          variant={hasConflicts ? 'destructive' : 'default'}
        >
          {hasConflicts ? 'Resolve Conflicts' : 'Manage Sync'}
        </Button>

        <Button
          onClick={autoReconcile}
          disabled={!isOnline || !hasPendingOperations || isSyncing}
          variant="outline"
        >
          Auto Reconcile
        </Button>
      </div>

      {/* Alerts */}
      {!isOnline && (
        <Alert>
          <WifiOff className="h-4 w-4" />
          <AlertDescription>
            You're offline. Changes will be saved locally and synced when you reconnect.
          </AlertDescription>
        </Alert>
      )}

      {hasConflicts && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            There are {reconciliationState.conflicts.length} conflicts that need manual resolution.
          </AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Property management */}
      <Card>
        <CardHeader>
          <CardTitle>Properties</CardTitle>
          <div className="flex items-center space-x-2">
            <Input
              placeholder="Property name"
              value={newPropertyName}
              onChange={(e) => setNewPropertyName(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleCreateProperty()}
            />
            <Button onClick={handleCreateProperty} disabled={!newPropertyName.trim()}>
              <Plus className="h-4 w-4 mr-2" />
              Add Property
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Loading properties...</p>
            </div>
          ) : (
            <div className="space-y-4">
              {Array.isArray(properties) && properties.length > 0 ? (
                properties.map((property) => (
                  <div
                    key={property.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex-1">
                      {editingProperty === property.id ? (
                        <Input
                          defaultValue={property.name}
                          onBlur={(e) => {
                            if (e.target.value !== property.name) {
                              handleUpdateProperty(property.id, { name: e.target.value });
                            } else {
                              setEditingProperty(null);
                            }
                          }}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              handleUpdateProperty(property.id, { name: e.currentTarget.value });
                            }
                          }}
                          autoFocus
                        />
                      ) : (
                        <div>
                          <h3 className="font-medium">{property.name}</h3>
                          <p className="text-sm text-gray-600">{property.address}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline">{property.type}</Badge>
                            {property.id.startsWith('temp_') && (
                              <Badge variant="secondary">Pending Sync</Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setEditingProperty(property.id)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDeleteProperty(property.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No properties found. Add one to get started.
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Reconciliation Manager Modal */}
      {showReconciliation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-auto">
            <ReconciliationManager onClose={() => setShowReconciliation(false)} />
          </div>
        </div>
      )}
    </div>
  );
}