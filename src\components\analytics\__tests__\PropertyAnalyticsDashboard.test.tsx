import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { PropertyAnalyticsDashboard } from '../PropertyAnalyticsDashboard';

// Mock Convex hooks
vi.mock('convex/react', () => ({
  useQuery: vi.fn(() => ({
    _id: 'property-123',
    name: 'Test Property',
    settings: { currency: 'KES' },
    totalUnits: 10,
    occupiedUnits: 8,
    vacantUnits: 2,
    occupancyRate: 80,
    totalMonthlyRevenue: 100000,
    activeLeases: 8,
    openMaintenanceTickets: 2,
    maintenanceUnits: 0,
    unitsByType: {
      apartment: { total: 8, occupied: 6, vacant: 2, maintenance: 0 },
      office: { total: 2, occupied: 2, vacant: 0, maintenance: 0 },
    },
  })),
}));

describe('PropertyAnalyticsDashboard', () => {
  const mockPropertyId = 'property-123' as any;

  it('renders analytics dashboard with key metrics', () => {
    render(<PropertyAnalyticsDashboard propertyId={mockPropertyId} />);
    
    expect(screen.getByText('Property Analytics')).toBeInTheDocument();
    expect(screen.getByText('Test Property Performance Dashboard')).toBeInTheDocument();
    
    // Check key metrics
    expect(screen.getByText('80.0%')).toBeInTheDocument(); // Occupancy rate
    expect(screen.getByText('KES 100,000')).toBeInTheDocument(); // Monthly revenue
  });

  it('displays unit type distribution', () => {
    render(<PropertyAnalyticsDashboard propertyId={mockPropertyId} />);
    
    expect(screen.getByText('Unit Type Distribution')).toBeInTheDocument();
    expect(screen.getByText('apartment')).toBeInTheDocument();
    expect(screen.getByText('office')).toBeInTheDocument();
  });

  it('shows unit status overview', () => {
    render(<PropertyAnalyticsDashboard propertyId={mockPropertyId} />);
    
    expect(screen.getByText('Unit Status Overview')).toBeInTheDocument();
    expect(screen.getByText('Occupied Units')).toBeInTheDocument();
    expect(screen.getByText('Vacant Units')).toBeInTheDocument();
    expect(screen.getByText('Maintenance Units')).toBeInTheDocument();
  });

  it('calculates and displays property health score', () => {
    render(<PropertyAnalyticsDashboard propertyId={mockPropertyId} />);
    
    expect(screen.getByText('Property Health Score')).toBeInTheDocument();
    expect(screen.getByText('Overall Health Score')).toBeInTheDocument();
  });

  it('provides recommendations based on performance', () => {
    render(<PropertyAnalyticsDashboard propertyId={mockPropertyId} />);
    
    expect(screen.getByText('Recommendations')).toBeInTheDocument();
    
    // Should show occupancy improvement recommendation since rate is 80%
    expect(screen.getByText('Improve Occupancy Rate')).toBeInTheDocument();
  });

  it('shows financial performance metrics', () => {
    render(<PropertyAnalyticsDashboard propertyId={mockPropertyId} />);
    
    expect(screen.getByText('Financial Performance')).toBeInTheDocument();
    expect(screen.getByText('Current Monthly Revenue')).toBeInTheDocument();
    expect(screen.getByText('Potential Monthly Revenue')).toBeInTheDocument();
    expect(screen.getByText('Revenue Efficiency')).toBeInTheDocument();
  });
});