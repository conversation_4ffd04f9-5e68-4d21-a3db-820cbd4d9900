import React, { useState, useMemo } from "react";
// import { useQuery, useMutation } from "convex/react";
// import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
// import { Input } from "../ui/input";
// import { Textarea } from "../ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { 
  Bell, 
  Send, 
  Calendar, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  Settings,
  Plus,
  Edit,
  Trash2
} from "lucide-react";
import { format, isPast, differenceInDays } from "date-fns";

interface PaymentRemindersProps {
  propertyId?: Id<"properties">;
}

interface ReminderTemplate {
  id: string;
  name: string;
  subject: string;
  message: string;
  daysBeforeDue: number;
  type: "email" | "sms" | "both";
}

export const PaymentReminders: React.FC<PaymentRemindersProps> = ({ propertyId: _propertyId }) => {
  const [selectedTemplate, setSelectedTemplate] = useState<string>("");
  // const [customMessage, setCustomMessage] = useState("");
  const [reminderType, setReminderType] = useState<"email" | "sms" | "both">("both");
  // const [isCreatingTemplate, setIsCreatingTemplate] = useState(false);
  // const [newTemplate, setNewTemplate] = useState<Partial<ReminderTemplate>>({
  //   name: "",
  //   subject: "",
  //   message: "",
  //   daysBeforeDue: 3,
  //   type: "both",
  // });

  // Fetch data
  // Mock invoices data for now since getInvoices doesn't exist yet
  const invoices: any[] = [];

  // Mock reminder templates (in a real app, these would come from the database)
  const reminderTemplates: ReminderTemplate[] = [
    {
      id: "gentle",
      name: "Gentle Reminder",
      subject: "Friendly Payment Reminder",
      message: "Hi {tenant_name}, this is a friendly reminder that your rent payment of {amount} is due on {due_date}. Please make your payment at your earliest convenience. Thank you!",
      daysBeforeDue: 3,
      type: "both",
    },
    {
      id: "urgent",
      name: "Urgent Notice",
      subject: "Urgent: Payment Due Soon",
      message: "Dear {tenant_name}, your rent payment of {amount} is due tomorrow ({due_date}). Please ensure payment is made to avoid any late fees. Contact us if you need assistance.",
      daysBeforeDue: 1,
      type: "both",
    },
    {
      id: "overdue",
      name: "Overdue Notice",
      subject: "Overdue Payment Notice",
      message: "Dear {tenant_name}, your rent payment of {amount} was due on {due_date} and is now overdue. Please make payment immediately to avoid additional charges. Contact our office if you're experiencing difficulties.",
      daysBeforeDue: -1,
      type: "both",
    },
  ];

  // Process invoices to identify those needing reminders
  const invoicesNeedingReminders = useMemo(() => {
    if (!invoices) return [];

    return invoices
      .filter(invoice => invoice.status === "pending")
      .map(invoice => {
        const dueDate = new Date(invoice.dueDate);
        const daysUntilDue = differenceInDays(dueDate, new Date());
        const isOverdue = isPast(dueDate);
        
        let urgency: "low" | "medium" | "high" | "overdue";
        if (isOverdue) {
          urgency = "overdue";
        } else if (daysUntilDue <= 1) {
          urgency = "high";
        } else if (daysUntilDue <= 3) {
          urgency = "medium";
        } else {
          urgency = "low";
        }

        return {
          ...invoice,
          daysUntilDue,
          isOverdue,
          urgency,
        };
      })
      .sort((a: any, b: any) => {
        // Sort by urgency first, then by due date
        const urgencyOrder: Record<string, number> = { overdue: 0, high: 1, medium: 2, low: 3 };
        if (urgencyOrder[a.urgency] !== urgencyOrder[b.urgency]) {
          return urgencyOrder[a.urgency] - urgencyOrder[b.urgency];
        }
        return a.dueDate - b.dueDate;
      });
  }, [invoices]);

  // Mock mutation for sending reminders
  // Mock sendReminder for now since notifications.create doesn't exist yet
  // const sendReminder = async () => {};

  const handleSendReminder = async (invoiceId: Id<"invoices">, _template: ReminderTemplate) => {
    try {
      const invoice = invoices?.find(inv => inv._id === invoiceId);
      if (!invoice) return;

      // In a real implementation, you would:
      // 1. Get tenant details
      // 2. Replace template variables
      // 3. Send via email/SMS service
      // 4. Log the reminder

      // await sendReminder({
      //   userId: invoice.tenantId,
      //   title: "Payment Reminder",
      //   message: template.message.replace("{amount}", `KES ${invoice.amount.toLocaleString()}`),
      //   type: "payment_reminder",
      //   priority: "medium",
      //   metadata: {
      //     invoiceId: invoice._id,
      //   },
      // });
      console.log("Reminder would be sent for invoice:", invoiceId);

      // Show success message (you might want to use a toast notification)
      console.log("Reminder sent successfully");
    } catch (error) {
      console.error("Failed to send reminder:", error);
    }
  };

  const handleBulkReminder = async () => {
    const template = reminderTemplates.find(t => t.id === selectedTemplate);
    if (!template) return;

    const invoicesToRemind = invoicesNeedingReminders.filter(invoice => {
      if (template.daysBeforeDue >= 0) {
        return invoice.daysUntilDue <= template.daysBeforeDue && !invoice.isOverdue;
      } else {
        return invoice.isOverdue;
      }
    });

    for (const invoice of invoicesToRemind) {
      await handleSendReminder(invoice._id, template);
    }
  };

  const getUrgencyBadge = (urgency: string) => {
    const variants = {
      low: "secondary" as const,
      medium: "default" as const,
      high: "destructive" as const,
      overdue: "destructive" as const,
    };

    const labels = {
      low: "Low Priority",
      medium: "Due Soon",
      high: "Due Tomorrow",
      overdue: "Overdue",
    };

    return (
      <Badge variant={variants[urgency as keyof typeof variants]}>
        {labels[urgency as keyof typeof labels]}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (!invoices) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Payment Reminders</h2>
          <p className="text-muted-foreground">
            Manage automated payment reminders and notifications
          </p>
        </div>
        <Button onClick={() => console.log('Create template')}>
          <Plus className="h-4 w-4 mr-2" />
          New Template
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {invoicesNeedingReminders.filter(inv => inv.urgency === "overdue").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Require immediate attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Due Tomorrow</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {invoicesNeedingReminders.filter(inv => inv.urgency === "high").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Due within 24 hours
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Due Soon</CardTitle>
            <Calendar className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {invoicesNeedingReminders.filter(inv => inv.urgency === "medium").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Due within 3 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pending</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {invoicesNeedingReminders.length}
            </div>
            <p className="text-xs text-muted-foreground">
              Invoices needing attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Send Bulk Reminders
          </CardTitle>
          <CardDescription>
            Send reminders to multiple tenants at once using predefined templates
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium">Template</label>
              <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                <SelectTrigger>
                  <SelectValue placeholder="Select template" />
                </SelectTrigger>
                <SelectContent>
                  {reminderTemplates.map(template => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Delivery Method</label>
              <Select value={reminderType} onValueChange={setReminderType as any}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="email">Email Only</SelectItem>
                  <SelectItem value="sms">SMS Only</SelectItem>
                  <SelectItem value="both">Email & SMS</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button 
                onClick={handleBulkReminder}
                disabled={!selectedTemplate}
                className="w-full"
              >
                <Send className="h-4 w-4 mr-2" />
                Send Reminders
              </Button>
            </div>
          </div>
          
          {selectedTemplate && (
            <div className="p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Preview</h4>
              <p className="text-sm">
                {reminderTemplates.find(t => t.id === selectedTemplate)?.message}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Invoices Needing Reminders */}
      <Card>
        <CardHeader>
          <CardTitle>Invoices Requiring Attention</CardTitle>
          <CardDescription>
            Invoices that may need payment reminders
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {invoicesNeedingReminders.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium">All caught up!</h3>
                <p className="text-muted-foreground">
                  No invoices currently need payment reminders.
                </p>
              </div>
            ) : (
              invoicesNeedingReminders.map((invoice) => (
                <div key={invoice._id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div>
                      <p className="font-medium">Invoice #{invoice._id.slice(-8)}</p>
                      <p className="text-sm text-muted-foreground">
                        Due: {format(new Date(invoice.dueDate), "MMM dd, yyyy")}
                      </p>
                    </div>
                    <div>
                      <p className="font-medium">{formatCurrency(invoice.amount)}</p>
                      <p className="text-sm text-muted-foreground">
                        {invoice.isOverdue 
                          ? `${Math.abs(invoice.daysUntilDue)} days overdue`
                          : `${invoice.daysUntilDue} days remaining`
                        }
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    {getUrgencyBadge(invoice.urgency)}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleSendReminder(invoice._id, reminderTemplates[0])}
                    >
                      <Send className="h-3 w-3 mr-1" />
                      Send Reminder
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Reminder Templates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Reminder Templates
          </CardTitle>
          <CardDescription>
            Manage your payment reminder templates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reminderTemplates.map((template) => (
              <div key={template.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{template.name}</h4>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">
                      {template.daysBeforeDue >= 0 
                        ? `${template.daysBeforeDue} days before`
                        : "After due date"
                      }
                    </Badge>
                    <Button size="sm" variant="ghost">
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  <strong>Subject:</strong> {template.subject}
                </p>
                <p className="text-sm text-muted-foreground">
                  {template.message}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};