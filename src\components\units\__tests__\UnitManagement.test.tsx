import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { UnitManagement } from '../UnitManagement';

// Mock Convex hooks
const mockCreateUnit = vi.fn();
const mockUpdateUnit = vi.fn();
const mockDeleteUnit = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: vi.fn(() => [
    {
      _id: 'unit1',
      unitNumber: 'A101',
      type: 'apartment',
      size: 1200,
      rent: 50000,
      status: 'occupied',
      propertyId: 'property1',
    },
    {
      _id: 'unit2',
      unitNumber: 'A102',
      type: 'apartment',
      size: 1000,
      rent: 45000,
      status: 'vacant',
      propertyId: 'property1',
    },
  ]),
  useMutation: vi.fn((mutation) => {
    if (mutation.toString().includes('create')) return mockCreateUnit;
    if (mutation.toString().includes('update')) return mockUpdateUnit;
    if (mutation.toString().includes('delete')) return mockDeleteUnit;
    return vi.fn();
  }),
}));

// Mock auth context
vi.mock('../../../lib/auth-context', () => ({
  useAuth: vi.fn(() => ({
    user: {
      _id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'manager',
    },
  })),
}));

// Mock RBAC
vi.mock('../../../lib/auth', () => ({
  useRBAC: vi.fn(() => ({
    canPerform: vi.fn(() => true),
  })),
}));

describe('UnitManagement', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders unit list with units', () => {
    render(<UnitManagement propertyId="property1" />);
    
    expect(screen.getByText('A101')).toBeInTheDocument();
    expect(screen.getByText('A102')).toBeInTheDocument();
    expect(screen.getByText('KES 50,000')).toBeInTheDocument();
    expect(screen.getByText('KES 45,000')).toBeInTheDocument();
  });

  it('shows unit status badges correctly', () => {
    render(<UnitManagement propertyId="property1" />);
    
    expect(screen.getByText('Occupied')).toBeInTheDocument();
    expect(screen.getByText('Vacant')).toBeInTheDocument();
  });

  it('opens create unit dialog when add button is clicked', () => {
    render(<UnitManagement propertyId="property1" />);
    
    const addButton = screen.getByRole('button', { name: /add unit/i });
    fireEvent.click(addButton);
    
    expect(screen.getByText(/create new unit/i)).toBeInTheDocument();
  });

  it('filters units by status', () => {
    render(<UnitManagement propertyId="property1" />);
    
    const statusFilter = screen.getByRole('combobox', { name: /filter by status/i });
    fireEvent.click(statusFilter);
    
    const vacantOption = screen.getByText('Vacant');
    fireEvent.click(vacantOption);
    
    expect(screen.getByText('A102')).toBeInTheDocument();
    expect(screen.queryByText('A101')).not.toBeInTheDocument();
  });

  it('searches units by unit number', () => {
    render(<UnitManagement propertyId="property1" />);
    
    const searchInput = screen.getByPlaceholderText(/search units/i);
    fireEvent.change(searchInput, { target: { value: 'A101' } });
    
    expect(screen.getByText('A101')).toBeInTheDocument();
    expect(screen.queryByText('A102')).not.toBeInTheDocument();
  });

  it('opens edit dialog when edit button is clicked', () => {
    render(<UnitManagement propertyId="property1" />);
    
    const editButtons = screen.getAllByRole('button', { name: /edit/i });
    fireEvent.click(editButtons[0]);
    
    expect(screen.getByText(/edit unit/i)).toBeInTheDocument();
    expect(screen.getByDisplayValue('A101')).toBeInTheDocument();
  });

  it('creates new unit successfully', async () => {
    mockCreateUnit.mockResolvedValue({ _id: 'new-unit' });
    
    render(<UnitManagement propertyId="property1" />);
    
    const addButton = screen.getByRole('button', { name: /add unit/i });
    fireEvent.click(addButton);
    
    // Fill form
    fireEvent.change(screen.getByLabelText(/unit number/i), { 
      target: { value: 'A103' } 
    });
    fireEvent.change(screen.getByLabelText(/size/i), { 
      target: { value: '1100' } 
    });
    fireEvent.change(screen.getByLabelText(/rent/i), { 
      target: { value: '48000' } 
    });
    
    const submitButton = screen.getByRole('button', { name: /create unit/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockCreateUnit).toHaveBeenCalledWith({
        propertyId: 'property1',
        unitNumber: 'A103',
        type: 'apartment',
        size: 1100,
        rent: 48000,
        status: 'vacant',
        amenities: [],
      });
    });
  });

  it('updates unit successfully', async () => {
    mockUpdateUnit.mockResolvedValue({ success: true });
    
    render(<UnitManagement propertyId="property1" />);
    
    const editButtons = screen.getAllByRole('button', { name: /edit/i });
    fireEvent.click(editButtons[0]);
    
    // Update rent
    const rentInput = screen.getByDisplayValue('50000');
    fireEvent.change(rentInput, { target: { value: '55000' } });
    
    const submitButton = screen.getByRole('button', { name: /update unit/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockUpdateUnit).toHaveBeenCalledWith({
        unitId: 'unit1',
        rent: 55000,
      });
    });
  });

  it('deletes unit with confirmation', async () => {
    mockDeleteUnit.mockResolvedValue({ success: true });
    
    // Mock window.confirm
    const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(true);
    
    render(<UnitManagement propertyId="property1" />);
    
    const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
    fireEvent.click(deleteButtons[0]);
    
    await waitFor(() => {
      expect(confirmSpy).toHaveBeenCalledWith(
        'Are you sure you want to delete unit A101? This action cannot be undone.'
      );
      expect(mockDeleteUnit).toHaveBeenCalledWith({ unitId: 'unit1' });
    });
    
    confirmSpy.mockRestore();
  });

  it('shows empty state when no units exist', () => {
    vi.mocked(require('convex/react').useQuery).mockReturnValue([]);
    
    render(<UnitManagement propertyId="property1" />);
    
    expect(screen.getByText(/no units found/i)).toBeInTheDocument();
    expect(screen.getByText(/create your first unit/i)).toBeInTheDocument();
  });

  it('handles loading state', () => {
    vi.mocked(require('convex/react').useQuery).mockReturnValue(undefined);
    
    render(<UnitManagement propertyId="property1" />);
    
    expect(screen.getByText(/loading units/i)).toBeInTheDocument();
  });

  it('shows error message on create failure', async () => {
    mockCreateUnit.mockRejectedValue(new Error('Unit number already exists'));
    
    render(<UnitManagement propertyId="property1" />);
    
    const addButton = screen.getByRole('button', { name: /add unit/i });
    fireEvent.click(addButton);
    
    // Fill form with duplicate unit number
    fireEvent.change(screen.getByLabelText(/unit number/i), { 
      target: { value: 'A101' } 
    });
    
    const submitButton = screen.getByRole('button', { name: /create unit/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/unit number already exists/i)).toBeInTheDocument();
    });
  });
});