import { useCallback } from 'react'

export interface NotificationOptions {
  title: string
  body: string
  icon?: string
  tag?: string
  requireInteraction?: boolean
}

export const useNativeNotifications = () => {
  const showNotification = useCallback(async (options: NotificationOptions) => {
    // Try native Electron notification first
    if (window.electronAPI) {
      try {
        const success = await window.electronAPI.showNotification({
          title: options.title,
          body: options.body,
          icon: options.icon
        })
        
        if (success) {
          return true
        }
      } catch (error) {
        console.warn('Native notification failed, falling back to web notification:', error)
      }
    }

    // Fallback to web notifications
    if ('Notification' in window) {
      if (Notification.permission === 'granted') {
        new Notification(options.title, {
          body: options.body,
          icon: options.icon,
          tag: options.tag,
          requireInteraction: options.requireInteraction
        })
        return true
      } else if (Notification.permission !== 'denied') {
        const permission = await Notification.requestPermission()
        if (permission === 'granted') {
          new Notification(options.title, {
            body: options.body,
            icon: options.icon,
            tag: options.tag,
            requireInteraction: options.requireInteraction
          })
          return true
        }
      }
    }

    return false
  }, [])

  const requestPermission = useCallback(async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }
    return false
  }, [])

  const isSupported = useCallback(() => {
    return window.electronAPI || 'Notification' in window
  }, [])

  const getPermission = useCallback(() => {
    if (window.electronAPI) {
      return 'granted' // Electron notifications don't require permission
    }
    
    if ('Notification' in window) {
      return Notification.permission
    }
    
    return 'denied'
  }, [])

  // Predefined notification types for common use cases
  const showMaintenanceAlert = useCallback((ticketId: string, priority: string) => {
    return showNotification({
      title: 'Maintenance Alert',
      body: `New ${priority} priority ticket #${ticketId} requires attention`,
      icon: '/icons/maintenance.png',
      tag: `maintenance-${ticketId}`,
      requireInteraction: priority === 'emergency'
    })
  }, [showNotification])

  const showPaymentReminder = useCallback((tenantName: string, amount: number) => {
    return showNotification({
      title: 'Payment Reminder',
      body: `${tenantName} has an overdue payment of $${amount}`,
      icon: '/icons/payment.png',
      tag: `payment-${tenantName}`,
      requireInteraction: false
    })
  }, [showNotification])

  const showLeaseExpiry = useCallback((tenantName: string, daysLeft: number) => {
    return showNotification({
      title: 'Lease Expiry Notice',
      body: `${tenantName}'s lease expires in ${daysLeft} days`,
      icon: '/icons/lease.png',
      tag: `lease-${tenantName}`,
      requireInteraction: daysLeft <= 7
    })
  }, [showNotification])

  const showSystemUpdate = useCallback((version: string) => {
    return showNotification({
      title: 'System Update Available',
      body: `EstatePulse ${version} is ready to install`,
      icon: '/icons/update.png',
      tag: 'system-update',
      requireInteraction: true
    })
  }, [showNotification])

  return {
    showNotification,
    requestPermission,
    isSupported,
    getPermission,
    // Predefined notifications
    showMaintenanceAlert,
    showPaymentReminder,
    showLeaseExpiry,
    showSystemUpdate
  }
}