import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useRealtimeData } from '../useRealtimeData';

// Mock Convex hooks
const mockUseQuery = vi.fn();
const mockUseMutation = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: mockUseQuery,
  useMutation: mockUseMutation,
}));

// Mock real-time sync utilities
vi.mock('../lib/realtime-sync', () => ({
  subscribeToUpdates: vi.fn(),
  unsubscribeFromUpdates: vi.fn(),
  syncData: vi.fn(),
}));

describe('useRealtimeData', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with loading state', () => {
    mockUseQuery.mockReturnValue(undefined);
    
    const { result } = renderHook(() => 
      useRealtimeData('properties', { ownerId: 'user1' })
    );

    expect(result.current.data).toBeUndefined();
    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBeNull();
  });

  it('should return data when query succeeds', () => {
    const mockData = [
      { _id: 'prop1', name: 'Property 1' },
      { _id: 'prop2', name: 'Property 2' },
    ];
    mockUseQuery.mockReturnValue(mockData);

    const { result } = renderHook(() => 
      useRealtimeData('properties', { ownerId: 'user1' })
    );

    expect(result.current.data).toEqual(mockData);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should handle query errors', () => {
    const mockError = new Error('Query failed');
    mockUseQuery.mockImplementation(() => {
      throw mockError;
    });

    const { result } = renderHook(() => 
      useRealtimeData('properties', { ownerId: 'user1' })
    );

    expect(result.current.data).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toEqual(mockError);
  });

  it('should update data when real-time updates occur', () => {
    const initialData = [{ _id: 'prop1', name: 'Property 1' }];
    const updatedData = [
      { _id: 'prop1', name: 'Updated Property 1' },
      { _id: 'prop2', name: 'Property 2' },
    ];

    mockUseQuery.mockReturnValueOnce(initialData);

    const { result, rerender } = renderHook(() => 
      useRealtimeData('properties', { ownerId: 'user1' })
    );

    expect(result.current.data).toEqual(initialData);

    // Simulate real-time update
    mockUseQuery.mockReturnValue(updatedData);
    rerender();

    expect(result.current.data).toEqual(updatedData);
  });

  it('should provide optimistic updates', async () => {
    const mockMutation = vi.fn().mockResolvedValue({ _id: 'new-prop' });
    mockUseMutation.mockReturnValue(mockMutation);
    
    const initialData = [{ _id: 'prop1', name: 'Property 1' }];
    mockUseQuery.mockReturnValue(initialData);

    const { result } = renderHook(() => 
      useRealtimeData('properties', { ownerId: 'user1' })
    );

    const newProperty = { name: 'New Property', type: 'residential' };

    await act(async () => {
      await result.current.optimisticUpdate('create', newProperty);
    });

    expect(mockMutation).toHaveBeenCalledWith(newProperty);
  });

  it('should handle optimistic update failures', async () => {
    const mockMutation = vi.fn().mockRejectedValue(new Error('Mutation failed'));
    mockUseMutation.mockReturnValue(mockMutation);
    
    const initialData = [{ _id: 'prop1', name: 'Property 1' }];
    mockUseQuery.mockReturnValue(initialData);

    const { result } = renderHook(() => 
      useRealtimeData('properties', { ownerId: 'user1' })
    );

    const newProperty = { name: 'New Property', type: 'residential' };

    await act(async () => {
      try {
        await result.current.optimisticUpdate('create', newProperty);
      } catch (error) {
        expect(error.message).toBe('Mutation failed');
      }
    });

    expect(result.current.error?.message).toBe('Mutation failed');
  });

  it('should debounce rapid updates', async () => {
    const mockData = [{ _id: 'prop1', name: 'Property 1' }];
    mockUseQuery.mockReturnValue(mockData);

    const { result } = renderHook(() => 
      useRealtimeData('properties', { ownerId: 'user1' }, { debounceMs: 100 })
    );

    // Simulate rapid updates
    act(() => {
      result.current.refresh();
      result.current.refresh();
      result.current.refresh();
    });

    // Should only trigger once after debounce
    await new Promise(resolve => setTimeout(resolve, 150));
    
    expect(mockUseQuery).toHaveBeenCalled();
  });

  it('should handle connection status changes', () => {
    mockUseQuery.mockReturnValue([]);

    const { result } = renderHook(() => 
      useRealtimeData('properties', { ownerId: 'user1' })
    );

    expect(result.current.connectionStatus).toBe('connected');

    // Simulate connection loss
    act(() => {
      // This would be triggered by the real-time sync system
      result.current.setConnectionStatus('disconnected');
    });

    expect(result.current.connectionStatus).toBe('disconnected');
  });

  it('should retry failed queries', async () => {
    let callCount = 0;
    mockUseQuery.mockImplementation(() => {
      callCount++;
      if (callCount < 3) {
        throw new Error('Network error');
      }
      return [{ _id: 'prop1', name: 'Property 1' }];
    });

    const { result } = renderHook(() => 
      useRealtimeData('properties', { ownerId: 'user1' }, { retryCount: 3 })
    );

    await act(async () => {
      await result.current.retry();
    });

    expect(result.current.data).toEqual([{ _id: 'prop1', name: 'Property 1' }]);
    expect(callCount).toBe(3);
  });

  it('should cleanup subscriptions on unmount', () => {
    const { unmount } = renderHook(() => 
      useRealtimeData('properties', { ownerId: 'user1' })
    );

    const { unsubscribeFromUpdates } = require('../lib/realtime-sync');

    unmount();

    expect(unsubscribeFromUpdates).toHaveBeenCalled();
  });

  it('should handle different data types', () => {
    const mockUnits = [
      { _id: 'unit1', unitNumber: 'A101' },
      { _id: 'unit2', unitNumber: 'A102' },
    ];
    mockUseQuery.mockReturnValue(mockUnits);

    const { result } = renderHook(() => 
      useRealtimeData('units', { propertyId: 'prop1' })
    );

    expect(result.current.data).toEqual(mockUnits);
  });

  it('should support custom query parameters', () => {
    mockUseQuery.mockReturnValue([]);

    renderHook(() => 
      useRealtimeData('properties', { 
        ownerId: 'user1',
        type: 'residential',
        status: 'active'
      })
    );

    expect(mockUseQuery).toHaveBeenCalledWith(
      expect.any(Object),
      expect.objectContaining({
        ownerId: 'user1',
        type: 'residential',
        status: 'active'
      })
    );
  });
});