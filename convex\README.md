# EstatePulse Data Models

This directory contains the Convex backend implementation for EstatePulse, including database schema, mutations, queries, and validation functions.

## Overview

The backend is built using Convex, providing real-time data synchronization and a type-safe API. The system supports property management, tenant management, lease tracking, maintenance ticketing, and financial operations.

## Database Schema

### Core Entities

#### Users
- **Purpose**: Manages all system users with role-based access
- **Roles**: owner, manager, vendor, tenant
- **Key Features**: KYC status tracking, property access control, authentication

#### Properties
- **Purpose**: Represents real estate properties (residential, commercial, mixed)
- **Key Features**: Owner/manager assignment, branding configuration, settings management
- **Relationships**: One-to-many with units, leases, maintenance tickets

#### Units
- **Purpose**: Individual units within properties (apartments, offices, retail, parking)
- **Key Features**: Status tracking (vacant/occupied/maintenance), amenities, pricing
- **Relationships**: Belongs to property, can have multiple leases over time

#### Leases
- **Purpose**: Rental agreements between tenants and property owners
- **Key Features**: E-signature support, term management, status tracking
- **Relationships**: Links properties, units, and tenants

#### Maintenance Tickets
- **Purpose**: Service request and maintenance tracking system
- **Key Features**: SLA tracking, vendor assignment, priority management
- **Relationships**: Links to properties, units, tenants, and vendors

#### Financial Entities
- **Invoices**: Billing and payment tracking
- **Payments**: Payment processing with M-PESA and Stripe integration
- **Documents**: File storage and management with role-based access

## API Structure

### Properties API (`convex/properties.ts`)

#### Queries
- `getProperties()` - Get all properties with optional filters
- `getPropertyById(id)` - Get specific property details
- `getPropertiesByOwner(ownerId)` - Get properties owned by specific user
- `getPropertiesByManager(managerId)` - Get properties managed by specific user
- `getPropertyAnalytics(propertyId)` - Get comprehensive property analytics

#### Mutations
- `createProperty(data)` - Create new property
- `updateProperty(id, updates)` - Update property details
- `deleteProperty(id)` - Soft delete property (sets isActive to false)

### Units API (`convex/units.ts`)

#### Queries
- `getUnits(filters)` - Get units with optional property/status/type filters
- `getUnitById(id)` - Get specific unit details
- `getUnitsByProperty(propertyId)` - Get all units in a property
- `getAvailableUnits(propertyId)` - Get vacant units available for lease
- `getUnitOccupancyAnalytics(propertyId)` - Get occupancy statistics

#### Mutations
- `createUnit(data)` - Create new unit
- `updateUnit(id, updates)` - Update unit details
- `updateUnitStatus(id, status)` - Update unit status
- `deleteUnit(id)` - Delete unit (with validation checks)
- `bulkUpdateUnitStatus(unitIds, status)` - Bulk status updates

## Validation System (`convex/lib/validation.ts`)

### Property Validation
- **validatePropertyData()**: Ensures required fields and valid property types
- **sanitizePropertyData()**: Cleans and trims input data

### Unit Validation
- **validateUnitData()**: Validates unit numbers, types, sizes, and pricing
- **sanitizeUnitData()**: Cleans amenities and descriptions

### Lease Validation
- **validateLeaseData()**: Ensures valid date ranges and positive amounts
- **validateDateRange()**: Checks start/end date logic

### Maintenance Validation
- **validateMaintenanceTicketData()**: Validates ticket requirements
- **calculateSLADeadline()**: Calculates SLA deadlines based on priority

### Utility Functions
- **validateEmail()**: Email format validation
- **validatePhoneNumber()**: Kenyan phone number validation
- **formatCurrency()**: Currency formatting for KES
- **validatePositiveNumber()**: Ensures positive values
- **validateNonNegativeNumber()**: Ensures non-negative values

## Testing

### Test Coverage
- **Unit Tests**: Validation functions and business logic
- **Integration Tests**: API endpoints and data operations
- **Test Files**: 
  - `convex/__tests__/properties.test.ts`
  - `convex/__tests__/units.test.ts`

### Running Tests
```bash
npm test
```

## Key Features Implemented

### ✅ Completed Features

1. **Database Schema**: Complete schema for all core entities
2. **Property Management**: Full CRUD operations with validation
3. **Unit Management**: Comprehensive unit tracking and analytics
4. **Data Validation**: Robust validation and sanitization
5. **Error Handling**: Proper error messages and validation
6. **Real-time Queries**: Convex-powered real-time data sync
7. **Analytics**: Property and unit occupancy analytics
8. **Bulk Operations**: Efficient bulk updates for units
9. **Relationship Management**: Proper foreign key relationships
10. **Test Coverage**: Comprehensive unit tests

### 🔄 Integration Points

The data models are designed to integrate with:
- **Authentication System**: User roles and permissions
- **Payment Processing**: M-PESA and Stripe integration
- **File Storage**: Document management system
- **Communication**: SMS and WhatsApp notifications
- **White-label Portals**: Tenant-facing interfaces

### 📊 Analytics Capabilities

- **Property Analytics**: Occupancy rates, revenue tracking, lease counts
- **Unit Analytics**: Status distribution, type breakdown, availability
- **Financial Tracking**: Invoice and payment status monitoring
- **Maintenance Metrics**: SLA compliance and ticket analytics

## Security Considerations

- **Input Validation**: All inputs are validated and sanitized
- **Role-based Access**: User permissions control data access
- **Soft Deletes**: Properties are deactivated rather than deleted
- **Relationship Integrity**: Foreign key validation prevents orphaned records
- **Data Consistency**: Proper validation prevents invalid state transitions

## Performance Optimizations

- **Indexed Queries**: Strategic database indexes for common queries
- **Filtered Results**: Efficient filtering at the database level
- **Bulk Operations**: Optimized bulk updates for better performance
- **Real-time Subscriptions**: Selective real-time updates

## Next Steps

The data models are ready for integration with:
1. **Frontend Components**: React components can now consume these APIs
2. **Authentication Layer**: RBAC system implementation
3. **Payment Integration**: M-PESA and Stripe payment processing
4. **File Upload**: Document management system
5. **Notification System**: SMS and email notifications