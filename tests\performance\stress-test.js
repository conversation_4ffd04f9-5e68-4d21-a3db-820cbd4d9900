import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');

// Stress test configuration - gradually increase load to find breaking point
export const options = {
  stages: [
    { duration: '1m', target: 50 },   // Ramp up to 50 users
    { duration: '2m', target: 100 },  // Ramp up to 100 users
    { duration: '2m', target: 200 },  // Ramp up to 200 users
    { duration: '2m', target: 300 },  // Ramp up to 300 users
    { duration: '2m', target: 400 },  // Ramp up to 400 users
    { duration: '5m', target: 400 },  // Stay at 400 users
    { duration: '2m', target: 0 },    // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests should be below 2s
    http_req_failed: ['rate<0.3'],     // Error rate should be below 30%
    errors: ['rate<0.3'],              // Custom error rate should be below 30%
  },
};

const BASE_URL = 'http://localhost:5173';
const AUTH_TOKEN = 'mock-jwt-token';

export default function () {
  // Simulate heavy concurrent operations
  const operations = [
    heavyPropertySearch,
    bulkUnitCreation,
    concurrentPaymentProcessing,
    massMaintenanceTickets,
    realTimeDataSync,
  ];
  
  const operation = operations[Math.floor(Math.random() * operations.length)];
  operation();
  
  sleep(0.5); // Shorter sleep for stress testing
}

function heavyPropertySearch() {
  // Simulate complex search with filters
  const searchParams = new URLSearchParams({
    search: 'apartment',
    type: 'residential',
    minRent: '30000',
    maxRent: '80000',
    city: 'Nairobi',
    occupancyRate: 'high',
    sortBy: 'rent',
    sortOrder: 'desc',
    page: Math.floor(Math.random() * 10) + 1,
    limit: 20,
  });
  
  const response = http.get(`${BASE_URL}/api/properties/search?${searchParams}`, {
    headers: {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
  });
  
  const success = check(response, {
    'heavy search status is 200': (r) => r.status === 200,
    'heavy search response time < 1500ms': (r) => r.timings.duration < 1500,
  });
  
  if (!success) {
    errorRate.add(1);
  }
}

function bulkUnitCreation() {
  // Simulate creating multiple units at once
  const units = [];
  for (let i = 0; i < 5; i++) {
    units.push({
      unitNumber: `STRESS-${Math.floor(Math.random() * 10000)}-${i}`,
      type: 'apartment',
      size: 1000 + Math.floor(Math.random() * 500),
      rent: 40000 + Math.floor(Math.random() * 20000),
      status: 'vacant',
      amenities: ['parking', 'balcony'],
    });
  }
  
  const response = http.post(`${BASE_URL}/api/units/bulk`, JSON.stringify({
    propertyId: 'stress-test-property',
    units: units,
  }), {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
  });
  
  const success = check(response, {
    'bulk unit creation status is 201 or 400': (r) => r.status === 201 || r.status === 400,
    'bulk unit creation response time < 2000ms': (r) => r.timings.duration < 2000,
  });
  
  if (!success) {
    errorRate.add(1);
  }
}

function concurrentPaymentProcessing() {
  // Simulate high-volume payment processing
  const paymentData = {
    invoiceId: `stress-invoice-${Math.floor(Math.random() * 1000)}`,
    amount: Math.floor(Math.random() * 100000) + 10000,
    method: Math.random() > 0.5 ? 'mpesa' : 'stripe',
    phoneNumber: `25471${Math.floor(Math.random() * 10000000)}`,
  };
  
  const endpoint = paymentData.method === 'mpesa' ? '/api/payments/mpesa' : '/api/payments/stripe';
  
  const response = http.post(`${BASE_URL}${endpoint}`, JSON.stringify(paymentData), {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
  });
  
  const success = check(response, {
    'concurrent payment status is 200 or 400': (r) => r.status === 200 || r.status === 400,
    'concurrent payment response time < 3000ms': (r) => r.timings.duration < 3000,
  });
  
  if (!success) {
    errorRate.add(1);
  }
}

function massMaintenanceTickets() {
  // Simulate creating many maintenance tickets
  const ticketData = {
    title: `Stress Test Ticket ${Math.floor(Math.random() * 10000)}`,
    description: `This is a stress test maintenance ticket created at ${new Date().toISOString()}. It contains a longer description to simulate real-world usage patterns and test the system's ability to handle larger payloads during high-load scenarios.`,
    priority: ['low', 'medium', 'high', 'emergency'][Math.floor(Math.random() * 4)],
    category: ['plumbing', 'electrical', 'hvac', 'general'][Math.floor(Math.random() * 4)],
    propertyId: 'stress-test-property',
    unitId: `stress-unit-${Math.floor(Math.random() * 100)}`,
  };
  
  const response = http.post(`${BASE_URL}/api/maintenance/tickets`, JSON.stringify(ticketData), {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
  });
  
  const success = check(response, {
    'mass ticket creation status is 201': (r) => r.status === 201,
    'mass ticket creation response time < 1000ms': (r) => r.timings.duration < 1000,
  });
  
  if (!success) {
    errorRate.add(1);
  }
}

function realTimeDataSync() {
  // Simulate heavy real-time data synchronization
  const syncData = {
    entityType: ['properties', 'units', 'leases', 'payments'][Math.floor(Math.random() * 4)],
    lastSyncTimestamp: Date.now() - Math.floor(Math.random() * 3600000), // Random time in last hour
    batchSize: 50,
  };
  
  const response = http.post(`${BASE_URL}/api/sync/pull`, JSON.stringify(syncData), {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
  });
  
  const success = check(response, {
    'realtime sync status is 200': (r) => r.status === 200,
    'realtime sync response time < 800ms': (r) => r.timings.duration < 800,
    'realtime sync returns data': (r) => {
      try {
        const data = JSON.parse(r.body);
        return data.updates !== undefined;
      } catch {
        return false;
      }
    },
  });
  
  if (!success) {
    errorRate.add(1);
  }
}