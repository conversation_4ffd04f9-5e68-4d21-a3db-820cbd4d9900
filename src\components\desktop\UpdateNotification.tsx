import React, { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import { Badge } from '../ui/badge'
import { Separator } from '../ui/separator'
import { 
  Download, 
  CheckCircle, 
  AlertCircle,
  ExternalLink,
  Clock
} from 'lucide-react'
import { useAutoUpdater } from '../../hooks/useAutoUpdater'

interface UpdateNotificationProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export const UpdateNotification: React.FC<UpdateNotificationProps> = ({
  open,
  onOpenChange
}) => {
  const [showReleaseNotes, setShowReleaseNotes] = useState(false)
  
  const {
    updateStatus,
    isDownloading,
    isUpdateAvailable,
    isUpdateDownloaded,
    downloadProgress,
    downloadUpdate,
    installUpdate
  } = useAutoUpdater()

  const handleDownload = async () => {
    try {
      await downloadUpdate()
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  const handleInstall = async () => {
    try {
      await installUpdate()
      // App will restart
    } catch (error) {
      console.error('Installation failed:', error)
    }
  }

  const handleLater = () => {
    onOpenChange(false)
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (!updateStatus || (!isUpdateAvailable && !isUpdateDownloaded)) {
    return null
  }

  const getDialogTitle = () => {
    if (isUpdateDownloaded) {
      return 'Update Ready to Install'
    }
    if (isDownloading) {
      return 'Downloading Update'
    }
    return 'Update Available'
  }

  const getDialogDescription = () => {
    if (isUpdateDownloaded) {
      return `EstatePulse ${updateStatus.version} has been downloaded and is ready to install. The application will restart to complete the installation.`
    }
    if (isDownloading) {
      return `Downloading EstatePulse ${updateStatus.version}...`
    }
    return `A new version of EstatePulse (${updateStatus.version}) is available for download.`
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isUpdateDownloaded ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : isDownloading ? (
              <Download className="h-5 w-5 text-blue-500" />
            ) : (
              <AlertCircle className="h-5 w-5 text-orange-500" />
            )}
            {getDialogTitle()}
          </DialogTitle>
          <DialogDescription>
            {getDialogDescription()}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Version Info */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">New Version:</span>
            <Badge variant="default">{updateStatus.version}</Badge>
          </div>

          {/* Release Date */}
          {updateStatus.releaseDate && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Release Date:</span>
              <span className="text-sm text-muted-foreground">
                {new Date(updateStatus.releaseDate).toLocaleDateString()}
              </span>
            </div>
          )}

          {/* Download Progress */}
          {isDownloading && (
            <div className="space-y-2">
              <Progress value={downloadProgress} className="w-full" />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>{Math.round(downloadProgress)}% complete</span>
                {updateStatus.bytesPerSecond && (
                  <span>
                    {formatBytes(updateStatus.bytesPerSecond)}/s
                  </span>
                )}
              </div>
              {updateStatus.transferred && updateStatus.total && (
                <div className="text-center text-sm text-muted-foreground">
                  {formatBytes(updateStatus.transferred)} / {formatBytes(updateStatus.total)}
                </div>
              )}
            </div>
          )}

          {/* Release Notes */}
          {updateStatus.releaseNotes && (
            <div className="space-y-2">
              <Separator />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowReleaseNotes(!showReleaseNotes)}
                className="w-full justify-start"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                {showReleaseNotes ? 'Hide' : 'Show'} Release Notes
              </Button>
              
              {showReleaseNotes && (
                <div className="max-h-32 overflow-y-auto p-3 bg-muted rounded-md text-sm">
                  <pre className="whitespace-pre-wrap">{updateStatus.releaseNotes}</pre>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          {isUpdateDownloaded ? (
            <>
              <Button variant="outline" onClick={handleLater}>
                <Clock className="h-4 w-4 mr-2" />
                Install Later
              </Button>
              <Button onClick={handleInstall}>
                <CheckCircle className="h-4 w-4 mr-2" />
                Install Now
              </Button>
            </>
          ) : isDownloading ? (
            <Button variant="outline" onClick={handleLater} disabled>
              Downloading...
            </Button>
          ) : (
            <>
              <Button variant="outline" onClick={handleLater}>
                <Clock className="h-4 w-4 mr-2" />
                Remind Later
              </Button>
              <Button onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download Now
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}