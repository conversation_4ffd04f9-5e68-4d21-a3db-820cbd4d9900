import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Doc, Id } from "./_generated/dataModel";
import { checkPermission } from "./permissions";

// Vendor Management Functions

export const createVendor = mutation({
  args: {
    userId: v.id("users"),
    companyName: v.string(),
    contactPerson: v.string(),
    phone: v.string(),
    email: v.string(),
    address: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      country: v.string(),
      postalCode: v.string(),
    }),
    specialties: v.array(v.union(
      v.literal("plumbing"),
      v.literal("electrical"),
      v.literal("hvac"),
      v.literal("appliance"),
      v.literal("structural"),
      v.literal("cleaning"),
      v.literal("security"),
      v.literal("other")
    )),
    serviceAreas: v.array(v.id("properties")),
    availability: v.object({
      workingHours: v.object({
        start: v.string(),
        end: v.string(),
      }),
      workingDays: v.array(v.union(
        v.literal("monday"),
        v.literal("tuesday"),
        v.literal("wednesday"),
        v.literal("thursday"),
        v.literal("friday"),
        v.literal("saturday"),
        v.literal("sunday")
      )),
      emergencyAvailable: v.boolean(),
    }),
    pricing: v.object({
      hourlyRate: v.optional(v.number()),
      calloutFee: v.optional(v.number()),
      emergencyRate: v.optional(v.number()),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "vendors", "create");
    if (!hasAccess) throw new Error("Insufficient permissions");

    // Verify the user exists and has vendor role
    const vendorUser = await ctx.db.get(args.userId);
    if (!vendorUser || vendorUser.role !== "vendor") {
      throw new Error("Invalid vendor user");
    }

    // Check if vendor profile already exists
    const existingVendor = await ctx.db
      .query("vendors")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (existingVendor) {
      throw new Error("Vendor profile already exists for this user");
    }

    const now = Date.now();

    const vendorId = await ctx.db.insert("vendors", {
      userId: args.userId,
      companyName: args.companyName,
      contactPerson: args.contactPerson,
      phone: args.phone,
      email: args.email,
      address: args.address,
      specialties: args.specialties,
      serviceAreas: args.serviceAreas,
      availability: args.availability,
      pricing: args.pricing,
      performance: {
        averageRating: 0,
        totalJobs: 0,
        completedJobs: 0,
        averageResponseTime: 0,
        averageCompletionTime: 0,
        slaCompliance: 100,
      },
      documents: [],
      isActive: true,
      isVerified: false,
      createdAt: now,
      updatedAt: now,
    });

    return vendorId;
  },
});

export const updateVendor = mutation({
  args: {
    vendorId: v.id("vendors"),
    companyName: v.optional(v.string()),
    contactPerson: v.optional(v.string()),
    phone: v.optional(v.string()),
    email: v.optional(v.string()),
    address: v.optional(v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      country: v.string(),
      postalCode: v.string(),
    })),
    specialties: v.optional(v.array(v.union(
      v.literal("plumbing"),
      v.literal("electrical"),
      v.literal("hvac"),
      v.literal("appliance"),
      v.literal("structural"),
      v.literal("cleaning"),
      v.literal("security"),
      v.literal("other")
    ))),
    serviceAreas: v.optional(v.array(v.id("properties"))),
    availability: v.optional(v.object({
      workingHours: v.object({
        start: v.string(),
        end: v.string(),
      }),
      workingDays: v.array(v.union(
        v.literal("monday"),
        v.literal("tuesday"),
        v.literal("wednesday"),
        v.literal("thursday"),
        v.literal("friday"),
        v.literal("saturday"),
        v.literal("sunday")
      )),
      emergencyAvailable: v.boolean(),
    })),
    pricing: v.optional(v.object({
      hourlyRate: v.optional(v.number()),
      calloutFee: v.optional(v.number()),
      emergencyRate: v.optional(v.number()),
    })),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const vendor = await ctx.db.get(args.vendorId);
    if (!vendor) throw new Error("Vendor not found");

    // Vendors can update their own profile, managers can update any
    const hasAccess = vendor.userId === user._id || 
                     await checkPermission(ctx, user._id, "vendors", "update");
    
    if (!hasAccess) throw new Error("Insufficient permissions");

    const updateData: any = {
      updatedAt: Date.now(),
    };

    // Only update provided fields
    if (args.companyName !== undefined) updateData.companyName = args.companyName;
    if (args.contactPerson !== undefined) updateData.contactPerson = args.contactPerson;
    if (args.phone !== undefined) updateData.phone = args.phone;
    if (args.email !== undefined) updateData.email = args.email;
    if (args.address !== undefined) updateData.address = args.address;
    if (args.specialties !== undefined) updateData.specialties = args.specialties;
    if (args.serviceAreas !== undefined) updateData.serviceAreas = args.serviceAreas;
    if (args.availability !== undefined) updateData.availability = args.availability;
    if (args.pricing !== undefined) updateData.pricing = args.pricing;
    if (args.isActive !== undefined) updateData.isActive = args.isActive;

    await ctx.db.patch(args.vendorId, updateData);

    return args.vendorId;
  },
});

export const verifyVendor = mutation({
  args: {
    vendorId: v.id("vendors"),
    isVerified: v.boolean(),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "vendors", "update");
    if (!hasAccess) throw new Error("Insufficient permissions");

    const vendor = await ctx.db.get(args.vendorId);
    if (!vendor) throw new Error("Vendor not found");

    await ctx.db.patch(args.vendorId, {
      isVerified: args.isVerified,
      updatedAt: Date.now(),
    });

    // Notify vendor of verification status
    await ctx.db.insert("notifications", {
      userId: vendor.userId,
      title: args.isVerified ? "Vendor Profile Verified" : "Vendor Profile Verification Failed",
      message: args.isVerified 
        ? "Your vendor profile has been verified and you can now receive maintenance assignments"
        : `Your vendor profile verification failed. ${args.notes || "Please contact support for more information."}`,
      type: "system",
      priority: "medium",
      isRead: false,
      createdAt: Date.now(),
    });

    return args.vendorId;
  },
});

export const findAvailableVendors = query({
  args: {
    propertyId: v.id("properties"),
    category: v.union(
      v.literal("plumbing"),
      v.literal("electrical"),
      v.literal("hvac"),
      v.literal("appliance"),
      v.literal("structural"),
      v.literal("cleaning"),
      v.literal("security"),
      v.literal("other")
    ),
    emergencyOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "read");
    if (!hasAccess) throw new Error("Insufficient permissions");

    // Get all active and verified vendors
    const allVendors = await ctx.db
      .query("vendors")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .collect();

    const availableVendors = allVendors.filter(vendor => {
      // Must be verified
      if (!vendor.isVerified) return false;

      // Must have the required specialty
      if (!vendor.specialties.includes(args.category)) return false;

      // Must service this property
      if (!vendor.serviceAreas.includes(args.propertyId)) return false;

      // If emergency, must be available for emergencies
      if (args.emergencyOnly && !vendor.availability.emergencyAvailable) return false;

      return true;
    });

    // Sort by performance metrics (rating, SLA compliance, response time)
    return availableVendors.sort((a, b) => {
      const scoreA = (a.performance.averageRating * 0.4) + 
                    (a.performance.slaCompliance * 0.4) + 
                    ((100 - a.performance.averageResponseTime) * 0.2);
      const scoreB = (b.performance.averageRating * 0.4) + 
                    (b.performance.slaCompliance * 0.4) + 
                    ((100 - b.performance.averageResponseTime) * 0.2);
      return scoreB - scoreA;
    });
  },
});

export const getVendorById = query({
  args: { vendorId: v.id("vendors") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const vendor = await ctx.db.get(args.vendorId);
    if (!vendor) throw new Error("Vendor not found");

    // Vendors can see their own profile, managers can see all
    const hasAccess = vendor.userId === user._id || 
                     await checkPermission(ctx, user._id, "vendors", "read");
    
    if (!hasAccess) throw new Error("Insufficient permissions");

    return vendor;
  },
});

export const getVendorByUserId = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const vendor = await ctx.db
      .query("vendors")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!vendor) return null;

    // Vendors can see their own profile, managers can see all
    const hasAccess = vendor.userId === user._id || 
                     await checkPermission(ctx, user._id, "vendors", "read");
    
    if (!hasAccess) throw new Error("Insufficient permissions");

    return vendor;
  },
});

export const getAllVendors = query({
  args: {
    isActive: v.optional(v.boolean()),
    isVerified: v.optional(v.boolean()),
    specialty: v.optional(v.union(
      v.literal("plumbing"),
      v.literal("electrical"),
      v.literal("hvac"),
      v.literal("appliance"),
      v.literal("structural"),
      v.literal("cleaning"),
      v.literal("security"),
      v.literal("other")
    )),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "vendors", "read");
    if (!hasAccess) throw new Error("Insufficient permissions");

    let vendors = await ctx.db.query("vendors").collect();

    // Apply filters
    if (args.isActive !== undefined) {
      vendors = vendors.filter(v => v.isActive === args.isActive);
    }

    if (args.isVerified !== undefined) {
      vendors = vendors.filter(v => v.isVerified === args.isVerified);
    }

    if (args.specialty) {
      vendors = vendors.filter(v => v.specialties.includes(args.specialty!));
    }

    return vendors.sort((a, b) => b.createdAt - a.createdAt);
  },
});

export const getBySpecialty = query({
  args: {
    specialty: v.union(
      v.literal("plumbing"),
      v.literal("electrical"),
      v.literal("hvac"),
      v.literal("appliance"),
      v.literal("structural"),
      v.literal("cleaning"),
      v.literal("security"),
      v.literal("other")
    ),
    propertyId: v.id("properties"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "read");
    if (!hasAccess) throw new Error("Insufficient permissions");

    // Get all active and verified vendors with the specialty
    const vendors = await ctx.db
      .query("vendors")
      .withIndex("by_specialty", (q) => q.eq("specialties", args.specialty))
      .collect();

    const availableVendors = vendors.filter(vendor => {
      return vendor.isActive && 
             vendor.isVerified && 
             vendor.serviceAreas.includes(args.propertyId);
    });

    // Sort by performance
    return availableVendors.sort((a, b) => {
      const scoreA = (a.performance.averageRating * 0.4) + 
                    (a.performance.slaCompliance * 0.4) + 
                    ((100 - a.performance.averageResponseTime) * 0.2);
      const scoreB = (b.performance.averageRating * 0.4) + 
                    (b.performance.slaCompliance * 0.4) + 
                    ((100 - b.performance.averageResponseTime) * 0.2);
      return scoreB - scoreA;
    });
  },
});

export const getVendorPerformanceMetrics = query({
  args: { 
    vendorId: v.id("vendors"),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const vendor = await ctx.db.get(args.vendorId);
    if (!vendor) throw new Error("Vendor not found");

    // Vendors can see their own metrics, managers can see all
    const hasAccess = vendor.userId === user._id || 
                     await checkPermission(ctx, user._id, "vendors", "read");
    
    if (!hasAccess) throw new Error("Insufficient permissions");

    // Get tickets assigned to this vendor
    const tickets = await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_vendor", (q) => q.eq("vendorId", vendor.userId))
      .collect();

    // Filter by date range if provided
    const filteredTickets = tickets.filter(ticket => {
      if (args.startDate && ticket.createdAt < args.startDate) return false;
      if (args.endDate && ticket.createdAt > args.endDate) return false;
      return true;
    });

    const completedTickets = filteredTickets.filter(t => 
      t.status === "completed" || t.status === "closed"
    );

    const slaCompliantTickets = completedTickets.filter(t => 
      t.completionDetails && t.completionDetails.completedAt <= t.slaDeadline
    );

    const averageRating = completedTickets.length > 0
      ? completedTickets.reduce((sum, ticket) => {
          return sum + (ticket.completionDetails?.satisfactionRating || 0);
        }, 0) / completedTickets.length
      : 0;

    return {
      ...vendor.performance,
      periodMetrics: {
        totalTickets: filteredTickets.length,
        completedTickets: completedTickets.length,
        slaCompliance: filteredTickets.length > 0 
          ? (slaCompliantTickets.length / filteredTickets.length) * 100 
          : 0,
        averageRating,
        ticketsByCategory: {
          plumbing: filteredTickets.filter(t => t.category === "plumbing").length,
          electrical: filteredTickets.filter(t => t.category === "electrical").length,
          hvac: filteredTickets.filter(t => t.category === "hvac").length,
          appliance: filteredTickets.filter(t => t.category === "appliance").length,
          structural: filteredTickets.filter(t => t.category === "structural").length,
          cleaning: filteredTickets.filter(t => t.category === "cleaning").length,
          security: filteredTickets.filter(t => t.category === "security").length,
          other: filteredTickets.filter(t => t.category === "other").length,
        },
      },
    };
  },
});