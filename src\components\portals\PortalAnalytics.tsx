import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Badge } from '../ui/badge';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts';
import { 
  TrendingUp, 
  Users, 
  Eye, 
  Clock, 
  Smartphone, 
  Monitor, 
  Tablet,
  Download,
  // Calendar,
  Activity,
  MousePointer
} from 'lucide-react';

interface PortalAnalyticsProps {
  portalId: Id<"portals">;
}

interface AnalyticsData {
  date: string;
  pageViews: number;
  uniqueVisitors: number;
  sessions: number;
  averageSessionDuration: number;
  bounceRate: number;
  topPages: Array<{ path: string; views: number }>;
  deviceBreakdown: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  browserBreakdown: Array<{ browser: string; count: number }>;
  tenantEngagement: {
    activeUsers: number;
    paymentTransactions: number;
    maintenanceRequests: number;
    documentDownloads: number;
    messagesSent: number;
  };
}

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

export const PortalAnalytics: React.FC<PortalAnalyticsProps> = ({ portalId }) => {
  const [dateRange, setDateRange] = useState('30d');
  // const [selectedMetric, setSelectedMetric] = useState('pageViews');

  const portal = useQuery(api.portals.getPortalById, { portalId });
  // const analyticsData = useQuery(api.portals.getPortalAnalytics, { 
  //   portalId, 
  //   dateRange 
  // });

  if (!portal) {
    return <div>Loading...</div>;
  }

  // Mock data for demonstration - in real implementation, this would come from the API
  const mockAnalyticsData: AnalyticsData[] = [
    {
      date: '2024-01-01',
      pageViews: 1250,
      uniqueVisitors: 890,
      sessions: 1100,
      averageSessionDuration: 180,
      bounceRate: 35,
      topPages: [
        { path: '/dashboard', views: 450 },
        { path: '/payments', views: 320 },
        { path: '/maintenance', views: 280 },
        { path: '/documents', views: 200 },
      ],
      deviceBreakdown: {
        desktop: 45,
        mobile: 40,
        tablet: 15,
      },
      browserBreakdown: [
        { browser: 'Chrome', count: 65 },
        { browser: 'Safari', count: 20 },
        { browser: 'Firefox', count: 10 },
        { browser: 'Edge', count: 5 },
      ],
      tenantEngagement: {
        activeUsers: 156,
        paymentTransactions: 89,
        maintenanceRequests: 23,
        documentDownloads: 67,
        messagesSent: 34,
      },
    },
    // Add more mock data points...
  ];

  const currentData = mockAnalyticsData[0];

  const MetricCard: React.FC<{
    title: string;
    value: string | number;
    change: number;
    icon: React.ReactNode;
    format?: 'number' | 'percentage' | 'duration';
  }> = ({ title, value, change, icon, format = 'number' }) => {
    const formatValue = (val: string | number) => {
      if (format === 'percentage') return `${val}%`;
      if (format === 'duration') return `${val}s`;
      return typeof val === 'number' ? val.toLocaleString() : val;
    };

    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">{title}</p>
              <p className="text-2xl font-bold">{formatValue(value)}</p>
            </div>
            <div className="p-2 bg-primary/10 text-primary rounded-lg">
              {icon}
            </div>
          </div>
          <div className="flex items-center mt-2">
            <TrendingUp className={`w-4 h-4 mr-1 ${
              change >= 0 ? 'text-green-600' : 'text-red-600'
            }`} />
            <span className={`text-sm ${
              change >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {change >= 0 ? '+' : ''}{change}% from last period
            </span>
          </div>
        </CardContent>
      </Card>
    );
  };

  const DeviceChart: React.FC = () => {
    const data = [
      { name: 'Desktop', value: currentData.deviceBreakdown.desktop, color: COLORS[0] },
      { name: 'Mobile', value: currentData.deviceBreakdown.mobile, color: COLORS[1] },
      { name: 'Tablet', value: currentData.deviceBreakdown.tablet, color: COLORS[2] },
    ];

    return (
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  const BrowserChart: React.FC = () => (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={currentData.browserBreakdown}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="browser" />
        <YAxis />
        <Tooltip />
        <Bar dataKey="count" fill={COLORS[0]} />
      </BarChart>
    </ResponsiveContainer>
  );

  const TopPagesChart: React.FC = () => (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={currentData.topPages} layout="horizontal">
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis type="number" />
        <YAxis dataKey="path" type="category" width={100} />
        <Tooltip />
        <Bar dataKey="views" fill={COLORS[1]} />
      </BarChart>
    </ResponsiveContainer>
  );

  const EngagementMetrics: React.FC = () => {
    const engagement = currentData.tenantEngagement;
    const metrics = [
      { label: 'Active Users', value: engagement.activeUsers, icon: <Users className="w-4 h-4" /> },
      { label: 'Payment Transactions', value: engagement.paymentTransactions, icon: <Activity className="w-4 h-4" /> },
      { label: 'Maintenance Requests', value: engagement.maintenanceRequests, icon: <MousePointer className="w-4 h-4" /> },
      { label: 'Document Downloads', value: engagement.documentDownloads, icon: <Download className="w-4 h-4" /> },
      { label: 'Messages Sent', value: engagement.messagesSent, icon: <Activity className="w-4 h-4" /> },
    ];

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-primary/10 text-primary rounded-lg">
                  {metric.icon}
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{metric.label}</p>
                  <p className="text-xl font-bold">{metric.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Portal Analytics</h2>
          <p className="text-muted-foreground">
            Track usage, engagement, and performance metrics for {portal.name}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Page Views"
          value={currentData.pageViews}
          change={12.5}
          icon={<Eye className="w-5 h-5" />}
        />
        <MetricCard
          title="Unique Visitors"
          value={currentData.uniqueVisitors}
          change={8.2}
          icon={<Users className="w-5 h-5" />}
        />
        <MetricCard
          title="Avg. Session Duration"
          value={currentData.averageSessionDuration}
          change={-2.1}
          icon={<Clock className="w-5 h-5" />}
          format="duration"
        />
        <MetricCard
          title="Bounce Rate"
          value={currentData.bounceRate}
          change={-5.3}
          icon={<TrendingUp className="w-5 h-5" />}
          format="percentage"
        />
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="engagement">Engagement</TabsTrigger>
          <TabsTrigger value="devices">Devices</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Pages</CardTitle>
                <CardDescription>Most visited pages in your portal</CardDescription>
              </CardHeader>
              <CardContent>
                <TopPagesChart />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Browser Usage</CardTitle>
                <CardDescription>Browser distribution of your visitors</CardDescription>
              </CardHeader>
              <CardContent>
                <BrowserChart />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Traffic Trends</CardTitle>
              <CardDescription>Page views and unique visitors over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={mockAnalyticsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Area 
                    type="monotone" 
                    dataKey="pageViews" 
                    stackId="1" 
                    stroke={COLORS[0]} 
                    fill={COLORS[0]} 
                    fillOpacity={0.6}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="uniqueVisitors" 
                    stackId="2" 
                    stroke={COLORS[1]} 
                    fill={COLORS[1]} 
                    fillOpacity={0.6}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="engagement" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tenant Engagement</CardTitle>
              <CardDescription>
                How tenants are interacting with your portal features
              </CardDescription>
            </CardHeader>
            <CardContent>
              <EngagementMetrics />
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Feature Usage</CardTitle>
                <CardDescription>Most used portal features</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { feature: 'Payment Portal', usage: 89, color: 'bg-blue-500' },
                    { feature: 'Maintenance Requests', usage: 67, color: 'bg-green-500' },
                    { feature: 'Document Access', usage: 54, color: 'bg-yellow-500' },
                    { feature: 'Communication Center', usage: 32, color: 'bg-purple-500' },
                  ].map((item, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium">{item.feature}</span>
                          <span className="text-sm text-muted-foreground">{item.usage}%</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${item.color}`}
                            style={{ width: `${item.usage}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Activity Timeline</CardTitle>
                <CardDescription>Peak usage hours</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={[
                    { hour: '00', activity: 5 },
                    { hour: '06', activity: 15 },
                    { hour: '09', activity: 45 },
                    { hour: '12', activity: 65 },
                    { hour: '15', activity: 55 },
                    { hour: '18', activity: 75 },
                    { hour: '21', activity: 35 },
                    { hour: '23', activity: 10 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="activity" stroke={COLORS[0]} strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="devices" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Device Breakdown</CardTitle>
                <CardDescription>How tenants access your portal</CardDescription>
              </CardHeader>
              <CardContent>
                <DeviceChart />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Device Statistics</CardTitle>
                <CardDescription>Detailed device usage metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { 
                      device: 'Desktop', 
                      percentage: currentData.deviceBreakdown.desktop, 
                      icon: <Monitor className="w-5 h-5" />,
                      sessions: 495,
                      avgDuration: '3:24'
                    },
                    { 
                      device: 'Mobile', 
                      percentage: currentData.deviceBreakdown.mobile, 
                      icon: <Smartphone className="w-5 h-5" />,
                      sessions: 440,
                      avgDuration: '2:15'
                    },
                    { 
                      device: 'Tablet', 
                      percentage: currentData.deviceBreakdown.tablet, 
                      icon: <Tablet className="w-5 h-5" />,
                      sessions: 165,
                      avgDuration: '2:45'
                    },
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-primary/10 text-primary rounded-lg">
                          {item.icon}
                        </div>
                        <div>
                          <p className="font-medium">{item.device}</p>
                          <p className="text-sm text-muted-foreground">
                            {item.sessions} sessions • {item.avgDuration} avg
                          </p>
                        </div>
                      </div>
                      <Badge variant="secondary">{item.percentage}%</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <MetricCard
              title="Page Load Time"
              value="1.2s"
              change={-8.5}
              icon={<Clock className="w-5 h-5" />}
            />
            <MetricCard
              title="Server Response"
              value="245ms"
              change={-12.3}
              icon={<Activity className="w-5 h-5" />}
            />
            <MetricCard
              title="Uptime"
              value="99.9%"
              change={0.1}
              icon={<TrendingUp className="w-5 h-5" />}
              format="percentage"
            />
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Performance Insights</CardTitle>
              <CardDescription>
                Recommendations to improve your portal performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2" />
                  <div>
                    <p className="font-medium text-green-900">Good Performance</p>
                    <p className="text-sm text-green-700">
                      Your portal loads quickly and provides a smooth user experience.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2" />
                  <div>
                    <p className="font-medium text-yellow-900">Optimization Opportunity</p>
                    <p className="text-sm text-yellow-700">
                      Consider optimizing images to reduce page load times on mobile devices.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};