"use node";

import { v } from "convex/values";
import { action } from "./_generated/server";

// Send SMS via Custom SMS API
export const sendCustomSMS = action({
  args: {
    phoneNumber: v.string(),
    message: v.string(),
    personalizedData: v.optional(v.record(v.string(), v.any())),
  },
  handler: async (_ctx, args) => {
    try {
      // Get custom SMS API credentials from environment
      const apiUrl = process.env.CUSTOM_SMS_API_URL;
      const apiKey = process.env.CUSTOM_SMS_API_KEY;
      const senderId = process.env.CUSTOM_SMS_SENDER_ID || 'EstatePulse';

      if (!apiUrl || !apiKey) {
        throw new Error('Custom SMS API credentials not configured');
      }

      // Process message with personalization data if provided
      let processedMessage = args.message;
      if (args.personalizedData) {
        Object.entries(args.personalizedData).forEach(([key, value]) => {
          processedMessage = processedMessage.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
        });
      }

      // Format phone number to international format
      const formattedNumber = formatPhoneNumber(args.phoneNumber);

      // Send SMS via custom API
      const response = await fetch(`${apiUrl}/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: formattedNumber,
          message: processedMessage,
          from: senderId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      return {
        success: true,
        messageId: data.messageId || data.id,
        status: data.status || 'sent',
      };
    } catch (error: unknown) {
      console.error("Custom SMS sending failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

// Send WhatsApp via Custom WhatsApp API
export const sendCustomWhatsApp = action({
  args: {
    phoneNumber: v.string(),
    message: v.string(),
    personalizedData: v.optional(v.record(v.string(), v.any())),
  },
  handler: async (_ctx, args) => {
    try {
      // Get custom WhatsApp API credentials from environment
      const apiUrl = process.env.CUSTOM_WHATSAPP_API_URL;
      const apiKey = process.env.CUSTOM_WHATSAPP_API_KEY;

      if (!apiUrl || !apiKey) {
        throw new Error('Custom WhatsApp API credentials not configured');
      }

      // Process message with personalization data if provided
      let processedMessage = args.message;
      if (args.personalizedData) {
        Object.entries(args.personalizedData).forEach(([key, value]) => {
          processedMessage = processedMessage.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
        });
      }

      // Format phone number to international format
      const formattedNumber = formatPhoneNumber(args.phoneNumber);

      // Send WhatsApp message via custom API
      const response = await fetch(`${apiUrl}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messaging_product: "whatsapp",
          to: formattedNumber.replace('+', ''),
          type: 'text',
          text: {
            body: processedMessage
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      return {
        success: true,
        messageId: data.messages?.[0]?.id,
        status: data.messages?.[0]?.message_status || 'sent',
      };
    } catch (error: unknown) {
      console.error("Custom WhatsApp sending failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

// Send Email via Custom Email API
export const sendCustomEmail = action({
  args: {
    to: v.string(),
    subject: v.string(),
    html: v.optional(v.string()),
    text: v.optional(v.string()),
    personalizedData: v.optional(v.record(v.string(), v.any())),
  },
  handler: async (_ctx, args) => {
    try {
      // Get custom email API credentials from environment
      const apiUrl = process.env.CUSTOM_EMAIL_API_URL || 'https://api.youremailservice.com';
      const apiKey = process.env.CUSTOM_EMAIL_API_KEY;
      const fromEmail = process.env.CUSTOM_EMAIL_FROM_EMAIL || '<EMAIL>';
      const fromName = process.env.CUSTOM_EMAIL_FROM_NAME || 'EstatePulse';

      if (!apiKey) {
        throw new Error('Custom Email API credentials not configured');
      }

      // Process content with personalization data if provided
      let processedSubject = args.subject;
      let processedHtml = args.html || '';
      let processedText = args.text || '';

      if (args.personalizedData) {
        Object.entries(args.personalizedData).forEach(([key, value]) => {
          const regex = new RegExp(`{{${key}}}`, 'g');
          processedSubject = processedSubject.replace(regex, String(value));
          processedHtml = processedHtml.replace(regex, String(value));
          processedText = processedText.replace(regex, String(value));
        });
      }

      // Validate email address
      if (!isValidEmail(args.to)) {
        throw new Error('Invalid email address');
      }

      // Send email via custom API
      const response = await fetch(`${apiUrl}/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: {
            email: fromEmail,
            name: fromName
          },
          to: [{ email: args.to }],
          subject: processedSubject,
          html: processedHtml || undefined,
          text: processedText || undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      return {
        success: true,
        messageId: data.messageId || data.id,
        status: 'sent',
      };
    } catch (error: unknown) {
      console.error("Custom Email sending failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

// Get delivery status for custom communication services
export const getCustomDeliveryStatus = action({
  args: {
    messageId: v.string(),
    service: v.union(v.literal("sms"), v.literal("whatsapp"), v.literal("email")),
  },
  handler: async (_ctx, args) => {
    try {
      let apiUrl: string;
      let apiKey: string;

      switch (args.service) {
        case 'sms':
          apiUrl = process.env.CUSTOM_SMS_API_URL || '';
          apiKey = process.env.CUSTOM_SMS_API_KEY || '';
          break;
        case 'whatsapp':
          apiUrl = process.env.CUSTOM_WHATSAPP_API_URL || '';
          apiKey = process.env.CUSTOM_WHATSAPP_API_KEY || '';
          break;
        case 'email':
          apiUrl = process.env.CUSTOM_EMAIL_API_URL || 'https://api.youremailservice.com';
          apiKey = process.env.CUSTOM_EMAIL_API_KEY || '';
          break;
        default:
          throw new Error('Invalid service type');
      }

      if (!apiUrl || !apiKey) {
        throw new Error(`${args.service.toUpperCase()} API credentials not configured`);
      }

      const endpoint = args.service === 'whatsapp' 
        ? `${apiUrl}/messages/${args.messageId}`
        : `${apiUrl}/status/${args.messageId}`;

      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
        },
      });

      if (!response.ok) {
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`,
        };
      }

      const data = await response.json();

      return {
        success: true,
        messageId: args.messageId,
        status: data.status || 'unknown',
        timestamp: data.timestamp,
        error: data.error,
      };
    } catch (error: unknown) {
      console.error("Failed to get delivery status:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

// Utility functions
function formatPhoneNumber(phoneNumber: string): string {
  // Remove all non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, '');

  // Convert to international format for Kenya
  if (cleaned.startsWith('0')) {
    cleaned = '254' + cleaned.substring(1);
  } else if (cleaned.startsWith('7') || cleaned.startsWith('1')) {
    cleaned = '254' + cleaned;
  } else if (!cleaned.startsWith('254')) {
    cleaned = '254' + cleaned;
  }

  return '+' + cleaned;
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
