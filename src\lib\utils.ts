import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number, currency: string = 'KES'): string {
  const formatter = new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
  });

  if (currency === 'KES') {
    return `KES ${amount.toLocaleString('en-KE', { minimumFractionDigits: 2 })}`;
  }

  return formatter.format(amount);
}

export function formatDate(date: Date | number, format?: string): string {
  const dateObj = typeof date === 'number' ? new Date(date) : date;
  
  if (format === 'yyyy-MM-dd') {
    return dateObj.toISOString().split('T')[0];
  }
  
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

export function calculateOccupancyRate(units: Array<{ status: string }>): number {
  if (units.length === 0) return 0;
  
  const occupiedUnits = units.filter(unit => unit.status === 'occupied').length;
  return Math.round((occupiedUnits / units.length) * 100);
}

export function validateEmail(email: string): boolean {
  if (!email) return false;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePhone(phone: string): boolean {
  if (!phone) return false;
  
  // Support Kenyan and international formats
  const phoneRegex = /^(\+254|0)?[17]\d{8}$|^\+\d{10,15}$/;
  return phoneRegex.test(phone);
}