import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Create e-signature request
export const createSignatureRequest = mutation({
  args: {
    leaseId: v.id("leases"),
    documentUrl: v.string(),
    signers: v.array(v.object({
      email: v.string(),
      name: v.string(),
      role: v.union(v.literal("tenant"), v.literal("landlord"), v.literal("witness")),
      order: v.number(),
    })),
    expiresAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Verify lease exists
    const lease = await ctx.db.get(args.leaseId);
    if (!lease) {
      throw new Error("Lease not found");
    }

    // Get related data for the signature request
    const property = await ctx.db.get(lease.propertyId);
    const unit = await ctx.db.get(lease.unitId);
    const tenant = await ctx.db.get(lease.tenantId);

    if (!property || !unit || !tenant) {
      throw new Error("Missing related data for lease");
    }

    const now = Date.now();
    const expiresAt = args.expiresAt || (now + (7 * 24 * 60 * 60 * 1000)); // 7 days from now

    // Create signature request record
    const signatureRequestId = await ctx.db.insert("signatureRequests", {
      leaseId: args.leaseId,
      documentUrl: args.documentUrl,
      status: "pending",
      signers: args.signers.map(signer => ({
        ...signer,
        status: "pending" as const,
        signedAt: undefined,
        ipAddress: undefined,
      })),
      externalRequestId: `req_${now}_${Math.random().toString(36).substr(2, 9)}`,
      expiresAt,
      createdAt: now,
      updatedAt: now,
    });

    // Update lease e-signature status
    await ctx.db.patch(args.leaseId, {
      eSignatureStatus: "pending",
      documentUrl: args.documentUrl,
      updatedAt: now,
    });

    // Create notifications for signers
    for (const signer of args.signers) {
      // Find user by email to create notification
      const user = await ctx.db
        .query("users")
        .withIndex("by_email", (q) => q.eq("email", signer.email))
        .first();

      if (user) {
        await ctx.db.insert("notifications", {
          userId: user._id,
          title: "Lease Agreement Signature Required",
          message: `Please review and sign the lease agreement for ${property.name}, Unit ${unit.unitNumber}.`,
          type: "signature_request",
          isRead: false,
          metadata: {
            leaseId: args.leaseId,
            signatureRequestId,
            role: signer.role,
          },
          createdAt: now,
        });
      }
    }

    return signatureRequestId;
  },
});

// Update signature status (called by webhook or manual update)
export const updateSignatureStatus = mutation({
  args: {
    signatureRequestId: v.id("signatureRequests"),
    status: v.union(v.literal("pending"), v.literal("signed"), v.literal("declined"), v.literal("expired")),
    signerEmail: v.optional(v.string()),
    signerStatus: v.optional(v.union(v.literal("pending"), v.literal("signed"), v.literal("declined"))),
    signedAt: v.optional(v.number()),
    ipAddress: v.optional(v.string()),
    signedDocumentUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const signatureRequest = await ctx.db.get(args.signatureRequestId);
    if (!signatureRequest) {
      throw new Error("Signature request not found");
    }

    const now = Date.now();
    const updates: any = {
      status: args.status,
      updatedAt: now,
    };

    // Update signer status if provided
    if (args.signerEmail && args.signerStatus) {
      const updatedSigners = signatureRequest.signers.map(signer => {
        if (signer.email === args.signerEmail) {
          return {
            ...signer,
            status: args.signerStatus,
            signedAt: args.signedAt,
            ipAddress: args.ipAddress,
          };
        }
        return signer;
      });
      updates.signers = updatedSigners;
    }

    if (args.signedDocumentUrl) {
      updates.signedDocumentUrl = args.signedDocumentUrl;
    }

    // Update signature request
    await ctx.db.patch(args.signatureRequestId, updates);

    // Update lease status based on signature completion
    const lease = await ctx.db.get(signatureRequest.leaseId);
    if (lease) {
      let leaseESignatureStatus = lease.eSignatureStatus;
      let leaseStatus = lease.status;

      if (args.status === "signed") {
        leaseESignatureStatus = "signed";
        // Activate lease if it was pending and now signed
        if (lease.status === "pending") {
          leaseStatus = "active";
        }
      } else if (args.status === "declined" || args.status === "expired") {
        leaseESignatureStatus = "expired";
      }

      await ctx.db.patch(signatureRequest.leaseId, {
        eSignatureStatus: leaseESignatureStatus,
        status: leaseStatus,
        documentUrl: args.signedDocumentUrl || lease.documentUrl,
        updatedAt: now,
      });

      // Update unit status if lease is activated
      if (leaseStatus === "active" && lease.status !== "active") {
        await ctx.db.patch(lease.unitId, {
          status: "occupied",
          updatedAt: now,
        });
      }
    }

    // Create notifications based on status change
    if (args.status === "signed") {
      // Notify property manager of successful signature
      const property = await ctx.db.get(lease?.propertyId!);
      if (property?.managerId) {
        await ctx.db.insert("notifications", {
          userId: property.managerId,
          title: "Lease Agreement Signed",
          message: `The lease agreement for ${property.name} has been fully signed and is now active.`,
          type: "signature_completed",
          isRead: false,
          metadata: {
            leaseId: signatureRequest.leaseId,
            signatureRequestId: args.signatureRequestId,
          },
          createdAt: now,
        });
      }
    } else if (args.status === "declined") {
      // Notify relevant parties of declined signature
      const property = await ctx.db.get(lease?.propertyId!);
      if (property?.managerId) {
        await ctx.db.insert("notifications", {
          userId: property.managerId,
          title: "Lease Agreement Declined",
          message: `The lease agreement for ${property.name} has been declined by a signer.`,
          type: "signature_declined",
          isRead: false,
          metadata: {
            leaseId: signatureRequest.leaseId,
            signatureRequestId: args.signatureRequestId,
            declinedBy: args.signerEmail,
          },
          createdAt: now,
        });
      }
    }

    return args.signatureRequestId;
  },
});

// Get signature request details
export const getSignatureRequest = query({
  args: { id: v.id("signatureRequests") },
  handler: async (ctx, args) => {
    const signatureRequest = await ctx.db.get(args.id);
    if (!signatureRequest) {
      return null;
    }

    // Get related lease data
    const lease = await ctx.db.get(signatureRequest.leaseId);
    const property = lease ? await ctx.db.get(lease.propertyId) : null;
    const unit = lease ? await ctx.db.get(lease.unitId) : null;
    const tenant = lease ? await ctx.db.get(lease.tenantId) : null;

    return {
      signatureRequest,
      lease,
      property,
      unit,
      tenant,
    };
  },
});

// Get signature requests for a lease
export const getSignatureRequestsByLease = query({
  args: { leaseId: v.id("leases") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("signatureRequests")
      .withIndex("by_lease", (q) => q.eq("leaseId", args.leaseId))
      .collect();
  },
});

// Get pending signature requests for a user
export const getPendingSignatureRequests = query({
  args: { userEmail: v.string() },
  handler: async (ctx, args) => {
    const signatureRequests = await ctx.db
      .query("signatureRequests")
      .withIndex("by_status", (q) => q.eq("status", "pending"))
      .collect();

    // Filter requests where the user is a signer
    const userRequests = signatureRequests.filter(request =>
      request.signers.some(signer => 
        signer.email === args.userEmail && signer.status === "pending"
      )
    );

    // Get related data for each request
    const requestsWithData = await Promise.all(
      userRequests.map(async (request) => {
        const lease = await ctx.db.get(request.leaseId);
        const property = lease ? await ctx.db.get(lease.propertyId) : null;
        const unit = lease ? await ctx.db.get(lease.unitId) : null;

        return {
          signatureRequest: request,
          lease,
          property,
          unit,
        };
      })
    );

    return requestsWithData;
  },
});

// Resend signature request
export const resendSignatureRequest = mutation({
  args: {
    signatureRequestId: v.id("signatureRequests"),
    signerEmail: v.string(),
  },
  handler: async (ctx, args) => {
    const signatureRequest = await ctx.db.get(args.signatureRequestId);
    if (!signatureRequest) {
      throw new Error("Signature request not found");
    }

    // Verify signer exists in the request
    const signer = signatureRequest.signers.find(s => s.email === args.signerEmail);
    if (!signer) {
      throw new Error("Signer not found in signature request");
    }

    if (signer.status === "signed") {
      throw new Error("Cannot resend to a signer who has already signed");
    }

    // In production, this would call the e-signature service to resend
    console.log(`Resending signature request to ${args.signerEmail}`);

    // Create notification for the signer
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.signerEmail))
      .first();

    if (user) {
      const lease = await ctx.db.get(signatureRequest.leaseId);
      const property = lease ? await ctx.db.get(lease.propertyId) : null;
      const unit = lease ? await ctx.db.get(lease.unitId) : null;

      await ctx.db.insert("notifications", {
        userId: user._id,
        title: "Lease Agreement Signature Reminder",
        message: `Reminder: Please review and sign the lease agreement for ${property?.name}, Unit ${unit?.unitNumber}.`,
        type: "signature_reminder",
        isRead: false,
        metadata: {
          leaseId: signatureRequest.leaseId,
          signatureRequestId: args.signatureRequestId,
          role: signer.role,
        },
        createdAt: Date.now(),
      });
    }

    return args.signatureRequestId;
  },
});

// Cancel signature request
export const cancelSignatureRequest = mutation({
  args: {
    signatureRequestId: v.id("signatureRequests"),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const signatureRequest = await ctx.db.get(args.signatureRequestId);
    if (!signatureRequest) {
      throw new Error("Signature request not found");
    }

    if (signatureRequest.status === "signed") {
      throw new Error("Cannot cancel a completed signature request");
    }

    const now = Date.now();

    // Update signature request status
    await ctx.db.patch(args.signatureRequestId, {
      status: "expired",
      updatedAt: now,
    });

    // Update lease e-signature status
    await ctx.db.patch(signatureRequest.leaseId, {
      eSignatureStatus: "expired",
      updatedAt: now,
    });

    // Notify signers of cancellation
    for (const signer of signatureRequest.signers) {
      if (signer.status === "pending") {
        const user = await ctx.db
          .query("users")
          .withIndex("by_email", (q) => q.eq("email", signer.email))
          .first();

        if (user) {
          await ctx.db.insert("notifications", {
            userId: user._id,
            title: "Lease Agreement Signature Cancelled",
            message: `The signature request for the lease agreement has been cancelled. ${args.reason ? `Reason: ${args.reason}` : ''}`,
            type: "signature_cancelled",
            isRead: false,
            metadata: {
              leaseId: signatureRequest.leaseId,
              signatureRequestId: args.signatureRequestId,
              reason: args.reason,
            },
            createdAt: now,
          });
        }
      }
    }

    return args.signatureRequestId;
  },
});