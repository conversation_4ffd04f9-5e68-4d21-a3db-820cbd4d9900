import { query } from "./_generated/server";
import { v } from "convex/values";

// Get comprehensive financial dashboard data
export const getFinancialDashboard = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const startDate = args.startDate || (now - (365 * 24 * 60 * 60 * 1000)); // 1 year ago
    const endDate = args.endDate || now;

    // Get all invoices within date range
    const allInvoices = await ctx.db.query("invoices").collect();
    
    let filteredInvoices = allInvoices.filter(invoice => 
      invoice.createdAt >= startDate && invoice.createdAt <= endDate
    );

    // Filter by property if specified
    if (args.propertyId) {
      filteredInvoices = filteredInvoices.filter(invoice => 
        invoice.propertyId === args.propertyId
      );
    }

    // Get all payments within date range
    const allPayments = await ctx.db.query("payments").collect();
    const filteredPayments = allPayments.filter(payment => 
      payment.createdAt >= startDate && payment.createdAt <= endDate
    );

    // Get maintenance tickets for expense tracking
    const allTickets = await ctx.db.query("maintenanceTickets").collect();
    let filteredTickets = allTickets.filter(ticket => 
      ticket.createdAt >= startDate && ticket.createdAt <= endDate
    );

    if (args.propertyId) {
      filteredTickets = filteredTickets.filter(ticket => 
        ticket.propertyId === args.propertyId
      );
    }

    // Calculate revenue metrics
    const totalRevenue = filteredInvoices
      .filter(invoice => invoice.status === "paid")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    const rentRevenue = filteredInvoices
      .filter(invoice => invoice.type === "rent" && invoice.status === "paid")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    const otherRevenue = totalRevenue - rentRevenue;

    const pendingRevenue = filteredInvoices
      .filter(invoice => invoice.status === "pending")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    const overdueRevenue = filteredInvoices
      .filter(invoice => invoice.status === "overdue")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    // Calculate expenses from maintenance
    const maintenanceExpenses = filteredTickets
      .filter(ticket => ticket.actualCost && ticket.status === "completed")
      .reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0);

    // Calculate collection rate
    const totalInvoiced = filteredInvoices.reduce((sum, invoice) => sum + invoice.amount, 0);
    const collectionRate = totalInvoiced > 0 ? (totalRevenue / totalInvoiced) * 100 : 0;

    // Monthly breakdown
    const monthlyData = new Map();
    
    // Process invoices by month
    filteredInvoices.forEach(invoice => {
      const date = new Date(invoice.createdAt);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!monthlyData.has(monthKey)) {
        monthlyData.set(monthKey, {
          month: date.getMonth() + 1,
          year: date.getFullYear(),
          revenue: 0,
          expenses: 0,
          netIncome: 0,
          invoiced: 0,
          paid: 0,
          pending: 0,
          overdue: 0,
        });
      }
      
      const monthData = monthlyData.get(monthKey);
      monthData.invoiced += invoice.amount;
      
      if (invoice.status === "paid") {
        monthData.paid += invoice.amount;
        monthData.revenue += invoice.amount;
      } else if (invoice.status === "pending") {
        monthData.pending += invoice.amount;
      } else if (invoice.status === "overdue") {
        monthData.overdue += invoice.amount;
      }
    });

    // Process maintenance expenses by month
    filteredTickets.forEach(ticket => {
      if (ticket.actualCost && ticket.status === "completed") {
        const date = new Date(ticket.updatedAt);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
        if (monthlyData.has(monthKey)) {
          const monthData = monthlyData.get(monthKey);
          monthData.expenses += ticket.actualCost;
          monthData.netIncome = monthData.revenue - monthData.expenses;
        }
      }
    });

    // Convert to array and sort
    const monthlyBreakdown = Array.from(monthlyData.values())
      .sort((a, b) => a.year !== b.year ? a.year - b.year : a.month - b.month);

    // Calculate net income
    const netIncome = totalRevenue - maintenanceExpenses;
    const profitMargin = totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0;

    return {
      period: { startDate, endDate },
      summary: {
        totalRevenue,
        rentRevenue,
        otherRevenue,
        totalExpenses: maintenanceExpenses,
        netIncome,
        profitMargin,
        collectionRate,
        pendingRevenue,
        overdueRevenue,
      },
      monthlyBreakdown,
      invoiceMetrics: {
        totalInvoices: filteredInvoices.length,
        paidInvoices: filteredInvoices.filter(i => i.status === "paid").length,
        pendingInvoices: filteredInvoices.filter(i => i.status === "pending").length,
        overdueInvoices: filteredInvoices.filter(i => i.status === "overdue").length,
      },
      paymentMetrics: {
        totalPayments: filteredPayments.length,
        successfulPayments: filteredPayments.filter(p => p.status === "completed").length,
        failedPayments: filteredPayments.filter(p => p.status === "failed").length,
        mpesaPayments: filteredPayments.filter(p => p.method === "mpesa").length,
        stripePayments: filteredPayments.filter(p => p.method === "stripe").length,
      },
    };
  },
});

// Generate P&L Statement
export const generatePLStatement = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    startDate: v.number(),
    endDate: v.number(),
  },
  handler: async (ctx, args) => {
    const { startDate, endDate } = args;

    // Get all invoices within date range
    const allInvoices = await ctx.db.query("invoices").collect();
    let filteredInvoices = allInvoices.filter(invoice => 
      invoice.createdAt >= startDate && invoice.createdAt <= endDate
    );

    if (args.propertyId) {
      filteredInvoices = filteredInvoices.filter(invoice => 
        invoice.propertyId === args.propertyId
      );
    }

    // Get maintenance tickets for expenses
    const allTickets = await ctx.db.query("maintenanceTickets").collect();
    let filteredTickets = allTickets.filter(ticket => 
      ticket.createdAt >= startDate && ticket.createdAt <= endDate
    );

    if (args.propertyId) {
      filteredTickets = filteredTickets.filter(ticket => 
        ticket.propertyId === args.propertyId
      );
    }

    // Revenue breakdown
    const rentRevenue = filteredInvoices
      .filter(invoice => invoice.type === "rent" && invoice.status === "paid")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    const depositRevenue = filteredInvoices
      .filter(invoice => invoice.type === "deposit" && invoice.status === "paid")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    const maintenanceRevenue = filteredInvoices
      .filter(invoice => invoice.type === "maintenance" && invoice.status === "paid")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    const otherRevenue = filteredInvoices
      .filter(invoice => invoice.type === "other" && invoice.status === "paid")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    const totalRevenue = rentRevenue + depositRevenue + maintenanceRevenue + otherRevenue;

    // Expense breakdown by category
    const expensesByCategory = {
      plumbing: 0,
      electrical: 0,
      hvac: 0,
      appliance: 0,
      structural: 0,
      cleaning: 0,
      security: 0,
      other: 0,
    };

    filteredTickets
      .filter(ticket => ticket.actualCost && ticket.status === "completed")
      .forEach(ticket => {
        expensesByCategory[ticket.category] += ticket.actualCost || 0;
      });

    const totalExpenses = Object.values(expensesByCategory).reduce((sum, amount) => sum + amount, 0);

    // Calculate metrics
    const grossProfit = totalRevenue - totalExpenses;
    const grossMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;

    return {
      period: { startDate, endDate },
      propertyId: args.propertyId,
      revenue: {
        rent: rentRevenue,
        deposits: depositRevenue,
        maintenance: maintenanceRevenue,
        other: otherRevenue,
        total: totalRevenue,
      },
      expenses: {
        byCategory: expensesByCategory,
        total: totalExpenses,
      },
      profitLoss: {
        grossProfit,
        grossMargin,
        netIncome: grossProfit, // Simplified - could include other expenses
      },
      generatedAt: Date.now(),
    };
  },
});

// Get cash flow analysis
export const getCashFlowAnalysis = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    months: v.optional(v.number()), // Number of months to analyze (default 12)
  },
  handler: async (ctx, args) => {
    const months = args.months || 12;
    const now = Date.now();
    const startDate = now - (months * 30 * 24 * 60 * 60 * 1000); // Approximate months

    // Get all invoices and payments
    const allInvoices = await ctx.db.query("invoices").collect();
    const allPayments = await ctx.db.query("payments").collect();
    const allTickets = await ctx.db.query("maintenanceTickets").collect();

    let filteredInvoices = allInvoices.filter(invoice => 
      invoice.createdAt >= startDate
    );
    let filteredPayments = allPayments.filter(payment => 
      payment.createdAt >= startDate
    );
    let filteredTickets = allTickets.filter(ticket => 
      ticket.createdAt >= startDate
    );

    if (args.propertyId) {
      filteredInvoices = filteredInvoices.filter(invoice => 
        invoice.propertyId === args.propertyId
      );
      filteredTickets = filteredTickets.filter(ticket => 
        ticket.propertyId === args.propertyId
      );
    }

    // Create monthly cash flow data
    const cashFlowData: Array<{
      month: number;
      year: number;
      monthName: string;
      cashIn: number;
      cashOut: number;
      netCashFlow: number;
      invoicesGenerated: number;
      paymentsReceived: number;
      maintenanceExpenses: number;
    }> = [];
    
    for (let i = 0; i < months; i++) {
      const monthStart = new Date(now - ((months - i - 1) * 30 * 24 * 60 * 60 * 1000));
      const monthEnd = new Date(now - ((months - i) * 30 * 24 * 60 * 60 * 1000));
      
      const monthInvoices = filteredInvoices.filter(invoice => 
        invoice.createdAt >= monthStart.getTime() && invoice.createdAt < monthEnd.getTime()
      );
      
      const monthPayments = filteredPayments.filter(payment => 
        payment.createdAt >= monthStart.getTime() && payment.createdAt < monthEnd.getTime()
      );
      
      const monthTickets = filteredTickets.filter(ticket => 
        ticket.updatedAt >= monthStart.getTime() && ticket.updatedAt < monthEnd.getTime()
      );

      const cashIn = monthPayments
        .filter(payment => payment.status === "completed")
        .reduce((sum, payment) => sum + payment.amount, 0);

      const cashOut = monthTickets
        .filter(ticket => ticket.actualCost && ticket.status === "completed")
        .reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0);

      const netCashFlow = cashIn - cashOut;

      cashFlowData.push({
        month: monthStart.getMonth() + 1,
        year: monthStart.getFullYear(),
        monthName: monthStart.toLocaleDateString('en-US', { month: 'long' }),
        cashIn,
        cashOut,
        netCashFlow,
        invoicesGenerated: monthInvoices.length,
        paymentsReceived: monthPayments.filter(p => p.status === "completed").length,
        maintenanceExpenses: monthTickets.filter(t => t.actualCost).length,
      });
    }

    // Calculate forecasting based on trends
    const recentMonths = cashFlowData.slice(-3); // Last 3 months for trend
    const avgCashIn = recentMonths.reduce((sum, month) => sum + month.cashIn, 0) / recentMonths.length;
    const avgCashOut = recentMonths.reduce((sum, month) => sum + month.cashOut, 0) / recentMonths.length;
    const avgNetFlow = avgCashIn - avgCashOut;

    // Generate next 3 months forecast
    const forecast: Array<{
      month: number;
      year: number;
      monthName: string;
      projectedCashIn: number;
      projectedCashOut: number;
      projectedNetFlow: number;
    }> = [];
    for (let i = 1; i <= 3; i++) {
      const futureDate = new Date(now + (i * 30 * 24 * 60 * 60 * 1000));
      forecast.push({
        month: futureDate.getMonth() + 1,
        year: futureDate.getFullYear(),
        monthName: futureDate.toLocaleDateString('en-US', { month: 'long' }),
        projectedCashIn: avgCashIn,
        projectedCashOut: avgCashOut,
        projectedNetFlow: avgNetFlow,
      });
    }

    return {
      propertyId: args.propertyId,
      period: { startDate, endDate: now },
      historicalData: cashFlowData,
      forecast,
      summary: {
        totalCashIn: cashFlowData.reduce((sum, month) => sum + month.cashIn, 0),
        totalCashOut: cashFlowData.reduce((sum, month) => sum + month.cashOut, 0),
        totalNetFlow: cashFlowData.reduce((sum, month) => sum + month.netCashFlow, 0),
        averageMonthlyInflow: avgCashIn,
        averageMonthlyOutflow: avgCashOut,
        averageNetFlow: avgNetFlow,
      },
    };
  },
});

// Get comparative analysis between properties
export const getComparativeAnalysis = query({
  args: {
    propertyIds: v.array(v.id("properties")),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const startDate = args.startDate || (now - (365 * 24 * 60 * 60 * 1000));
    const endDate = args.endDate || now;

    const comparativeData: Array<{
      propertyId: string;
      propertyName: string;
      propertyType: string;
      metrics: {
        totalUnits: number;
        occupiedUnits: number;
        occupancyRate: number;
        totalRevenue: number;
        rentRevenue: number;
        maintenanceExpenses: number;
        netIncome: number;
        profitMargin: number;
        collectionRate: number;
        revenuePerUnit: number;
        expensesPerUnit: number;
        netIncomePerUnit: number;
      };
      invoiceStats: {
        totalInvoices: number;
        paidInvoices: number;
        pendingInvoices: number;
        overdueInvoices: number;
      };
      maintenanceStats: {
        totalTickets: number;
        completedTickets: number;
        averageCost: number;
      };
    }> = [];

    for (const propertyId of args.propertyIds) {
      // Get property details
      const property = await ctx.db.get(propertyId);
      if (!property) continue;

      // Get units for occupancy calculation
      const units = await ctx.db
        .query("units")
        .withIndex("by_property", (q) => q.eq("propertyId", propertyId))
        .collect();

      const occupiedUnits = units.filter(unit => unit.status === "occupied").length;
      const occupancyRate = units.length > 0 ? (occupiedUnits / units.length) * 100 : 0;

      // Get financial data
      const invoices = await ctx.db
        .query("invoices")
        .filter((q) => q.eq(q.field("propertyId"), propertyId))
        .collect();

      const filteredInvoices = invoices.filter(invoice => 
        invoice.createdAt >= startDate && invoice.createdAt <= endDate
      );

      const totalRevenue = filteredInvoices
        .filter(invoice => invoice.status === "paid")
        .reduce((sum, invoice) => sum + invoice.amount, 0);

      const rentRevenue = filteredInvoices
        .filter(invoice => invoice.type === "rent" && invoice.status === "paid")
        .reduce((sum, invoice) => sum + invoice.amount, 0);

      // Get maintenance expenses
      const tickets = await ctx.db
        .query("maintenanceTickets")
        .withIndex("by_property", (q) => q.eq("propertyId", propertyId))
        .collect();

      const filteredTickets = tickets.filter(ticket => 
        ticket.createdAt >= startDate && ticket.createdAt <= endDate
      );

      const maintenanceExpenses = filteredTickets
        .filter(ticket => ticket.actualCost && ticket.status === "completed")
        .reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0);

      const netIncome = totalRevenue - maintenanceExpenses;
      const profitMargin = totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0;

      // Calculate per unit metrics
      const revenuePerUnit = units.length > 0 ? totalRevenue / units.length : 0;
      const expensesPerUnit = units.length > 0 ? maintenanceExpenses / units.length : 0;

      // Collection rate
      const totalInvoiced = filteredInvoices.reduce((sum, invoice) => sum + invoice.amount, 0);
      const collectionRate = totalInvoiced > 0 ? (totalRevenue / totalInvoiced) * 100 : 0;

      comparativeData.push({
        propertyId,
        propertyName: property.name,
        propertyType: property.type,
        metrics: {
          totalUnits: units.length,
          occupiedUnits,
          occupancyRate,
          totalRevenue,
          rentRevenue,
          maintenanceExpenses,
          netIncome,
          profitMargin,
          collectionRate,
          revenuePerUnit,
          expensesPerUnit,
          netIncomePerUnit: revenuePerUnit - expensesPerUnit,
        },
        invoiceStats: {
          totalInvoices: filteredInvoices.length,
          paidInvoices: filteredInvoices.filter(i => i.status === "paid").length,
          pendingInvoices: filteredInvoices.filter(i => i.status === "pending").length,
          overdueInvoices: filteredInvoices.filter(i => i.status === "overdue").length,
        },
        maintenanceStats: {
          totalTickets: filteredTickets.length,
          completedTickets: filteredTickets.filter(t => t.status === "completed").length,
          averageCost: filteredTickets.length > 0 ? maintenanceExpenses / filteredTickets.length : 0,
        },
      });
    }

    // Calculate benchmarks
    const totalProperties = comparativeData.length;
    if (totalProperties === 0) {
      return { properties: [], benchmarks: null, period: { startDate, endDate } };
    }

    const benchmarks = {
      averageOccupancyRate: comparativeData.reduce((sum, p) => sum + p.metrics.occupancyRate, 0) / totalProperties,
      averageProfitMargin: comparativeData.reduce((sum, p) => sum + p.metrics.profitMargin, 0) / totalProperties,
      averageCollectionRate: comparativeData.reduce((sum, p) => sum + p.metrics.collectionRate, 0) / totalProperties,
      averageRevenuePerUnit: comparativeData.reduce((sum, p) => sum + p.metrics.revenuePerUnit, 0) / totalProperties,
      averageExpensesPerUnit: comparativeData.reduce((sum, p) => sum + p.metrics.expensesPerUnit, 0) / totalProperties,
      topPerformer: {
        byRevenue: comparativeData.reduce((max, p) => p.metrics.totalRevenue > max.metrics.totalRevenue ? p : max),
        byProfitMargin: comparativeData.reduce((max, p) => p.metrics.profitMargin > max.metrics.profitMargin ? p : max),
        byOccupancy: comparativeData.reduce((max, p) => p.metrics.occupancyRate > max.metrics.occupancyRate ? p : max),
      },
    };

    return {
      properties: comparativeData,
      benchmarks,
      period: { startDate, endDate },
      generatedAt: Date.now(),
    };
  },
});

// Get occupancy and performance analytics
export const getOccupancyAnalytics = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    months: v.optional(v.number()), // Number of months for trend analysis
  },
  handler: async (ctx, args) => {
    const months = args.months || 12;
    const now = Date.now();
    
    // Get all properties or specific property
    let properties;
    if (args.propertyId) {
      const property = await ctx.db.get(args.propertyId);
      properties = property ? [property] : [];
    } else {
      properties = await ctx.db.query("properties").collect();
    }

    const occupancyData: Array<{
      propertyId: string;
      propertyName: string;
      propertyType: string;
      currentOccupancy: {
        totalUnits: number;
        occupiedUnits: number;
        vacantUnits: number;
        maintenanceUnits: number;
        occupancyRate: number;
      };
      monthlyTrends: Array<{
        month: number;
        year: number;
        monthName: string;
        totalUnits: number;
        occupiedUnits: number;
        occupancyRate: number;
        newLeases: number;
        terminatedLeases: number;
        renewedLeases: number;
      }>;
      heatmapData: Array<{
        unitId: string;
        unitNumber: string;
        status: string;
        occupancyDays: number;
        vacancyDays: number;
        lastOccupied: number | null;
        currentTenant: string | null;
      }>;
    }> = [];

    for (const property of properties) {
      // Get all units for this property
      const units = await ctx.db
        .query("units")
        .withIndex("by_property", (q) => q.eq("propertyId", property._id))
        .collect();

      // Current occupancy snapshot
      const occupiedUnits = units.filter(unit => unit.status === "occupied").length;
      const vacantUnits = units.filter(unit => unit.status === "vacant").length;
      const maintenanceUnits = units.filter(unit => unit.status === "maintenance").length;
      const occupancyRate = units.length > 0 ? (occupiedUnits / units.length) * 100 : 0;

      // Get all leases for this property
      const allLeases = await ctx.db
        .query("leases")
        .withIndex("by_property", (q) => q.eq("propertyId", property._id))
        .collect();

      // Generate monthly trends
      const monthlyTrends: Array<{
        month: number;
        year: number;
        monthName: string;
        totalUnits: number;
        occupiedUnits: number;
        occupancyRate: number;
        newLeases: number;
        terminatedLeases: number;
        renewedLeases: number;
      }> = [];

      for (let i = 0; i < months; i++) {
        const monthStart = new Date(now - ((months - i - 1) * 30 * 24 * 60 * 60 * 1000));
        const monthEnd = new Date(now - ((months - i) * 30 * 24 * 60 * 60 * 1000));
        
        // Count active leases during this month
        const activeLeases = allLeases.filter(lease => 
          lease.startDate <= monthEnd.getTime() && 
          (lease.endDate >= monthStart.getTime() || lease.status === "active")
        );

        // Count new leases started in this month
        const newLeases = allLeases.filter(lease => 
          lease.startDate >= monthStart.getTime() && lease.startDate < monthEnd.getTime()
        );

        // Count terminated leases in this month
        const terminatedLeases = allLeases.filter(lease => 
          lease.endDate >= monthStart.getTime() && 
          lease.endDate < monthEnd.getTime() && 
          lease.status === "terminated"
        );

        // Count renewed leases (simplified - leases that were extended)
        const renewedLeases = allLeases.filter(lease => 
          lease.updatedAt >= monthStart.getTime() && 
          lease.updatedAt < monthEnd.getTime() && 
          lease.status === "active" &&
          lease.endDate > lease.startDate + (365 * 24 * 60 * 60 * 1000) // Extended beyond 1 year
        );

        const monthOccupancyRate = units.length > 0 ? (activeLeases.length / units.length) * 100 : 0;

        monthlyTrends.push({
          month: monthStart.getMonth() + 1,
          year: monthStart.getFullYear(),
          monthName: monthStart.toLocaleDateString('en-US', { month: 'long' }),
          totalUnits: units.length,
          occupiedUnits: activeLeases.length,
          occupancyRate: monthOccupancyRate,
          newLeases: newLeases.length,
          terminatedLeases: terminatedLeases.length,
          renewedLeases: renewedLeases.length,
        });
      }

      // Generate heatmap data for units
      const heatmapData: Array<{
        unitId: string;
        unitNumber: string;
        status: string;
        occupancyDays: number;
        vacancyDays: number;
        lastOccupied: number | null;
        currentTenant: string | null;
      }> = [];

      for (const unit of units) {
        // Get current lease for this unit
        const currentLease = allLeases.find(lease => 
          lease.unitId === unit._id && lease.status === "active"
        );

        // Calculate occupancy days in the last year
        const yearAgo = now - (365 * 24 * 60 * 60 * 1000);
        const unitLeases = allLeases.filter(lease => 
          lease.unitId === unit._id && 
          (lease.endDate >= yearAgo || lease.status === "active")
        );

        let occupancyDays = 0;
        let lastOccupied: number | null = null;

        for (const lease of unitLeases) {
          const leaseStart = Math.max(lease.startDate, yearAgo);
          const leaseEnd = lease.status === "active" ? now : Math.min(lease.endDate, now);
          
          if (leaseEnd > leaseStart) {
            occupancyDays += Math.floor((leaseEnd - leaseStart) / (24 * 60 * 60 * 1000));
            lastOccupied = Math.max(lastOccupied || 0, leaseEnd);
          }
        }

        const vacancyDays = 365 - occupancyDays;

        heatmapData.push({
          unitId: unit._id,
          unitNumber: unit.unitNumber,
          status: unit.status,
          occupancyDays,
          vacancyDays,
          lastOccupied,
          currentTenant: currentLease ? currentLease.tenantId : null,
        });
      }

      occupancyData.push({
        propertyId: property._id,
        propertyName: property.name,
        propertyType: property.type,
        currentOccupancy: {
          totalUnits: units.length,
          occupiedUnits,
          vacantUnits,
          maintenanceUnits,
          occupancyRate,
        },
        monthlyTrends,
        heatmapData,
      });
    }

    return {
      properties: occupancyData,
      generatedAt: now,
    };
  },
});

// Get tenant retention and turnover analytics
export const getTenantRetentionAnalytics = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    months: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const months = args.months || 12;
    const now = Date.now();
    const startDate = now - (months * 30 * 24 * 60 * 60 * 1000);

    // Get all leases within the time period
    const allLeases = await ctx.db.query("leases").collect();
    let filteredLeases = allLeases.filter(lease => 
      lease.startDate >= startDate || lease.endDate >= startDate || lease.status === "active"
    );

    if (args.propertyId) {
      filteredLeases = filteredLeases.filter(lease => 
        lease.propertyId === args.propertyId
      );
    }

    // Calculate retention metrics
    const totalLeases = filteredLeases.length;
    const activeLeases = filteredLeases.filter(lease => lease.status === "active").length;
    const terminatedLeases = filteredLeases.filter(lease => lease.status === "terminated").length;
    const expiredLeases = filteredLeases.filter(lease => lease.status === "expired").length;

    // Calculate average lease duration
    const completedLeases = filteredLeases.filter(lease => 
      lease.status === "terminated" || lease.status === "expired"
    );
    
    const averageLeaseDuration = completedLeases.length > 0 
      ? completedLeases.reduce((sum, lease) => {
          const duration = lease.endDate - lease.startDate;
          return sum + duration;
        }, 0) / completedLeases.length / (24 * 60 * 60 * 1000) // Convert to days
      : 0;

    // Calculate turnover rate
    const turnoverRate = totalLeases > 0 ? ((terminatedLeases + expiredLeases) / totalLeases) * 100 : 0;
    const retentionRate = 100 - turnoverRate;

    // Monthly turnover trends
    const monthlyTurnover: Array<{
      month: number;
      year: number;
      monthName: string;
      newLeases: number;
      terminatedLeases: number;
      renewedLeases: number;
      turnoverRate: number;
      retentionRate: number;
    }> = [];

    for (let i = 0; i < months; i++) {
      const monthStart = new Date(now - ((months - i - 1) * 30 * 24 * 60 * 60 * 1000));
      const monthEnd = new Date(now - ((months - i) * 30 * 24 * 60 * 60 * 1000));
      
      const monthNewLeases = filteredLeases.filter(lease => 
        lease.startDate >= monthStart.getTime() && lease.startDate < monthEnd.getTime()
      ).length;

      const monthTerminatedLeases = filteredLeases.filter(lease => 
        lease.endDate >= monthStart.getTime() && 
        lease.endDate < monthEnd.getTime() && 
        (lease.status === "terminated" || lease.status === "expired")
      ).length;

      // Simplified renewal calculation
      const monthRenewedLeases = filteredLeases.filter(lease => 
        lease.updatedAt >= monthStart.getTime() && 
        lease.updatedAt < monthEnd.getTime() && 
        lease.status === "active" &&
        lease.endDate > lease.startDate + (365 * 24 * 60 * 60 * 1000)
      ).length;

      const monthTotalLeases = monthNewLeases + monthTerminatedLeases + monthRenewedLeases;
      const monthTurnoverRate = monthTotalLeases > 0 ? (monthTerminatedLeases / monthTotalLeases) * 100 : 0;

      monthlyTurnover.push({
        month: monthStart.getMonth() + 1,
        year: monthStart.getFullYear(),
        monthName: monthStart.toLocaleDateString('en-US', { month: 'long' }),
        newLeases: monthNewLeases,
        terminatedLeases: monthTerminatedLeases,
        renewedLeases: monthRenewedLeases,
        turnoverRate: monthTurnoverRate,
        retentionRate: 100 - monthTurnoverRate,
      });
    }

    // Tenant lifecycle analysis
    const tenantLifecycle: Array<{
      tenantId: string;
      totalLeases: number;
      totalDuration: number; // in days
      averageLeaseDuration: number;
      isActive: boolean;
      lastLeaseEnd: number | null;
      renewalCount: number;
    }> = [];

    // Group leases by tenant
    const leasesByTenant = new Map<string, typeof filteredLeases>();
    filteredLeases.forEach(lease => {
      if (!leasesByTenant.has(lease.tenantId)) {
        leasesByTenant.set(lease.tenantId, []);
      }
      leasesByTenant.get(lease.tenantId)!.push(lease);
    });

    leasesByTenant.forEach((tenantLeases, tenantId) => {
      const totalDuration = tenantLeases.reduce((sum, lease) => {
        const endDate = lease.status === "active" ? now : lease.endDate;
        return sum + (endDate - lease.startDate);
      }, 0) / (24 * 60 * 60 * 1000); // Convert to days

      const isActive = tenantLeases.some(lease => lease.status === "active");
      const lastLeaseEnd = Math.max(...tenantLeases.map(lease => 
        lease.status === "active" ? now : lease.endDate
      ));

      // Count renewals (simplified - multiple leases for same tenant)
      const renewalCount = Math.max(0, tenantLeases.length - 1);

      tenantLifecycle.push({
        tenantId,
        totalLeases: tenantLeases.length,
        totalDuration,
        averageLeaseDuration: totalDuration / tenantLeases.length,
        isActive,
        lastLeaseEnd,
        renewalCount,
      });
    });

    return {
      summary: {
        totalLeases,
        activeLeases,
        terminatedLeases,
        expiredLeases,
        retentionRate,
        turnoverRate,
        averageLeaseDuration,
        totalTenants: tenantLifecycle.length,
        activeTenants: tenantLifecycle.filter(t => t.isActive).length,
      },
      monthlyTrends: monthlyTurnover,
      tenantLifecycle: tenantLifecycle.sort((a, b) => b.totalDuration - a.totalDuration),
      period: { startDate, endDate: now },
      generatedAt: now,
    };
  },
});

// Get rent optimization recommendations
export const getRentOptimizationRecommendations = query({
  args: {
    propertyId: v.optional(v.id("properties")),
  },
  handler: async (ctx, args) => {
    // Get all properties or specific property
    let properties;
    if (args.propertyId) {
      const property = await ctx.db.get(args.propertyId);
      properties = property ? [property] : [];
    } else {
      properties = await ctx.db.query("properties").collect();
    }

    const recommendations: Array<{
      propertyId: string;
      propertyName: string;
      unitAnalysis: Array<{
        unitId: string;
        unitNumber: string;
        currentRent: number;
        marketRate: number;
        recommendedRent: number;
        potentialIncrease: number;
        potentialIncreasePercent: number;
        reasoning: string;
        riskLevel: "low" | "medium" | "high";
        occupancyHistory: {
          daysOccupied: number;
          daysVacant: number;
          occupancyRate: number;
        };
      }>;
      propertyRecommendations: {
        averageCurrentRent: number;
        averageMarketRate: number;
        totalPotentialIncrease: number;
        highPerformingUnits: number;
        underperformingUnits: number;
        vacancyRisk: number;
      };
    }> = [];

    for (const property of properties) {
      // Get units and their current leases
      const units = await ctx.db
        .query("units")
        .withIndex("by_property", (q) => q.eq("propertyId", property._id))
        .collect();

      const allLeases = await ctx.db
        .query("leases")
        .withIndex("by_property", (q) => q.eq("propertyId", property._id))
        .collect();

      const unitAnalysis: Array<{
        unitId: string;
        unitNumber: string;
        currentRent: number;
        marketRate: number;
        recommendedRent: number;
        potentialIncrease: number;
        potentialIncreasePercent: number;
        reasoning: string;
        riskLevel: "low" | "medium" | "high";
        occupancyHistory: {
          daysOccupied: number;
          daysVacant: number;
          occupancyRate: number;
        };
      }> = [];

      // Calculate market rates based on similar units
      const unitsByType = new Map<string, typeof units>();
      units.forEach(unit => {
        if (!unitsByType.has(unit.type)) {
          unitsByType.set(unit.type, []);
        }
        unitsByType.get(unit.type)!.push(unit);
      });

      for (const unit of units) {
        // Get current lease
        const currentLease = allLeases.find(lease => 
          lease.unitId === unit._id && lease.status === "active"
        );

        const currentRent = currentLease ? currentLease.monthlyRent : unit.rent;

        // Calculate market rate based on similar units
        const similarUnits = unitsByType.get(unit.type) || [];
        const similarRents = similarUnits
          .map(u => {
            const lease = allLeases.find(l => l.unitId === u._id && l.status === "active");
            return lease ? lease.monthlyRent : u.rent;
          })
          .filter(rent => rent > 0);

        const marketRate = similarRents.length > 0 
          ? similarRents.reduce((sum, rent) => sum + rent, 0) / similarRents.length
          : currentRent;

        // Calculate occupancy history (last 365 days)
        const yearAgo = Date.now() - (365 * 24 * 60 * 60 * 1000);
        const unitLeases = allLeases.filter(lease => 
          lease.unitId === unit._id && 
          (lease.endDate >= yearAgo || lease.status === "active")
        );

        let daysOccupied = 0;
        for (const lease of unitLeases) {
          const leaseStart = Math.max(lease.startDate, yearAgo);
          const leaseEnd = lease.status === "active" ? Date.now() : Math.min(lease.endDate, Date.now());
          
          if (leaseEnd > leaseStart) {
            daysOccupied += Math.floor((leaseEnd - leaseStart) / (24 * 60 * 60 * 1000));
          }
        }

        const daysVacant = 365 - daysOccupied;
        const occupancyRate = (daysOccupied / 365) * 100;

        // Generate recommendation
        let recommendedRent = currentRent;
        let reasoning = "Maintain current rent";
        let riskLevel: "low" | "medium" | "high" = "low";

        if (occupancyRate >= 90) {
          // High occupancy - can increase rent
          if (currentRent < marketRate * 0.95) {
            recommendedRent = Math.min(currentRent * 1.1, marketRate);
            reasoning = "High occupancy rate allows for rent increase";
            riskLevel = "low";
          } else if (currentRent < marketRate) {
            recommendedRent = marketRate;
            reasoning = "Align with market rate";
            riskLevel = "medium";
          }
        } else if (occupancyRate >= 70) {
          // Moderate occupancy
          if (currentRent > marketRate * 1.05) {
            recommendedRent = marketRate;
            reasoning = "Reduce rent to improve occupancy";
            riskLevel = "medium";
          }
        } else {
          // Low occupancy - consider reducing rent
          recommendedRent = Math.max(currentRent * 0.9, marketRate * 0.9);
          reasoning = "Low occupancy suggests rent reduction needed";
          riskLevel = "high";
        }

        const potentialIncrease = recommendedRent - currentRent;
        const potentialIncreasePercent = currentRent > 0 ? (potentialIncrease / currentRent) * 100 : 0;

        unitAnalysis.push({
          unitId: unit._id,
          unitNumber: unit.unitNumber,
          currentRent,
          marketRate,
          recommendedRent,
          potentialIncrease,
          potentialIncreasePercent,
          reasoning,
          riskLevel,
          occupancyHistory: {
            daysOccupied,
            daysVacant,
            occupancyRate,
          },
        });
      }

      // Calculate property-level recommendations
      const averageCurrentRent = unitAnalysis.length > 0 
        ? unitAnalysis.reduce((sum, unit) => sum + unit.currentRent, 0) / unitAnalysis.length
        : 0;

      const averageMarketRate = unitAnalysis.length > 0 
        ? unitAnalysis.reduce((sum, unit) => sum + unit.marketRate, 0) / unitAnalysis.length
        : 0;

      const totalPotentialIncrease = unitAnalysis.reduce((sum, unit) => sum + unit.potentialIncrease, 0);

      const highPerformingUnits = unitAnalysis.filter(unit => 
        unit.occupancyHistory.occupancyRate >= 90
      ).length;

      const underperformingUnits = unitAnalysis.filter(unit => 
        unit.occupancyHistory.occupancyRate < 70
      ).length;

      const vacancyRisk = unitAnalysis.length > 0 
        ? (underperformingUnits / unitAnalysis.length) * 100
        : 0;

      recommendations.push({
        propertyId: property._id,
        propertyName: property.name,
        unitAnalysis,
        propertyRecommendations: {
          averageCurrentRent,
          averageMarketRate,
          totalPotentialIncrease,
          highPerformingUnits,
          underperformingUnits,
          vacancyRisk,
        },
      });
    }

    return {
      recommendations,
      generatedAt: Date.now(),
    };
  },
});

// Get market comparison and benchmarking
export const getMarketBenchmarking = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    includeExternal: v.optional(v.boolean()), // Future: include external market data
  },
  handler: async (ctx, args) => {
    // Get all properties for internal benchmarking
    const allProperties = await ctx.db.query("properties").collect();
    
    let targetProperties;
    if (args.propertyId) {
      const property = await ctx.db.get(args.propertyId);
      targetProperties = property ? [property] : [];
    } else {
      targetProperties = allProperties;
    }

    const benchmarkData: Array<{
      propertyId: string;
      propertyName: string;
      propertyType: string;
      metrics: {
        occupancyRate: number;
        averageRent: number;
        revenuePerUnit: number;
        maintenanceCostPerUnit: number;
        profitMargin: number;
        tenantRetentionRate: number;
        averageLeaseDuration: number;
      };
      benchmarkComparison: {
        occupancyRanking: number;
        rentRanking: number;
        profitabilityRanking: number;
        overallScore: number;
        percentile: number;
      };
      recommendations: string[];
    }> = [];

    // Calculate metrics for all properties first for benchmarking
    const allPropertyMetrics: Array<{
      propertyId: string;
      propertyType: string;
      occupancyRate: number;
      averageRent: number;
      revenuePerUnit: number;
      maintenanceCostPerUnit: number;
      profitMargin: number;
      tenantRetentionRate: number;
      averageLeaseDuration: number;
    }> = [];

    for (const property of allProperties) {
      // Get units and leases
      const units = await ctx.db
        .query("units")
        .withIndex("by_property", (q) => q.eq("propertyId", property._id))
        .collect();

      const leases = await ctx.db
        .query("leases")
        .withIndex("by_property", (q) => q.eq("propertyId", property._id))
        .collect();

      const invoices = await ctx.db
        .query("invoices")
        .filter((q) => q.eq(q.field("propertyId"), property._id))
        .collect();

      const tickets = await ctx.db
        .query("maintenanceTickets")
        .withIndex("by_property", (q) => q.eq("propertyId", property._id))
        .collect();

      // Calculate metrics
      const occupiedUnits = units.filter(unit => unit.status === "occupied").length;
      const occupancyRate = units.length > 0 ? (occupiedUnits / units.length) * 100 : 0;

      const activeLeases = leases.filter(lease => lease.status === "active");
      const averageRent = activeLeases.length > 0 
        ? activeLeases.reduce((sum, lease) => sum + lease.monthlyRent, 0) / activeLeases.length
        : 0;

      // Revenue per unit (last 12 months)
      const yearAgo = Date.now() - (365 * 24 * 60 * 60 * 1000);
      const recentInvoices = invoices.filter(invoice => 
        invoice.createdAt >= yearAgo && invoice.status === "paid"
      );
      const totalRevenue = recentInvoices.reduce((sum, invoice) => sum + invoice.amount, 0);
      const revenuePerUnit = units.length > 0 ? totalRevenue / units.length : 0;

      // Maintenance cost per unit
      const recentTickets = tickets.filter(ticket => 
        ticket.createdAt >= yearAgo && ticket.actualCost && ticket.status === "completed"
      );
      const totalMaintenanceCost = recentTickets.reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0);
      const maintenanceCostPerUnit = units.length > 0 ? totalMaintenanceCost / units.length : 0;

      // Profit margin
      const profitMargin = totalRevenue > 0 ? ((totalRevenue - totalMaintenanceCost) / totalRevenue) * 100 : 0;

      // Tenant retention rate (simplified)
      const completedLeases = leases.filter(lease => 
        lease.status === "terminated" || lease.status === "expired"
      );
      const renewedLeases = leases.filter(lease => 
        lease.status === "active" && 
        lease.endDate > lease.startDate + (365 * 24 * 60 * 60 * 1000)
      );
      const tenantRetentionRate = completedLeases.length > 0 
        ? (renewedLeases.length / (completedLeases.length + renewedLeases.length)) * 100
        : 0;

      // Average lease duration
      const averageLeaseDuration = completedLeases.length > 0 
        ? completedLeases.reduce((sum, lease) => {
            return sum + (lease.endDate - lease.startDate);
          }, 0) / completedLeases.length / (24 * 60 * 60 * 1000) // Convert to days
        : 0;

      allPropertyMetrics.push({
        propertyId: property._id,
        propertyType: property.type,
        occupancyRate,
        averageRent,
        revenuePerUnit,
        maintenanceCostPerUnit,
        profitMargin,
        tenantRetentionRate,
        averageLeaseDuration,
      });
    }

    // Generate benchmark comparisons for target properties
    for (const property of targetProperties) {
      const propertyMetrics = allPropertyMetrics.find(m => m.propertyId === property._id);
      if (!propertyMetrics) continue;

      // Filter similar properties for comparison
      const similarProperties = allPropertyMetrics.filter(m => 
        m.propertyType === propertyMetrics.propertyType && m.propertyId !== property._id
      );

      // Calculate rankings
      const occupancyRanking = allPropertyMetrics
        .sort((a, b) => b.occupancyRate - a.occupancyRate)
        .findIndex(m => m.propertyId === property._id) + 1;

      const rentRanking = allPropertyMetrics
        .sort((a, b) => b.averageRent - a.averageRent)
        .findIndex(m => m.propertyId === property._id) + 1;

      const profitabilityRanking = allPropertyMetrics
        .sort((a, b) => b.profitMargin - a.profitMargin)
        .findIndex(m => m.propertyId === property._id) + 1;

      // Calculate overall score (weighted average of key metrics)
      const occupancyScore = (propertyMetrics.occupancyRate / 100) * 30;
      const profitScore = Math.min(propertyMetrics.profitMargin / 30, 1) * 25;
      const retentionScore = (propertyMetrics.tenantRetentionRate / 100) * 25;
      const revenueScore = Math.min(propertyMetrics.revenuePerUnit / 100000, 1) * 20; // Normalize to 100k
      
      const overallScore = occupancyScore + profitScore + retentionScore + revenueScore;
      const percentile = ((allPropertyMetrics.length - Math.max(occupancyRanking, rentRanking, profitabilityRanking) + 1) / allPropertyMetrics.length) * 100;

      // Generate recommendations
      const recommendations: string[] = [];
      
      if (propertyMetrics.occupancyRate < 85) {
        recommendations.push("Focus on improving occupancy rate through marketing and competitive pricing");
      }
      
      if (propertyMetrics.profitMargin < 20) {
        recommendations.push("Review maintenance costs and consider rent optimization");
      }
      
      if (propertyMetrics.tenantRetentionRate < 70) {
        recommendations.push("Implement tenant retention programs and improve tenant satisfaction");
      }
      
      if (similarProperties.length > 0) {
        const avgSimilarRent = similarProperties.reduce((sum, p) => sum + p.averageRent, 0) / similarProperties.length;
        if (propertyMetrics.averageRent < avgSimilarRent * 0.9) {
          recommendations.push("Consider rent increases to align with similar properties");
        } else if (propertyMetrics.averageRent > avgSimilarRent * 1.1) {
          recommendations.push("Review pricing strategy as rents are above market average");
        }
      }

      benchmarkData.push({
        propertyId: property._id,
        propertyName: property.name,
        propertyType: property.type,
        metrics: {
          occupancyRate: propertyMetrics.occupancyRate,
          averageRent: propertyMetrics.averageRent,
          revenuePerUnit: propertyMetrics.revenuePerUnit,
          maintenanceCostPerUnit: propertyMetrics.maintenanceCostPerUnit,
          profitMargin: propertyMetrics.profitMargin,
          tenantRetentionRate: propertyMetrics.tenantRetentionRate,
          averageLeaseDuration: propertyMetrics.averageLeaseDuration,
        },
        benchmarkComparison: {
          occupancyRanking,
          rentRanking,
          profitabilityRanking,
          overallScore,
          percentile,
        },
        recommendations,
      });
    }

    // Calculate market averages
    const marketAverages = {
      occupancyRate: allPropertyMetrics.reduce((sum, p) => sum + p.occupancyRate, 0) / allPropertyMetrics.length,
      averageRent: allPropertyMetrics.reduce((sum, p) => sum + p.averageRent, 0) / allPropertyMetrics.length,
      revenuePerUnit: allPropertyMetrics.reduce((sum, p) => sum + p.revenuePerUnit, 0) / allPropertyMetrics.length,
      maintenanceCostPerUnit: allPropertyMetrics.reduce((sum, p) => sum + p.maintenanceCostPerUnit, 0) / allPropertyMetrics.length,
      profitMargin: allPropertyMetrics.reduce((sum, p) => sum + p.profitMargin, 0) / allPropertyMetrics.length,
      tenantRetentionRate: allPropertyMetrics.reduce((sum, p) => sum + p.tenantRetentionRate, 0) / allPropertyMetrics.length,
      averageLeaseDuration: allPropertyMetrics.reduce((sum, p) => sum + p.averageLeaseDuration, 0) / allPropertyMetrics.length,
    };

    return {
      properties: benchmarkData,
      marketAverages,
      totalProperties: allPropertyMetrics.length,
      generatedAt: Date.now(),
    };
  },
});

// Get financial KPIs for dashboard
export const getFinancialKPIs = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    period: v.optional(v.union(v.literal("month"), v.literal("quarter"), v.literal("year"))),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const period = args.period || "month";
    
    let startDate: number;
    switch (period) {
      case "month":
        startDate = now - (30 * 24 * 60 * 60 * 1000);
        break;
      case "quarter":
        startDate = now - (90 * 24 * 60 * 60 * 1000);
        break;
      case "year":
        startDate = now - (365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = now - (30 * 24 * 60 * 60 * 1000);
    }

    // Get current period data
    const currentData = await getFinancialDashboard(ctx, {
      propertyId: args.propertyId,
      startDate,
      endDate: now,
    });

    // Calculate additional KPIs
    const { summary, monthlyBreakdown } = currentData;
    
    // Growth rates (comparing to previous period)
    const previousPeriodStart = startDate - (now - startDate);
    const previousData = await getFinancialDashboard(ctx, {
      propertyId: args.propertyId,
      startDate: previousPeriodStart,
      endDate: startDate,
    });

    const revenueGrowth = previousData.summary.totalRevenue > 0 
      ? ((summary.totalRevenue - previousData.summary.totalRevenue) / previousData.summary.totalRevenue) * 100 
      : 0;

    const profitGrowth = previousData.summary.netIncome > 0 
      ? ((summary.netIncome - previousData.summary.netIncome) / previousData.summary.netIncome) * 100 
      : 0;

    // Trend analysis
    const recentMonths = monthlyBreakdown.slice(-3);
    const isRevenueTrendingUp = recentMonths.length >= 2 && 
      recentMonths[recentMonths.length - 1].revenue > recentMonths[0].revenue;

    return {
      period: { startDate, endDate: now, type: period },
      kpis: {
        totalRevenue: summary.totalRevenue,
        netIncome: summary.netIncome,
        profitMargin: summary.profitMargin,
        collectionRate: summary.collectionRate,
        revenueGrowth,
        profitGrowth,
        pendingAmount: summary.pendingRevenue,
        overdueAmount: summary.overdueRevenue,
      },
      trends: {
        isRevenueTrendingUp,
        monthlyAverage: monthlyBreakdown.length > 0 
          ? monthlyBreakdown.reduce((sum: number, month: any) => sum + month.revenue, 0) / monthlyBreakdown.length 
          : 0,
      },
      comparison: {
        previousPeriod: {
          revenue: previousData.summary.totalRevenue,
          netIncome: previousData.summary.netIncome,
          profitMargin: previousData.summary.profitMargin,
        },
      },
    };
  },
});