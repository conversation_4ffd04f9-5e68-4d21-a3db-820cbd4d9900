/**
 * Offline Sync Reconciliation System
 * Handles conflict detection, resolution, and data merging when coming back online
 */

import { offlineStorageManager } from './offline-storage';
import { api } from '../../convex/_generated/api';
import { useMutation, useQuery } from 'convex/react';
import { Id } from '../../convex/_generated/dataModel';

export interface ConflictInfo {
  entityType: string;
  entityId: string;
  localData: any;
  serverData: any;
  conflictType: 'version_mismatch' | 'concurrent_edit' | 'deleted_on_server';
  localVersion: number;
  serverVersion: number;
  timestamp: number;
}

export interface ReconciliationResult {
  success: boolean;
  conflicts: ConflictInfo[];
  resolvedConflicts: number;
  syncedOperations: number;
  failedOperations: number;
  errors: string[];
}

export type ConflictResolutionStrategy = 
  | 'local_wins'      // Always use local changes
  | 'server_wins'     // Always use server changes  
  | 'last_write_wins' // Use most recent timestamp
  | 'manual'          // Require user intervention
  | 'merge_fields';   // Attempt automatic field-level merge

export interface ReconciliationOptions {
  strategy: ConflictResolutionStrategy;
  batchSize: number;
  maxRetries: number;
  fieldMergeRules?: Record<string, 'local' | 'server' | 'concat' | 'merge'>;
}

class OfflineSyncReconciliation {
  private defaultOptions: ReconciliationOptions = {
    strategy: 'last_write_wins',
    batchSize: 50,
    maxRetries: 3,
    fieldMergeRules: {
      // Default merge rules for common fields
      name: 'local',
      description: 'local', 
      notes: 'concat',
      tags: 'merge',
      status: 'local',
      priority: 'local',
    }
  };

  /**
   * Main reconciliation function - processes offline queue and resolves conflicts
   */
  async reconcileOfflineChanges(
    convexClient: any,
    options: Partial<ReconciliationOptions> = {}
  ): Promise<ReconciliationResult> {
    const opts = { ...this.defaultOptions, ...options };
    const result: ReconciliationResult = {
      success: true,
      conflicts: [],
      resolvedConflicts: 0,
      syncedOperations: 0,
      failedOperations: 0,
      errors: []
    };

    try {
      // Get all pending offline operations
      const offlineQueue = await offlineStorageManager.getOfflineQueue();
      
      if (offlineQueue.length === 0) {
        return result;
      }

      console.log(`Starting reconciliation of ${offlineQueue.length} offline operations`);

      // Process operations in batches
      const batches = this.createBatches(offlineQueue, opts.batchSize);
      
      for (const batch of batches) {
        const batchResult = await this.processBatch(convexClient, batch, opts);
        
        // Merge results
        result.conflicts.push(...batchResult.conflicts);
        result.resolvedConflicts += batchResult.resolvedConflicts;
        result.syncedOperations += batchResult.syncedOperations;
        result.failedOperations += batchResult.failedOperations;
        result.errors.push(...batchResult.errors);
      }

      // Handle any remaining conflicts based on strategy
      if (result.conflicts.length > 0) {
        const conflictResults = await this.resolveConflicts(
          convexClient, 
          result.conflicts, 
          opts
        );
        
        result.resolvedConflicts += conflictResults.resolved;
        result.failedOperations += conflictResults.failed;
        result.errors.push(...conflictResults.errors);
      }

      // Clean up completed operations
      await offlineStorageManager.clearCompletedQueueItems();

      result.success = result.failedOperations === 0;
      
      console.log('Reconciliation completed:', result);
      return result;

    } catch (error) {
      console.error('Reconciliation failed:', error);
      result.success = false;
      result.errors.push(error instanceof Error ? error.message : 'Unknown error');
      return result;
    }
  }

  /**
   * Process a batch of offline operations
   */
  private async processBatch(
    convexClient: any,
    batch: any[],
    options: ReconciliationOptions
  ): Promise<ReconciliationResult> {
    const result: ReconciliationResult = {
      success: true,
      conflicts: [],
      resolvedConflicts: 0,
      syncedOperations: 0,
      failedOperations: 0,
      errors: []
    };

    // Prepare operations for batch sync
    const operations = batch.map(item => ({
      type: item.operation,
      entityType: item.entityType,
      entityId: item.entityId,
      data: item.data,
      clientVersion: item.data.updatedAt || item.timestamp,
      timestamp: item.timestamp
    }));

    try {
      // Send batch to server
      const batchResult = await convexClient.mutation(api.realtimeSync.batchSync, {
        operations
      });

      // Process successful operations
      for (const opResult of batchResult.results) {
        if (opResult.success) {
          result.syncedOperations++;
          // Mark as completed in offline queue
          const queueItem = batch.find(item => item.entityId === opResult.operationId);
          if (queueItem) {
            await offlineStorageManager.updateQueueItemStatus(queueItem.id, 'completed');
          }
        } else {
          result.failedOperations++;
          result.errors.push(opResult.error);
        }
      }

      // Handle conflicts
      for (const conflict of batchResult.conflicts) {
        const conflictInfo = await this.analyzeConflict(convexClient, conflict);
        result.conflicts.push(conflictInfo);
      }

    } catch (error) {
      console.error('Batch processing failed:', error);
      result.failedOperations += batch.length;
      result.errors.push(error instanceof Error ? error.message : 'Batch failed');
    }

    return result;
  }

  /**
   * Analyze a conflict to determine the best resolution approach
   */
  private async analyzeConflict(
    convexClient: any,
    conflict: any
  ): Promise<ConflictInfo> {
    // Get current server data
    const serverVersion = await convexClient.query(api.realtimeSync.getEntityVersion, {
      entityType: conflict.entityType,
      entityId: conflict.entityId
    });

    // Get local cached data
    const localData = await offlineStorageManager.getCachedEntity(
      conflict.entityType as any,
      conflict.entityId
    );

    return {
      entityType: conflict.entityType,
      entityId: conflict.entityId,
      localData,
      serverData: serverVersion,
      conflictType: this.determineConflictType(conflict, serverVersion),
      localVersion: conflict.clientVersion,
      serverVersion: serverVersion.version,
      timestamp: Date.now()
    };
  }

  /**
   * Determine the type of conflict
   */
  private determineConflictType(
    conflict: any,
    serverVersion: any
  ): ConflictInfo['conflictType'] {
    if (!serverVersion.exists) {
      return 'deleted_on_server';
    }
    
    if (serverVersion.version > conflict.clientVersion) {
      return 'version_mismatch';
    }
    
    return 'concurrent_edit';
  }

  /**
   * Resolve conflicts based on the specified strategy
   */
  private async resolveConflicts(
    convexClient: any,
    conflicts: ConflictInfo[],
    options: ReconciliationOptions
  ): Promise<{ resolved: number; failed: number; errors: string[] }> {
    const result = { resolved: 0, failed: 0, errors: [] };

    for (const conflict of conflicts) {
      try {
        const resolution = await this.resolveConflict(conflict, options);
        
        if (resolution) {
          await convexClient.mutation(api.realtimeSync.resolveConflict, {
            entityType: conflict.entityType,
            entityId: conflict.entityId,
            resolution: resolution.strategy,
            localData: conflict.localData,
            serverData: conflict.serverData,
            mergedData: resolution.mergedData
          });

          // Update local cache with resolved data
          await offlineStorageManager.cacheEntity(
            conflict.entityType as any,
            conflict.entityId,
            resolution.finalData
          );

          result.resolved++;
        } else {
          // Manual resolution required
          result.failed++;
          result.errors.push(`Manual resolution required for ${conflict.entityType}:${conflict.entityId}`);
        }

      } catch (error) {
        result.failed++;
        result.errors.push(
          `Failed to resolve conflict for ${conflict.entityType}:${conflict.entityId}: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`
        );
      }
    }

    return result;
  }

  /**
   * Resolve a single conflict based on strategy
   */
  private async resolveConflict(
    conflict: ConflictInfo,
    options: ReconciliationOptions
  ): Promise<{
    strategy: 'local' | 'server' | 'merge';
    mergedData?: any;
    finalData: any;
  } | null> {
    
    switch (options.strategy) {
      case 'local_wins':
        return {
          strategy: 'local',
          finalData: conflict.localData
        };

      case 'server_wins':
        return {
          strategy: 'server', 
          finalData: conflict.serverData
        };

      case 'last_write_wins':
        const useLocal = conflict.localVersion > conflict.serverVersion;
        return {
          strategy: useLocal ? 'local' : 'server',
          finalData: useLocal ? conflict.localData : conflict.serverData
        };

      case 'merge_fields':
        const mergedData = this.mergeFields(
          conflict.localData,
          conflict.serverData,
          options.fieldMergeRules || {}
        );
        return {
          strategy: 'merge',
          mergedData,
          finalData: mergedData
        };

      case 'manual':
        // Return null to indicate manual resolution needed
        return null;

      default:
        throw new Error(`Unknown resolution strategy: ${options.strategy}`);
    }
  }

  /**
   * Merge fields from local and server data based on rules
   */
  private mergeFields(
    localData: any,
    serverData: any,
    mergeRules: Record<string, 'local' | 'server' | 'concat' | 'merge'>
  ): any {
    const merged = { ...serverData }; // Start with server data as base

    for (const [field, rule] of Object.entries(mergeRules)) {
      const localValue = localData[field];
      const serverValue = serverData[field];

      switch (rule) {
        case 'local':
          if (localValue !== undefined) {
            merged[field] = localValue;
          }
          break;

        case 'server':
          // Already using server data as base
          break;

        case 'concat':
          if (typeof localValue === 'string' && typeof serverValue === 'string') {
            merged[field] = `${serverValue}\n---\n${localValue}`;
          } else if (Array.isArray(localValue) && Array.isArray(serverValue)) {
            merged[field] = [...serverValue, ...localValue];
          } else {
            merged[field] = localValue || serverValue;
          }
          break;

        case 'merge':
          if (Array.isArray(localValue) && Array.isArray(serverValue)) {
            // Merge arrays, removing duplicates
            merged[field] = [...new Set([...serverValue, ...localValue])];
          } else if (typeof localValue === 'object' && typeof serverValue === 'object') {
            // Merge objects
            merged[field] = { ...serverValue, ...localValue };
          } else {
            merged[field] = localValue || serverValue;
          }
          break;
      }
    }

    return merged;
  }

  /**
   * Create batches from operations array
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Get conflict summary for UI display
   */
  async getConflictSummary(): Promise<{
    totalConflicts: number;
    conflictsByType: Record<string, number>;
    conflictsByEntity: Record<string, number>;
  }> {
    // This would typically be stored during reconciliation
    // For now, return empty summary
    return {
      totalConflicts: 0,
      conflictsByType: {},
      conflictsByEntity: {}
    };
  }

  /**
   * Preview what would happen during reconciliation without actually doing it
   */
  async previewReconciliation(
    convexClient: any,
    options: Partial<ReconciliationOptions> = {}
  ): Promise<{
    operationsToSync: number;
    potentialConflicts: ConflictInfo[];
    estimatedDuration: number;
  }> {
    const offlineQueue = await offlineStorageManager.getOfflineQueue();
    const potentialConflicts: ConflictInfo[] = [];

    // Check each operation for potential conflicts
    for (const item of offlineQueue) {
      if (item.operation === 'update' && item.entityId) {
        try {
          const serverVersion = await convexClient.query(api.realtimeSync.getEntityVersion, {
            entityType: item.entityType,
            entityId: item.entityId
          });

          if (serverVersion.exists && serverVersion.version > (item.data.updatedAt || item.timestamp)) {
            const conflict = await this.analyzeConflict(convexClient, {
              entityType: item.entityType,
              entityId: item.entityId,
              clientVersion: item.data.updatedAt || item.timestamp
            });
            potentialConflicts.push(conflict);
          }
        } catch (error) {
          console.warn('Failed to check for conflict:', error);
        }
      }
    }

    return {
      operationsToSync: offlineQueue.length,
      potentialConflicts,
      estimatedDuration: Math.ceil(offlineQueue.length / (options.batchSize || 50)) * 2000 // Rough estimate
    };
  }
}

export const offlineSyncReconciliation = new OfflineSyncReconciliation();