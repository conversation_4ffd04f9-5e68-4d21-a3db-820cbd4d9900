/**
 * Accessible data table component with proper ARIA attributes and keyboard navigation
 */
import React, { useRef, useState } from 'react';
import { useAccessibility } from '../../contexts/AccessibilityContext';
import { useKeyboardNavigation, KEYBOARD_KEYS, FocusManager } from '../../lib/accessibility';
import { Badge } from '../ui/badge';
import { AccessibleButton } from './AccessibleButton';

export interface TableColumn<T = any> {
  key: string;
  header: string;
  accessor: keyof T | ((item: T) => React.ReactNode);
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  ariaLabel?: (item: T) => string;
}

export interface TableAction<T = any> {
  label: string;
  onClick: (item: T) => void;
  icon?: React.ReactNode;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  disabled?: (item: T) => boolean;
  ariaLabel?: (item: T) => string;
}

interface AccessibleTableProps<T = any> {
  data: T[];
  columns: TableColumn<T>[];
  actions?: TableAction<T>[];
  caption?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  loading?: boolean;
  emptyMessage?: string;
  rowKey?: keyof T | ((item: T) => string);
  selectable?: boolean;
  selectedRows?: Set<string>;
  onSelectionChange?: (selectedRows: Set<string>) => void;
  className?: string;
}

export function AccessibleTable<T = any>({
  data,
  columns,
  actions = [],
  caption,
  sortBy,
  sortDirection = 'asc',
  onSort,
  loading = false,
  emptyMessage = 'No data available',
  rowKey = 'id' as keyof T,
  selectable = false,
  selectedRows = new Set(),
  onSelectionChange,
  className = '',
}: AccessibleTableProps<T>) {
  const { announceToScreenReader, settings } = useAccessibility();
  const tableRef = useRef<HTMLTableElement>(null);
  const [focusedCell, setFocusedCell] = useState<{ row: number; col: number } | null>(null);

  // Generate row key
  const getRowKey = (item: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(item);
    }
    return String(item[rowKey] || index);
  };

  // Handle sorting
  const handleSort = (column: TableColumn<T>) => {
    if (!column.sortable || !onSort) return;

    const newDirection = sortBy === column.key && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(column.key, newDirection);
    
    announceToScreenReader(
      `Table sorted by ${column.header} in ${newDirection}ending order`,
      'polite'
    );
  };

  // Handle row selection
  const handleRowSelection = (rowKey: string, selected: boolean) => {
    if (!onSelectionChange) return;

    const newSelection = new Set(selectedRows);
    if (selected) {
      newSelection.add(rowKey);
    } else {
      newSelection.delete(rowKey);
    }
    
    onSelectionChange(newSelection);
    announceToScreenReader(
      `Row ${selected ? 'selected' : 'deselected'}. ${newSelection.size} of ${data.length} rows selected.`,
      'polite'
    );
  };

  // Handle select all
  const handleSelectAll = (selected: boolean) => {
    if (!onSelectionChange) return;

    const newSelection = selected ? new Set(data.map((item, index) => getRowKey(item, index))) : new Set();
    onSelectionChange(newSelection);
    
    announceToScreenReader(
      selected ? `All ${data.length} rows selected` : 'All rows deselected',
      'polite'
    );
  };

  // Keyboard navigation for table cells
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!focusedCell) return;

    const { row, col } = focusedCell;
    const maxRow = data.length - 1;
    const maxCol = columns.length + (selectable ? 1 : 0) + (actions.length > 0 ? 1 : 0) - 1;

    switch (event.key) {
      case KEYBOARD_KEYS.ARROW_UP:
        event.preventDefault();
        if (row > 0) {
          setFocusedCell({ row: row - 1, col });
        }
        break;
      case KEYBOARD_KEYS.ARROW_DOWN:
        event.preventDefault();
        if (row < maxRow) {
          setFocusedCell({ row: row + 1, col });
        }
        break;
      case KEYBOARD_KEYS.ARROW_LEFT:
        event.preventDefault();
        if (col > 0) {
          setFocusedCell({ row, col: col - 1 });
        }
        break;
      case KEYBOARD_KEYS.ARROW_RIGHT:
        event.preventDefault();
        if (col < maxCol) {
          setFocusedCell({ row, col: col + 1 });
        }
        break;
      case KEYBOARD_KEYS.HOME:
        event.preventDefault();
        setFocusedCell({ row, col: 0 });
        break;
      case KEYBOARD_KEYS.END:
        event.preventDefault();
        setFocusedCell({ row, col: maxCol });
        break;
    }
  };

  // Get cell value
  const getCellValue = (item: T, column: TableColumn<T>) => {
    if (typeof column.accessor === 'function') {
      return column.accessor(item);
    }
    return item[column.accessor];
  };

  // Get cell aria label
  const getCellAriaLabel = (item: T, column: TableColumn<T>, rowIndex: number) => {
    if (column.ariaLabel) {
      return column.ariaLabel(item);
    }
    
    const value = getCellValue(item, column);
    return `${column.header}: ${value}, row ${rowIndex + 1}`;
  };

  const allSelected = data.length > 0 && selectedRows.size === data.length;
  const someSelected = selectedRows.size > 0 && selectedRows.size < data.length;

  return (
    <div className={`overflow-auto ${className}`}>
      <table
        ref={tableRef}
        className="w-full border-collapse"
        role="table"
        aria-label={caption}
        onKeyDown={handleKeyDown}
      >
        {caption && (
          <caption className="sr-only">
            {caption}
          </caption>
        )}
        
        <thead>
          <tr role="row">
            {selectable && (
              <th
                role="columnheader"
                className="p-3 text-left border-b font-medium"
                scope="col"
              >
                <input
                  type="checkbox"
                  checked={allSelected}
                  ref={someSelected ? (el) => el && (el.indeterminate = true) : undefined}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  aria-label={`Select all rows. ${selectedRows.size} of ${data.length} rows selected.`}
                  className="rounded border-gray-300 focus:ring-2 focus:ring-primary"
                />
              </th>
            )}
            
            {columns.map((column) => (
              <th
                key={column.key}
                role="columnheader"
                scope="col"
                className={`
                  p-3 border-b font-medium
                  ${column.align === 'center' ? 'text-center' : ''}
                  ${column.align === 'right' ? 'text-right' : 'text-left'}
                  ${column.sortable ? 'cursor-pointer hover:bg-muted/50' : ''}
                `}
                style={{ width: column.width }}
                onClick={() => column.sortable && handleSort(column)}
                onKeyDown={(e) => {
                  if (column.sortable && (e.key === KEYBOARD_KEYS.ENTER || e.key === KEYBOARD_KEYS.SPACE)) {
                    e.preventDefault();
                    handleSort(column);
                  }
                }}
                tabIndex={column.sortable ? 0 : -1}
                aria-sort={
                  column.sortable && sortBy === column.key
                    ? sortDirection === 'asc' ? 'ascending' : 'descending'
                    : column.sortable ? 'none' : undefined
                }
              >
                <div className="flex items-center gap-2">
                  {column.header}
                  {column.sortable && (
                    <span className="text-xs text-muted-foreground">
                      {sortBy === column.key ? (
                        sortDirection === 'asc' ? '↑' : '↓'
                      ) : (
                        '↕'
                      )}
                    </span>
                  )}
                </div>
              </th>
            ))}
            
            {actions.length > 0 && (
              <th
                role="columnheader"
                scope="col"
                className="p-3 text-right border-b font-medium"
              >
                Actions
              </th>
            )}
          </tr>
        </thead>
        
        <tbody>
          {loading ? (
            <tr>
              <td
                colSpan={columns.length + (selectable ? 1 : 0) + (actions.length > 0 ? 1 : 0)}
                className="p-8 text-center text-muted-foreground"
              >
                <div className="flex items-center justify-center gap-2">
                  <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                  Loading...
                </div>
              </td>
            </tr>
          ) : data.length === 0 ? (
            <tr>
              <td
                colSpan={columns.length + (selectable ? 1 : 0) + (actions.length > 0 ? 1 : 0)}
                className="p-8 text-center text-muted-foreground"
              >
                {emptyMessage}
              </td>
            </tr>
          ) : (
            data.map((item, rowIndex) => {
              const key = getRowKey(item, rowIndex);
              const isSelected = selectedRows.has(key);
              
              return (
                <tr
                  key={key}
                  role="row"
                  className={`
                    border-b hover:bg-muted/50
                    ${isSelected ? 'bg-muted' : ''}
                  `}
                  aria-selected={selectable ? isSelected : undefined}
                >
                  {selectable && (
                    <td role="gridcell" className="p-3">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e) => handleRowSelection(key, e.target.checked)}
                        aria-label={`Select row ${rowIndex + 1}`}
                        className="rounded border-gray-300 focus:ring-2 focus:ring-primary"
                      />
                    </td>
                  )}
                  
                  {columns.map((column, colIndex) => (
                    <td
                      key={column.key}
                      role="gridcell"
                      className={`
                        p-3
                        ${column.align === 'center' ? 'text-center' : ''}
                        ${column.align === 'right' ? 'text-right' : 'text-left'}
                        ${focusedCell?.row === rowIndex && focusedCell?.col === colIndex + (selectable ? 1 : 0) ? 'ring-2 ring-primary' : ''}
                      `}
                      tabIndex={0}
                      onFocus={() => setFocusedCell({ row: rowIndex, col: colIndex + (selectable ? 1 : 0) })}
                      aria-label={getCellAriaLabel(item, column, rowIndex)}
                    >
                      {getCellValue(item, column)}
                    </td>
                  ))}
                  
                  {actions.length > 0 && (
                    <td role="gridcell" className="p-3 text-right">
                      <div className="flex items-center justify-end gap-2">
                        {actions.map((action, actionIndex) => (
                          <AccessibleButton
                            key={actionIndex}
                            size="sm"
                            variant={action.variant || 'outline'}
                            onClick={() => action.onClick(item)}
                            disabled={action.disabled?.(item)}
                            aria-label={action.ariaLabel?.(item) || `${action.label} for row ${rowIndex + 1}`}
                          >
                            {action.icon && <span className="mr-1">{action.icon}</span>}
                            {action.label}
                          </AccessibleButton>
                        ))}
                      </div>
                    </td>
                  )}
                </tr>
              );
            })
          )}
        </tbody>
      </table>
      
      {data.length > 0 && (
        <div className="mt-4 text-sm text-muted-foreground" role="status" aria-live="polite">
          Showing {data.length} {data.length === 1 ? 'row' : 'rows'}
          {selectable && selectedRows.size > 0 && (
            <span>, {selectedRows.size} selected</span>
          )}
        </div>
      )}
    </div>
  );
}