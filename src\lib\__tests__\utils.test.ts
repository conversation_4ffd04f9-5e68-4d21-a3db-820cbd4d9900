import { describe, it, expect } from 'vitest';
import { cn, formatCurrency, formatDate, calculateOccupancyRate, validateEmail, validatePhone } from '../utils';

describe('utils', () => {
  describe('cn (className utility)', () => {
    it('should merge class names correctly', () => {
      expect(cn('class1', 'class2')).toBe('class1 class2');
      expect(cn('class1', undefined, 'class2')).toBe('class1 class2');
      expect(cn('class1', false && 'class2', 'class3')).toBe('class1 class3');
    });

    it('should handle conditional classes', () => {
      expect(cn('base', true && 'conditional')).toBe('base conditional');
      expect(cn('base', false && 'conditional')).toBe('base');
    });
  });

  describe('formatCurrency', () => {
    it('should format currency in KES', () => {
      expect(formatCurrency(1000)).toBe('KES 1,000.00');
      expect(formatCurrency(1234.56)).toBe('KES 1,234.56');
      expect(formatCurrency(0)).toBe('KES 0.00');
    });

    it('should handle negative amounts', () => {
      expect(formatCurrency(-1000)).toBe('-KES 1,000.00');
    });

    it('should format with different currencies', () => {
      expect(formatCurrency(1000, 'USD')).toBe('$1,000.00');
      expect(formatCurrency(1000, 'EUR')).toBe('€1,000.00');
    });
  });

  describe('formatDate', () => {
    it('should format dates correctly', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      expect(formatDate(date)).toBe('Jan 15, 2024');
    });

    it('should handle timestamp input', () => {
      const timestamp = new Date('2024-01-15').getTime();
      expect(formatDate(timestamp)).toBe('Jan 15, 2024');
    });

    it('should format with custom format', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      expect(formatDate(date, 'yyyy-MM-dd')).toBe('2024-01-15');
    });
  });

  describe('calculateOccupancyRate', () => {
    it('should calculate occupancy rate correctly', () => {
      const units = [
        { status: 'occupied' },
        { status: 'occupied' },
        { status: 'vacant' },
        { status: 'maintenance' },
      ];
      expect(calculateOccupancyRate(units as any)).toBe(50); // 2 out of 4 occupied
    });

    it('should handle empty units array', () => {
      expect(calculateOccupancyRate([])).toBe(0);
    });

    it('should handle all occupied units', () => {
      const units = [
        { status: 'occupied' },
        { status: 'occupied' },
      ];
      expect(calculateOccupancyRate(units as any)).toBe(100);
    });

    it('should handle all vacant units', () => {
      const units = [
        { status: 'vacant' },
        { status: 'maintenance' },
      ];
      expect(calculateOccupancyRate(units as any)).toBe(0);
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@domain.com')).toBe(false);
      expect(validateEmail('<EMAIL>')).toBe(false);
    });

    it('should handle empty or null input', () => {
      expect(validateEmail('')).toBe(false);
      expect(validateEmail(null as any)).toBe(false);
      expect(validateEmail(undefined as any)).toBe(false);
    });
  });

  describe('validatePhone', () => {
    it('should validate Kenyan phone numbers', () => {
      expect(validatePhone('+254712345678')).toBe(true);
      expect(validatePhone('0712345678')).toBe(true);
      expect(validatePhone('712345678')).toBe(true);
    });

    it('should validate international phone numbers', () => {
      expect(validatePhone('+1234567890')).toBe(true);
      expect(validatePhone('+44123456789')).toBe(true);
    });

    it('should reject invalid phone numbers', () => {
      expect(validatePhone('123')).toBe(false);
      expect(validatePhone('abcdefghij')).toBe(false);
      expect(validatePhone('+254abc123')).toBe(false);
    });

    it('should handle empty or null input', () => {
      expect(validatePhone('')).toBe(false);
      expect(validatePhone(null as any)).toBe(false);
      expect(validatePhone(undefined as any)).toBe(false);
    });
  });
});