import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useNativeFileSystem } from '../useNativeFileSystem'

const mockElectronAPI = {
  showOpenDialog: vi.fn(),
  showSaveDialog: vi.fn(),
  readFile: vi.fn(),
  writeFile: vi.fn()
}

beforeEach(() => {
  // @ts-ignore
  global.window.electronAPI = mockElectronAPI
  vi.clearAllMocks()
})

describe('useNativeFileSystem', () => {
  it('opens file dialog with correct options', async () => {
    mockElectronAPI.showOpenDialog.mockResolvedValue({
      canceled: false,
      filePaths: ['/test/file.txt']
    })

    const { result } = renderHook(() => useNativeFileSystem())

    await act(async () => {
      const dialogResult = await result.current.openFileDialog({
        title: 'Select File',
        filters: [{ name: 'Text Files', extensions: ['txt'] }]
      })

      expect(mockElectronAPI.showOpenDialog).toHaveBeenCalledWith({
        properties: ['openFile'],
        filters: [{ name: 'Text Files', extensions: ['txt'] }],
        title: 'Select File'
      })
      expect(dialogResult.canceled).toBe(false)
      expect(dialogResult.filePaths).toEqual(['/test/file.txt'])
    })
  })

  it('opens directory dialog', async () => {
    mockElectronAPI.showOpenDialog.mockResolvedValue({
      canceled: false,
      filePaths: ['/test/directory']
    })

    const { result } = renderHook(() => useNativeFileSystem())

    await act(async () => {
      await result.current.openDirectoryDialog({
        title: 'Select Directory'
      })

      expect(mockElectronAPI.showOpenDialog).toHaveBeenCalledWith({
        title: 'Select Directory',
        properties: ['openDirectory']
      })
    })
  })

  it('saves file with dialog', async () => {
    mockElectronAPI.showSaveDialog.mockResolvedValue({
      canceled: false,
      filePath: '/test/output.txt'
    })
    mockElectronAPI.writeFile.mockResolvedValue({ success: true })

    const { result } = renderHook(() => useNativeFileSystem())

    await act(async () => {
      const saveResult = await result.current.saveFileDialog({
        title: 'Save File',
        defaultPath: 'output.txt'
      })

      expect(mockElectronAPI.showSaveDialog).toHaveBeenCalledWith({
        filters: [{ name: 'All Files', extensions: ['*'] }],
        title: 'Save File',
        defaultPath: 'output.txt'
      })
      expect(saveResult.canceled).toBe(false)
      expect(saveResult.filePath).toBe('/test/output.txt')
    })
  })

  it('reads file content', async () => {
    mockElectronAPI.readFile.mockResolvedValue({
      success: true,
      content: 'file content'
    })

    const { result } = renderHook(() => useNativeFileSystem())

    await act(async () => {
      const readResult = await result.current.readFile('/test/file.txt')

      expect(mockElectronAPI.readFile).toHaveBeenCalledWith('/test/file.txt')
      expect(readResult.success).toBe(true)
      expect(readResult.content).toBe('file content')
    })
  })

  it('writes file content', async () => {
    mockElectronAPI.writeFile.mockResolvedValue({ success: true })

    const { result } = renderHook(() => useNativeFileSystem())

    await act(async () => {
      const writeResult = await result.current.writeFile('/test/file.txt', 'content')

      expect(mockElectronAPI.writeFile).toHaveBeenCalledWith('/test/file.txt', 'content')
      expect(writeResult.success).toBe(true)
    })
  })

  it('imports multiple documents', async () => {
    mockElectronAPI.showOpenDialog.mockResolvedValue({
      canceled: false,
      filePaths: ['/test/file1.pdf', '/test/file2.doc']
    })
    mockElectronAPI.readFile
      .mockResolvedValueOnce({ success: true, content: 'pdf content' })
      .mockResolvedValueOnce({ success: true, content: 'doc content' })

    const { result } = renderHook(() => useNativeFileSystem())

    await act(async () => {
      const files = await result.current.importDocuments()

      expect(files).toHaveLength(2)
      expect(files[0].name).toBe('file1.pdf')
      expect(files[0].content).toBe('pdf content')
      expect(files[1].name).toBe('file2.doc')
      expect(files[1].content).toBe('doc content')
    })
  })

  it('exports report with save dialog', async () => {
    mockElectronAPI.showSaveDialog.mockResolvedValue({
      canceled: false,
      filePath: '/test/report.txt'
    })
    mockElectronAPI.writeFile.mockResolvedValue({ success: true })

    const { result } = renderHook(() => useNativeFileSystem())

    await act(async () => {
      const exportResult = await result.current.exportReport('report content', 'report.txt')

      expect(mockElectronAPI.showSaveDialog).toHaveBeenCalledWith({
        title: 'Export Report',
        defaultPath: 'report.txt',
        filters: [
          { name: 'Text Files', extensions: ['txt'] },
          { name: 'CSV Files', extensions: ['csv'] },
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      })
      expect(mockElectronAPI.writeFile).toHaveBeenCalledWith('/test/report.txt', 'report content')
      expect(exportResult.success).toBe(true)
    })
  })

  it('handles canceled export', async () => {
    mockElectronAPI.showSaveDialog.mockResolvedValue({
      canceled: true
    })

    const { result } = renderHook(() => useNativeFileSystem())

    await act(async () => {
      const exportResult = await result.current.exportReport('content')

      expect(exportResult.success).toBe(false)
      expect(exportResult.error).toBe('Export cancelled')
    })
  })

  it('throws error when not in Electron environment', async () => {
    // @ts-ignore
    global.window.electronAPI = undefined

    const { result } = renderHook(() => useNativeFileSystem())

    await act(async () => {
      await expect(result.current.openFileDialog()).rejects.toThrow(
        'Native file system not available in web environment'
      )
    })
  })
})