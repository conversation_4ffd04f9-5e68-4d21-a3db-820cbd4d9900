import { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Textarea } from '../ui/textarea';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  FileText, 
  Download, 
  Calendar, 
  Send,
  Plus,
  Eye,
  Clock,
  CheckCircle,
  AlertTriangle,
  Building,
  Users,
  DollarSign,
  TrendingUp,
  Filter
} from 'lucide-react';
import { Id } from '../../../convex/_generated/dataModel';

interface RegulatoryReportingProps {
  propertyId?: string;
}

export function RegulatoryReporting({ propertyId }: RegulatoryReportingProps) {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedReportType, setSelectedReportType] = useState<string>('');
  const [reportPeriod, setReportPeriod] = useState({
    startDate: '',
    endDate: '',
    frequency: 'monthly' as 'monthly' | 'quarterly' | 'annual'
  });
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [recipients, setRecipients] = useState<Array<{email: string; name: string; role: string}>>([]);
  const [newRecipient, setNewRecipient] = useState({email: '', name: '', role: ''});

  // Fetch data
  const regulatoryReports = useQuery(
    api.compliance.getRegulatoryReports,
    propertyId ? { propertyId: propertyId as Id<"properties"> } : {}
  );

  const properties = useQuery(api.properties.getProperties, {});

  // Mutations
  const generateReport = useMutation(api.compliance.generateRegulatoryReport);

  const reportTypes = [
    { value: 'etims_vat', label: 'ETIMS VAT Report', description: 'VAT compliance report for KRA ETIMS system' },
    { value: 'etims_income_tax', label: 'ETIMS Income Tax Report', description: 'Income tax report for KRA ETIMS system' },
    { value: 'rental_income', label: 'Rental Income Report', description: 'Comprehensive rental income summary' },
    { value: 'tenant_registry', label: 'Tenant Registry Report', description: 'Complete tenant information registry' },
    { value: 'compliance_summary', label: 'Compliance Summary Report', description: 'Overall compliance status summary' },
    { value: 'kyc_status', label: 'KYC Status Report', description: 'Know Your Customer compliance status' },
    { value: 'custom', label: 'Custom Report', description: 'Create a custom regulatory report' }
  ];

  const fileFormats = [
    { value: 'pdf', label: 'PDF Document' },
    { value: 'excel', label: 'Excel Spreadsheet' },
    { value: 'csv', label: 'CSV File' },
    { value: 'xml', label: 'XML File (ETIMS Compatible)' }
  ];

  const handleCreateReport = async () => {
    if (!selectedReportType || !reportPeriod.startDate || !reportPeriod.endDate) {
      return;
    }

    try {
      const propertyIds = selectedProperties.length > 0 
        ? selectedProperties 
        : propertyId 
          ? [propertyId] 
          : properties?.map(p => p._id) || [];

      await generateReport({
        name: `${reportTypes.find(t => t.value === selectedReportType)?.label} - ${new Date().toLocaleDateString()}`,
        reportType: selectedReportType as any,
        propertyIds: propertyIds as Id<"properties">[],
        reportPeriod: {
          startDate: new Date(reportPeriod.startDate).getTime(),
          endDate: new Date(reportPeriod.endDate).getTime(),
          frequency: reportPeriod.frequency,
        },
        fileFormat: 'pdf',
        recipients,
        generatedBy: "current-user" as Id<"users">, // Replace with actual user ID
      });

      setShowCreateForm(false);
      resetForm();
    } catch (error) {
      console.error('Failed to generate report:', error);
    }
  };

  const resetForm = () => {
    setSelectedReportType('');
    setReportPeriod({ startDate: '', endDate: '', frequency: 'monthly' });
    setSelectedProperties([]);
    setRecipients([]);
    setNewRecipient({email: '', name: '', role: ''});
  };

  const addRecipient = () => {
    if (newRecipient.email && newRecipient.name) {
      setRecipients([...recipients, newRecipient]);
      setNewRecipient({email: '', name: '', role: ''});
    }
  };

  const removeRecipient = (index: number) => {
    setRecipients(recipients.filter((_, i) => i !== index));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50';
      case 'generating': return 'text-blue-600 bg-blue-50';
      case 'failed': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'generating': return <Clock className="h-4 w-4" />;
      case 'failed': return <AlertTriangle className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getReportIcon = (reportType: string) => {
    switch (reportType) {
      case 'etims_vat':
      case 'etims_income_tax':
        return <DollarSign className="h-5 w-5 text-green-600" />;
      case 'rental_income':
        return <TrendingUp className="h-5 w-5 text-blue-600" />;
      case 'tenant_registry':
        return <Users className="h-5 w-5 text-purple-600" />;
      case 'compliance_summary':
      case 'kyc_status':
        return <CheckCircle className="h-5 w-5 text-orange-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <FileText className="h-8 w-8 text-blue-600" />
            Regulatory Reporting
          </h1>
          <p className="text-gray-600 mt-1">
            Generate and manage regulatory compliance reports
          </p>
        </div>
        <Button 
          onClick={() => setShowCreateForm(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Generate Report
        </Button>
      </div>

      {/* Report Type Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {reportTypes.slice(0, 6).map((type) => (
          <Card key={type.value} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                {getReportIcon(type.value)}
                <div className="flex-1">
                  <h3 className="font-semibold text-sm">{type.label}</h3>
                  <p className="text-xs text-gray-500 mt-1">{type.description}</p>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="mt-2"
                    onClick={() => {
                      setSelectedReportType(type.value);
                      setShowCreateForm(true);
                    }}
                  >
                    Generate
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Reports */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Recent Reports
          </CardTitle>
          <CardDescription>
            Generated regulatory reports and their status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {regulatoryReports?.length ? (
            <div className="space-y-3">
              {regulatoryReports.map((report) => (
                <div key={report._id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getReportIcon(report.reportType)}
                    <div>
                      <h3 className="font-medium text-sm">{report.name}</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {report.reportType.replace('_', ' ').toUpperCase()}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {new Date(report.createdAt).toLocaleDateString()}
                        </span>
                        <span className="text-xs text-gray-500">
                          {report.propertyIds.length} propert{report.propertyIds.length === 1 ? 'y' : 'ies'}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(report.status)}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(report.status)}
                        {report.status}
                      </div>
                    </Badge>
                    {report.status === 'completed' && (
                      <Button size="sm" variant="outline">
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </Button>
                    )}
                    <Button size="sm" variant="outline">
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No reports generated yet</p>
              <Button 
                variant="outline" 
                className="mt-2"
                onClick={() => setShowCreateForm(true)}
              >
                Generate Your First Report
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Report Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">Generate Regulatory Report</h2>
              <p className="text-sm text-gray-600">Create a new compliance or regulatory report</p>
            </div>
            
            <div className="p-6 space-y-6">
              {/* Report Type Selection */}
              <div className="space-y-2">
                <Label htmlFor="reportType">Report Type</Label>
                <Select value={selectedReportType} onValueChange={setSelectedReportType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select report type" />
                  </SelectTrigger>
                  <SelectContent>
                    {reportTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div>
                          <div className="font-medium">{type.label}</div>
                          <div className="text-xs text-gray-500">{type.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Report Period */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={reportPeriod.startDate}
                    onChange={(e) => setReportPeriod({...reportPeriod, startDate: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={reportPeriod.endDate}
                    onChange={(e) => setReportPeriod({...reportPeriod, endDate: e.target.value})}
                  />
                </div>
              </div>

              {/* Frequency */}
              <div className="space-y-2">
                <Label htmlFor="frequency">Frequency</Label>
                <Select 
                  value={reportPeriod.frequency} 
                  onValueChange={(value: 'monthly' | 'quarterly' | 'annual') => 
                    setReportPeriod({...reportPeriod, frequency: value})
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="annual">Annual</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Property Selection */}
              {!propertyId && (
                <div className="space-y-2">
                  <Label>Properties (leave empty for all)</Label>
                  <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                    {properties?.map((property) => (
                      <label key={property._id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={selectedProperties.includes(property._id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedProperties([...selectedProperties, property._id]);
                            } else {
                              setSelectedProperties(selectedProperties.filter(id => id !== property._id));
                            }
                          }}
                        />
                        <span className="text-sm">{property.name}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* Recipients */}
              <div className="space-y-2">
                <Label>Report Recipients</Label>
                <div className="space-y-2">
                  {recipients.map((recipient, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                      <span className="text-sm flex-1">{recipient.name} ({recipient.email})</span>
                      <Button 
                        size="sm" 
                        variant="ghost" 
                        onClick={() => removeRecipient(index)}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                  
                  <div className="grid grid-cols-3 gap-2">
                    <Input
                      placeholder="Name"
                      value={newRecipient.name}
                      onChange={(e) => setNewRecipient({...newRecipient, name: e.target.value})}
                    />
                    <Input
                      placeholder="Email"
                      type="email"
                      value={newRecipient.email}
                      onChange={(e) => setNewRecipient({...newRecipient, email: e.target.value})}
                    />
                    <Button onClick={addRecipient} variant="outline" size="sm">
                      Add
                    </Button>
                  </div>
                </div>
              </div>

              {/* ETIMS Information */}
              {(selectedReportType === 'etims_vat' || selectedReportType === 'etims_income_tax') && (
                <Alert>
                  <FileText className="h-4 w-4" />
                  <AlertDescription>
                    This report will be generated in ETIMS-compatible format for direct submission to KRA.
                    Ensure all property and tenant information is up to date before generating.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            <div className="p-6 border-t flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateReport}>
                <FileText className="h-4 w-4 mr-2" />
                Generate Report
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}