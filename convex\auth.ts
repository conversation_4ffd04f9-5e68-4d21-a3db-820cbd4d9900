import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Simple session storage for demo purposes
// In production, this would use proper JWT tokens
const sessions = new Map<string, { userId: string; expiresAt: number }>();

// Helper function to generate session token
function generateSessionToken(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

// Helper function to hash password (simplified for demo)
function hashPassword(password: string): string {
  // In production, use proper password hashing like bcrypt
  return Buffer.from(password).toString('base64');
}

// Helper function to verify password
function verifyPassword(password: string, hash: string): boolean {
  return hashPassword(password) === hash;
}

// Get current authenticated user
export const getCurrentUser = query({
  args: { sessionToken: v.optional(v.string()) },
  handler: async (ctx, args) => {
    if (!args.sessionToken) {
      return null;
    }

    const session = sessions.get(args.sessionToken);
    if (!session || session.expiresAt < Date.now()) {
      if (session) {
        sessions.delete(args.sessionToken);
      }
      return null;
    }
    
    const user = await ctx.db.get(session.userId as any);
    if (!user) {
      sessions.delete(args.sessionToken);
      return null;
    }

    // Update last login timestamp
    await ctx.db.patch(session.userId as any, {
      lastLogin: Date.now(),
      updatedAt: Date.now(),
    });

    return user;
  },
});

// Sign in user
export const signIn = mutation({
  args: {
    email: v.string(),
    password: v.string(),
  },
  handler: async (ctx, args) => {
    // Find user by email
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
    
    if (!user) {
      throw new Error("Invalid email or password");
    }

    if (!user.isActive) {
      throw new Error("Account is deactivated");
    }

    // For demo purposes, we'll store password hash in a separate table
    // In production, this would be handled by the auth provider
    const authRecord = await ctx.db
      .query("authCredentials")
      .withIndex("by_user_id", (q) => q.eq("userId", user._id))
      .first();

    if (!authRecord || !verifyPassword(args.password, authRecord.passwordHash)) {
      throw new Error("Invalid email or password");
    }

    // Create session
    const sessionToken = generateSessionToken();
    const expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours

    sessions.set(sessionToken, {
      userId: user._id,
      expiresAt,
    });

    // Update last login
    await ctx.db.patch(user._id, {
      lastLogin: Date.now(),
      updatedAt: Date.now(),
    });

    return {
      sessionToken,
      user,
      expiresAt,
    };
  },
});

// Sign up new user
export const signUp = mutation({
  args: {
    email: v.string(),
    password: v.string(),
    name: v.string(),
    role: v.union(v.literal("owner"), v.literal("manager"), v.literal("vendor"), v.literal("tenant")),
    phone: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
    
    if (existingUser) {
      throw new Error("User with this email already exists");
    }

    const now = Date.now();
    
    // Create user record
    const userId = await ctx.db.insert("users", {
      email: args.email,
      name: args.name,
      role: args.role,
      propertyAccess: [],
      kycStatus: "pending",
      phone: args.phone,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    // Store password hash
    await ctx.db.insert("authCredentials", {
      userId,
      passwordHash: hashPassword(args.password),
      createdAt: now,
    });

    // Create session
    const sessionToken = generateSessionToken();
    const expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours

    sessions.set(sessionToken, {
      userId,
      expiresAt,
    });

    const user = await ctx.db.get(userId);

    return {
      sessionToken,
      user,
      expiresAt,
    };
  },
});

// Sign out user
export const signOut = mutation({
  args: { sessionToken: v.string() },
  handler: async (ctx, args) => {
    sessions.delete(args.sessionToken);
    return { success: true };
  },
});

// Update user profile
export const updateUser = mutation({
  args: {
    sessionToken: v.string(),
    userId: v.id("users"),
    name: v.optional(v.string()),
    phone: v.optional(v.string()),
    avatar: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const session = sessions.get(args.sessionToken);
    if (!session || session.expiresAt < Date.now()) {
      throw new Error("Not authenticated");
    }

    const { sessionToken, userId, ...updates } = args;
    
    // Users can only update their own profile unless they're an owner/manager
    const currentUser = await ctx.db.get(session.userId as any);
    if (!currentUser) {
      throw new Error("User not found");
    }

    if (session.userId !== userId && !["owner", "manager"].includes(currentUser.role)) {
      throw new Error("Not authorized to update this user");
    }
    
    await ctx.db.patch(userId, {
      ...updates,
      updatedAt: Date.now(),
    });

    return userId;
  },
});

// Get user by email
export const getUserByEmail = query({
  args: { 
    sessionToken: v.string(),
    email: v.string() 
  },
  handler: async (ctx, args) => {
    const session = sessions.get(args.sessionToken);
    if (!session || session.expiresAt < Date.now()) {
      throw new Error("Not authenticated");
    }

    return await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
  },
});

// Verify user session and get user info
export const verifySession = query({
  args: { sessionToken: v.string() },
  handler: async (ctx, args) => {
    const session = sessions.get(args.sessionToken);
    if (!session || session.expiresAt < Date.now()) {
      if (session) {
        sessions.delete(args.sessionToken);
      }
      return { isAuthenticated: false, user: null };
    }

    const user = await ctx.db.get(session.userId as any);
    return {
      isAuthenticated: true,
      user: user || null,
    };
  },
});

// Refresh authentication token
export const refreshToken = mutation({
  args: { sessionToken: v.string() },
  handler: async (ctx, args) => {
    const session = sessions.get(args.sessionToken);
    if (!session || session.expiresAt < Date.now()) {
      throw new Error("Not authenticated");
    }

    // Extend session
    const newExpiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
    sessions.set(args.sessionToken, {
      ...session,
      expiresAt: newExpiresAt,
    });

    // Update last activity timestamp
    await ctx.db.patch(session.userId as any, {
      updatedAt: Date.now(),
    });

    return { 
      success: true,
      expiresAt: newExpiresAt,
    };
  },
});

// Change password
export const changePassword = mutation({
  args: {
    sessionToken: v.string(),
    currentPassword: v.string(),
    newPassword: v.string(),
  },
  handler: async (ctx, args) => {
    const session = sessions.get(args.sessionToken);
    if (!session || session.expiresAt < Date.now()) {
      throw new Error("Not authenticated");
    }

    // Get current auth record
    const authRecord = await ctx.db
      .query("authCredentials")
      .withIndex("by_user_id", (q) => q.eq("userId", session.userId as any))
      .first();

    if (!authRecord || !verifyPassword(args.currentPassword, authRecord.passwordHash)) {
      throw new Error("Current password is incorrect");
    }

    // Update password hash
    await ctx.db.patch(authRecord._id, {
      passwordHash: hashPassword(args.newPassword),
    });

    return { success: true };
  },
});