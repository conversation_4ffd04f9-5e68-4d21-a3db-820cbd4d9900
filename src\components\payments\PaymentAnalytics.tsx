import React, { useState, useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Button } from "../ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import { 
  BarChart3, 
  TrendingUp, 
  // Calendar, 
  Download,
  PieChart,
  LineChart,
  DollarSign
} from "lucide-react";
import { 
  format, 
  subDays, 
  startOfDay, 
  endOfDay, 
  eachDayOfInterval,
  eachMonthOfInterval,
  startOfMonth,
  endOfMonth,
  subMonths
} from "date-fns";

interface PaymentAnalyticsProps {
  propertyId?: Id<"properties">;
}

interface ChartData {
  date: string;
  revenue: number;
  transactions: number;
  mpesa: number;
  stripe: number;
}

export const PaymentAnalytics: React.FC<PaymentAnalyticsProps> = ({ propertyId: _propertyId }) => {
  const [timeframe, setTimeframe] = useState("30d");
  const [chartType, setChartType] = useState("revenue");

  // Fetch data
  const payments = useQuery(api.payments.getPaymentsByStatus, { 
    status: undefined,
    limit: 5000 
  });

  // Process data for charts
  const chartData = useMemo(() => {
    if (!payments) return [];

    const now = new Date();
    let startDate: Date;
    let interval: "day" | "month";

    switch (timeframe) {
      case "7d":
        startDate = subDays(now, 7);
        interval = "day";
        break;
      case "30d":
        startDate = subDays(now, 30);
        interval = "day";
        break;
      case "90d":
        startDate = subDays(now, 90);
        interval = "day";
        break;
      case "12m":
        startDate = subMonths(now, 12);
        interval = "month";
        break;
      default:
        startDate = subDays(now, 30);
        interval = "day";
    }

    const dateRange = interval === "day" 
      ? eachDayOfInterval({ start: startDate, end: now })
      : eachMonthOfInterval({ start: startOfMonth(startDate), end: endOfMonth(now) });

    const data: ChartData[] = dateRange.map(date => {
      const dateStart = interval === "day" ? startOfDay(date) : startOfMonth(date);
      const dateEnd = interval === "day" ? endOfDay(date) : endOfMonth(date);

      const dayPayments = payments.filter((payment: any) => {
        const paymentDate = new Date(payment.createdAt);
        return paymentDate >= dateStart && paymentDate <= dateEnd;
      });

      const completedPayments = dayPayments.filter((p: any) => p.status === "completed");
      const mpesaPayments = completedPayments.filter((p: any) => p.method === "mpesa");
      const stripePayments = completedPayments.filter((p: any) => p.method === "stripe");

      return {
        date: interval === "day" ? format(date, "MMM dd") : format(date, "MMM yyyy"),
        revenue: completedPayments.reduce((sum: number, p: any) => sum + p.amount, 0),
        transactions: completedPayments.length,
        mpesa: mpesaPayments.reduce((sum: number, p: any) => sum + p.amount, 0),
        stripe: stripePayments.reduce((sum: number, p: any) => sum + p.amount, 0),
      };
    });

    return data;
  }, [payments, timeframe]);

  // Calculate summary metrics
  const summaryMetrics = useMemo(() => {
    if (!payments) return null;

    const completedPayments = payments.filter((p: any) => p.status === "completed");
    const totalRevenue = completedPayments.reduce((sum: number, p: any) => sum + p.amount, 0);
    const totalTransactions = completedPayments.length;
    const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

    const mpesaRevenue = completedPayments
      .filter((p: any) => p.method === "mpesa")
      .reduce((sum: number, p: any) => sum + p.amount, 0);
    
    const stripeRevenue = completedPayments
      .filter((p: any) => p.method === "stripe")
      .reduce((sum: number, p: any) => sum + p.amount, 0);

    const failedPayments = payments.filter((p: any) => p.status === "failed");
    const failureRate = payments.length > 0 ? (failedPayments.length / payments.length) * 100 : 0;

    // Calculate growth compared to previous period
    const currentPeriodDays = timeframe === "7d" ? 7 : timeframe === "30d" ? 30 : 90;
    const currentPeriodStart = subDays(new Date(), currentPeriodDays);
    const previousPeriodStart = subDays(currentPeriodStart, currentPeriodDays);

    const currentPeriodPayments = completedPayments.filter((p: any) => {
      const paymentDate = new Date(p.createdAt);
      return paymentDate >= currentPeriodStart;
    });

    const previousPeriodPayments = completedPayments.filter((p: any) => {
      const paymentDate = new Date(p.createdAt);
      return paymentDate >= previousPeriodStart && paymentDate < currentPeriodStart;
    });

    const currentRevenue = currentPeriodPayments.reduce((sum: number, p: any) => sum + p.amount, 0);
    const previousRevenue = previousPeriodPayments.reduce((sum: number, p: any) => sum + p.amount, 0);
    const revenueGrowth = previousRevenue > 0 ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0;

    return {
      totalRevenue,
      totalTransactions,
      averageTransaction,
      mpesaRevenue,
      stripeRevenue,
      failureRate,
      revenueGrowth,
      mpesaPercentage: totalRevenue > 0 ? (mpesaRevenue / totalRevenue) * 100 : 0,
      stripePercentage: totalRevenue > 0 ? (stripeRevenue / totalRevenue) * 100 : 0,
    };
  }, [payments, timeframe]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (!payments || !summaryMetrics) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Payment Analytics</h2>
          <p className="text-muted-foreground">
            Detailed insights and trends for payment performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="12m">Last 12 months</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(summaryMetrics.totalRevenue)}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 mr-1 ${summaryMetrics.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`} />
              {formatPercentage(Math.abs(summaryMetrics.revenueGrowth))} from last period
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transactions</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summaryMetrics.totalTransactions.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Avg: {formatCurrency(summaryMetrics.averageTransaction)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(100 - summaryMetrics.failureRate)}</div>
            <p className="text-xs text-muted-foreground">
              {formatPercentage(summaryMetrics.failureRate)} failure rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">M-PESA Share</CardTitle>
            <PieChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(summaryMetrics.mpesaPercentage)}</div>
            <p className="text-xs text-muted-foreground">
              {formatCurrency(summaryMetrics.mpesaRevenue)} total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <Tabs value={chartType} onValueChange={setChartType} className="space-y-4">
        <TabsList>
          <TabsTrigger value="revenue">Revenue Trend</TabsTrigger>
          <TabsTrigger value="transactions">Transaction Volume</TabsTrigger>
          <TabsTrigger value="methods">Payment Methods</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5" />
                Revenue Trend
              </CardTitle>
              <CardDescription>
                Daily revenue over the selected time period
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80 w-full">
                {/* Simple bar chart representation */}
                <div className="flex items-end justify-between h-full space-x-1">
                  {chartData.map((data, index) => {
                    const maxRevenue = Math.max(...chartData.map(d => d.revenue));
                    const height = maxRevenue > 0 ? (data.revenue / maxRevenue) * 100 : 0;
                    
                    return (
                      <div key={index} className="flex flex-col items-center flex-1">
                        <div 
                          className="bg-primary rounded-t w-full min-h-[2px] transition-all hover:bg-primary/80"
                          style={{ height: `${height}%` }}
                          title={`${data.date}: ${formatCurrency(data.revenue)}`}
                        />
                        <div className="text-xs text-muted-foreground mt-2 rotate-45 origin-left">
                          {data.date}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Transaction Volume
              </CardTitle>
              <CardDescription>
                Number of transactions over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80 w-full">
                <div className="flex items-end justify-between h-full space-x-1">
                  {chartData.map((data, index) => {
                    const maxTransactions = Math.max(...chartData.map(d => d.transactions));
                    const height = maxTransactions > 0 ? (data.transactions / maxTransactions) * 100 : 0;
                    
                    return (
                      <div key={index} className="flex flex-col items-center flex-1">
                        <div 
                          className="bg-blue-500 rounded-t w-full min-h-[2px] transition-all hover:bg-blue-400"
                          style={{ height: `${height}%` }}
                          title={`${data.date}: ${data.transactions} transactions`}
                        />
                        <div className="text-xs text-muted-foreground mt-2 rotate-45 origin-left">
                          {data.date}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="methods" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Payment Methods Comparison
              </CardTitle>
              <CardDescription>
                Revenue breakdown by payment method over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80 w-full">
                <div className="flex items-end justify-between h-full space-x-1">
                  {chartData.map((data, index) => {
                    const maxAmount = Math.max(...chartData.map(d => Math.max(d.mpesa, d.stripe)));
                    const mpesaHeight = maxAmount > 0 ? (data.mpesa / maxAmount) * 100 : 0;
                    const stripeHeight = maxAmount > 0 ? (data.stripe / maxAmount) * 100 : 0;
                    
                    return (
                      <div key={index} className="flex flex-col items-center flex-1 space-y-1">
                        <div className="flex flex-col w-full space-y-1">
                          <div 
                            className="bg-green-500 rounded-t w-full min-h-[2px] transition-all hover:bg-green-400"
                            style={{ height: `${mpesaHeight}%` }}
                            title={`${data.date} M-PESA: ${formatCurrency(data.mpesa)}`}
                          />
                          <div 
                            className="bg-blue-500 w-full min-h-[2px] transition-all hover:bg-blue-400"
                            style={{ height: `${stripeHeight}%` }}
                            title={`${data.date} Stripe: ${formatCurrency(data.stripe)}`}
                          />
                        </div>
                        <div className="text-xs text-muted-foreground mt-2 rotate-45 origin-left">
                          {data.date}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              <div className="flex items-center justify-center space-x-6 mt-4">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded" />
                  <span className="text-sm">M-PESA</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded" />
                  <span className="text-sm">Card Payment</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Detailed Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Payment Method Performance</CardTitle>
            <CardDescription>Detailed breakdown by payment method</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-green-500 rounded" />
                  <div>
                    <p className="font-medium">M-PESA</p>
                    <p className="text-sm text-muted-foreground">Mobile Money</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{formatCurrency(summaryMetrics.mpesaRevenue)}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatPercentage(summaryMetrics.mpesaPercentage)}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-blue-500 rounded" />
                  <div>
                    <p className="font-medium">Card Payment</p>
                    <p className="text-sm text-muted-foreground">Credit/Debit Cards</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{formatCurrency(summaryMetrics.stripeRevenue)}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatPercentage(summaryMetrics.stripePercentage)}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Key Insights</CardTitle>
            <CardDescription>Important trends and observations</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-sm font-medium">Revenue Growth</p>
                <p className="text-xs text-muted-foreground">
                  {summaryMetrics.revenueGrowth >= 0 ? "↗️" : "↘️"} 
                  {" "}{formatPercentage(Math.abs(summaryMetrics.revenueGrowth))} compared to previous period
                </p>
              </div>
              
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-sm font-medium">Preferred Method</p>
                <p className="text-xs text-muted-foreground">
                  {summaryMetrics.mpesaPercentage > summaryMetrics.stripePercentage ? "M-PESA" : "Card Payment"} 
                  {" "}is the preferred payment method
                </p>
              </div>
              
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-sm font-medium">Success Rate</p>
                <p className="text-xs text-muted-foreground">
                  {formatPercentage(100 - summaryMetrics.failureRate)} of payments are successful
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};