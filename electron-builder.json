{"appId": "com.estatepulse.app", "productName": "EstatePulse", "copyright": "Copyright © 2025 EstatePulse Team", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "public", "to": "public", "filter": ["**/*"]}], "mac": {"category": "public.app-category.business", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "public/icon.icns", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "public/icon.ico", "publisherName": "EstatePulse Team", "verifyUpdateCodeSignature": false}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "public/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "EstatePulse"}, "publish": {"provider": "github", "owner": "estate-pulse", "repo": "estate-pulse-app", "private": false}, "afterSign": "build/notarize.js", "protocols": [{"name": "EstatePulse Protocol", "schemes": ["estate-pulse"]}]}