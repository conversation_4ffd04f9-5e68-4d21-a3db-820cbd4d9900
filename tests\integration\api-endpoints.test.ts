import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { convexTest } from 'convex-test';
import { api } from '../../convex/_generated/api';
import schema from '../../convex/schema';

describe('API Endpoints Integration', () => {
  let t: any;

  beforeEach(async () => {
    t = convexTest(schema);
  });

  afterEach(async () => {
    // Cleanup test data
    if (t) {
      await t.finishTest();
    }
  });

  describe('Properties API Integration', () => {
    it('should create, read, update, and delete property', async () => {
      // Create user first
      const userId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      // Create property
      const propertyData = {
        name: 'Integration Test Property',
        type: 'residential' as const,
        address: {
          street: '123 Test Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: userId,
      };

      const propertyId = await t.mutation(api.properties.create, propertyData);
      expect(propertyId).toBeDefined();

      // Read property
      const property = await t.query(api.properties.getById, { id: propertyId });
      expect(property).toMatchObject({
        name: 'Integration Test Property',
        type: 'residential',
        ownerId: userId,
      });

      // Update property
      await t.mutation(api.properties.update, {
        id: propertyId,
        name: 'Updated Property Name',
      });

      const updatedProperty = await t.query(api.properties.getById, { id: propertyId });
      expect(updatedProperty?.name).toBe('Updated Property Name');

      // Delete property
      await t.mutation(api.properties.remove, { id: propertyId });

      const deletedProperty = await t.query(api.properties.getById, { id: propertyId });
      expect(deletedProperty).toBeNull();
    });

    it('should handle property with units and leases', async () => {
      // Create user and property
      const userId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: 'Complex Property',
        type: 'residential' as const,
        address: {
          street: '123 Complex Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: userId,
      });

      // Create units
      const unit1Id = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A101',
        type: 'apartment' as const,
        size: 1200,
        rent: 50000,
        status: 'vacant' as const,
        amenities: ['parking'],
      });

      const unit2Id = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A102',
        type: 'apartment' as const,
        size: 1000,
        rent: 45000,
        status: 'occupied' as const,
        amenities: ['balcony'],
      });

      // Create tenant
      const tenantId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Test Tenant',
          role: 'tenant',
          propertyAccess: [propertyId],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      // Create lease
      const leaseId = await t.mutation(api.leases.create, {
        propertyId,
        unitId: unit2Id,
        tenantId,
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: 45000,
        deposit: 90000,
      });

      // Verify relationships
      const units = await t.query(api.units.getByProperty, { propertyId });
      expect(units).toHaveLength(2);

      const leases = await t.query(api.leases.getByProperty, { propertyId });
      expect(leases).toHaveLength(1);

      const occupancyRate = await t.query(api.units.getOccupancyRate, { propertyId });
      expect(occupancyRate).toBe(50); // 1 out of 2 units occupied
    });
  });

  describe('Payment Flow Integration', () => {
    it('should process complete payment workflow', async () => {
      // Setup: Create user, property, unit, lease
      const ownerId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const tenantId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Test Tenant',
          role: 'tenant',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: 'Payment Test Property',
        type: 'residential' as const,
        address: {
          street: '123 Payment Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId,
      });

      const unitId = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'P101',
        type: 'apartment' as const,
        size: 1200,
        rent: 50000,
        status: 'occupied' as const,
        amenities: [],
      });

      const leaseId = await t.mutation(api.leases.create, {
        propertyId,
        unitId,
        tenantId,
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: 50000,
        deposit: 100000,
      });

      // Generate invoice
      const invoiceId = await t.mutation(api.invoices.create, {
        leaseId,
        amount: 50000,
        dueDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
        description: 'Monthly Rent',
      });

      // Process payment
      const paymentId = await t.mutation(api.payments.create, {
        invoiceId,
        amount: 50000,
        method: 'mpesa' as const,
        transactionId: 'MPESA123456',
        metadata: {
          phoneNumber: '254712345678',
        },
      });

      // Verify payment
      const payment = await t.query(api.payments.getById, { id: paymentId });
      expect(payment).toMatchObject({
        amount: 50000,
        method: 'mpesa',
        transactionId: 'MPESA123456',
        status: 'completed',
      });

      // Verify invoice is marked as paid
      const invoice = await t.query(api.invoices.getById, { id: invoiceId });
      expect(invoice?.status).toBe('paid');
    });

    it('should handle payment failures and retries', async () => {
      // Create minimal setup for payment
      const tenantId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Test Tenant',
          role: 'tenant',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const invoiceId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('invoices', {
          leaseId: 'lease1',
          amount: 50000,
          dueDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
          status: 'pending',
          items: [],
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      // Attempt payment that fails
      try {
        await t.mutation(api.payments.create, {
          invoiceId,
          amount: 50000,
          method: 'mpesa' as const,
          transactionId: 'FAILED123',
          metadata: {
            phoneNumber: 'invalid',
            error: 'Insufficient funds',
          },
        });
      } catch (error) {
        expect(error.message).toContain('Payment failed');
      }

      // Retry with successful payment
      const paymentId = await t.mutation(api.payments.create, {
        invoiceId,
        amount: 50000,
        method: 'mpesa' as const,
        transactionId: 'SUCCESS123',
        metadata: {
          phoneNumber: '254712345678',
        },
      });

      const payment = await t.query(api.payments.getById, { id: paymentId });
      expect(payment?.status).toBe('completed');
    });
  });

  describe('Maintenance Workflow Integration', () => {
    it('should handle complete maintenance ticket lifecycle', async () => {
      // Setup: Create users, property, unit
      const managerId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Manager',
          role: 'manager',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const tenantId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Test Tenant',
          role: 'tenant',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const vendorId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Maintenance Vendor',
          role: 'vendor',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: 'Maintenance Test Property',
        type: 'residential' as const,
        address: {
          street: '123 Maintenance Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: managerId,
      });

      const unitId = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'M101',
        type: 'apartment' as const,
        size: 1200,
        rent: 50000,
        status: 'occupied' as const,
        amenities: [],
      });

      // Create maintenance ticket
      const ticketId = await t.mutation(api.maintenance.createTicket, {
        propertyId,
        unitId,
        tenantId,
        title: 'Leaking faucet',
        description: 'Kitchen faucet is leaking water',
        priority: 'medium' as const,
        category: 'plumbing',
      });

      // Verify ticket creation
      const ticket = await t.query(api.maintenance.getTicketById, { id: ticketId });
      expect(ticket).toMatchObject({
        title: 'Leaking faucet',
        status: 'open',
        priority: 'medium',
      });

      // Assign vendor
      await t.mutation(api.maintenance.assignVendor, {
        ticketId,
        vendorId,
      });

      // Update ticket status
      await t.mutation(api.maintenance.updateTicketStatus, {
        ticketId,
        status: 'in_progress' as const,
      });

      // Complete ticket
      await t.mutation(api.maintenance.updateTicketStatus, {
        ticketId,
        status: 'completed' as const,
      });

      // Verify final state
      const completedTicket = await t.query(api.maintenance.getTicketById, { id: ticketId });
      expect(completedTicket?.status).toBe('completed');
      expect(completedTicket?.assignedVendorId).toBe(vendorId);
    });

    it('should handle SLA tracking and escalation', async () => {
      // Create high priority ticket
      const tenantId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Test Tenant',
          role: 'tenant',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('properties', {
          name: 'SLA Test Property',
          type: 'residential',
          address: {
            street: '123 SLA Street',
            city: 'Nairobi',
            state: 'Nairobi County',
            postalCode: '00100',
            country: 'Kenya',
          },
          ownerId: tenantId,
          branding: {},
          settings: {},
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const ticketId = await t.mutation(api.maintenance.createTicket, {
        propertyId,
        tenantId,
        title: 'Emergency repair',
        description: 'Urgent issue requiring immediate attention',
        priority: 'emergency' as const,
        category: 'emergency',
      });

      // Check SLA deadline calculation
      const ticket = await t.query(api.maintenance.getTicketById, { id: ticketId });
      expect(ticket?.slaDeadline).toBeDefined();
      
      // Emergency tickets should have 4-hour SLA
      const expectedDeadline = ticket!.createdAt + (4 * 60 * 60 * 1000);
      expect(ticket?.slaDeadline).toBeCloseTo(expectedDeadline, -3); // Within 1 second

      // Test escalation
      await t.mutation(api.maintenance.escalateTicket, {
        ticketId,
        reason: 'SLA deadline exceeded',
      });

      const escalatedTicket = await t.query(api.maintenance.getTicketById, { id: ticketId });
      expect(escalatedTicket?.isEscalated).toBe(true);
    });
  });

  describe('Real-time Synchronization Integration', () => {
    it('should sync data changes across multiple clients', async () => {
      // Create property
      const userId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: 'Sync Test Property',
        type: 'residential' as const,
        address: {
          street: '123 Sync Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: userId,
      });

      // Simulate multiple clients watching the same data
      const client1Properties = await t.query(api.properties.getAll);
      const client2Properties = await t.query(api.properties.getAll);

      expect(client1Properties).toHaveLength(1);
      expect(client2Properties).toHaveLength(1);

      // Update property from one client
      await t.mutation(api.properties.update, {
        id: propertyId,
        name: 'Updated Sync Property',
      });

      // Both clients should see the update
      const updatedClient1Properties = await t.query(api.properties.getAll);
      const updatedClient2Properties = await t.query(api.properties.getAll);

      expect(updatedClient1Properties[0].name).toBe('Updated Sync Property');
      expect(updatedClient2Properties[0].name).toBe('Updated Sync Property');
    });

    it('should handle concurrent updates with conflict resolution', async () => {
      const userId = await t.run(async (ctx: any) => {
        return await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: 'Conflict Test Property',
        type: 'residential' as const,
        address: {
          street: '123 Conflict Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
        ownerId: userId,
      });

      // Simulate concurrent updates
      const update1Promise = t.mutation(api.properties.update, {
        id: propertyId,
        name: 'Update from Client 1',
      });

      const update2Promise = t.mutation(api.properties.update, {
        id: propertyId,
        name: 'Update from Client 2',
      });

      // Both updates should succeed (last-write-wins)
      await Promise.all([update1Promise, update2Promise]);

      const finalProperty = await t.query(api.properties.getById, { id: propertyId });
      expect(finalProperty?.name).toMatch(/Update from Client [12]/);
    });
  });
});