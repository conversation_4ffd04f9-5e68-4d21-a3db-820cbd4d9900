import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { 
  Calculator, 
  Download, 
  DollarSign, 
  Calendar, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Building,
  Users
} from 'lucide-react';

interface RentRollGeneratorProps {
  propertyId: Id<"properties">;
}

interface RentRollData {
  propertyId: Id<"properties">;
  month: number;
  year: number;
  generatedAt: number;
  entries: RentRollEntry[];
  summary: RentRollSummary;
}

interface RentRollEntry {
  leaseId: Id<"leases">;
  invoiceId: Id<"invoices">;
  tenant: {
    id: Id<"users">;
    name: string;
    email: string;
  };
  unit: {
    id: Id<"units">;
    unitNumber: string;
    type: string;
  };
  rent: number;
  lateFee: number;
  totalAmount: number;
  dueDate: number;
  status: string;
  leaseStartDate: number;
  leaseEndDate: number;
}

interface RentRollSummary {
  totalUnits: number;
  totalRent: number;
  totalLateFees: number;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  overdueAmount: number;
  collectionRate: number;
}

export function RentRollGenerator({ propertyId }: RentRollGeneratorProps) {
  const generateRentRoll = useMutation(api.invoices.generateRentRoll);
  const property = useQuery(api.properties.getPropertyById, { id: propertyId });

  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [includeLateFees, setIncludeLateFees] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [rentRollData, setRentRollData] = useState<RentRollData | null>(null);

  const handleGenerateRentRoll = async () => {
    setIsGenerating(true);
    try {
      const result = await generateRentRoll({
        propertyId,
        month: selectedMonth,
        year: selectedYear,
        includeLateFees,
      });
      setRentRollData(result);
    } catch (error) {
      console.error('Error generating rent roll:', error);
      alert('Failed to generate rent roll');
    } finally {
      setIsGenerating(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'overdue':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const months = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' },
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - 2 + i);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Rent Roll Generator</h2>
          <p className="text-gray-600">
            Generate monthly rent roll for {property?.name}
          </p>
        </div>
      </div>

      {/* Generation Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Generate Rent Roll
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="month">Month</Label>
              <Select value={selectedMonth.toString()} onValueChange={(value) => setSelectedMonth(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select month" />
                </SelectTrigger>
                <SelectContent>
                  {months.map((month) => (
                    <SelectItem key={month.value} value={month.value.toString()}>
                      {month.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="year">Year</Label>
              <Select value={selectedYear.toString()} onValueChange={(value) => setSelectedYear(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select year" />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Options</Label>
              <div className="flex items-center space-x-2">
                <input
                  id="includeLateFees"
                  type="checkbox"
                  checked={includeLateFees}
                  onChange={(e) => setIncludeLateFees(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="includeLateFees" className="text-sm">
                  Include late fees
                </Label>
              </div>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button 
                onClick={handleGenerateRentRoll} 
                disabled={isGenerating}
                className="w-full"
              >
                {isGenerating ? 'Generating...' : 'Generate Rent Roll'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Rent Roll Results */}
      {rentRollData && (
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Units</p>
                    <p className="text-2xl font-bold">{rentRollData.summary.totalUnits}</p>
                  </div>
                  <Building className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Rent</p>
                    <p className="text-2xl font-bold">{formatCurrency(rentRollData.summary.totalRent)}</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Collection Rate</p>
                    <p className="text-2xl font-bold">{rentRollData.summary.collectionRate.toFixed(1)}%</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Late Fees</p>
                    <p className="text-2xl font-bold">{formatCurrency(rentRollData.summary.totalLateFees)}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Financial Summary */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>
                  Financial Summary - {months.find(m => m.value === rentRollData.month)?.label} {rentRollData.year}
                </CardTitle>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export PDF
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-800">Collected</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Paid Amount:</span>
                      <span className="font-medium text-green-600">
                        {formatCurrency(rentRollData.summary.paidAmount)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-semibold text-yellow-800">Pending</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Pending Amount:</span>
                      <span className="font-medium text-yellow-600">
                        {formatCurrency(rentRollData.summary.pendingAmount)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-semibold text-red-800">Overdue</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Overdue Amount:</span>
                      <span className="font-medium text-red-600">
                        {formatCurrency(rentRollData.summary.overdueAmount)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold">Total Amount:</span>
                <span className="text-xl font-bold">
                  {formatCurrency(rentRollData.summary.totalAmount)}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Detailed Rent Roll */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Detailed Rent Roll
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-2">Unit</th>
                      <th className="text-left py-3 px-2">Tenant</th>
                      <th className="text-right py-3 px-2">Rent</th>
                      <th className="text-right py-3 px-2">Late Fee</th>
                      <th className="text-right py-3 px-2">Total</th>
                      <th className="text-center py-3 px-2">Status</th>
                      <th className="text-center py-3 px-2">Due Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {rentRollData.entries.map((entry) => (
                      <tr key={entry.leaseId} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-2">
                          <div>
                            <p className="font-medium">{entry.unit.unitNumber}</p>
                            <p className="text-xs text-gray-500 capitalize">{entry.unit.type}</p>
                          </div>
                        </td>
                        <td className="py-3 px-2">
                          <div>
                            <p className="font-medium">{entry.tenant.name}</p>
                            <p className="text-xs text-gray-500">{entry.tenant.email}</p>
                          </div>
                        </td>
                        <td className="py-3 px-2 text-right font-medium">
                          {formatCurrency(entry.rent)}
                        </td>
                        <td className="py-3 px-2 text-right">
                          {entry.lateFee > 0 ? (
                            <span className="text-red-600 font-medium">
                              {formatCurrency(entry.lateFee)}
                            </span>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </td>
                        <td className="py-3 px-2 text-right font-bold">
                          {formatCurrency(entry.totalAmount)}
                        </td>
                        <td className="py-3 px-2 text-center">
                          <div className="flex items-center justify-center gap-1">
                            {getStatusIcon(entry.status)}
                            <Badge className={getStatusColor(entry.status)}>
                              {entry.status}
                            </Badge>
                          </div>
                        </td>
                        <td className="py-3 px-2 text-center text-sm">
                          {formatDate(entry.dueDate)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {rentRollData.entries.length === 0 && (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Active Leases</h3>
                  <p className="text-gray-600">
                    No active leases found for the selected month and year.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}