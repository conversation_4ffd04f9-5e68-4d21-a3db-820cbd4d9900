import { v } from "convex/values";
import { mutation, query, action, internalMutation, ActionCtx } from "./_generated/server";
import { Id, Doc } from "./_generated/dataModel";
import { api, internal } from "./_generated/api";

// Notification trigger types
export const triggerTypes = v.union(
  v.literal("payment_due"),
  v.literal("payment_overdue"),
  v.literal("lease_expiry"),
  v.literal("maintenance_assigned"),
  v.literal("maintenance_escalated"),
  v.literal("maintenance_completed"),
  v.literal("sla_warning"),
  v.literal("welcome_tenant"),
  v.literal("scheduled_reminder")
);

// Create automated notification trigger
export const createNotificationTrigger = mutation({
  args: {
    name: v.string(),
    type: triggerTypes,
    propertyId: v.optional(v.id("properties")),
    conditions: v.object({
      entityType: v.union(v.literal("invoice"), v.literal("lease"), v.literal("maintenance"), v.literal("user")),
      triggerEvent: v.string(), // e.g., "status_change", "date_reached", "created"
      conditionExpression: v.string(), // JSON string with condition logic
    }),
    notificationConfig: v.object({
      channels: v.array(v.union(v.literal("sms"), v.literal("whatsapp"), v.literal("email"), v.literal("in_app"))),
      templateId: v.optional(v.id("messageTemplates")),
      customMessage: v.optional(v.string()),
      priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("urgent")),
      respectQuietHours: v.boolean(),
    }),
    timing: v.object({
      delay: v.optional(v.number()), // Minutes to delay after trigger
      schedule: v.optional(v.object({
        frequency: v.union(v.literal("once"), v.literal("daily"), v.literal("weekly"), v.literal("monthly")),
        interval: v.optional(v.number()),
        endCondition: v.optional(v.string()),
      })),
    }),
    isActive: v.boolean(),
    createdBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("notificationTriggers", {
      ...args,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  },
});

// Get notification triggers
export const getNotificationTriggers = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    type: v.optional(triggerTypes),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("notificationTriggers");

    if (args.propertyId) {
      query = query.filter(q => q.eq(q.field("propertyId"), args.propertyId));
    }

    if (args.type) {
      query = query.filter(q => q.eq(q.field("type"), args.type));
    }

    if (args.isActive !== undefined) {
      query = query.filter(q => q.eq(q.field("isActive"), args.isActive));
    }

    return await query.order("desc").collect();
  },
});

// Update notification trigger
export const updateNotificationTrigger = mutation({
  args: {
    triggerId: v.id("notificationTriggers"),
    name: v.optional(v.string()),
    conditions: v.optional(v.object({
      entityType: v.union(v.literal("invoice"), v.literal("lease"), v.literal("maintenance"), v.literal("user")),
      triggerEvent: v.string(),
      conditionExpression: v.string(),
    })),
    notificationConfig: v.optional(v.object({
      channels: v.array(v.union(v.literal("sms"), v.literal("whatsapp"), v.literal("email"), v.literal("in_app"))),
      templateId: v.optional(v.id("messageTemplates")),
      customMessage: v.optional(v.string()),
      priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("urgent")),
      respectQuietHours: v.boolean(),
    })),
    timing: v.optional(v.object({
      delay: v.optional(v.number()),
      schedule: v.optional(v.object({
        frequency: v.union(v.literal("once"), v.literal("daily"), v.literal("weekly"), v.literal("monthly")),
        interval: v.optional(v.number()),
        endCondition: v.optional(v.string()),
      })),
    })),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { triggerId, ...updates } = args;
    return await ctx.db.patch(triggerId, {
      ...updates,
      updatedAt: Date.now(),
    });
  },
});

// Delete notification trigger
export const deleteNotificationTrigger = mutation({
  args: { triggerId: v.id("notificationTriggers") },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.triggerId);
  },
});

// Process notification triggers (called by scheduled functions)
export const processNotificationTriggers = action({
  args: {
    entityType: v.union(v.literal("invoice"), v.literal("lease"), v.literal("maintenance"), v.literal("user")),
    entityId: v.string(),
    event: v.string(),
    eventData: v.optional(v.record(v.string(), v.any())),
  },
  handler: async (ctx: ActionCtx, args) => {
    // Get all active triggers for this entity type and event
    const triggers: Doc<"notificationTriggers">[] = await ctx.runQuery(api.notificationTriggers.getNotificationTriggers, {
      isActive: true,
    });

    const relevantTriggers: Doc<"notificationTriggers">[] = triggers.filter((trigger: Doc<"notificationTriggers">) =>
      trigger.conditions.entityType === args.entityType &&
      trigger.conditions.triggerEvent === args.event
    );

    const results: Array<{
      triggerId: Id<"notificationTriggers">;
      notificationId?: Id<"notifications">[];
      scheduledFor?: number;
      success: boolean;
      error?: string;
    }> = [];

    for (const trigger of relevantTriggers) {
      try {
        // Evaluate trigger conditions
        const shouldTrigger: boolean = await evaluateTriggerConditions(
          trigger.conditions.conditionExpression,
          args.entityId,
          args.eventData || {}
        );

        if (shouldTrigger) {
          // Create scheduled notification
          const scheduledFor: number = Date.now() + (trigger.timing.delay || 0) * 60 * 1000;

          const notificationId: Id<"notifications">[] = await ctx.runMutation(internal.notificationTriggers.createTriggeredNotification, {
            triggerId: trigger._id,
            entityType: args.entityType,
            entityId: args.entityId,
            scheduledFor,
            eventData: args.eventData,
          });

          results.push({
            triggerId: trigger._id,
            notificationId,
            scheduledFor,
            success: true,
          });
        }
      } catch (error: unknown) {
        results.push({
          triggerId: trigger._id,
          success: false,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    return results;
  },
});

// Create triggered notification (internal)
export const createTriggeredNotification = internalMutation({
  args: {
    triggerId: v.id("notificationTriggers"),
    entityType: v.union(v.literal("invoice"), v.literal("lease"), v.literal("maintenance"), v.literal("user")),
    entityId: v.string(),
    scheduledFor: v.number(),
    eventData: v.optional(v.record(v.string(), v.any())),
  },
  handler: async (ctx, args) => {
    // Get trigger details
    const trigger = await ctx.db.get(args.triggerId);
    if (!trigger) {
      throw new Error("Trigger not found");
    }

    // Get entity details to determine recipient
    let recipientId: Id<"users"> | undefined;

    switch (args.entityType) {
      case "invoice":
        const invoice = await ctx.db.get(args.entityId as Id<"invoices">);
        if (invoice) {
          recipientId = invoice.tenantId;
        }
        break;
      case "lease":
        const lease = await ctx.db.get(args.entityId as Id<"leases">);
        if (lease) {
          recipientId = lease.tenantId;
        }
        break;
      case "maintenance":
        const ticket = await ctx.db.get(args.entityId as Id<"maintenanceTickets">);
        if (ticket) {
          recipientId = ticket.tenantId;
        }
        break;
      case "user":
        recipientId = args.entityId as Id<"users">;
        break;
    }

    if (!recipientId) {
      throw new Error("Could not determine notification recipient");
    }

    // Create notification based on trigger type
    let notificationType: string;
    switch (trigger.type) {
      case "payment_due":
      case "payment_overdue":
        notificationType = "payment_reminder";
        break;
      case "maintenance_assigned":
        notificationType = "maintenance_assigned";
        break;
      case "maintenance_escalated":
        notificationType = "maintenance_escalated";
        break;
      case "maintenance_completed":
        notificationType = "maintenance_completed";
        break;
      case "sla_warning":
        notificationType = "sla_warning";
        break;
      case "lease_expiry":
        notificationType = "lease_expiry";
        break;
      default:
        notificationType = "system";
    }

    const notificationId = await ctx.db.insert("notifications", {
      userId: recipientId,
      title: `Notification: ${trigger.name}`,
      message: trigger.notificationConfig.customMessage || `${trigger.name} notification`,
      type: notificationType as any,
      priority: trigger.notificationConfig.priority,
      isRead: false,
      metadata: {
        invoiceId: args.entityType === "invoice" ? args.entityId as Id<"invoices"> : undefined,
        ticketId: args.entityType === "maintenance" ? args.entityId as Id<"maintenanceTickets"> : undefined,
        leaseId: args.entityType === "lease" ? args.entityId as Id<"leases"> : undefined,
      },
      scheduledFor: args.scheduledFor,
      createdAt: Date.now(),
    });

    return [notificationId];
  },
});

// Process scheduled notifications (called by cron job)
export const processScheduledNotifications = action({
  args: {},
  handler: async (ctx: ActionCtx) => {
    const now: number = Date.now();

    // Get all scheduled notifications that are due
    const dueNotifications: Doc<"notifications">[] = await ctx.runQuery(api.notificationTriggers.getDueNotifications, {
      beforeTime: now,
    });

    const results: Array<{
      notificationId: Id<"notifications">;
      success: boolean;
      error?: string;
    }> = [];

    for (const notification of dueNotifications) {
      try {
        // Validate notification has required fields
        if (!notification.userId || !notification.message) {
          console.warn("Skipping invalid notification:", notification._id);
          continue;
        }

        // Check user's notification preferences
        const preferences = await ctx.runQuery(api.communications.getNotificationPreferences, {
          userId: notification.userId,
        });

        // Check if this type of notification is enabled for in-app notifications
        const inAppPrefs = preferences.preferences.inApp;
        if (!inAppPrefs || !inAppPrefs.enabled) {
          // Delete notification due to user preferences
          await ctx.runMutation(internal.notificationTriggers.deleteNotification, {
            notificationId: notification._id,
          });
          continue;
        }

        // Check quiet hours (only applies to external notifications, not in-app)
        if (preferences.quietHours.enabled) {
          const isQuietHour: boolean = await checkQuietHours(preferences.quietHours, now);
          if (isQuietHour) {
            // Reschedule for after quiet hours
            const nextScheduledTime: number = await calculateNextScheduledTime(preferences.quietHours, now);
            await ctx.runMutation(internal.notificationTriggers.rescheduleNotification, {
              notificationId: notification._id,
              newScheduledTime: nextScheduledTime,
            });
            continue;
          }
        }

        // For in-app notifications, just mark as sent since they're already created
        const sendResult = await sendInAppNotification(ctx, notification);

        // Update notification status
        if (sendResult.success) {
          await ctx.runMutation(internal.notificationTriggers.updateScheduledNotificationStatus, {
            notificationId: notification._id,
            sentAt: now,
          });
        }

        results.push({
          notificationId: notification._id,
          success: sendResult.success,
          error: sendResult.error,
        });

      } catch (error: unknown) {
        // For failed notifications, we could either delete them or leave them for retry
        // For now, we'll leave them as is

        results.push({
          notificationId: notification._id,
          success: false,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    return {
      processed: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results,
    };
  },
});

// Get due notifications
export const getDueNotifications = query({
  args: { beforeTime: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("notifications")
      .filter(q =>
        q.and(
          q.neq(q.field("scheduledFor"), undefined),
          q.lte(q.field("scheduledFor"), args.beforeTime),
          q.eq(q.field("sentAt"), undefined)
        )
      )
      .collect();
  },
});

// Update scheduled notification status (internal)
export const updateScheduledNotificationStatus = internalMutation({
  args: {
    notificationId: v.id("notifications"),
    sentAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { notificationId, sentAt } = args;
    return await ctx.db.patch(notificationId, {
      sentAt,
    });
  },
});

// Reschedule notification (internal)
export const rescheduleNotification = internalMutation({
  args: {
    notificationId: v.id("notifications"),
    newScheduledTime: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.notificationId, {
      scheduledFor: args.newScheduledTime,
    });
  },
});

// Delete notification (internal)
export const deleteNotification = internalMutation({
  args: {
    notificationId: v.id("notifications"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.notificationId);
  },
});

// Get user (internal)
export const getUser = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.userId);
  },
});

// Helper functions
async function evaluateTriggerConditions(
  conditionExpression: string,
  _entityId: string,
  eventData: Record<string, any>
): Promise<boolean> {
  try {
    // Parse condition expression (simplified JSON-based conditions)
    const conditions = JSON.parse(conditionExpression);

    // Basic condition evaluation logic
    // This would be expanded based on specific business rules
    if (conditions.field && conditions.operator && conditions.value !== undefined) {
      const fieldValue = eventData[conditions.field];

      switch (conditions.operator) {
        case "equals":
          return fieldValue === conditions.value;
        case "not_equals":
          return fieldValue !== conditions.value;
        case "greater_than":
          return fieldValue > conditions.value;
        case "less_than":
          return fieldValue < conditions.value;
        case "contains":
          return String(fieldValue).includes(String(conditions.value));
        default:
          return false;
      }
    }

    return true; // Default to true if no specific conditions
  } catch (error: unknown) {
    console.error("Error evaluating trigger conditions:", error);
    return false;
  }
}

async function checkQuietHours(quietHours: any, currentTime: number): Promise<boolean> {
  if (!quietHours.enabled) return false;

  const now = new Date(currentTime);
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  const currentTimeMinutes = currentHour * 60 + currentMinute;

  const [startHour, startMinute] = quietHours.startTime.split(':').map(Number);
  const [endHour, endMinute] = quietHours.endTime.split(':').map(Number);

  const startTimeMinutes = startHour * 60 + startMinute;
  const endTimeMinutes = endHour * 60 + endMinute;

  // Handle overnight quiet hours (e.g., 22:00 to 08:00)
  if (startTimeMinutes > endTimeMinutes) {
    return currentTimeMinutes >= startTimeMinutes || currentTimeMinutes <= endTimeMinutes;
  } else {
    return currentTimeMinutes >= startTimeMinutes && currentTimeMinutes <= endTimeMinutes;
  }
}

async function calculateNextScheduledTime(quietHours: any, currentTime: number): Promise<number> {
  const now = new Date(currentTime);
  const [endHour, endMinute] = quietHours.endTime.split(':').map(Number);

  const nextScheduled = new Date(now);
  nextScheduled.setHours(endHour, endMinute, 0, 0);

  // If end time is today but already passed, schedule for tomorrow
  if (nextScheduled <= now) {
    nextScheduled.setDate(nextScheduled.getDate() + 1);
  }

  return nextScheduled.getTime();
}

async function sendInAppNotification(ctx: ActionCtx, notification: Doc<"notifications">): Promise<{ success: boolean; error?: string; result?: any }> {
  try {
    // The notification is already created, just mark it as sent
    await ctx.runMutation(internal.notificationTriggers.updateScheduledNotificationStatus, {
      notificationId: notification._id,
      sentAt: Date.now(),
    });

    return { success: true, result: { notificationId: notification._id } };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}