import { contextBridge, ipcRenderer } from 'electron'

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getVersion: () => ipcRenderer.invoke('app-version'),
  getPlatform: () => ipcRenderer.invoke('platform'),
  
  // File system access
  showOpenDialog: (options: any) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options: any) => ipcRenderer.invoke('show-save-dialog', options),
  readFile: (filePath: string) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath: string, content: string) => ipcRenderer.invoke('write-file', filePath, content),
  
  // System notifications
  showNotification: (options: { title: string; body: string; icon?: string }) => 
    ipcRenderer.invoke('show-notification', options),
  
  // Print functionality
  printDocument: (html: string, options?: any) => ipcRenderer.invoke('print-document', html, options),
  printToPDF: (html: string, options?: any) => ipcRenderer.invoke('print-to-pdf', html, options),
  
  // Window management
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  hideWindow: () => ipcRenderer.invoke('hide-window'),
  showWindow: () => ipcRenderer.invoke('show-window'),
  
  // Listen to main process messages
  onMainMessage: (callback: (message: string) => void) => {
    ipcRenderer.on('main-process-message', (_event, message) => callback(message))
  },
  
  // Listen to menu actions
  onMenuAction: (callback: (action: string) => void) => {
    ipcRenderer.on('menu-action', (_event, action) => callback(action))
  },
  
  // Auto-updater
  checkForUpdates: () => ipcRenderer.invoke('check-for-updates'),
  downloadUpdate: () => ipcRenderer.invoke('download-update'),
  installUpdate: () => ipcRenderer.invoke('install-update'),
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  setAutoLaunch: (enable: boolean) => ipcRenderer.invoke('set-auto-launch', enable),
  getAutoLaunch: () => ipcRenderer.invoke('get-auto-launch'),
  reportCrash: (crashData: any) => ipcRenderer.invoke('report-crash', crashData),
  
  // Update status listeners
  onUpdateStatus: (callback: (status: any) => void) => {
    ipcRenderer.on('update-status', (_event, status) => callback(status))
  },
  
  onUpdateAction: (callback: (action: string) => void) => {
    ipcRenderer.on('update-action', (_event, action) => callback(action))
  },
  
  // Remove listeners
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel)
  }
})

// Types for the exposed API
export interface ElectronAPI {
  // App info
  getVersion: () => Promise<string>
  getPlatform: () => Promise<string>
  
  // File system access
  showOpenDialog: (options: any) => Promise<{ canceled: boolean; filePaths: string[] }>
  showSaveDialog: (options: any) => Promise<{ canceled: boolean; filePath?: string }>
  readFile: (filePath: string) => Promise<{ success: boolean; content?: string; error?: string }>
  writeFile: (filePath: string, content: string) => Promise<{ success: boolean; error?: string }>
  
  // System notifications
  showNotification: (options: { title: string; body: string; icon?: string }) => Promise<boolean>
  
  // Print functionality
  printDocument: (html: string, options?: any) => Promise<{ success: boolean; result?: any; error?: string }>
  printToPDF: (html: string, options?: any) => Promise<{ success: boolean; buffer?: number[]; error?: string }>
  
  // Window management
  minimizeWindow: () => Promise<void>
  maximizeWindow: () => Promise<void>
  closeWindow: () => Promise<void>
  hideWindow: () => Promise<void>
  showWindow: () => Promise<void>
  
  // Auto-updater
  checkForUpdates: () => Promise<{ success: boolean; updateInfo?: any; error?: string }>
  downloadUpdate: () => Promise<{ success: boolean; error?: string }>
  installUpdate: () => Promise<void>
  getAppVersion: () => Promise<string>
  setAutoLaunch: (enable: boolean) => Promise<{ success: boolean; error?: string }>
  getAutoLaunch: () => Promise<boolean>
  reportCrash: (crashData: any) => Promise<{ success: boolean }>
  
  // Event listeners
  onMainMessage: (callback: (message: string) => void) => void
  onMenuAction: (callback: (action: string) => void) => void
  onUpdateStatus: (callback: (status: any) => void) => void
  onUpdateAction: (callback: (action: string) => void) => void
  removeAllListeners: (channel: string) => void
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}