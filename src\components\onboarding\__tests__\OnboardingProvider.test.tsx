/**
 * Tests for OnboardingProvider
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { OnboardingProvider, useOnboarding, OnboardingFlow } from '../OnboardingProvider';
import { AccessibilityProvider } from '../../../contexts/AccessibilityContext';

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <AccessibilityProvider>
    <OnboardingProvider>
      {children}
    </OnboardingProvider>
  </AccessibilityProvider>
);

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

const testFlow: OnboardingFlow = {
  id: 'test-flow',
  name: 'Test Flow',
  description: 'A test onboarding flow',
  completionKey: 'test-flow-completed',
  steps: [
    {
      id: 'step1',
      title: 'Step 1',
      content: 'This is step 1',
      position: 'center',
    },
    {
      id: 'step2',
      title: 'Step 2',
      content: 'This is step 2',
      position: 'center',
    },
  ],
};

// Test component that uses the onboarding context
const TestComponent = () => {
  const {
    currentFlow,
    currentStep,
    isActive,
    startFlow,
    nextStep,
    previousStep,
    completeFlow,
    dismissFlow,
    registerFlow,
    isFlowCompleted,
  } = useOnboarding();

  React.useEffect(() => {
    registerFlow(testFlow);
  }, [registerFlow]);

  return (
    <div>
      <div data-testid="is-active">{isActive.toString()}</div>
      <div data-testid="current-step">{currentStep}</div>
      <div data-testid="current-flow">{currentFlow?.name || 'none'}</div>
      <div data-testid="is-completed">{isFlowCompleted('test-flow').toString()}</div>
      
      <button onClick={() => startFlow('test-flow')}>Start Flow</button>
      <button onClick={nextStep}>Next Step</button>
      <button onClick={previousStep}>Previous Step</button>
      <button onClick={completeFlow}>Complete Flow</button>
      <button onClick={dismissFlow}>Dismiss Flow</button>
    </div>
  );
};

describe('OnboardingProvider', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  it('provides default state', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('is-active')).toHaveTextContent('false');
    expect(screen.getByTestId('current-step')).toHaveTextContent('0');
    expect(screen.getByTestId('current-flow')).toHaveTextContent('none');
    expect(screen.getByTestId('is-completed')).toHaveTextContent('false');
  });

  it('starts a flow correctly', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    fireEvent.click(screen.getByText('Start Flow'));

    expect(screen.getByTestId('is-active')).toHaveTextContent('true');
    expect(screen.getByTestId('current-step')).toHaveTextContent('0');
    expect(screen.getByTestId('current-flow')).toHaveTextContent('Test Flow');
  });

  it('navigates through steps', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    fireEvent.click(screen.getByText('Start Flow'));
    expect(screen.getByTestId('current-step')).toHaveTextContent('0');

    fireEvent.click(screen.getByText('Next Step'));
    expect(screen.getByTestId('current-step')).toHaveTextContent('1');

    fireEvent.click(screen.getByText('Previous Step'));
    expect(screen.getByTestId('current-step')).toHaveTextContent('0');
  });

  it('completes a flow', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    fireEvent.click(screen.getByText('Start Flow'));
    fireEvent.click(screen.getByText('Complete Flow'));

    expect(screen.getByTestId('is-active')).toHaveTextContent('false');
    expect(screen.getByTestId('current-flow')).toHaveTextContent('none');
    expect(screen.getByTestId('is-completed')).toHaveTextContent('true');
  });

  it('dismisses a flow', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    fireEvent.click(screen.getByText('Start Flow'));
    fireEvent.click(screen.getByText('Dismiss Flow'));

    expect(screen.getByTestId('is-active')).toHaveTextContent('false');
    expect(screen.getByTestId('current-flow')).toHaveTextContent('none');
    expect(screen.getByTestId('is-completed')).toHaveTextContent('false');
  });

  it('persists completion status', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    fireEvent.click(screen.getByText('Start Flow'));
    fireEvent.click(screen.getByText('Complete Flow'));

    // Check localStorage
    expect(localStorage.getItem('test-flow-completed')).toBe('true');
  });

  it('prevents starting completed flows', () => {
    localStorage.setItem('test-flow-completed', 'true');

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    fireEvent.click(screen.getByText('Start Flow'));

    // Should not start because it's already completed
    expect(screen.getByTestId('is-active')).toHaveTextContent('false');
  });
});