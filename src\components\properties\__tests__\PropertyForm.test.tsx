import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { PropertyForm } from '../PropertyForm';

// Mock Convex hooks
vi.mock('convex/react', () => ({
  useMutation: vi.fn(() => vi.fn()),
  useQuery: vi.fn(() => null),
}));

// Mock auth context
vi.mock('../../../lib/auth-context', () => ({
  useAuth: vi.fn(() => ({
    user: {
      _id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'owner',
    },
  })),
}));

describe('PropertyForm', () => {
  it('renders property form with required fields', () => {
    render(<PropertyForm />);
    
    expect(screen.getByLabelText(/property name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/property type/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/street address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/city/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/state\/region/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/postal code/i)).toBeInTheDocument();
  });

  it('shows validation errors for empty required fields', async () => {
    render(<PropertyForm />);
    
    const submitButton = screen.getByRole('button', { name: /create property/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/property name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/street address is required/i)).toBeInTheDocument();
      expect(screen.getByText(/city is required/i)).toBeInTheDocument();
    });
  });

  it('allows filling out the form', () => {
    render(<PropertyForm />);
    
    const nameInput = screen.getByLabelText(/property name/i);
    const streetInput = screen.getByLabelText(/street address/i);
    const cityInput = screen.getByLabelText(/city/i);
    
    fireEvent.change(nameInput, { target: { value: 'Test Property' } });
    fireEvent.change(streetInput, { target: { value: '123 Test Street' } });
    fireEvent.change(cityInput, { target: { value: 'Nairobi' } });
    
    expect(nameInput).toHaveValue('Test Property');
    expect(streetInput).toHaveValue('123 Test Street');
    expect(cityInput).toHaveValue('Nairobi');
  });
});