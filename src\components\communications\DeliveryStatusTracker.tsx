import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  MessageSquare, 
  CheckCircle, 
  XCircle, 
  Clock,
  BarChart3,
  Calendar,
  Filter
} from 'lucide-react';

interface DeliveryStatusTrackerProps {
  propertyId?: Id<"properties">;
}

export function DeliveryStatusTracker({ propertyId }: DeliveryStatusTrackerProps) {
  const [dateRange, setDateRange] = useState('7d');
  const [messageType, setMessageType] = useState<'all' | 'sms' | 'whatsapp'>('all');
  
  // Calculate date range
  const getDateRange = () => {
    const end = Date.now();
    let start = end;
    
    switch (dateRange) {
      case '1d':
        start = end - (24 * 60 * 60 * 1000);
        break;
      case '7d':
        start = end - (7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        start = end - (30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        start = end - (90 * 24 * 60 * 60 * 1000);
        break;
    }
    
    return { start, end };
  };

  const { start, end } = getDateRange();
  
  const deliveryReport = useQuery(api.communications.getDeliveryReport, {
    propertyId,
    startDate: start,
    endDate: end,
    type: messageType === 'all' ? undefined : messageType,
  });

  const bulkMessages = useQuery(api.communications.getBulkMessages, {
    propertyId,
    limit: 10,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered':
      case 'sent':
        return <CheckCircle className="h-4 w-4" />;
      case 'failed':
        return <XCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  // Prepare chart data
  const pieChartData = deliveryReport ? [
    { name: 'Delivered', value: deliveryReport.summary.deliveredMessages, color: '#10B981' },
    { name: 'Sent', value: deliveryReport.summary.sentMessages - deliveryReport.summary.deliveredMessages, color: '#3B82F6' },
    { name: 'Failed', value: deliveryReport.summary.failedMessages, color: '#EF4444' },
    { name: 'Pending', value: deliveryReport.summary.pendingMessages, color: '#F59E0B' },
  ].filter(item => item.value > 0) : [];

  const dailyChartData = deliveryReport ? Object.entries(deliveryReport.byDate).map(([date, data]: [string, any]) => ({
    date: new Date(date).toLocaleDateString(),
    total: data.total,
    delivered: data.delivered,
    failed: data.failed,
  })).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()) : [];

  const typeChartData = deliveryReport ? Object.entries(deliveryReport.byType).map(([type, data]: [string, any]) => ({
    type: type.toUpperCase(),
    total: data.total,
    delivered: data.delivered,
    failed: data.failed,
    deliveryRate: data.total > 0 ? (data.delivered / data.total) * 100 : 0,
  })) : [];

  if (!deliveryReport) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading delivery report...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <BarChart3 className="h-6 w-6" />
          <h2 className="text-2xl font-bold">Delivery Status Tracker</h2>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4" />
            <Select value={messageType} onValueChange={(value: 'all' | 'sms' | 'whatsapp') => setMessageType(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="sms">SMS</SelectItem>
                <SelectItem value="whatsapp">WhatsApp</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1d">Last 24h</SelectItem>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Messages</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{deliveryReport.summary.totalMessages}</div>
            <p className="text-xs text-muted-foreground">
              {dateRange === '1d' ? 'in last 24 hours' : `in last ${dateRange}`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivery Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {deliveryReport.summary.deliveryRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              {deliveryReport.summary.deliveredMessages} delivered
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {deliveryReport.summary.successRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              {deliveryReport.summary.sentMessages} sent successfully
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed Messages</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {deliveryReport.summary.failedMessages}
            </div>
            <p className="text-xs text-muted-foreground">
              {deliveryReport.summary.totalMessages > 0 
                ? ((deliveryReport.summary.failedMessages / deliveryReport.summary.totalMessages) * 100).toFixed(1)
                : 0}% failure rate
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Messages</TabsTrigger>
          <TabsTrigger value="recent">Recent Messages</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Message Status Distribution</CardTitle>
                <CardDescription>Breakdown of message statuses</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={pieChartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {pieChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Type Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Performance by Type</CardTitle>
                <CardDescription>Delivery rates by message type</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={typeChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="type" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="delivered" fill="#10B981" name="Delivered" />
                    <Bar dataKey="failed" fill="#EF4444" name="Failed" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Daily Message Trends</CardTitle>
              <CardDescription>Message volume and delivery trends over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={dailyChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="total" stroke="#8884d8" name="Total Messages" />
                  <Line type="monotone" dataKey="delivered" stroke="#10B981" name="Delivered" />
                  <Line type="monotone" dataKey="failed" stroke="#EF4444" name="Failed" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Message History</CardTitle>
              <CardDescription>Recent bulk message campaigns</CardDescription>
            </CardHeader>
            <CardContent>
              {bulkMessages && bulkMessages.length > 0 ? (
                <div className="space-y-4">
                  {bulkMessages.map((bulk) => (
                    <div key={bulk._id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="font-medium">{bulk.name}</h4>
                            <Badge className={getStatusColor(bulk.status)}>
                              {bulk.status}
                            </Badge>
                          </div>
                          <div className="text-sm text-gray-600 space-y-1">
                            <p>Type: {bulk.type.toUpperCase()}</p>
                            <p>Recipients: {bulk.totalRecipients}</p>
                            <p>Sent: {bulk.sentCount} | Failed: {bulk.failedCount}</p>
                            {bulk.completedAt && (
                              <p>Completed: {new Date(bulk.completedAt).toLocaleString()}</p>
                            )}
                          </div>
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(bulk.createdAt).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No bulk messages found
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Messages</CardTitle>
              <CardDescription>Latest individual messages</CardDescription>
            </CardHeader>
            <CardContent>
              {deliveryReport.recentMessages.length > 0 ? (
                <div className="space-y-3">
                  {deliveryReport.recentMessages.map((message) => (
                    <div key={message._id} className="border rounded-lg p-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <Badge variant="outline" className="uppercase">
                              {message.type}
                            </Badge>
                            <Badge className={getStatusColor(message.status)}>
                              <div className="flex items-center space-x-1">
                                {getStatusIcon(message.status)}
                                <span>{message.status}</span>
                              </div>
                            </Badge>
                          </div>
                          <div className="text-sm text-gray-600 mb-1">
                            To: {message.recipient}
                          </div>
                          <div className="text-sm">
                            {message.content.length > 100 
                              ? `${message.content.substring(0, 100)}...` 
                              : message.content}
                          </div>
                          {message.error && (
                            <div className="text-sm text-red-600 mt-1">
                              Error: {message.error}
                            </div>
                          )}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(message.createdAt).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No recent messages found
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}