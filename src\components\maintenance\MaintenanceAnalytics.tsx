import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Clock, 
  AlertTriangle, 
  CheckCircle,
  Users,
  Calendar,
  Download
} from 'lucide-react';
import { format, subDays, subMonths, startOfMonth, endOfMonth } from 'date-fns';

interface MaintenanceAnalyticsProps {
  propertyId: Id<"properties">;
}

export const MaintenanceAnalytics: React.FC<MaintenanceAnalyticsProps> = ({ propertyId }) => {
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Calculate date range
  const getDateRange = () => {
    const now = new Date();
    switch (timeRange) {
      case '7d':
        return { startDate: subDays(now, 7).getTime(), endDate: now.getTime() };
      case '30d':
        return { startDate: subDays(now, 30).getTime(), endDate: now.getTime() };
      case '90d':
        return { startDate: subDays(now, 90).getTime(), endDate: now.getTime() };
      case '6m':
        return { startDate: subMonths(now, 6).getTime(), endDate: now.getTime() };
      case '1y':
        return { startDate: subMonths(now, 12).getTime(), endDate: now.getTime() };
      case 'mtd':
        return { startDate: startOfMonth(now).getTime(), endDate: now.getTime() };
      default:
        return { startDate: subDays(now, 30).getTime(), endDate: now.getTime() };
    }
  };

  const dateRange = getDateRange();

  // Fetch analytics data
  const maintenanceAnalytics = useQuery(api.maintenance.getMaintenanceAnalytics, {
    propertyId,
    startDate: dateRange.startDate,
    endDate: dateRange.endDate,
    category: selectedCategory !== 'all' ? selectedCategory as any : undefined
  });

  const costAnalytics = useQuery(api.maintenance.getCostAnalytics, {
    propertyId,
    startDate: dateRange.startDate,
    endDate: dateRange.endDate
  });

  const vendorPerformance = useQuery(api.maintenance.getVendorPerformanceAnalytics, {
    propertyId,
    startDate: dateRange.startDate,
    endDate: dateRange.endDate
  });

  const trendData = useQuery(api.maintenance.getMaintenanceTrends, {
    propertyId,
    startDate: dateRange.startDate,
    endDate: dateRange.endDate,
    interval: timeRange === '7d' ? 'daily' : timeRange === '30d' ? 'daily' : 'weekly'
  });

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'plumbing', label: 'Plumbing' },
    { value: 'electrical', label: 'Electrical' },
    { value: 'hvac', label: 'HVAC' },
    { value: 'appliance', label: 'Appliance' },
    { value: 'structural', label: 'Structural' },
    { value: 'cleaning', label: 'Cleaning' },
    { value: 'security', label: 'Security' },
    { value: 'other', label: 'Other' }
  ];

  const timeRanges = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: 'mtd', label: 'Month to date' },
    { value: '6m', label: 'Last 6 months' },
    { value: '1y', label: 'Last year' }
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#FF7C7C'];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) {
      return <TrendingUp className="h-4 w-4 text-green-600" />;
    } else if (current < previous) {
      return <TrendingDown className="h-4 w-4 text-red-600" />;
    }
    return <div className="h-4 w-4" />;
  };

  const getTrendPercentage = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const exportData = () => {
    // In a real implementation, this would generate and download a report
    console.log('Exporting maintenance analytics data...');
  };

  if (!maintenanceAnalytics || !costAnalytics || !vendorPerformance || !trendData) {
    return (
      <div className="space-y-6">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Filters */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Maintenance Analytics</h1>
          <p className="text-muted-foreground">
            Comprehensive maintenance performance and cost analysis
          </p>
        </div>
        
        <div className="flex gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {timeRanges.map((range) => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button onClick={exportData} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tickets</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{maintenanceAnalytics.totalTickets}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getTrendIcon(maintenanceAnalytics.totalTickets, maintenanceAnalytics.previousPeriod.totalTickets)}
              <span className="ml-1">
                {getTrendPercentage(maintenanceAnalytics.totalTickets, maintenanceAnalytics.previousPeriod.totalTickets).toFixed(1)}% from last period
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(costAnalytics.totalCost)}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getTrendIcon(costAnalytics.totalCost, costAnalytics.previousPeriod.totalCost)}
              <span className="ml-1">
                {getTrendPercentage(costAnalytics.totalCost, costAnalytics.previousPeriod.totalCost).toFixed(1)}% from last period
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Resolution Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{maintenanceAnalytics.averageResolutionTime.toFixed(1)}h</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getTrendIcon(maintenanceAnalytics.previousPeriod.averageResolutionTime, maintenanceAnalytics.averageResolutionTime)}
              <span className="ml-1">
                {getTrendPercentage(maintenanceAnalytics.previousPeriod.averageResolutionTime, maintenanceAnalytics.averageResolutionTime).toFixed(1)}% from last period
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SLA Compliance</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{maintenanceAnalytics.slaCompliance.toFixed(1)}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getTrendIcon(maintenanceAnalytics.slaCompliance, maintenanceAnalytics.previousPeriod.slaCompliance)}
              <span className="ml-1">
                {getTrendPercentage(maintenanceAnalytics.slaCompliance, maintenanceAnalytics.previousPeriod.slaCompliance).toFixed(1)}% from last period
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="costs">Cost Analysis</TabsTrigger>
          <TabsTrigger value="vendors">Vendor Performance</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Tickets by Category */}
            <Card>
              <CardHeader>
                <CardTitle>Tickets by Category</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={maintenanceAnalytics.ticketsByCategory}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {maintenanceAnalytics.ticketsByCategory.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Tickets by Priority */}
            <Card>
              <CardHeader>
                <CardTitle>Tickets by Priority</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={maintenanceAnalytics.ticketsByPriority}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="priority" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Resolution Time by Category */}
            <Card>
              <CardHeader>
                <CardTitle>Avg Resolution Time by Category</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={maintenanceAnalytics.resolutionTimeByCategory}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`${value} hours`, 'Resolution Time']} />
                    <Bar dataKey="averageTime" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* SLA Compliance by Priority */}
            <Card>
              <CardHeader>
                <CardTitle>SLA Compliance by Priority</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {maintenanceAnalytics.slaComplianceByPriority.map((item) => (
                    <div key={item.priority} className="space-y-2">
                      <div className="flex justify-between">
                        <span className="capitalize font-medium">{item.priority}</span>
                        <span>{item.compliance.toFixed(1)}%</span>
                      </div>
                      <Progress value={item.compliance} />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="costs" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Cost Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Cost Breakdown by Category</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={costAnalytics.costByCategory}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="cost"
                    >
                      {costAnalytics.costByCategory.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Cost vs Budget */}
            <Card>
              <CardHeader>
                <CardTitle>Cost vs Budget</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Total Spent</span>
                    <span className="font-bold">{formatCurrency(costAnalytics.totalCost)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Budget</span>
                    <span className="font-bold">{formatCurrency(costAnalytics.budget || 0)}</span>
                  </div>
                  <Progress 
                    value={costAnalytics.budget ? (costAnalytics.totalCost / costAnalytics.budget) * 100 : 0} 
                    className="h-3"
                  />
                  <div className="text-sm text-muted-foreground">
                    {costAnalytics.budget ? 
                      `${((costAnalytics.totalCost / costAnalytics.budget) * 100).toFixed(1)}% of budget used` :
                      'No budget set'
                    }
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Average Cost by Priority */}
            <Card>
              <CardHeader>
                <CardTitle>Average Cost by Priority</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={costAnalytics.averageCostByPriority}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="priority" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Bar dataKey="averageCost" fill="#ffc658" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Cost Efficiency Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Cost Efficiency</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Cost per Ticket</span>
                    <span className="font-bold">
                      {formatCurrency(costAnalytics.totalCost / maintenanceAnalytics.totalTickets)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cost per Resolved Ticket</span>
                    <span className="font-bold">
                      {formatCurrency(costAnalytics.totalCost / maintenanceAnalytics.completedTickets)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Emergency vs Regular Cost Ratio</span>
                    <span className="font-bold">
                      {(costAnalytics.emergencyCostRatio * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="vendors" className="space-y-4">
          <div className="grid grid-cols-1 gap-6">
            {/* Top Performing Vendors */}
            <Card>
              <CardHeader>
                <CardTitle>Vendor Performance Ranking</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {vendorPerformance.topVendors.map((vendor, index) => (
                    <div key={vendor.vendorId} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center font-bold text-blue-600">
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="font-semibold">{vendor.companyName}</h4>
                          <p className="text-sm text-muted-foreground">
                            {vendor.completedJobs} jobs completed
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{vendor.averageRating.toFixed(1)} ⭐</Badge>
                          <Badge variant="outline">{vendor.slaCompliance.toFixed(1)}% SLA</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {vendor.averageResponseTime.toFixed(1)}h response time
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Vendor Cost Analysis */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Cost by Vendor</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={vendorPerformance.costByVendor}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="companyName" />
                      <YAxis />
                      <Tooltip formatter={(value) => formatCurrency(value as number)} />
                      <Bar dataKey="totalCost" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Vendor Utilization</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={vendorPerformance.utilizationByVendor}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="jobCount"
                      >
                        {vendorPerformance.utilizationByVendor.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <div className="grid grid-cols-1 gap-6">
            {/* Ticket Volume Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Ticket Volume Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={trendData.ticketVolume}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="count" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Cost Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Cost Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trendData.costTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(value as number)} />
                    <Line type="monotone" dataKey="cost" stroke="#82ca9d" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Resolution Time Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Resolution Time Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trendData.resolutionTimeTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`${value} hours`, 'Avg Resolution Time']} />
                    <Line type="monotone" dataKey="averageTime" stroke="#ffc658" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};