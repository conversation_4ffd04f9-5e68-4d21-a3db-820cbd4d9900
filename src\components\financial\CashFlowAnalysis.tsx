import React, { useState, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  ArrowUpCircle, 
  ArrowDownCircle,
  DollarSign,
  Calendar,
  BarChart3,
  Download
} from 'lucide-react';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts';
import { format } from 'date-fns';
import { Id } from '../../../convex/_generated/dataModel';

interface CashFlowAnalysisProps {
  propertyId?: Id<"properties">;
}

export const CashFlowAnalysis: React.FC<CashFlowAnalysisProps> = ({ propertyId }) => {
  const [months, setMonths] = useState<number>(12);
  const [viewType, setViewType] = useState<'combined' | 'separate'>('combined');

  // Fetch cash flow data
  const cashFlowData = useQuery(api.financialAnalytics.getCashFlowAnalysis, {
    propertyId,
    months,
  });

  if (!cashFlowData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading cash flow analysis...</p>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Prepare chart data
  const chartData = cashFlowData.historicalData.map(month => ({
    name: `${month.monthName.substring(0, 3)} ${month.year}`,
    fullName: `${month.monthName} ${month.year}`,
    cashIn: month.cashIn,
    cashOut: month.cashOut,
    netCashFlow: month.netCashFlow,
    month: month.month,
    year: month.year,
  }));

  // Calculate trends
  const recentMonths = cashFlowData.historicalData.slice(-3);
  const isNetFlowImproving = recentMonths.length >= 2 && 
    recentMonths[recentMonths.length - 1].netCashFlow > recentMonths[0].netCashFlow;

  const avgNetFlow = cashFlowData.summary.averageNetFlow;
  const isHealthy = avgNetFlow > 0;

  // Calculate cash flow velocity (how quickly cash flows in vs out)
  const cashVelocity = cashFlowData.summary.totalCashIn > 0 
    ? (cashFlowData.summary.totalCashOut / cashFlowData.summary.totalCashIn) * 100 
    : 0;

  const handleExport = () => {
    const csvContent = [
      ['Cash Flow Analysis'],
      [`Period: ${months} months`],
      [''],
      ['SUMMARY'],
      ['Total Cash In', formatCurrency(cashFlowData.summary.totalCashIn)],
      ['Total Cash Out', formatCurrency(cashFlowData.summary.totalCashOut)],
      ['Net Cash Flow', formatCurrency(cashFlowData.summary.totalNetFlow)],
      ['Average Monthly Inflow', formatCurrency(cashFlowData.summary.averageMonthlyInflow)],
      ['Average Monthly Outflow', formatCurrency(cashFlowData.summary.averageMonthlyOutflow)],
      [''],
      ['MONTHLY BREAKDOWN'],
      ['Month', 'Cash In', 'Cash Out', 'Net Flow'],
      ...cashFlowData.historicalData.map(month => [
        `${month.monthName} ${month.year}`,
        formatCurrency(month.cashIn),
        formatCurrency(month.cashOut),
        formatCurrency(month.netCashFlow)
      ]),
      [''],
      ['FORECAST'],
      ['Month', 'Projected In', 'Projected Out', 'Projected Net'],
      ...cashFlowData.forecast.map(month => [
        `${month.monthName} ${month.year}`,
        formatCurrency(month.projectedCashIn),
        formatCurrency(month.projectedCashOut),
        formatCurrency(month.projectedNetFlow)
      ]),
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cash-flow-analysis-${months}months.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <BarChart3 className="h-6 w-6" />
            Cash Flow Analysis
          </h1>
          <p className="text-gray-600">
            Track cash inflows and outflows over time
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={months.toString()} onValueChange={(value) => setMonths(parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3">3 Months</SelectItem>
              <SelectItem value="6">6 Months</SelectItem>
              <SelectItem value="12">12 Months</SelectItem>
              <SelectItem value="24">24 Months</SelectItem>
            </SelectContent>
          </Select>

          <Select value={viewType} onValueChange={(value: 'combined' | 'separate') => setViewType(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="combined">Combined</SelectItem>
              <SelectItem value="separate">Separate</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Cash In</CardTitle>
            <ArrowUpCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(cashFlowData.summary.totalCashIn)}
            </div>
            <p className="text-xs text-muted-foreground">
              Avg: {formatCurrency(cashFlowData.summary.averageMonthlyInflow)}/month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Cash Out</CardTitle>
            <ArrowDownCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(cashFlowData.summary.totalCashOut)}
            </div>
            <p className="text-xs text-muted-foreground">
              Avg: {formatCurrency(cashFlowData.summary.averageMonthlyOutflow)}/month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Cash Flow</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${cashFlowData.summary.totalNetFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatCurrency(cashFlowData.summary.totalNetFlow)}
            </div>
            <div className="flex items-center text-xs text-muted-foreground">
              {isNetFlowImproving ? (
                <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1 text-red-600" />
              )}
              <span className={isNetFlowImproving ? 'text-green-600' : 'text-red-600'}>
                {isNetFlowImproving ? 'Improving' : 'Declining'} trend
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cash Health</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Badge variant={isHealthy ? "default" : "destructive"}>
                {isHealthy ? "Healthy" : "Needs Attention"}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Velocity: {formatPercentage(cashVelocity)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Cash Flow Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Cash Flow Trend</CardTitle>
          <CardDescription>
            Monthly cash inflows, outflows, and net cash flow over {months} months
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            {viewType === 'combined' ? (
              <AreaChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis tickFormatter={(value) => formatCurrency(value)} />
                <Tooltip 
                  formatter={(value, name) => [formatCurrency(Number(value)), name]}
                  labelFormatter={(label) => chartData.find(d => d.name === label)?.fullName || label}
                />
                <Area 
                  type="monotone" 
                  dataKey="cashIn" 
                  stackId="1" 
                  stroke="#10b981" 
                  fill="#10b981" 
                  fillOpacity={0.6}
                  name="Cash In"
                />
                <Area 
                  type="monotone" 
                  dataKey="cashOut" 
                  stackId="2" 
                  stroke="#ef4444" 
                  fill="#ef4444" 
                  fillOpacity={0.6}
                  name="Cash Out"
                />
                <Line 
                  type="monotone" 
                  dataKey="netCashFlow" 
                  stroke="#3b82f6" 
                  strokeWidth={3}
                  name="Net Flow"
                />
              </AreaChart>
            ) : (
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis tickFormatter={(value) => formatCurrency(value)} />
                <Tooltip 
                  formatter={(value, name) => [formatCurrency(Number(value)), name]}
                  labelFormatter={(label) => chartData.find(d => d.name === label)?.fullName || label}
                />
                <Bar dataKey="cashIn" fill="#10b981" name="Cash In" />
                <Bar dataKey="cashOut" fill="#ef4444" name="Cash Out" />
                <Bar dataKey="netCashFlow" fill="#3b82f6" name="Net Flow" />
              </BarChart>
            )}
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Breakdown</CardTitle>
            <CardDescription>Detailed cash flow by month</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {cashFlowData.historicalData.slice().reverse().map((month, index) => (
                <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">{month.monthName} {month.year}</div>
                    <div className="text-sm text-gray-500">
                      {month.paymentsReceived} payments • {month.maintenanceExpenses} expenses
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm space-y-1">
                      <div className="text-green-600">+{formatCurrency(month.cashIn)}</div>
                      <div className="text-red-600">-{formatCurrency(month.cashOut)}</div>
                      <div className={`font-medium ${month.netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCurrency(month.netCashFlow)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Forecast */}
        <Card>
          <CardHeader>
            <CardTitle>3-Month Forecast</CardTitle>
            <CardDescription>Projected cash flow based on trends</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {cashFlowData.forecast.map((month, index) => (
                <div key={index} className="p-4 border rounded-lg bg-blue-50">
                  <div className="flex justify-between items-center mb-2">
                    <div className="font-semibold">{month.monthName} {month.year}</div>
                    <Badge variant="outline">Projected</Badge>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-gray-600">Cash In</div>
                      <div className="font-medium text-green-600">
                        {formatCurrency(month.projectedCashIn)}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-600">Cash Out</div>
                      <div className="font-medium text-red-600">
                        {formatCurrency(month.projectedCashOut)}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-600">Net Flow</div>
                      <div className={`font-medium ${month.projectedNetFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCurrency(month.projectedNetFlow)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Forecast Confidence</div>
                <div className="flex items-center gap-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '75%' }}></div>
                  </div>
                  <span className="text-sm font-medium">75%</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Based on {months} months of historical data
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cash Flow Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Cash Flow Insights</CardTitle>
          <CardDescription>Key observations and recommendations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3 text-green-700">Positive Indicators</h4>
              <div className="space-y-2">
                {isHealthy && (
                  <div className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Overall positive cash flow</span>
                  </div>
                )}
                {isNetFlowImproving && (
                  <div className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Improving cash flow trend</span>
                  </div>
                )}
                {cashVelocity < 80 && (
                  <div className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Good cash retention ratio</span>
                  </div>
                )}
                {cashFlowData.summary.averageMonthlyInflow > cashFlowData.summary.averageMonthlyOutflow && (
                  <div className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Consistent positive monthly flow</span>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-3 text-red-700">Areas for Attention</h4>
              <div className="space-y-2">
                {!isHealthy && (
                  <div className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span>Negative overall cash flow</span>
                  </div>
                )}
                {!isNetFlowImproving && (
                  <div className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span>Declining cash flow trend</span>
                  </div>
                )}
                {cashVelocity > 90 && (
                  <div className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span>High cash outflow ratio</span>
                  </div>
                )}
                {cashFlowData.forecast.some(month => month.projectedNetFlow < 0) && (
                  <div className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span>Projected negative flow in forecast</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold mb-2 text-blue-700">Recommendations</h4>
            <ul className="text-sm space-y-1 text-blue-800">
              {!isHealthy && (
                <li>• Focus on improving collection rates and reducing unnecessary expenses</li>
              )}
              {cashVelocity > 90 && (
                <li>• Review maintenance expenses for cost optimization opportunities</li>
              )}
              <li>• Maintain {Math.ceil(cashFlowData.summary.averageMonthlyOutflow / 1000) * 1000} in reserves for operational continuity</li>
              <li>• Consider implementing automated payment reminders to improve cash inflow timing</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};