import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Get documents by lease
export const getLeaseDocuments = query({
  args: { leaseId: v.id("leases") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("documents")
      .withIndex("by_related", (q) => 
        q.eq("relatedTo.type", "lease").eq("relatedTo.id", args.leaseId)
      )
      .collect();
  },
});

// Get documents by property
export const getPropertyDocuments = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("documents")
      .withIndex("by_related", (q) => 
        q.eq("relatedTo.type", "property").eq("relatedTo.id", args.propertyId)
      )
      .filter((q) => q.eq(q.field("isPublic"), true))
      .collect();
  },
});

// Get documents by tenant
export const getTenantDocuments = query({
  args: { tenantId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("documents")
      .withIndex("by_related", (q) => 
        q.eq("relatedTo.type", "tenant").eq("relatedTo.id", args.tenantId)
      )
      .collect();
  },
});

// Upload document
export const uploadDocument = mutation({
  args: {
    name: v.string(),
    type: v.string(),
    url: v.string(),
    size: v.number(),
    uploadedBy: v.id("users"),
    relatedTo: v.object({
      type: v.union(v.literal("property"), v.literal("lease"), v.literal("tenant"), v.literal("maintenance")),
      id: v.string(),
    }),
    tags: v.array(v.string()),
    isPublic: v.boolean(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("documents", {
      ...args,
      createdAt: Date.now(),
    });
  },
});

// Delete document
export const deleteDocument = mutation({
  args: { documentId: v.id("documents") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.documentId);
  },
});

// Get document by ID
export const getDocumentById = query({
  args: { documentId: v.id("documents") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.documentId);
  },
});