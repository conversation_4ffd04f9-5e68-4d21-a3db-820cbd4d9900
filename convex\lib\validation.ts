// Property validation functions
export const validatePropertyData = (data: any) => {
  if (!data.name || data.name.trim().length === 0) {
    throw new Error("Property name is required");
  }
  if (!data.address || !data.address.street || !data.address.city) {
    throw new Error("Complete address is required");
  }
  if (!["residential", "commercial", "mixed"].includes(data.type)) {
    throw new Error("Invalid property type");
  }
};

export const sanitizePropertyData = (data: any) => {
  return {
    ...data,
    name: data.name?.trim(),
    address: {
      ...data.address,
      street: data.address.street?.trim(),
      city: data.address.city?.trim(),
      state: data.address.state?.trim(),
      country: data.address.country?.trim(),
      postalCode: data.address.postalCode?.trim(),
    },
  };
};

// Unit validation functions
export const validateUnitData = (data: any) => {
  if (!data.unitNumber || data.unitNumber.trim().length === 0) {
    throw new Error("Unit number is required");
  }
  if (!data.type || !["apartment", "office", "retail", "parking"].includes(data.type)) {
    throw new Error("Invalid unit type");
  }
  if (data.size <= 0) {
    throw new Error("Unit size must be greater than 0");
  }
  if (data.rent < 0) {
    throw new Error("Rent cannot be negative");
  }
  if (data.deposit < 0) {
    throw new Error("Deposit cannot be negative");
  }
};

export const sanitizeUnitData = (data: any) => {
  return {
    ...data,
    unitNumber: data.unitNumber?.trim(),
    description: data.description?.trim(),
    amenities: data.amenities?.map((amenity: string) => amenity.trim()).filter(Boolean) || [],
  };
};

// Lease validation functions
export const validateLeaseData = (data: any) => {
  if (!data.propertyId) {
    throw new Error("Property ID is required");
  }
  if (!data.unitId) {
    throw new Error("Unit ID is required");
  }
  if (!data.tenantId) {
    throw new Error("Tenant ID is required");
  }
  if (!data.startDate || data.startDate <= 0) {
    throw new Error("Valid start date is required");
  }
  if (!data.endDate || data.endDate <= data.startDate) {
    throw new Error("End date must be after start date");
  }
  if (data.monthlyRent <= 0) {
    throw new Error("Monthly rent must be greater than 0");
  }
  if (data.deposit < 0) {
    throw new Error("Deposit cannot be negative");
  }
};

export const sanitizeLeaseData = (data: any) => {
  return {
    ...data,
    documentUrl: data.documentUrl?.trim(),
  };
};

// Lease lifecycle validation functions
export const validateLeaseStatus = (status: string) => {
  const validStatuses = ["active", "expired", "terminated", "pending"];
  if (!validStatuses.includes(status)) {
    throw new Error(`Invalid lease status. Must be one of: ${validStatuses.join(", ")}`);
  }
};

export const validateESignatureStatus = (status: string) => {
  const validStatuses = ["pending", "signed", "expired"];
  if (!validStatuses.includes(status)) {
    throw new Error(`Invalid e-signature status. Must be one of: ${validStatuses.join(", ")}`);
  }
};

export const validateLeaseTerms = (terms: any) => {
  if (terms.noticePeriod < 0) {
    throw new Error("Notice period cannot be negative");
  }
  if (terms.lateFeePercentage < 0 || terms.lateFeePercentage > 100) {
    throw new Error("Late fee percentage must be between 0 and 100");
  }
  if (terms.gracePeriod < 0) {
    throw new Error("Grace period cannot be negative");
  }
  if (typeof terms.renewalOption !== "boolean") {
    throw new Error("Renewal option must be a boolean value");
  }
};

// Tenant onboarding validation functions
export const validateTenantOnboardingData = (data: any) => {
  if (!data.email || !validateEmail(data.email)) {
    throw new Error("Valid email address is required");
  }
  if (!data.name || data.name.trim().length === 0) {
    throw new Error("Tenant name is required");
  }
  if (data.phone && !validatePhoneNumber(data.phone)) {
    throw new Error("Valid phone number is required");
  }
  if (!["pending", "verified", "rejected"].includes(data.kycStatus)) {
    throw new Error("Invalid KYC status");
  }
};

export const sanitizeTenantData = (data: any) => {
  return {
    ...data,
    name: data.name?.trim(),
    email: data.email?.trim().toLowerCase(),
    phone: data.phone?.replace(/\s/g, ''),
  };
};

// Lease renewal validation
export const validateLeaseRenewal = (currentLease: any, renewalData: any) => {
  if (currentLease.status !== "active") {
    throw new Error("Only active leases can be renewed");
  }
  if (!currentLease.terms.renewalOption) {
    throw new Error("This lease does not have a renewal option");
  }
  if (renewalData.newEndDate <= currentLease.endDate) {
    throw new Error("New end date must be after current end date");
  }
  if (renewalData.newMonthlyRent !== undefined && renewalData.newMonthlyRent <= 0) {
    throw new Error("New monthly rent must be greater than 0");
  }
};

// Lease termination validation
export const validateLeaseTermination = (lease: any, terminationData: any) => {
  if (lease.status !== "active") {
    throw new Error("Only active leases can be terminated");
  }
  if (!terminationData.reason || terminationData.reason.trim().length === 0) {
    throw new Error("Termination reason is required");
  }
  if (terminationData.terminationDate <= 0) {
    throw new Error("Valid termination date is required");
  }
  
  const isEarlyTermination = terminationData.terminationDate < lease.endDate;
  if (isEarlyTermination && !terminationData.earlyTermination) {
    throw new Error("Early termination flag must be set for termination before lease end date");
  }
};

// Maintenance ticket validation functions
export const validateMaintenanceTicketData = (data: any) => {
  if (!data.propertyId) {
    throw new Error("Property ID is required");
  }
  if (!data.tenantId) {
    throw new Error("Tenant ID is required");
  }
  if (!data.title || data.title.trim().length === 0) {
    throw new Error("Ticket title is required");
  }
  if (!data.description || data.description.trim().length === 0) {
    throw new Error("Ticket description is required");
  }
  if (!["low", "medium", "high", "emergency"].includes(data.priority)) {
    throw new Error("Invalid priority level");
  }
  if (!["plumbing", "electrical", "hvac", "appliance", "structural", "cleaning", "security", "other"].includes(data.category)) {
    throw new Error("Invalid maintenance category");
  }
};

export const sanitizeMaintenanceTicketData = (data: any) => {
  return {
    ...data,
    title: data.title?.trim(),
    description: data.description?.trim(),
    category: data.category?.trim(),
  };
};

// Vendor validation functions
export const validateVendorData = (data: any) => {
  if (!data.userId) {
    throw new Error("User ID is required");
  }
  if (!data.companyName || data.companyName.trim().length === 0) {
    throw new Error("Company name is required");
  }
  if (!data.contactPerson || data.contactPerson.trim().length === 0) {
    throw new Error("Contact person is required");
  }
  if (!data.phone || !validatePhoneNumber(data.phone)) {
    throw new Error("Valid phone number is required");
  }
  if (!data.email || !validateEmail(data.email)) {
    throw new Error("Valid email address is required");
  }
  if (!data.address || !data.address.street || !data.address.city) {
    throw new Error("Complete address is required");
  }
  if (!data.specialties || data.specialties.length === 0) {
    throw new Error("At least one specialty is required");
  }
  if (!data.serviceAreas || data.serviceAreas.length === 0) {
    throw new Error("At least one service area is required");
  }
};

export const sanitizeVendorData = (data: any) => {
  return {
    ...data,
    companyName: data.companyName?.trim(),
    contactPerson: data.contactPerson?.trim(),
    phone: data.phone?.replace(/\s/g, ''),
    email: data.email?.trim().toLowerCase(),
    address: {
      ...data.address,
      street: data.address.street?.trim(),
      city: data.address.city?.trim(),
      state: data.address.state?.trim(),
      country: data.address.country?.trim(),
      postalCode: data.address.postalCode?.trim(),
    },
  };
};

// Ticket status validation
export const validateTicketStatus = (status: string) => {
  const validStatuses = ["open", "assigned", "in_progress", "completed", "closed", "escalated"];
  if (!validStatuses.includes(status)) {
    throw new Error(`Invalid ticket status. Must be one of: ${validStatuses.join(", ")}`);
  }
};

// Escalation validation
export const validateEscalationData = (data: any) => {
  if (!data.ticketId) {
    throw new Error("Ticket ID is required");
  }
  if (!data.escalatedTo) {
    throw new Error("Escalation target is required");
  }
  if (!["sla_breach", "vendor_unavailable", "complexity", "cost_approval", "tenant_complaint", "manual"].includes(data.reason)) {
    throw new Error("Invalid escalation reason");
  }
  if (!data.notes || data.notes.trim().length === 0) {
    throw new Error("Escalation notes are required");
  }
};

// Vendor assignment validation
export const validateVendorAssignment = (data: any) => {
  if (!data.ticketId) {
    throw new Error("Ticket ID is required");
  }
  if (!data.vendorId) {
    throw new Error("Vendor ID is required");
  }
  if (data.estimatedCost !== undefined && data.estimatedCost < 0) {
    throw new Error("Estimated cost cannot be negative");
  }
  if (data.estimatedDuration !== undefined && data.estimatedDuration <= 0) {
    throw new Error("Estimated duration must be greater than 0");
  }
};

// Calculate SLA deadline based on priority
export const calculateSLADeadline = (priority: string, maintenanceSLA: number): number => {
  const now = Date.now();
  const baseHours = maintenanceSLA;
  
  switch (priority) {
    case "emergency":
      return now + (2 * 60 * 60 * 1000); // 2 hours
    case "high":
      return now + (4 * 60 * 60 * 1000); // 4 hours
    case "medium":
      return now + (baseHours * 60 * 60 * 1000); // Default SLA
    case "low":
      return now + (baseHours * 2 * 60 * 60 * 1000); // Double the SLA
    default:
      return now + (baseHours * 60 * 60 * 1000);
  }
};

// Additional validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhoneNumber = (phone: string): boolean => {
  // Kenyan phone number format validation
  const phoneRegex = /^(\+254|0)[17]\d{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

export const sanitizeString = (str: string | undefined): string => {
  return str?.trim() || '';
};

export const validatePositiveNumber = (value: number, fieldName: string): void => {
  if (value <= 0) {
    throw new Error(`${fieldName} must be greater than 0`);
  }
};

export const validateNonNegativeNumber = (value: number, fieldName: string): void => {
  if (value < 0) {
    throw new Error(`${fieldName} cannot be negative`);
  }
};

// Date validation utilities
export const validateDateRange = (startDate: number, endDate: number): void => {
  if (startDate >= endDate) {
    throw new Error("End date must be after start date");
  }
};

export const validateFutureDate = (date: number): void => {
  if (date <= Date.now()) {
    throw new Error("Date must be in the future");
  }
};

// Currency formatting utility
export const formatCurrency = (amount: number, currency: string = "KES"): string => {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: currency,
  }).format(amount);
};