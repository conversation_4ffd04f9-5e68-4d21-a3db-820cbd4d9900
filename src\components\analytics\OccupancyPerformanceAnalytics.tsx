import React, { useState, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { <PERSON><PERSON> } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Home, 
  Users, 
  Calendar,
  Target,
  AlertTriangle,
  CheckCircle,
  Download,
  BarChart3
} from 'lucide-react';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Scatter<PERSON>hart,
  <PERSON>atter,
  Cell
} from 'recharts';
import { format, subMonths } from 'date-fns';
import { Id } from '../../../convex/_generated/dataModel';

interface OccupancyPerformanceAnalyticsProps {
  propertyId?: Id<"properties">;
}

const HEATMAP_COLORS = {
  high: '#10b981', // Green for high occupancy
  medium: '#f59e0b', // Yellow for medium occupancy
  low: '#ef4444', // Red for low occupancy
  vacant: '#6b7280', // Gray for vacant
};

export const OccupancyPerformanceAnalytics: React.FC<OccupancyPerformanceAnalyticsProps> = ({ 
  propertyId 
}) => {
  const [selectedTab, setSelectedTab] = useState('occupancy');
  const [timeRange, setTimeRange] = useState<number>(12);

  // Fetch analytics data
  const occupancyData = useQuery(api.financialAnalytics.getOccupancyAnalytics, {
    propertyId,
    months: timeRange,
  });

  const retentionData = useQuery(api.financialAnalytics.getTenantRetentionAnalytics, {
    propertyId,
    months: timeRange,
  });

  const rentOptimization = useQuery(api.financialAnalytics.getRentOptimizationRecommendations, {
    propertyId,
  });

  const marketBenchmarking = useQuery(api.financialAnalytics.getMarketBenchmarking, {
    propertyId,
  });

  const isLoading = !occupancyData || !retentionData || !rentOptimization || !marketBenchmarking;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading analytics data...</p>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Prepare chart data
  const occupancyTrendData = occupancyData.properties[0]?.monthlyTrends.map((month: any) => ({
    name: `${month.monthName.slice(0, 3)} ${month.year}`,
    occupancyRate: month.occupancyRate,
    newLeases: month.newLeases,
    terminatedLeases: month.terminatedLeases,
    renewedLeases: month.renewedLeases,
  })) || [];

  const retentionTrendData = retentionData.monthlyTrends.map(month => ({
    name: `${month.monthName.slice(0, 3)} ${month.year}`,
    retentionRate: month.retentionRate,
    turnoverRate: month.turnoverRate,
    newLeases: month.newLeases,
    terminatedLeases: month.terminatedLeases,
  }));

  // Get heatmap data for the first property
  const heatmapData = occupancyData.properties[0]?.heatmapData || [];

  // Prepare rent optimization scatter plot data
  const rentScatterData = rentOptimization.recommendations[0]?.unitAnalysis.map(unit => ({
    x: unit.occupancyHistory.occupancyRate,
    y: unit.currentRent,
    unitNumber: unit.unitNumber,
    recommendedRent: unit.recommendedRent,
    potentialIncrease: unit.potentialIncrease,
    riskLevel: unit.riskLevel,
  })) || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Occupancy & Performance Analytics</h1>
          <p className="text-gray-600">
            Comprehensive occupancy trends, tenant retention, and rent optimization insights
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={timeRange.toString()} onValueChange={(value) => setTimeRange(parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="6">6 Months</SelectItem>
              <SelectItem value="12">12 Months</SelectItem>
              <SelectItem value="24">24 Months</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Occupancy</CardTitle>
            <Home className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatPercentage(occupancyData.properties[0]?.currentOccupancy.occupancyRate || 0)}
            </div>
            <div className="text-xs text-muted-foreground">
              {occupancyData.properties[0]?.currentOccupancy.occupiedUnits || 0} of{' '}
              {occupancyData.properties[0]?.currentOccupancy.totalUnits || 0} units occupied
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tenant Retention</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatPercentage(retentionData.summary.retentionRate)}
            </div>
            <div className="text-xs text-muted-foreground">
              {retentionData.summary.activeTenants} active tenants
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Lease Duration</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(retentionData.summary.averageLeaseDuration)} days
            </div>
            <div className="text-xs text-muted-foreground">
              Average tenant stay
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rent Optimization</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(rentOptimization.recommendations[0]?.propertyRecommendations.totalPotentialIncrease || 0)}
            </div>
            <div className="text-xs text-muted-foreground">
              Potential monthly increase
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="occupancy">Occupancy Heatmap</TabsTrigger>
          <TabsTrigger value="retention">Tenant Retention</TabsTrigger>
          <TabsTrigger value="optimization">Rent Optimization</TabsTrigger>
          <TabsTrigger value="benchmarking">Market Benchmarking</TabsTrigger>
        </TabsList>

        <TabsContent value="occupancy" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Occupancy Trend Chart */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Occupancy Trends</CardTitle>
                <CardDescription>Monthly occupancy rates and lease activity</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={occupancyTrendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey="occupancyRate" 
                      stroke="#10b981" 
                      fill="#10b981" 
                      fillOpacity={0.3}
                      name="Occupancy Rate (%)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Lease Activity Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Lease Activity</CardTitle>
                <CardDescription>Recent lease movements</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">New Leases</span>
                    <span className="font-semibold text-green-600">
                      {occupancyTrendData.reduce((sum, month) => sum + month.newLeases, 0)}
                    </span>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Terminated Leases</span>
                    <span className="font-semibold text-red-600">
                      {occupancyTrendData.reduce((sum, month) => sum + month.terminatedLeases, 0)}
                    </span>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Renewed Leases</span>
                    <span className="font-semibold text-blue-600">
                      {occupancyTrendData.reduce((sum, month) => sum + month.renewedLeases, 0)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Unit Heatmap */}
          <Card>
            <CardHeader>
              <CardTitle>Unit Occupancy Heatmap</CardTitle>
              <CardDescription>Visual representation of unit occupancy performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-8 md:grid-cols-12 lg:grid-cols-16 gap-2">
                {heatmapData.map((unit) => {
                  const occupancyRate = unit.occupancyDays / 365 * 100;
                  let colorClass = 'bg-gray-300';
                  
                  if (unit.status === 'vacant') {
                    colorClass = 'bg-gray-400';
                  } else if (occupancyRate >= 90) {
                    colorClass = 'bg-green-500';
                  } else if (occupancyRate >= 70) {
                    colorClass = 'bg-yellow-500';
                  } else {
                    colorClass = 'bg-red-500';
                  }

                  return (
                    <div
                      key={unit.unitId}
                      className={`aspect-square ${colorClass} rounded text-xs text-white flex items-center justify-center font-medium cursor-pointer hover:opacity-80 transition-opacity`}
                      title={`Unit ${unit.unitNumber}: ${occupancyRate.toFixed(1)}% occupancy`}
                    >
                      {unit.unitNumber}
                    </div>
                  );
                })}
              </div>
              
              {/* Legend */}
              <div className="flex items-center justify-center gap-6 mt-4 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-green-500 rounded"></div>
                  <span>High (90%+)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                  <span>Medium (70-90%)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-red-500 rounded"></div>
                  <span>Low (&lt;70%)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-gray-400 rounded"></div>
                  <span>Vacant</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="retention" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Retention Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Retention Trends</CardTitle>
                <CardDescription>Monthly tenant retention and turnover rates</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={retentionTrendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip formatter={(value) => `${Number(value).toFixed(1)}%`} />
                    <Line 
                      type="monotone" 
                      dataKey="retentionRate" 
                      stroke="#10b981" 
                      strokeWidth={2}
                      name="Retention Rate"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="turnoverRate" 
                      stroke="#ef4444" 
                      strokeWidth={2}
                      name="Turnover Rate"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Tenant Lifecycle Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Tenant Lifecycle</CardTitle>
                <CardDescription>Top tenants by duration and renewals</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-80 overflow-y-auto">
                  {retentionData.tenantLifecycle.slice(0, 10).map((tenant, index) => (
                    <div key={tenant.tenantId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">Tenant {tenant.tenantId.slice(-6)}</div>
                          <div className="text-sm text-gray-600">
                            {Math.round(tenant.totalDuration)} days • {tenant.renewalCount} renewals
                          </div>
                        </div>
                      </div>
                      <Badge variant={tenant.isActive ? "default" : "secondary"}>
                        {tenant.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Retention Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Retention Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Total Tenants:</span>
                  <span className="font-semibold">{retentionData.summary.totalTenants}</span>
                </div>
                <div className="flex justify-between">
                  <span>Active Tenants:</span>
                  <span className="font-semibold text-green-600">{retentionData.summary.activeTenants}</span>
                </div>
                <div className="flex justify-between">
                  <span>Retention Rate:</span>
                  <span className="font-semibold">{formatPercentage(retentionData.summary.retentionRate)}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Lease Activity</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Active Leases:</span>
                  <span className="font-semibold text-green-600">{retentionData.summary.activeLeases}</span>
                </div>
                <div className="flex justify-between">
                  <span>Terminated:</span>
                  <span className="font-semibold text-red-600">{retentionData.summary.terminatedLeases}</span>
                </div>
                <div className="flex justify-between">
                  <span>Expired:</span>
                  <span className="font-semibold text-yellow-600">{retentionData.summary.expiredLeases}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Avg Duration:</span>
                  <span className="font-semibold">{Math.round(retentionData.summary.averageLeaseDuration)} days</span>
                </div>
                <div className="flex justify-between">
                  <span>Turnover Rate:</span>
                  <span className="font-semibold">{formatPercentage(retentionData.summary.turnoverRate)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Leases:</span>
                  <span className="font-semibold">{retentionData.summary.totalLeases}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="optimization" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Rent vs Occupancy Scatter Plot */}
            <Card>
              <CardHeader>
                <CardTitle>Rent vs Occupancy Analysis</CardTitle>
                <CardDescription>Unit performance by rent and occupancy rate</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ScatterChart data={rentScatterData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="x" 
                      name="Occupancy Rate" 
                      unit="%" 
                      domain={[0, 100]}
                    />
                    <YAxis 
                      dataKey="y" 
                      name="Current Rent" 
                      tickFormatter={(value) => formatCurrency(value)}
                    />
                    <Tooltip 
                      formatter={(value, name) => [
                        name === 'Current Rent' ? formatCurrency(Number(value)) : `${value}%`,
                        name
                      ]}
                      labelFormatter={(_, payload) => 
                        payload?.[0]?.payload?.unitNumber ? `Unit ${payload[0].payload.unitNumber}` : ''
                      }
                    />
                    <Scatter dataKey="y" fill="#3b82f6">
                      {occupancyTrends.monthlyData.map((month: any, index: number) => (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={
                            month.riskLevel === 'low' ? '#10b981' :
                            month.riskLevel === 'medium' ? '#f59e0b' : '#ef4444'
                          } 
                        />
                      ))}
                    </Scatter>
                  </ScatterChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Optimization Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Optimization Summary</CardTitle>
                <CardDescription>Property-level rent optimization insights</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {rentOptimization.recommendations[0] && (
                  <>
                    <div>
                      <div className="text-sm text-gray-600">Current Avg Rent</div>
                      <div className="text-xl font-bold">
                        {formatCurrency(rentOptimization.recommendations[0].propertyRecommendations.averageCurrentRent)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Market Rate</div>
                      <div className="text-xl font-bold">
                        {formatCurrency(rentOptimization.recommendations[0].propertyRecommendations.averageMarketRate)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Potential Monthly Increase</div>
                      <div className="text-xl font-bold text-green-600">
                        {formatCurrency(rentOptimization.recommendations[0].propertyRecommendations.totalPotentialIncrease)}
                      </div>
                    </div>
                    <div className="pt-4 border-t space-y-2">
                      <div className="flex justify-between">
                        <span>High Performing Units:</span>
                        <Badge variant="default">
                          {rentOptimization.recommendations[0].propertyRecommendations.highPerformingUnits}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Underperforming Units:</span>
                        <Badge variant="destructive">
                          {rentOptimization.recommendations[0].propertyRecommendations.underperformingUnits}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Vacancy Risk:</span>
                        <Badge variant={
                          rentOptimization.recommendations[0].propertyRecommendations.vacancyRisk < 20 ? "default" :
                          rentOptimization.recommendations[0].propertyRecommendations.vacancyRisk < 40 ? "secondary" : "destructive"
                        }>
                          {formatPercentage(rentOptimization.recommendations[0].propertyRecommendations.vacancyRisk)}
                        </Badge>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Unit Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle>Unit-Level Recommendations</CardTitle>
              <CardDescription>Specific rent optimization suggestions for each unit</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {rentOptimization.recommendations[0]?.unitAnalysis
                  .filter(unit => Math.abs(unit.potentialIncrease) > 100) // Only show significant changes
                  .map((unit) => (
                    <div key={unit.unitId} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="text-lg font-semibold">Unit {unit.unitNumber}</div>
                        <div className="space-y-1">
                          <div className="text-sm">
                            Current: {formatCurrency(unit.currentRent)} → 
                            Recommended: {formatCurrency(unit.recommendedRent)}
                          </div>
                          <div className="text-xs text-gray-600">{unit.reasoning}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-lg font-bold ${unit.potentialIncrease >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {unit.potentialIncrease >= 0 ? '+' : ''}{formatCurrency(unit.potentialIncrease)}
                        </div>
                        <Badge variant={
                          unit.riskLevel === 'low' ? 'default' :
                          unit.riskLevel === 'medium' ? 'secondary' : 'destructive'
                        }>
                          {unit.riskLevel} risk
                        </Badge>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="benchmarking" className="space-y-4">
          {/* Market Comparison */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Market Position</CardTitle>
                <CardDescription>How your property compares to market averages</CardDescription>
              </CardHeader>
              <CardContent>
                {marketBenchmarking.properties[0] && (
                  <div className="space-y-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">
                        {marketBenchmarking.properties[0].benchmarkComparison.percentile.toFixed(0)}th
                      </div>
                      <div className="text-sm text-gray-600">Percentile</div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span>Occupancy Ranking:</span>
                        <Badge variant="outline">
                          #{marketBenchmarking.properties[0].benchmarkComparison.occupancyRanking}
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Rent Ranking:</span>
                        <Badge variant="outline">
                          #{marketBenchmarking.properties[0].benchmarkComparison.rentRanking}
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Profitability Ranking:</span>
                        <Badge variant="outline">
                          #{marketBenchmarking.properties[0].benchmarkComparison.profitabilityRanking}
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Performance Metrics Comparison */}
            <Card>
              <CardHeader>
                <CardTitle>Performance vs Market</CardTitle>
                <CardDescription>Key metrics compared to market averages</CardDescription>
              </CardHeader>
              <CardContent>
                {marketBenchmarking.properties[0] && (
                  <div className="space-y-4">
                    {[
                      {
                        label: 'Occupancy Rate',
                        value: marketBenchmarking.properties[0].metrics.occupancyRate,
                        market: marketBenchmarking.marketAverages.occupancyRate,
                        format: formatPercentage,
                      },
                      {
                        label: 'Average Rent',
                        value: marketBenchmarking.properties[0].metrics.averageRent,
                        market: marketBenchmarking.marketAverages.averageRent,
                        format: formatCurrency,
                      },
                      {
                        label: 'Profit Margin',
                        value: marketBenchmarking.properties[0].metrics.profitMargin,
                        market: marketBenchmarking.marketAverages.profitMargin,
                        format: formatPercentage,
                      },
                      {
                        label: 'Retention Rate',
                        value: marketBenchmarking.properties[0].metrics.tenantRetentionRate,
                        market: marketBenchmarking.marketAverages.tenantRetentionRate,
                        format: formatPercentage,
                      },
                    ].map((metric) => {
                      const isAboveMarket = metric.value > metric.market;
                      const difference = ((metric.value - metric.market) / metric.market) * 100;
                      
                      return (
                        <div key={metric.label} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">{metric.label}</span>
                            <div className="flex items-center gap-2">
                              {isAboveMarket ? (
                                <TrendingUp className="h-4 w-4 text-green-600" />
                              ) : (
                                <TrendingDown className="h-4 w-4 text-red-600" />
                              )}
                              <span className={`text-sm ${isAboveMarket ? 'text-green-600' : 'text-red-600'}`}>
                                {difference > 0 ? '+' : ''}{difference.toFixed(1)}%
                              </span>
                            </div>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Your Property: {metric.format(metric.value)}</span>
                            <span className="text-gray-600">Market: {metric.format(metric.market)}</span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle>Market-Based Recommendations</CardTitle>
              <CardDescription>Actionable insights based on market comparison</CardDescription>
            </CardHeader>
            <CardContent>
              {marketBenchmarking.properties[0]?.recommendations.length > 0 ? (
                <div className="space-y-3">
                  {marketBenchmarking.properties[0].recommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                      <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-blue-900">{recommendation.title}</p>
                        <p className="text-sm text-blue-700 mt-1">{recommendation.description}</p>
                        {recommendation.impact && (
                          <p className="text-xs text-blue-600 mt-2">
                            Expected Impact: {recommendation.impact}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">
                  No specific recommendations available. Continue monitoring market trends.
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};