import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Doc, Id } from "./_generated/dataModel";
import { checkPermission } from "./permissions";

// Ticket Management Functions

export const createTicket = mutation({
  args: {
    propertyId: v.id("properties"),
    unitId: v.optional(v.id("units")),
    title: v.string(),
    description: v.string(),
    category: v.union(
      v.literal("plumbing"),
      v.literal("electrical"),
      v.literal("hvac"),
      v.literal("appliance"),
      v.literal("structural"),
      v.literal("cleaning"),
      v.literal("security"),
      v.literal("other")
    ),
    priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("emergency")),
    images: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    // Check if user has access to this property
    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "create");
    if (!hasAccess) throw new Error("Insufficient permissions");

    // Get property settings for SLA calculation
    const property = await ctx.db.get(args.propertyId);
    if (!property) throw new Error("Property not found");

    // Calculate SLA deadline based on priority and property settings
    const now = Date.now();
    let slaHours = property.settings.maintenanceSLA;
    
    switch (args.priority) {
      case "emergency":
        slaHours = 2;
        break;
      case "high":
        slaHours = 8;
        break;
      case "medium":
        slaHours = 24;
        break;
      case "low":
        slaHours = 72;
        break;
    }

    const slaDeadline = now + (slaHours * 60 * 60 * 1000);
    const escalationDeadline = now + ((slaHours * 0.8) * 60 * 60 * 1000); // 80% of SLA

    const ticketId = await ctx.db.insert("maintenanceTickets", {
      propertyId: args.propertyId,
      unitId: args.unitId,
      tenantId: user._id,
      title: args.title,
      description: args.description,
      category: args.category,
      priority: args.priority,
      status: "open",
      slaDeadline,
      escalationDeadline,
      images: args.images || [],
      attachments: [],
      notes: [{
        userId: user._id,
        message: `Ticket created: ${args.description}`,
        timestamp: now,
        type: "comment" as const,
      }],
      escalationHistory: [],
      createdAt: now,
      updatedAt: now,
    });

    // Create notification for property manager
    if (property.managerId) {
      await ctx.db.insert("notifications", {
        userId: property.managerId,
        title: `New Maintenance Request: ${args.title}`,
        message: `A new ${args.priority} priority maintenance request has been submitted for ${property.name}`,
        type: "maintenance_update",
        priority: args.priority === "emergency" ? "urgent" : "medium",
        isRead: false,
        actionUrl: `/maintenance/${ticketId}`,
        metadata: { ticketId },
        createdAt: now,
      });
    }

    return ticketId;
  },
});

export const assignVendor = mutation({
  args: {
    ticketId: v.id("maintenanceTickets"),
    vendorId: v.id("users"),
    estimatedCost: v.optional(v.number()),
    estimatedDuration: v.optional(v.number()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "update");
    if (!hasAccess) throw new Error("Insufficient permissions");

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) throw new Error("Ticket not found");

    // Verify vendor exists and has vendor role
    const vendor = await ctx.db.get(args.vendorId);
    if (!vendor || vendor.role !== "vendor") {
      throw new Error("Invalid vendor");
    }

    const now = Date.now();

    // Update ticket with vendor assignment
    await ctx.db.patch(args.ticketId, {
      vendorId: args.vendorId,
      assignedBy: user._id,
      status: "assigned",
      estimatedCost: args.estimatedCost,
      estimatedDuration: args.estimatedDuration,
      notes: [
        ...ticket.notes,
        {
          userId: user._id,
          message: args.notes || `Assigned to vendor: ${vendor.name}`,
          timestamp: now,
          type: "assignment" as const,
        },
      ],
      updatedAt: now,
    });

    // Notify vendor of assignment
    await ctx.db.insert("notifications", {
      userId: args.vendorId,
      title: `New Maintenance Assignment: ${ticket.title}`,
      message: `You have been assigned a ${ticket.priority} priority maintenance ticket`,
      type: "maintenance_assigned",
      priority: ticket.priority === "emergency" ? "urgent" : "medium",
      isRead: false,
      actionUrl: `/maintenance/${args.ticketId}`,
      metadata: { ticketId: args.ticketId },
      createdAt: now,
    });

    // Notify tenant of assignment
    await ctx.db.insert("notifications", {
      userId: ticket.tenantId,
      title: `Maintenance Request Assigned: ${ticket.title}`,
      message: `Your maintenance request has been assigned to ${vendor.name}`,
      type: "maintenance_update",
      priority: "medium",
      isRead: false,
      actionUrl: `/maintenance/${args.ticketId}`,
      metadata: { ticketId: args.ticketId },
      createdAt: now,
    });

    return args.ticketId;
  },
});

export const updateTicketStatus = mutation({
  args: {
    ticketId: v.id("maintenanceTickets"),
    status: v.union(
      v.literal("open"),
      v.literal("assigned"),
      v.literal("in_progress"),
      v.literal("completed"),
      v.literal("closed")
    ),
    notes: v.optional(v.string()),
    actualCost: v.optional(v.number()),
    actualDuration: v.optional(v.number()),
    satisfactionRating: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) throw new Error("Ticket not found");

    // Check permissions based on user role and ticket ownership
    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "update") ||
                     ticket.tenantId === user._id ||
                     ticket.vendorId === user._id;
    
    if (!hasAccess) throw new Error("Insufficient permissions");

    const now = Date.now();
    const updateData: any = {
      status: args.status,
      notes: [
        ...ticket.notes,
        {
          userId: user._id,
          message: args.notes || `Status changed to: ${args.status}`,
          timestamp: now,
          type: "status_change" as const,
        },
      ],
      updatedAt: now,
    };

    if (args.actualCost !== undefined) {
      updateData.actualCost = args.actualCost;
    }

    if (args.actualDuration !== undefined) {
      updateData.actualDuration = args.actualDuration;
    }

    // Handle completion
    if (args.status === "completed") {
      updateData.completionDetails = {
        completedBy: user._id,
        completionNotes: args.notes || "Work completed",
        satisfactionRating: args.satisfactionRating,
        completedAt: now,
      };

      // Update vendor performance metrics
      if (ticket.vendorId) {
        const vendor = await ctx.db
          .query("vendors")
          .withIndex("by_user", (q) => q.eq("userId", ticket.vendorId!))
          .first();

        if (vendor) {
          const responseTime = (now - ticket.createdAt) / (1000 * 60 * 60); // hours
          const slaCompliant = now <= ticket.slaDeadline;
          
          await ctx.db.patch(vendor._id, {
            performance: {
              ...vendor.performance,
              totalJobs: vendor.performance.totalJobs + 1,
              completedJobs: vendor.performance.completedJobs + 1,
              averageResponseTime: (vendor.performance.averageResponseTime + responseTime) / 2,
              averageCompletionTime: args.actualDuration 
                ? (vendor.performance.averageCompletionTime + args.actualDuration) / 2
                : vendor.performance.averageCompletionTime,
              slaCompliance: slaCompliant 
                ? Math.min(100, vendor.performance.slaCompliance + 1)
                : Math.max(0, vendor.performance.slaCompliance - 1),
              averageRating: args.satisfactionRating
                ? (vendor.performance.averageRating + args.satisfactionRating) / 2
                : vendor.performance.averageRating,
            },
            updatedAt: now,
          });
        }
      }
    }

    await ctx.db.patch(args.ticketId, updateData);

    // Send notifications based on status change
    const notificationTargets = [];
    
    if (ticket.tenantId !== user._id) {
      notificationTargets.push(ticket.tenantId);
    }
    
    if (ticket.vendorId && ticket.vendorId !== user._id) {
      notificationTargets.push(ticket.vendorId);
    }

    const property = await ctx.db.get(ticket.propertyId);
    if (property?.managerId && property.managerId !== user._id) {
      notificationTargets.push(property.managerId);
    }

    for (const targetUserId of notificationTargets) {
      await ctx.db.insert("notifications", {
        userId: targetUserId,
        title: `Maintenance Update: ${ticket.title}`,
        message: `Status changed to: ${args.status}`,
        type: "maintenance_update",
        priority: "medium",
        isRead: false,
        actionUrl: `/maintenance/${args.ticketId}`,
        metadata: { ticketId: args.ticketId },
        createdAt: now,
      });
    }

    return args.ticketId;
  },
});

export const escalateTicket = mutation({
  args: {
    ticketId: v.id("maintenanceTickets"),
    escalatedTo: v.id("users"),
    reason: v.union(
      v.literal("sla_breach"),
      v.literal("vendor_unavailable"),
      v.literal("complexity"),
      v.literal("cost_approval"),
      v.literal("tenant_complaint"),
      v.literal("manual")
    ),
    notes: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "update");
    if (!hasAccess) throw new Error("Insufficient permissions");

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) throw new Error("Ticket not found");

    const now = Date.now();

    // Get current escalation level
    const currentEscalations = await ctx.db
      .query("maintenanceEscalations")
      .withIndex("by_ticket", (q) => q.eq("ticketId", args.ticketId))
      .collect();

    const escalationLevel = currentEscalations.length + 1;

    // Create escalation record
    const escalationId = await ctx.db.insert("maintenanceEscalations", {
      ticketId: args.ticketId,
      propertyId: ticket.propertyId,
      escalationLevel,
      escalatedFrom: user._id,
      escalatedTo: args.escalatedTo,
      reason: args.reason,
      escalationRules: {
        triggerCondition: args.reason,
        autoEscalate: false,
        notificationSent: true,
      },
      createdAt: now,
    });

    // Update ticket
    await ctx.db.patch(args.ticketId, {
      status: "escalated",
      escalationHistory: [
        ...ticket.escalationHistory,
        {
          escalatedBy: user._id,
          escalatedTo: args.escalatedTo,
          reason: args.notes,
          timestamp: now,
        },
      ],
      notes: [
        ...ticket.notes,
        {
          userId: user._id,
          message: `Ticket escalated: ${args.notes}`,
          timestamp: now,
          type: "escalation" as const,
        },
      ],
      updatedAt: now,
    });

    // Notify escalation target
    await ctx.db.insert("notifications", {
      userId: args.escalatedTo,
      title: `Escalated Maintenance Ticket: ${ticket.title}`,
      message: `A ${ticket.priority} priority maintenance ticket has been escalated to you. Reason: ${args.reason}`,
      type: "maintenance_escalated",
      priority: "urgent",
      isRead: false,
      actionUrl: `/maintenance/${args.ticketId}`,
      metadata: { 
        ticketId: args.ticketId,
        escalationId,
      },
      createdAt: now,
    });

    return escalationId;
  },
});

// Query Functions

export const getTicketsByProperty = query({
  args: {
    propertyId: v.id("properties"),
    status: v.optional(v.union(
      v.literal("open"),
      v.literal("assigned"),
      v.literal("in_progress"),
      v.literal("completed"),
      v.literal("closed"),
      v.literal("escalated")
    )),
    priority: v.optional(v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("emergency"))),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "read");
    if (!hasAccess) throw new Error("Insufficient permissions");

    let query = ctx.db
      .query("maintenanceTickets")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId));

    if (args.status) {
      query = ctx.db
        .query("maintenanceTickets")
        .withIndex("by_property_status", (q) => 
          q.eq("propertyId", args.propertyId).eq("status", args.status)
        );
    }

    const tickets = await query.collect();

    // Filter by priority if specified
    const filteredTickets = args.priority 
      ? tickets.filter(ticket => ticket.priority === args.priority)
      : tickets;

    return filteredTickets.sort((a, b) => b.createdAt - a.createdAt);
  },
});

export const getTicketsByVendor = query({
  args: {
    vendorId: v.id("users"),
    status: v.optional(v.union(
      v.literal("assigned"),
      v.literal("in_progress"),
      v.literal("completed"),
      v.literal("closed")
    )),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    // Vendors can only see their own tickets, managers can see all
    const hasAccess = user._id === args.vendorId || 
                     await checkPermission(ctx, user._id, "maintenance", "read");
    
    if (!hasAccess) throw new Error("Insufficient permissions");

    let query = ctx.db
      .query("maintenanceTickets")
      .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId));

    if (args.status) {
      query = ctx.db
        .query("maintenanceTickets")
        .withIndex("by_vendor_status", (q) => 
          q.eq("vendorId", args.vendorId).eq("status", args.status)
        );
    }

    const tickets = await query.collect();
    return tickets.sort((a, b) => b.createdAt - a.createdAt);
  },
});

export const getTicketById = query({
  args: { ticketId: v.id("maintenanceTickets") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) throw new Error("Ticket not found");

    // Check if user has access to this ticket
    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "read") ||
                     ticket.tenantId === user._id ||
                     ticket.vendorId === user._id;

    if (!hasAccess) throw new Error("Insufficient permissions");

    return ticket;
  },
});

export const getSLAMetrics = query({
  args: { 
    propertyId: v.id("properties"),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "read");
    if (!hasAccess) throw new Error("Insufficient permissions");

    const tickets = await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();

    // Filter by date range if provided
    const filteredTickets = tickets.filter(ticket => {
      if (args.startDate && ticket.createdAt < args.startDate) return false;
      if (args.endDate && ticket.createdAt > args.endDate) return false;
      return true;
    });

    const totalTickets = filteredTickets.length;
    const completedTickets = filteredTickets.filter(t => t.status === "completed" || t.status === "closed");
    const slaCompliantTickets = completedTickets.filter(t => 
      t.completionDetails && t.completionDetails.completedAt <= t.slaDeadline
    );

    const averageResolutionTime = completedTickets.length > 0
      ? completedTickets.reduce((sum, ticket) => {
          if (ticket.completionDetails) {
            return sum + (ticket.completionDetails.completedAt - ticket.createdAt);
          }
          return sum;
        }, 0) / completedTickets.length / (1000 * 60 * 60) // Convert to hours
      : 0;

    return {
      totalTickets,
      completedTickets: completedTickets.length,
      slaCompliance: totalTickets > 0 ? (slaCompliantTickets.length / totalTickets) * 100 : 0,
      averageResolutionTime,
      ticketsByPriority: {
        emergency: filteredTickets.filter(t => t.priority === "emergency").length,
        high: filteredTickets.filter(t => t.priority === "high").length,
        medium: filteredTickets.filter(t => t.priority === "medium").length,
        low: filteredTickets.filter(t => t.priority === "low").length,
      },
      ticketsByStatus: {
        open: filteredTickets.filter(t => t.status === "open").length,
        assigned: filteredTickets.filter(t => t.status === "assigned").length,
        in_progress: filteredTickets.filter(t => t.status === "in_progress").length,
        completed: filteredTickets.filter(t => t.status === "completed").length,
        closed: filteredTickets.filter(t => t.status === "closed").length,
        escalated: filteredTickets.filter(t => t.status === "escalated").length,
      },
    };
  },
});

export const getTicketsApproachingSLA = query({
  args: {
    propertyId: v.id("properties"),
    hoursAhead: v.number(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "read");
    if (!hasAccess) throw new Error("Insufficient permissions");

    const now = Date.now();
    const futureTime = now + (args.hoursAhead * 60 * 60 * 1000);

    const tickets = await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();

    return tickets.filter(ticket => {
      // Only include active tickets
      if (['completed', 'closed'].includes(ticket.status)) return false;
      
      // SLA deadline is within the specified time range
      return ticket.slaDeadline >= now && ticket.slaDeadline <= futureTime;
    }).sort((a, b) => a.slaDeadline - b.slaDeadline);
  },
});

export const getOverdueTickets = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "read");
    if (!hasAccess) throw new Error("Insufficient permissions");

    const now = Date.now();

    const tickets = await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();

    return tickets.filter(ticket => {
      // Only include active tickets that are overdue
      return !['completed', 'closed'].includes(ticket.status) && 
             ticket.slaDeadline < now;
    }).sort((a, b) => a.slaDeadline - b.slaDeadline);
  },
});

export const getTicketEscalations = query({
  args: { ticketId: v.id("maintenanceTickets") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const ticket = await ctx.db.get(args.ticketId);
    if (!ticket) throw new Error("Ticket not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "read") ||
                     ticket.tenantId === user._id ||
                     ticket.vendorId === user._id;

    if (!hasAccess) throw new Error("Insufficient permissions");

    return await ctx.db
      .query("maintenanceEscalations")
      .withIndex("by_ticket", (q) => q.eq("ticketId", args.ticketId))
      .collect();
  },
});

export const getMaintenanceAnalytics = query({
  args: {
    propertyId: v.id("properties"),
    startDate: v.number(),
    endDate: v.number(),
    category: v.optional(v.union(
      v.literal("plumbing"),
      v.literal("electrical"),
      v.literal("hvac"),
      v.literal("appliance"),
      v.literal("structural"),
      v.literal("cleaning"),
      v.literal("security"),
      v.literal("other")
    )),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "read");
    if (!hasAccess) throw new Error("Insufficient permissions");

    // Get tickets for the period
    const tickets = await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();

    const filteredTickets = tickets.filter(ticket => {
      const inDateRange = ticket.createdAt >= args.startDate && ticket.createdAt <= args.endDate;
      const matchesCategory = !args.category || ticket.category === args.category;
      return inDateRange && matchesCategory;
    });

    // Get previous period for comparison
    const periodLength = args.endDate - args.startDate;
    const previousStartDate = args.startDate - periodLength;
    const previousEndDate = args.startDate;

    const previousTickets = tickets.filter(ticket => 
      ticket.createdAt >= previousStartDate && ticket.createdAt <= previousEndDate
    );

    const completedTickets = filteredTickets.filter(t => t.status === "completed" || t.status === "closed");
    const slaCompliantTickets = completedTickets.filter(t => 
      t.completionDetails && t.completionDetails.completedAt <= t.slaDeadline
    );

    const averageResolutionTime = completedTickets.length > 0
      ? completedTickets.reduce((sum, ticket) => {
          if (ticket.completionDetails) {
            return sum + (ticket.completionDetails.completedAt - ticket.createdAt);
          }
          return sum;
        }, 0) / completedTickets.length / (1000 * 60 * 60)
      : 0;

    // Previous period metrics
    const previousCompleted = previousTickets.filter(t => t.status === "completed" || t.status === "closed");
    const previousSLACompliant = previousCompleted.filter(t => 
      t.completionDetails && t.completionDetails.completedAt <= t.slaDeadline
    );
    const previousAvgResolution = previousCompleted.length > 0
      ? previousCompleted.reduce((sum, ticket) => {
          if (ticket.completionDetails) {
            return sum + (ticket.completionDetails.completedAt - ticket.createdAt);
          }
          return sum;
        }, 0) / previousCompleted.length / (1000 * 60 * 60)
      : 0;

    // Category breakdown
    const categories = ["plumbing", "electrical", "hvac", "appliance", "structural", "cleaning", "security", "other"];
    const ticketsByCategory = categories.map(category => ({
      category,
      count: filteredTickets.filter(t => t.category === category).length
    })).filter(item => item.count > 0);

    // Priority breakdown
    const priorities = ["emergency", "high", "medium", "low"];
    const ticketsByPriority = priorities.map(priority => ({
      priority,
      count: filteredTickets.filter(t => t.priority === priority).length
    }));

    // Resolution time by category
    const resolutionTimeByCategory = categories.map(category => {
      const categoryTickets = completedTickets.filter(t => t.category === category);
      const avgTime = categoryTickets.length > 0
        ? categoryTickets.reduce((sum, ticket) => {
            if (ticket.completionDetails) {
              return sum + (ticket.completionDetails.completedAt - ticket.createdAt);
            }
            return sum;
          }, 0) / categoryTickets.length / (1000 * 60 * 60)
        : 0;
      
      return {
        category,
        averageTime: avgTime
      };
    }).filter(item => item.averageTime > 0);

    // SLA compliance by priority
    const slaComplianceByPriority = priorities.map(priority => {
      const priorityTickets = completedTickets.filter(t => t.priority === priority);
      const compliantTickets = priorityTickets.filter(t => 
        t.completionDetails && t.completionDetails.completedAt <= t.slaDeadline
      );
      
      return {
        priority,
        compliance: priorityTickets.length > 0 ? (compliantTickets.length / priorityTickets.length) * 100 : 0
      };
    });

    return {
      totalTickets: filteredTickets.length,
      completedTickets: completedTickets.length,
      slaCompliance: filteredTickets.length > 0 ? (slaCompliantTickets.length / filteredTickets.length) * 100 : 0,
      averageResolutionTime,
      ticketsByCategory,
      ticketsByPriority,
      resolutionTimeByCategory,
      slaComplianceByPriority,
      previousPeriod: {
        totalTickets: previousTickets.length,
        completedTickets: previousCompleted.length,
        slaCompliance: previousTickets.length > 0 ? (previousSLACompliant.length / previousTickets.length) * 100 : 0,
        averageResolutionTime: previousAvgResolution
      }
    };
  },
});

export const getCostAnalytics = query({
  args: {
    propertyId: v.id("properties"),
    startDate: v.number(),
    endDate: v.number(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "read");
    if (!hasAccess) throw new Error("Insufficient permissions");

    const tickets = await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();

    const filteredTickets = tickets.filter(ticket => 
      ticket.createdAt >= args.startDate && 
      ticket.createdAt <= args.endDate &&
      ticket.actualCost !== undefined
    );

    const totalCost = filteredTickets.reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0);

    // Previous period comparison
    const periodLength = args.endDate - args.startDate;
    const previousTickets = tickets.filter(ticket => 
      ticket.createdAt >= (args.startDate - periodLength) && 
      ticket.createdAt <= args.startDate &&
      ticket.actualCost !== undefined
    );
    const previousTotalCost = previousTickets.reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0);

    // Cost by category
    const categories = ["plumbing", "electrical", "hvac", "appliance", "structural", "cleaning", "security", "other"];
    const costByCategory = categories.map(category => ({
      category,
      cost: filteredTickets
        .filter(t => t.category === category)
        .reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0)
    })).filter(item => item.cost > 0);

    // Average cost by priority
    const priorities = ["emergency", "high", "medium", "low"];
    const averageCostByPriority = priorities.map(priority => {
      const priorityTickets = filteredTickets.filter(t => t.priority === priority);
      const totalCost = priorityTickets.reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0);
      
      return {
        priority,
        averageCost: priorityTickets.length > 0 ? totalCost / priorityTickets.length : 0
      };
    });

    // Emergency vs regular cost ratio
    const emergencyTickets = filteredTickets.filter(t => t.priority === "emergency");
    const emergencyCost = emergencyTickets.reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0);
    const emergencyCostRatio = totalCost > 0 ? emergencyCost / totalCost : 0;

    return {
      totalCost,
      costByCategory,
      averageCostByPriority,
      emergencyCostRatio,
      budget: 10000, // This would come from property settings
      previousPeriod: {
        totalCost: previousTotalCost
      }
    };
  },
});

export const getVendorPerformanceAnalytics = query({
  args: {
    propertyId: v.id("properties"),
    startDate: v.number(),
    endDate: v.number(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "read");
    if (!hasAccess) throw new Error("Insufficient permissions");

    const tickets = await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();

    const filteredTickets = tickets.filter(ticket => 
      ticket.createdAt >= args.startDate && 
      ticket.createdAt <= args.endDate &&
      ticket.vendorId
    );

    // Get vendor information
    const vendors = await ctx.db.query("vendors").collect();
    const vendorMap = new Map(vendors.map(v => [v.userId, v]));

    // Group tickets by vendor
    const vendorTickets = new Map();
    filteredTickets.forEach(ticket => {
      if (ticket.vendorId) {
        if (!vendorTickets.has(ticket.vendorId)) {
          vendorTickets.set(ticket.vendorId, []);
        }
        vendorTickets.get(ticket.vendorId).push(ticket);
      }
    });

    // Calculate vendor performance
    const topVendors = Array.from(vendorTickets.entries()).map(([vendorId, tickets]) => {
      const vendor = vendorMap.get(vendorId);
      if (!vendor) return null;

      const completedTickets = tickets.filter(t => t.status === "completed" || t.status === "closed");
      const slaCompliantTickets = completedTickets.filter(t => 
        t.completionDetails && t.completionDetails.completedAt <= t.slaDeadline
      );

      const averageRating = completedTickets.length > 0
        ? completedTickets.reduce((sum, ticket) => sum + (ticket.completionDetails?.satisfactionRating || 0), 0) / completedTickets.length
        : 0;

      const averageResponseTime = tickets.length > 0
        ? tickets.reduce((sum, ticket) => {
            if (ticket.vendorId && ticket.assignedBy) {
              // Calculate time from assignment to first update
              return sum + 2; // Simplified calculation
            }
            return sum;
          }, 0) / tickets.length
        : 0;

      return {
        vendorId,
        companyName: vendor.companyName,
        totalJobs: tickets.length,
        completedJobs: completedTickets.length,
        slaCompliance: tickets.length > 0 ? (slaCompliantTickets.length / tickets.length) * 100 : 0,
        averageRating,
        averageResponseTime
      };
    }).filter(Boolean).sort((a, b) => {
      const scoreA = (a.averageRating * 0.4) + (a.slaCompliance * 0.4) + ((100 - a.averageResponseTime) * 0.2);
      const scoreB = (b.averageRating * 0.4) + (b.slaCompliance * 0.4) + ((100 - b.averageResponseTime) * 0.2);
      return scoreB - scoreA;
    });

    // Cost by vendor
    const costByVendor = Array.from(vendorTickets.entries()).map(([vendorId, tickets]) => {
      const vendor = vendorMap.get(vendorId);
      if (!vendor) return null;

      const totalCost = tickets.reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0);
      
      return {
        vendorId,
        companyName: vendor.companyName,
        totalCost
      };
    }).filter(Boolean);

    // Utilization by vendor
    const utilizationByVendor = Array.from(vendorTickets.entries()).map(([vendorId, tickets]) => {
      const vendor = vendorMap.get(vendorId);
      if (!vendor) return null;

      return {
        vendorId,
        name: vendor.companyName,
        jobCount: tickets.length
      };
    }).filter(Boolean);

    return {
      topVendors,
      costByVendor,
      utilizationByVendor
    };
  },
});

export const getMaintenanceTrends = query({
  args: {
    propertyId: v.id("properties"),
    startDate: v.number(),
    endDate: v.number(),
    interval: v.union(v.literal("daily"), v.literal("weekly"), v.literal("monthly")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    const hasAccess = await checkPermission(ctx, user._id, "maintenance", "read");
    if (!hasAccess) throw new Error("Insufficient permissions");

    const tickets = await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();

    const filteredTickets = tickets.filter(ticket => 
      ticket.createdAt >= args.startDate && ticket.createdAt <= args.endDate
    );

    // Generate time buckets based on interval
    const buckets = new Map();
    const bucketSize = args.interval === "daily" ? 24 * 60 * 60 * 1000 : 
                     args.interval === "weekly" ? 7 * 24 * 60 * 60 * 1000 :
                     30 * 24 * 60 * 60 * 1000;

    for (let time = args.startDate; time <= args.endDate; time += bucketSize) {
      const dateKey = new Date(time).toISOString().split('T')[0];
      buckets.set(dateKey, {
        date: dateKey,
        tickets: [],
        count: 0,
        cost: 0,
        totalResolutionTime: 0,
        completedCount: 0
      });
    }

    // Distribute tickets into buckets
    filteredTickets.forEach(ticket => {
      const ticketDate = new Date(ticket.createdAt).toISOString().split('T')[0];
      const bucket = buckets.get(ticketDate);
      if (bucket) {
        bucket.tickets.push(ticket);
        bucket.count++;
        bucket.cost += ticket.actualCost || 0;
        
        if (ticket.completionDetails) {
          bucket.totalResolutionTime += ticket.completionDetails.completedAt - ticket.createdAt;
          bucket.completedCount++;
        }
      }
    });

    // Convert to arrays for charts
    const ticketVolume = Array.from(buckets.values()).map(bucket => ({
      date: bucket.date,
      count: bucket.count
    }));

    const costTrend = Array.from(buckets.values()).map(bucket => ({
      date: bucket.date,
      cost: bucket.cost
    }));

    const resolutionTimeTrend = Array.from(buckets.values()).map(bucket => ({
      date: bucket.date,
      averageTime: bucket.completedCount > 0 ? 
        (bucket.totalResolutionTime / bucket.completedCount) / (1000 * 60 * 60) : 0
    }));

    return {
      ticketVolume,
      costTrend,
      resolutionTimeTrend
    };
  },
});

// Get open tickets for tenant
export const getOpenTickets = query({
  args: { tenantId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_tenant", (q) => q.eq("tenantId", args.tenantId))
      .filter((q) => q.neq(q.field("status"), "completed"))
      .filter((q) => q.neq(q.field("status"), "closed"))
      .collect();
  },
});

// Get tickets by tenant
export const getTicketsByTenant = query({
  args: { tenantId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_tenant", (q) => q.eq("tenantId", args.tenantId))
      .order("desc")
      .collect();
  },
});