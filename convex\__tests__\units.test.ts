import { convexTest } from 'convex-test';
import { describe, it, expect, beforeEach } from 'vitest';
import { api } from '../_generated/api';
import schema from '../schema';

describe('Units API', () => {
  let t: any;

  beforeEach(async () => {
    t = convexTest(schema);
  });

  describe('createUnit', () => {
    it('should create a unit successfully', async () => {
      // Create test property first
      const propertyId = await t.run(async (ctx: any) => {
        const userId = await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert('properties', {
          name: 'Test Property',
          type: 'residential',
          address: {
            street: '123 Main Street',
            city: 'Nairobi',
            state: 'Nairobi County',
            postalCode: '00100',
            country: 'Kenya',
          },
          ownerId: userId,
          branding: {},
          settings: {},
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const unitData = {
        propertyId,
        unitNumber: 'A101',
        type: 'apartment' as const,
        size: 1200,
        rent: 50000,
        status: 'vacant' as const,
        amenities: ['parking', 'balcony'],
      };

      const unitId = await t.mutation(api.units.create, unitData);
      expect(unitId).toBeDefined();

      const unit = await t.query(api.units.getById, { id: unitId });
      expect(unit).toMatchObject({
        unitNumber: 'A101',
        type: 'apartment',
        size: 1200,
        rent: 50000,
        status: 'vacant',
      });
    });

    it('should validate required fields', async () => {
      const invalidData = {
        propertyId: 'invalid-id',
        unitNumber: '',
        type: 'apartment' as const,
        size: -1,
        rent: -100,
      };

      await expect(
        t.mutation(api.units.create, invalidData)
      ).rejects.toThrow();
    });

    it('should enforce unique unit numbers per property', async () => {
      const propertyId = await t.run(async (ctx: any) => {
        const userId = await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert('properties', {
          name: 'Test Property',
          type: 'residential',
          address: {
            street: '123 Main Street',
            city: 'Nairobi',
            state: 'Nairobi County',
            postalCode: '00100',
            country: 'Kenya',
          },
          ownerId: userId,
          branding: {},
          settings: {},
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const unitData = {
        propertyId,
        unitNumber: 'A101',
        type: 'apartment' as const,
        size: 1200,
        rent: 50000,
        status: 'vacant' as const,
        amenities: [],
      };

      // Create first unit
      await t.mutation(api.units.create, unitData);

      // Try to create duplicate
      await expect(
        t.mutation(api.units.create, unitData)
      ).rejects.toThrow('Unit number already exists in this property');
    });
  });

  describe('updateUnit', () => {
    it('should update unit successfully', async () => {
      const propertyId = await t.run(async (ctx: any) => {
        const userId = await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert('properties', {
          name: 'Test Property',
          type: 'residential',
          address: {
            street: '123 Main Street',
            city: 'Nairobi',
            state: 'Nairobi County',
            postalCode: '00100',
            country: 'Kenya',
          },
          ownerId: userId,
          branding: {},
          settings: {},
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const unitId = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A101',
        type: 'apartment' as const,
        size: 1200,
        rent: 50000,
        status: 'vacant' as const,
        amenities: [],
      });

      await t.mutation(api.units.update, {
        id: unitId,
        rent: 55000,
        status: 'occupied' as const,
      });

      const updatedUnit = await t.query(api.units.getById, { id: unitId });
      expect(updatedUnit?.rent).toBe(55000);
      expect(updatedUnit?.status).toBe('occupied');
    });

    it('should prevent status change if unit has active lease', async () => {
      const propertyId = await t.run(async (ctx: any) => {
        const userId = await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert('properties', {
          name: 'Test Property',
          type: 'residential',
          address: {
            street: '123 Main Street',
            city: 'Nairobi',
            state: 'Nairobi County',
            postalCode: '00100',
            country: 'Kenya',
          },
          ownerId: userId,
          branding: {},
          settings: {},
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const unitId = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A101',
        type: 'apartment' as const,
        size: 1200,
        rent: 50000,
        status: 'occupied' as const,
        amenities: [],
      });

      // Create active lease
      await t.run(async (ctx: any) => {
        const tenantId = await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Tenant User',
          role: 'tenant',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        await ctx.db.insert('leases', {
          propertyId,
          unitId,
          tenantId,
          startDate: Date.now(),
          endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
          monthlyRent: 50000,
          deposit: 100000,
          status: 'active',
          documents: [],
          eSignatureStatus: 'signed',
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      await expect(
        t.mutation(api.units.update, {
          id: unitId,
          status: 'vacant' as const,
        })
      ).rejects.toThrow('Cannot change status of unit with active lease');
    });
  });

  describe('getUnitsByProperty', () => {
    it('should return units for a property', async () => {
      const propertyId = await t.run(async (ctx: any) => {
        const userId = await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert('properties', {
          name: 'Test Property',
          type: 'residential',
          address: {
            street: '123 Main Street',
            city: 'Nairobi',
            state: 'Nairobi County',
            postalCode: '00100',
            country: 'Kenya',
          },
          ownerId: userId,
          branding: {},
          settings: {},
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      // Create multiple units
      await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A101',
        type: 'apartment' as const,
        size: 1200,
        rent: 50000,
        status: 'vacant' as const,
        amenities: [],
      });

      await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A102',
        type: 'apartment' as const,
        size: 1000,
        rent: 45000,
        status: 'occupied' as const,
        amenities: [],
      });

      const units = await t.query(api.units.getByProperty, { propertyId });
      expect(units).toHaveLength(2);
      expect(units.map(u => u.unitNumber)).toEqual(['A101', 'A102']);
    });

    it('should filter units by status', async () => {
      const propertyId = await t.run(async (ctx: any) => {
        const userId = await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert('properties', {
          name: 'Test Property',
          type: 'residential',
          address: {
            street: '123 Main Street',
            city: 'Nairobi',
            state: 'Nairobi County',
            postalCode: '00100',
            country: 'Kenya',
          },
          ownerId: userId,
          branding: {},
          settings: {},
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A101',
        type: 'apartment' as const,
        size: 1200,
        rent: 50000,
        status: 'vacant' as const,
        amenities: [],
      });

      await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A102',
        type: 'apartment' as const,
        size: 1000,
        rent: 45000,
        status: 'occupied' as const,
        amenities: [],
      });

      const vacantUnits = await t.query(api.units.getByStatus, {
        propertyId,
        status: 'vacant',
      });
      expect(vacantUnits).toHaveLength(1);
      expect(vacantUnits[0].unitNumber).toBe('A101');
    });
  });

  describe('deleteUnit', () => {
    it('should delete unit successfully', async () => {
      const propertyId = await t.run(async (ctx: any) => {
        const userId = await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert('properties', {
          name: 'Test Property',
          type: 'residential',
          address: {
            street: '123 Main Street',
            city: 'Nairobi',
            state: 'Nairobi County',
            postalCode: '00100',
            country: 'Kenya',
          },
          ownerId: userId,
          branding: {},
          settings: {},
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const unitId = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A101',
        type: 'apartment' as const,
        size: 1200,
        rent: 50000,
        status: 'vacant' as const,
        amenities: [],
      });

      await t.mutation(api.units.remove, { id: unitId });

      const deletedUnit = await t.query(api.units.getById, { id: unitId });
      expect(deletedUnit).toBeNull();
    });

    it('should prevent deletion if unit has active lease', async () => {
      const propertyId = await t.run(async (ctx: any) => {
        const userId = await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert('properties', {
          name: 'Test Property',
          type: 'residential',
          address: {
            street: '123 Main Street',
            city: 'Nairobi',
            state: 'Nairobi County',
            postalCode: '00100',
            country: 'Kenya',
          },
          ownerId: userId,
          branding: {},
          settings: {},
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const unitId = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A101',
        type: 'apartment' as const,
        size: 1200,
        rent: 50000,
        status: 'occupied' as const,
        amenities: [],
      });

      // Create active lease
      await t.run(async (ctx: any) => {
        const tenantId = await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Tenant User',
          role: 'tenant',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        await ctx.db.insert('leases', {
          propertyId,
          unitId,
          tenantId,
          startDate: Date.now(),
          endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
          monthlyRent: 50000,
          deposit: 100000,
          status: 'active',
          documents: [],
          eSignatureStatus: 'signed',
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      await expect(
        t.mutation(api.units.remove, { id: unitId })
      ).rejects.toThrow('Cannot delete unit with active lease');
    });
  });

  describe('calculateOccupancyRate', () => {
    it('should calculate occupancy rate correctly', async () => {
      const propertyId = await t.run(async (ctx: any) => {
        const userId = await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert('properties', {
          name: 'Test Property',
          type: 'residential',
          address: {
            street: '123 Main Street',
            city: 'Nairobi',
            state: 'Nairobi County',
            postalCode: '00100',
            country: 'Kenya',
          },
          ownerId: userId,
          branding: {},
          settings: {},
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      // Create units with different statuses
      await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A101',
        type: 'apartment' as const,
        size: 1200,
        rent: 50000,
        status: 'occupied' as const,
        amenities: [],
      });

      await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A102',
        type: 'apartment' as const,
        size: 1000,
        rent: 45000,
        status: 'occupied' as const,
        amenities: [],
      });

      await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A103',
        type: 'apartment' as const,
        size: 900,
        rent: 40000,
        status: 'vacant' as const,
        amenities: [],
      });

      await t.mutation(api.units.create, {
        propertyId,
        unitNumber: 'A104',
        type: 'apartment' as const,
        size: 1100,
        rent: 48000,
        status: 'maintenance' as const,
        amenities: [],
      });

      const occupancyRate = await t.query(api.units.getOccupancyRate, { propertyId });
      expect(occupancyRate).toBe(50); // 2 out of 4 units occupied
    });

    it('should return 0 for property with no units', async () => {
      const propertyId = await t.run(async (ctx: any) => {
        const userId = await ctx.db.insert('users', {
          email: '<EMAIL>',
          name: 'Property Owner',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert('properties', {
          name: 'Empty Property',
          type: 'residential',
          address: {
            street: '123 Main Street',
            city: 'Nairobi',
            state: 'Nairobi County',
            postalCode: '00100',
            country: 'Kenya',
          },
          ownerId: userId,
          branding: {},
          settings: {},
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const occupancyRate = await t.query(api.units.getOccupancyRate, { propertyId });
      expect(occupancyRate).toBe(0);
    });
  });
});