// Custom WhatsApp Service to replace Twilio WhatsApp functionality
export interface CustomWhatsAppConfig {
  apiUrl: string;
  apiKey: string;
  businessPhoneNumberId?: string;
}

export interface WhatsAppMessage {
  phoneNumber: string;
  message: string;
  type?: 'text' | 'template' | 'media';
  mediaUrl?: string;
  mediaType?: 'image' | 'document' | 'video' | 'audio';
  templateName?: string;
  templateLanguage?: string;
  templateParameters?: string[];
}

export interface WhatsAppResult {
  success: boolean;
  messageId?: string;
  status?: string;
  error?: string;
}

export interface WhatsAppTemplate {
  name: string;
  language: string;
  content: string;
  variables: string[];
  category: 'marketing' | 'utility' | 'authentication';
}

export interface WhatsAppDeliveryStatus {
  messageId: string;
  status: 'pending' | 'sent' | 'delivered' | 'read' | 'failed';
  timestamp?: Date;
  error?: string;
}

export interface BulkWhatsAppRequest {
  messages: WhatsAppMessage[];
  template?: WhatsAppTemplate;
  personalizedData?: Record<string, Record<string, any>>;
}

export interface BulkWhatsAppResult {
  success: boolean;
  totalMessages: number;
  successfulMessages: number;
  failedMessages: number;
  results: WhatsAppResult[];
  error?: string;
}

export class CustomWhatsAppService {
  private config: CustomWhatsAppConfig;

  constructor(config: CustomWhatsAppConfig) {
    this.config = config;
  }

  /**
   * Send a WhatsApp text message
   */
  async sendMessage(message: WhatsAppMessage): Promise<WhatsAppResult> {
    try {
      // Format phone number
      const formattedNumber = this.formatPhoneNumber(message.phoneNumber);
      
      if (!this.isValidPhoneNumber(formattedNumber)) {
        return {
          success: false,
          error: "Invalid phone number format",
        };
      }

      // TODO: Replace with actual WhatsApp Business API integration
      // This is a placeholder implementation
      const payload = {
        messaging_product: "whatsapp",
        to: formattedNumber.replace('+', ''),
        type: message.type || 'text',
        ...(message.type === 'text' && {
          text: {
            body: message.message
          }
        }),
        ...(message.type === 'template' && {
          template: {
            name: message.templateName,
            language: {
              code: message.templateLanguage || 'en'
            },
            components: message.templateParameters ? [{
              type: "body",
              parameters: message.templateParameters.map(param => ({
                type: "text",
                text: param
              }))
            }] : []
          }
        }),
        ...(message.type === 'media' && {
          [message.mediaType || 'image']: {
            link: message.mediaUrl
          }
        })
      };

      const response = await fetch(`${this.config.apiUrl}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`,
        };
      }

      const data = await response.json();
      
      return {
        success: true,
        messageId: data.messages?.[0]?.id,
        status: data.messages?.[0]?.message_status || 'sent',
      };
    } catch (error) {
      console.error('WhatsApp message sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Send templated WhatsApp message
   */
  async sendTemplateMessage(
    phoneNumber: string,
    template: WhatsAppTemplate,
    parameters: Record<string, any>
  ): Promise<WhatsAppResult> {
    try {
      // Process template parameters
      const templateParams = template.variables.map(variable => 
        String(parameters[variable] || '')
      );

      return await this.sendMessage({
        phoneNumber,
        message: '', // Not used for template messages
        type: 'template',
        templateName: template.name,
        templateLanguage: template.language,
        templateParameters: templateParams,
      });
    } catch (error) {
      console.error('WhatsApp template message sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Template processing failed',
      };
    }
  }

  /**
   * Send media message (image, document, etc.)
   */
  async sendMediaMessage(
    phoneNumber: string,
    mediaUrl: string,
    mediaType: 'image' | 'document' | 'video' | 'audio',
    caption?: string
  ): Promise<WhatsAppResult> {
    try {
      return await this.sendMessage({
        phoneNumber,
        message: caption || '',
        type: 'media',
        mediaUrl,
        mediaType,
      });
    } catch (error) {
      console.error('WhatsApp media message sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Media message failed',
      };
    }
  }

  /**
   * Send bulk WhatsApp messages
   */
  async sendBulkMessages(request: BulkWhatsAppRequest): Promise<BulkWhatsAppResult> {
    try {
      const results: WhatsAppResult[] = [];
      let successfulMessages = 0;
      let failedMessages = 0;

      for (const message of request.messages) {
        let processedMessage = { ...message };

        // Apply template if provided
        if (request.template && request.personalizedData) {
          const personalData = request.personalizedData[message.phoneNumber];
          if (personalData) {
            const result = await this.sendTemplateMessage(
              message.phoneNumber,
              request.template,
              personalData
            );
            results.push(result);
            
            if (result.success) {
              successfulMessages++;
            } else {
              failedMessages++;
            }
            continue;
          }
        }

        const result = await this.sendMessage(processedMessage);
        results.push(result);

        if (result.success) {
          successfulMessages++;
        } else {
          failedMessages++;
        }

        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      return {
        success: successfulMessages > 0,
        totalMessages: request.messages.length,
        successfulMessages,
        failedMessages,
        results,
      };
    } catch (error) {
      console.error('Bulk WhatsApp sending failed:', error);
      return {
        success: false,
        totalMessages: request.messages.length,
        successfulMessages: 0,
        failedMessages: request.messages.length,
        results: [],
        error: error instanceof Error ? error.message : 'Bulk WhatsApp failed',
      };
    }
  }

  /**
   * Get message delivery status
   */
  async getDeliveryStatus(messageId: string): Promise<WhatsAppDeliveryStatus | null> {
    try {
      // TODO: Replace with actual WhatsApp Business API integration
      const response = await fetch(`${this.config.apiUrl}/messages/${messageId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      
      return {
        messageId,
        status: data.status || 'pending',
        timestamp: data.timestamp ? new Date(data.timestamp) : undefined,
        error: data.error,
      };
    } catch (error) {
      console.error('Failed to get WhatsApp delivery status:', error);
      return null;
    }
  }

  /**
   * Format phone number for WhatsApp
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, '');

    // Convert to international format for Kenya
    if (cleaned.startsWith('0')) {
      cleaned = '254' + cleaned.substring(1);
    } else if (cleaned.startsWith('7') || cleaned.startsWith('1')) {
      cleaned = '254' + cleaned;
    } else if (!cleaned.startsWith('254')) {
      cleaned = '254' + cleaned;
    }

    return '+' + cleaned;
  }

  /**
   * Validate phone number for WhatsApp
   */
  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Remove + and check for valid format
    const cleaned = phoneNumber.replace(/^\+/, '');
    const pattern = /^254[17]\d{8}$/;
    return pattern.test(cleaned);
  }
}

// Factory function to create CustomWhatsAppService instance
export function createCustomWhatsAppService(): CustomWhatsAppService {
  const config: CustomWhatsAppConfig = {
    apiUrl: process.env.CUSTOM_WHATSAPP_API_URL || '',
    apiKey: process.env.CUSTOM_WHATSAPP_API_KEY || '',
    businessPhoneNumberId: process.env.CUSTOM_WHATSAPP_PHONE_NUMBER_ID,
  };

  if (!config.apiUrl || !config.apiKey) {
    throw new Error('Custom WhatsApp API configuration missing. Please set CUSTOM_WHATSAPP_API_URL and CUSTOM_WHATSAPP_API_KEY environment variables.');
  }

  return new CustomWhatsAppService(config);
}
