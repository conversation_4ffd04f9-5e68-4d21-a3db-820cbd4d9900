import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { Separator } from '../ui/separator'
import { Switch } from '../ui/switch'
import { Alert, AlertDescription } from '../ui/alert'
import { 
  Download, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle, 
  Info,
  Settings,
  Rocket
} from 'lucide-react'
import { useAutoUpdater } from '../../hooks/useAutoUpdater'
import { useToast } from '../ui/use-toast'

export const UpdateManager: React.FC = () => {
  const [showReleaseNotes, setShowReleaseNotes] = useState(false)
  const { toast } = useToast()
  
  const {
    updateStatus,
    isChecking,
    isDownloading,
    currentVersion,
    autoLaunch,
    isUpdateAvailable,
    isUpdateDownloaded,
    downloadProgress,
    checkForUpdates,
    downloadUpdate,
    installUpdate,
    setAutoLaunch,
    reportCrash
  } = useAutoUpdater()

  const handleCheckForUpdates = async () => {
    try {
      await checkForUpdates()
      toast({
        title: 'Update Check',
        description: 'Checking for updates...'
      })
    } catch (error) {
      toast({
        title: 'Update Check Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive'
      })
    }
  }

  const handleDownloadUpdate = async () => {
    try {
      await downloadUpdate()
      toast({
        title: 'Download Started',
        description: 'Update download has started'
      })
    } catch (error) {
      toast({
        title: 'Download Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive'
      })
    }
  }

  const handleInstallUpdate = async () => {
    try {
      await installUpdate()
      // App will restart, so this won't be reached
    } catch (error) {
      toast({
        title: 'Installation Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive'
      })
    }
  }

  const handleAutoLaunchToggle = async (enabled: boolean) => {
    try {
      await setAutoLaunch(enabled)
      toast({
        title: 'Auto-launch Updated',
        description: `Auto-launch ${enabled ? 'enabled' : 'disabled'} successfully`
      })
    } catch (error) {
      toast({
        title: 'Auto-launch Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive'
      })
    }
  }

  const handleTestCrashReport = () => {
    const crashData = {
      timestamp: new Date().toISOString(),
      version: currentVersion,
      error: 'Test crash report',
      stack: 'Test stack trace',
      userAgent: navigator.userAgent
    }
    
    reportCrash(crashData)
    toast({
      title: 'Crash Report Sent',
      description: 'Test crash report has been logged'
    })
  }

  const getStatusIcon = () => {
    if (isChecking) return <RefreshCw className="h-4 w-4 animate-spin" />
    if (updateStatus?.type === 'error') return <AlertCircle className="h-4 w-4 text-red-500" />
    if (isUpdateDownloaded) return <CheckCircle className="h-4 w-4 text-green-500" />
    if (isUpdateAvailable) return <Download className="h-4 w-4 text-blue-500" />
    return <Info className="h-4 w-4" />
  }

  const getStatusText = () => {
    if (isChecking) return 'Checking for updates...'
    if (updateStatus?.type === 'error') return `Error: ${updateStatus.error}`
    if (isUpdateDownloaded) return `Update ${updateStatus.version} ready to install`
    if (isUpdateAvailable) return `Update ${updateStatus.version} available`
    if (updateStatus?.type === 'not-available') return 'No updates available'
    if (updateStatus?.type === 'download-progress') return `Downloading... ${Math.round(downloadProgress)}%`
    return 'Ready to check for updates'
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatSpeed = (bytesPerSecond: number) => {
    return formatBytes(bytesPerSecond) + '/s'
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Rocket className="h-5 w-5" />
            Auto-Update Manager
          </CardTitle>
          <CardDescription>
            Manage application updates and deployment settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Version */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">Current Version</h3>
              <p className="text-sm text-muted-foreground">{currentVersion}</p>
            </div>
            <Badge variant="outline">{currentVersion}</Badge>
          </div>
          
          <Separator />
          
          {/* Update Status */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <span className="font-medium">Update Status</span>
            </div>
            
            <Alert>
              <AlertDescription>
                {getStatusText()}
              </AlertDescription>
            </Alert>
            
            {/* Download Progress */}
            {isDownloading && updateStatus?.type === 'download-progress' && (
              <div className="space-y-2">
                <Progress value={downloadProgress} className="w-full" />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>{Math.round(downloadProgress)}% complete</span>
                  <span>
                    {updateStatus.bytesPerSecond && formatSpeed(updateStatus.bytesPerSecond)}
                  </span>
                </div>
                {updateStatus.transferred && updateStatus.total && (
                  <div className="text-sm text-muted-foreground">
                    {formatBytes(updateStatus.transferred)} / {formatBytes(updateStatus.total)}
                  </div>
                )}
              </div>
            )}
            
            {/* Release Notes */}
            {isUpdateAvailable && updateStatus?.releaseNotes && (
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowReleaseNotes(!showReleaseNotes)}
                >
                  {showReleaseNotes ? 'Hide' : 'Show'} Release Notes
                </Button>
                
                {showReleaseNotes && (
                  <div className="p-3 bg-muted rounded-md text-sm">
                    <pre className="whitespace-pre-wrap">{updateStatus.releaseNotes}</pre>
                  </div>
                )}
              </div>
            )}
          </div>
          
          <Separator />
          
          {/* Update Actions */}
          <div className="flex gap-2">
            <Button
              onClick={handleCheckForUpdates}
              disabled={isChecking || isDownloading}
              variant="outline"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isChecking ? 'animate-spin' : ''}`} />
              Check for Updates
            </Button>
            
            {isUpdateAvailable && !isDownloading && !isUpdateDownloaded && (
              <Button onClick={handleDownloadUpdate}>
                <Download className="h-4 w-4 mr-2" />
                Download Update
              </Button>
            )}
            
            {isUpdateDownloaded && (
              <Button onClick={handleInstallUpdate} variant="default">
                <CheckCircle className="h-4 w-4 mr-2" />
                Install & Restart
              </Button>
            )}
          </div>
          
          <Separator />
          
          {/* Settings */}
          <div className="space-y-4">
            <h3 className="font-semibold flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </h3>
            
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Auto-launch on startup</p>
                <p className="text-sm text-muted-foreground">
                  Start EstatePulse automatically when you log in
                </p>
              </div>
              <Switch
                checked={autoLaunch}
                onCheckedChange={handleAutoLaunchToggle}
              />
            </div>
          </div>
          
          <Separator />
          
          {/* Development Tools */}
          <div className="space-y-3">
            <h3 className="font-semibold">Development Tools</h3>
            <Button
              onClick={handleTestCrashReport}
              variant="outline"
              size="sm"
            >
              Test Crash Reporting
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}