import React, { useCallback, useEffect, useRef } from 'react';
import {
    MonitoringService,
    trackEvent,
    reportError,
    trackPerformance,
    monitorQuery
} from '../lib/monitoring';

interface UseMonitoringOptions {
    userId?: string;
    userProperties?: Record<string, any>;
    autoTrackPageViews?: boolean;
    autoTrackErrors?: boolean;
}

interface MonitoringHook {
    // Event tracking
    track: (event: string, properties?: Record<string, any>) => void;

    // Error handling
    captureError: (error: Error, context?: Record<string, any>) => void;

    // Performance monitoring
    measurePerformance: (name: string, fn: () => void | Promise<void>) => Promise<void>;
    recordMetric: (metric: string, value: number, context?: Record<string, any>) => void;

    // Database monitoring
    monitorDatabaseQuery: <T>(
        queryName: string,
        queryFn: () => Promise<T>,
        context?: Record<string, any>
    ) => Promise<T>;

    // Feature flags
    isFeatureEnabled: (flag: string) => boolean;
    getFeatureFlag: (flag: string) => boolean | string;

    // User management
    identifyUser: (userId: string, properties?: Record<string, any>) => void;

    // Page tracking
    trackPageView: (page: string, properties?: Record<string, any>) => void;

    // Component lifecycle tracking
    trackComponentMount: (componentName: string, props?: Record<string, any>) => void;
    trackComponentUnmount: (componentName: string, duration?: number) => void;
}

export function useMonitoring(options: UseMonitoringOptions = {}): MonitoringHook {
    const {
        userId,
        userProperties,
        autoTrackPageViews = true,
        autoTrackErrors = true
    } = options;

    const monitoring = MonitoringService.getInstance();
    const mountTimeRef = useRef<number>(Date.now());


    // Initialize user identification
    useEffect(() => {
        if (userId) {
            monitoring.identifyUser(userId, userProperties);
        }
    }, [userId, userProperties, monitoring]);

    // Auto-track page views
    useEffect(() => {
        if (autoTrackPageViews) {
            const currentPath = window.location.pathname;
            trackEvent('page_view', {
                path: currentPath,
                referrer: document.referrer,
                timestamp: Date.now()
            });
        }
    }, [autoTrackPageViews]);

    // Auto-track unhandled errors
    useEffect(() => {
        if (autoTrackErrors) {
            const handleError = (event: ErrorEvent) => {
                reportError(new Error(event.message), {
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error?.stack
                });
            };

            const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
                reportError(new Error(`Unhandled Promise Rejection: ${event.reason}`), {
                    type: 'unhandled_promise_rejection',
                    reason: event.reason
                });
            };

            window.addEventListener('error', handleError);
            window.addEventListener('unhandledrejection', handleUnhandledRejection);

            return () => {
                window.removeEventListener('error', handleError);
                window.removeEventListener('unhandledrejection', handleUnhandledRejection);
            };
        }
    }, [autoTrackErrors]);

    // Event tracking
    const track = useCallback((event: string, properties?: Record<string, any>) => {
        trackEvent(event, {
            ...properties,
            timestamp: Date.now(),
            page: window.location.pathname
        });
    }, []);

    // Error capture with enhanced context
    const captureError = useCallback((error: Error, context?: Record<string, any>) => {
        reportError(error, {
            ...context,
            page: window.location.pathname,
            userAgent: navigator.userAgent,
            timestamp: Date.now()
        });
    }, []);

    // Performance measurement
    const measurePerformance = useCallback(async (name: string, fn: () => void | Promise<void>) => {
        const startTime = performance.now();

        try {
            await fn();
            const duration = performance.now() - startTime;
            trackPerformance(name, duration, {
                page: window.location.pathname,
                success: true
            });
        } catch (error) {
            const duration = performance.now() - startTime;
            trackPerformance(name, duration, {
                page: window.location.pathname,
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            throw error;
        }
    }, []);

    // Record custom metrics
    const recordMetric = useCallback((metric: string, value: number, context?: Record<string, any>) => {
        trackPerformance(metric, value, {
            ...context,
            page: window.location.pathname,
            timestamp: Date.now()
        });
    }, []);

    // Database query monitoring
    const monitorDatabaseQuery = useCallback(<T>(
        queryName: string,
        queryFn: () => Promise<T>,
        context?: Record<string, any>
    ) => {
        return monitorQuery(queryName, queryFn, {
            ...context,
            page: window.location.pathname
        });
    }, []);

    // Feature flags
    const isFeatureEnabled = useCallback((flag: string) => {
        return monitoring.isFeatureEnabled(flag);
    }, [monitoring]);

    const getFeatureFlag = useCallback((flag: string) => {
        return monitoring.getFeatureFlag(flag);
    }, [monitoring]);

    // User identification
    const identifyUser = useCallback((userId: string, properties?: Record<string, any>) => {
        monitoring.identifyUser(userId, properties);
    }, [monitoring]);

    // Page view tracking
    const trackPageView = useCallback((page: string, properties?: Record<string, any>) => {
        track('page_view', {
            ...properties,
            page,
            referrer: document.referrer
        });
    }, [track]);

    // Component lifecycle tracking
    const trackComponentMount = useCallback((componentName: string, props?: Record<string, any>) => {
        track('component_mount', {
            component: componentName,
            props: props ? Object.keys(props) : undefined,
            page: window.location.pathname
        });
    }, [track]);

    const trackComponentUnmount = useCallback((componentName: string, duration?: number) => {
        const actualDuration = duration || (Date.now() - mountTimeRef.current);
        track('component_unmount', {
            component: componentName,
            duration: actualDuration,
            page: window.location.pathname
        });
    }, [track]);

    return {
        track,
        captureError,
        measurePerformance,
        recordMetric,
        monitorDatabaseQuery,
        isFeatureEnabled,
        getFeatureFlag,
        identifyUser,
        trackPageView,
        trackComponentMount,
        trackComponentUnmount
    };
}

// Export type for the HOC - implementation moved to separate file
export type WithMonitoringHOC = <P extends object>(
    WrappedComponent: React.ComponentType<P>,
    componentName?: string
) => React.ComponentType<P>;

export default useMonitoring;