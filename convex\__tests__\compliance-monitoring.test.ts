import { convexTest } from "convex-test";
import { describe, it, expect } from "vitest";
import { api } from "../_generated/api";
import schema from "../schema";

describe("Compliance Monitoring", () => {
  it("should create compliance checklist", async () => {
    const t = convexTest(schema);

    // Create a user first
    const userId = await t.mutation(api.users.createUser, {
      email: "<EMAIL>",
      name: "Test User",
      role: "manager",
    });

    // Create a property
    const propertyId = await t.mutation(api.properties.createProperty, {
      name: "Test Property",
      type: "residential",
      address: {
        street: "123 Test St",
        city: "Test City",
        state: "Test State",
        country: "Test Country",
        postalCode: "12345",
      },
      ownerId: userId,
      branding: {
        primaryColor: "#000000",
        secondaryColor: "#ffffff",
      },
      settings: {
        currency: "KES",
        timezone: "Africa/Nairobi",
        language: "en",
        autoRentReminders: true,
        maintenanceSLA: 24,
      },
    });

    // Create compliance checklist
    const checklistId = await t.mutation(api.compliance.createComplianceChecklist, {
      name: "Tenant KYC Checklist",
      description: "Standard KYC requirements for tenants",
      entityType: "tenant",
      propertyId,
      requirements: [
        {
          id: "req_1",
          title: "National ID Verification",
          description: "Valid national ID document required",
          category: "kyc",
          priority: "high",
          requiredDocuments: ["national_id"],
          isRequired: true,
          automationRules: {
            autoCheck: true,
            checkFrequency: "monthly",
            conditions: "status == 'active'",
          },
        },
        {
          id: "req_2",
          title: "Proof of Income",
          description: "Recent salary slip or bank statement",
          category: "financial",
          priority: "medium",
          requiredDocuments: ["proof_of_income", "bank_statement"],
          isRequired: true,
          automationRules: {
            autoCheck: false,
          },
        },
      ],
      createdBy: userId,
    });

    expect(checklistId).toBeDefined();

    // Verify checklist was created
    const checklists = await t.query(api.compliance.getComplianceChecklists, {
      propertyId,
    });

    expect(checklists).toHaveLength(1);
    expect(checklists[0].name).toBe("Tenant KYC Checklist");
    expect(checklists[0].requirements).toHaveLength(2);
  });

  it("should run compliance check and update status", async () => {
    const t = convexTest(schema);

    // Create test data
    const userId = await t.mutation(api.users.createUser, {
      email: "<EMAIL>",
      name: "Test Tenant",
      role: "tenant",
    });

    const propertyId = await t.mutation(api.properties.createProperty, {
      name: "Test Property",
      type: "residential",
      address: {
        street: "123 Test St",
        city: "Test City",
        state: "Test State",
        country: "Test Country",
        postalCode: "12345",
      },
      ownerId: userId,
      branding: {
        primaryColor: "#000000",
        secondaryColor: "#ffffff",
      },
      settings: {
        currency: "KES",
        timezone: "Africa/Nairobi",
        language: "en",
        autoRentReminders: true,
        maintenanceSLA: 24,
      },
    });

    // Create checklist
    const checklistId = await t.mutation(api.compliance.createComplianceChecklist, {
      name: "Test Checklist",
      description: "Test compliance checklist",
      entityType: "tenant",
      propertyId,
      requirements: [
        {
          id: "req_1",
          title: "Test Requirement",
          description: "Test requirement description",
          category: "kyc",
          priority: "high",
          requiredDocuments: ["national_id"],
          isRequired: true,
          automationRules: {
            autoCheck: true,
          },
        },
      ],
      createdBy: userId,
    });

    // Run compliance check
    const results = await t.mutation(api.compliance.runComplianceCheck, {
      entityId: userId,
      entityType: "tenant",
      checklistId,
      propertyId,
      triggeredBy: userId,
    });

    expect(results).toHaveLength(1);
    expect(results[0].checklistId).toBe(checklistId);

    // Verify compliance status was created
    const complianceStatus = await t.query(api.compliance.getComplianceStatus, {
      entityId: userId,
      entityType: "tenant",
      checklistId,
    });

    expect(complianceStatus).toHaveLength(1);
    expect(complianceStatus[0].entityId).toBe(userId);
    expect(complianceStatus[0].entityType).toBe("tenant");
  });

  it("should generate regulatory reports", async () => {
    const t = convexTest(schema);

    // Create test data
    const userId = await t.mutation(api.users.createUser, {
      email: "<EMAIL>",
      name: "Test Manager",
      role: "manager",
    });

    const propertyId = await t.mutation(api.properties.createProperty, {
      name: "Test Property",
      type: "residential",
      address: {
        street: "123 Test St",
        city: "Test City",
        state: "Test State",
        country: "Test Country",
        postalCode: "12345",
      },
      ownerId: userId,
      branding: {
        primaryColor: "#000000",
        secondaryColor: "#ffffff",
      },
      settings: {
        currency: "KES",
        timezone: "Africa/Nairobi",
        language: "en",
        autoRentReminders: true,
        maintenanceSLA: 24,
      },
    });

    // Generate compliance summary report
    const reportId = await t.mutation(api.compliance.generateRegulatoryReport, {
      name: "Test Compliance Report",
      reportType: "compliance_summary",
      propertyIds: [propertyId],
      reportPeriod: {
        startDate: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30 days ago
        endDate: Date.now(),
        frequency: "monthly",
      },
      fileFormat: "pdf",
      recipients: [
        {
          email: "<EMAIL>",
          name: "Test Manager",
          role: "manager",
        },
      ],
      generatedBy: userId,
    });

    expect(reportId).toBeDefined();

    // Verify report was created
    const reports = await t.query(api.compliance.getRegulatoryReports, {
      reportType: "compliance_summary",
    });

    expect(reports).toHaveLength(1);
    expect(reports[0].name).toBe("Test Compliance Report");
    expect(reports[0].status).toBe("completed");
  });

  it("should track compliance alerts", async () => {
    const t = convexTest(schema);

    // Create test data
    const userId = await t.mutation(api.users.createUser, {
      email: "<EMAIL>",
      name: "Test Tenant",
      role: "tenant",
    });

    const propertyId = await t.mutation(api.properties.createProperty, {
      name: "Test Property",
      type: "residential",
      address: {
        street: "123 Test St",
        city: "Test City",
        state: "Test State",
        country: "Test Country",
        postalCode: "12345",
      },
      ownerId: userId,
      branding: {
        primaryColor: "#000000",
        secondaryColor: "#ffffff",
      },
      settings: {
        currency: "KES",
        timezone: "Africa/Nairobi",
        language: "en",
        autoRentReminders: true,
        maintenanceSLA: 24,
      },
    });

    // Create checklist
    const checklistId = await t.mutation(api.compliance.createComplianceChecklist, {
      name: "Test Checklist",
      description: "Test compliance checklist",
      entityType: "tenant",
      propertyId,
      requirements: [
        {
          id: "req_1",
          title: "Test Requirement",
          description: "Test requirement description",
          category: "kyc",
          priority: "critical",
          requiredDocuments: ["national_id"],
          isRequired: true,
        },
      ],
      createdBy: userId,
    });

    // Update compliance status with non-compliant status to generate alerts
    await t.mutation(api.compliance.updateComplianceStatus, {
      entityId: userId,
      entityType: "tenant",
      checklistId,
      propertyId,
      requirementStatuses: [
        {
          requirementId: "req_1",
          status: "non_compliant",
          notes: "Missing required document",
          documentIds: [],
        },
      ],
      reviewedBy: userId,
    });

    // Get compliance alerts
    const alerts = await t.query(api.compliance.getComplianceAlerts, {
      propertyId,
    });

    expect(alerts.length).toBeGreaterThan(0);
    expect(alerts[0].severity).toBe("error");
    expect(alerts[0].entityId).toBe(userId);
    expect(alerts[0].entityType).toBe("tenant");
  });

  it("should create audit trail entries", async () => {
    const t = convexTest(schema);

    // Create test data
    const userId = await t.mutation(api.users.createUser, {
      email: "<EMAIL>",
      name: "Test User",
      role: "manager",
    });

    // Upload a KYC document (this should create audit trail)
    const documentId = await t.mutation(api.compliance.uploadKYCDocument, {
      userId,
      documentType: "national_id",
      documentNumber: "12345678",
      documentUrl: "https://example.com/doc.pdf",
      fileName: "national_id.pdf",
      fileSize: 1024,
      mimeType: "application/pdf",
      tags: ["kyc", "identity"],
      uploadedBy: userId,
    });

    expect(documentId).toBeDefined();

    // Check audit trail
    const auditEntries = await t.query(api.compliance.getAuditTrail, {
      entityId: documentId,
      entityType: "kyc_document",
    });

    expect(auditEntries).toHaveLength(1);
    expect(auditEntries[0].action).toBe("created");
    expect(auditEntries[0].performedBy).toBe(userId);
  });
});