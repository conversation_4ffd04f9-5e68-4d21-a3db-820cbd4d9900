import { describe, it, expect } from "vitest";
import { 
  validateLeaseData,
  validateLeaseTerms,
  validateTenantOnboardingData,
  sanitizeTenantData
} from "../lib/validation";

describe("Lease Integration Tests", () => {
  describe("End-to-End Lease Creation Validation", () => {
    it("should validate complete lease creation workflow", () => {
      // Step 1: Validate tenant onboarding data
      const tenantData = {
        email: "<EMAIL>",
        name: "<PERSON>",
        phone: "+254712345678",
        kycStatus: "pending" as const,
      };

      expect(() => validateTenantOnboardingData(tenantData)).not.toThrow();
      const sanitizedTenant = sanitizeTenantData(tenantData);
      expect(sanitizedTenant.email).toBe("<EMAIL>");

      // Step 2: Validate lease terms
      const leaseTerms = {
        noticePeriod: 30,
        lateFeePercentage: 5,
        gracePeriod: 5,
        renewalOption: true,
      };

      expect(() => validateLeaseTerms(leaseTerms)).not.toThrow();

      // Step 3: Validate complete lease data
      const leaseData = {
        propertyId: "property123",
        unitId: "unit123", 
        tenantId: "tenant123",
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: 50000,
        deposit: 100000,
        terms: leaseTerms,
      };

      expect(() => validateLeaseData(leaseData)).not.toThrow();
    });

    it("should handle lease lifecycle status transitions", () => {
      const lease = {
        status: "pending" as const,
        terms: { renewalOption: true },
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
      };

      // Test activation
      const activeLease = { ...lease, status: "active" as const };
      expect(activeLease.status).toBe("active");

      // Test renewal validation
      const renewalData = {
        newEndDate: activeLease.endDate + 365 * 24 * 60 * 60 * 1000,
        newMonthlyRent: 55000,
      };

      // Should not throw for valid renewal
      expect(renewalData.newEndDate).toBeGreaterThan(activeLease.endDate);
      expect(renewalData.newMonthlyRent).toBeGreaterThan(0);

      // Test termination
      const terminationData = {
        terminationDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
        reason: "Tenant requested termination",
        earlyTermination: true,
      };

      expect(terminationData.reason.length).toBeGreaterThan(0);
      expect(terminationData.terminationDate).toBeGreaterThan(Date.now());
    });
  });

  describe("Tenant Onboarding Workflow", () => {
    it("should validate complete tenant onboarding process", () => {
      // Step 1: Initial tenant creation
      const initialTenantData = {
        email: "  <EMAIL>  ",
        name: "  Jane Smith  ",
        phone: "+254 701 234 567",
        kycStatus: "pending" as const,
      };

      const sanitized = sanitizeTenantData(initialTenantData);
      expect(sanitized.email).toBe("<EMAIL>");
      expect(sanitized.name).toBe("Jane Smith");
      expect(sanitized.phone).toBe("+254701234567");

      expect(() => validateTenantOnboardingData(sanitized)).not.toThrow();

      // Step 2: KYC document submission
      const kycDocuments = [
        {
          type: "id_card",
          url: "https://storage.example.com/id_card.pdf",
          name: "National ID Card",
          size: 1024000,
        },
        {
          type: "proof_of_income",
          url: "https://storage.example.com/payslip.pdf", 
          name: "Latest Payslip",
          size: 512000,
        },
      ];

      expect(kycDocuments).toHaveLength(2);
      expect(kycDocuments[0].type).toBe("id_card");
      expect(kycDocuments[1].type).toBe("proof_of_income");

      // Step 3: Profile completion
      const profileUpdate = {
        name: "Jane Smith",
        phone: "+254701234567",
        avatar: "https://storage.example.com/avatar.jpg",
        emergencyContact: {
          name: "John Smith",
          phone: "+254702345678",
          relationship: "Spouse",
        },
      };

      expect(profileUpdate.emergencyContact.name).toBeTruthy();
      expect(profileUpdate.emergencyContact.phone).toBeTruthy();
      expect(profileUpdate.emergencyContact.relationship).toBeTruthy();
    });

    it("should calculate onboarding completion status", () => {
      const onboardingSteps = {
        profileComplete: true,
        kycSubmitted: true,
        kycVerified: false,
        leaseActive: false,
      };

      const completedSteps = Object.values(onboardingSteps).filter(Boolean).length;
      const totalSteps = Object.keys(onboardingSteps).length;
      const completionPercentage = (completedSteps / totalSteps) * 100;

      expect(completionPercentage).toBe(50); // 2 out of 4 steps completed
      expect(completionPercentage < 100).toBe(true); // Not fully onboarded
    });
  });

  describe("Lease Analytics Calculations", () => {
    it("should calculate lease analytics correctly", () => {
      const mockLeases = [
        {
          status: "active",
          monthlyRent: 50000,
          startDate: Date.now() - 100 * 24 * 60 * 60 * 1000,
          endDate: Date.now() + 265 * 24 * 60 * 60 * 1000,
        },
        {
          status: "active", 
          monthlyRent: 60000,
          startDate: Date.now() - 50 * 24 * 60 * 60 * 1000,
          endDate: Date.now() + 315 * 24 * 60 * 60 * 1000,
        },
        {
          status: "expired",
          monthlyRent: 45000,
          startDate: Date.now() - 400 * 24 * 60 * 60 * 1000,
          endDate: Date.now() - 35 * 24 * 60 * 60 * 1000,
        },
      ];

      const activeLeases = mockLeases.filter(lease => lease.status === "active");
      const expiredLeases = mockLeases.filter(lease => lease.status === "expired");

      // Calculate analytics
      const totalLeases = mockLeases.length;
      const activeLeasesCount = activeLeases.length;
      const totalMonthlyRevenue = activeLeases.reduce((sum, lease) => sum + lease.monthlyRent, 0);
      const avgMonthlyRent = totalMonthlyRevenue / activeLeasesCount;

      expect(totalLeases).toBe(3);
      expect(activeLeasesCount).toBe(2);
      expect(totalMonthlyRevenue).toBe(110000);
      expect(avgMonthlyRent).toBe(55000);
      expect(expiredLeases.length).toBe(1);
    });

    it("should identify leases expiring soon", () => {
      const now = Date.now();
      const thirtyDaysFromNow = now + (30 * 24 * 60 * 60 * 1000);

      const mockLeases = [
        {
          status: "active",
          endDate: now + (15 * 24 * 60 * 60 * 1000), // Expires in 15 days
        },
        {
          status: "active",
          endDate: now + (45 * 24 * 60 * 60 * 1000), // Expires in 45 days
        },
        {
          status: "active", 
          endDate: now + (10 * 24 * 60 * 60 * 1000), // Expires in 10 days
        },
      ];

      const expiringSoon = mockLeases.filter(lease => 
        lease.status === "active" && 
        lease.endDate <= thirtyDaysFromNow && 
        lease.endDate > now
      );

      expect(expiringSoon).toHaveLength(2); // 15 days and 10 days
      
      // Sort by expiration date
      const sortedExpiring = expiringSoon.sort((a, b) => a.endDate - b.endDate);
      expect(sortedExpiring[0].endDate).toBeLessThan(sortedExpiring[1].endDate);
    });
  });
});