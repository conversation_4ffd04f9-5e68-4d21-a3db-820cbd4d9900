import { RateLimitConfig } from './input-validation';

// Rate limiter implementation for client-side protection
export class RateLimiter {
  private static instances = new Map<string, RateLimiter>();
  private requests: Map<string, number[]> = new Map();
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = config;
  }

  /**
   * Get or create a rate limiter instance
   */
  static getInstance(key: string, config: RateLimitConfig): RateLimiter {
    if (!this.instances.has(key)) {
      this.instances.set(key, new RateLimiter(config));
    }
    return this.instances.get(key)!;
  }

  /**
   * Check if request is allowed for given identifier
   */
  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    
    // Get existing requests for this identifier
    const userRequests = this.requests.get(identifier) || [];
    
    // Remove old requests outside the window
    const validRequests = userRequests.filter(timestamp => timestamp > windowStart);
    
    // Check if under limit
    if (validRequests.length >= this.config.maxRequests) {
      return false;
    }
    
    // Add current request
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return true;
  }

  /**
   * Get remaining requests for identifier
   */
  getRemainingRequests(identifier: string): number {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    const userRequests = this.requests.get(identifier) || [];
    const validRequests = userRequests.filter(timestamp => timestamp > windowStart);
    
    return Math.max(0, this.config.maxRequests - validRequests.length);
  }

  /**
   * Get reset time for identifier
   */
  getResetTime(identifier: string): number {
    const userRequests = this.requests.get(identifier) || [];
    if (userRequests.length === 0) return 0;
    
    const oldestRequest = Math.min(...userRequests);
    return oldestRequest + this.config.windowMs;
  }

  /**
   * Clear all requests for identifier
   */
  reset(identifier: string): void {
    this.requests.delete(identifier);
  }

  /**
   * Clear old requests (cleanup)
   */
  cleanup(): void {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    
    for (const [identifier, requests] of this.requests.entries()) {
      const validRequests = requests.filter(timestamp => timestamp > windowStart);
      if (validRequests.length === 0) {
        this.requests.delete(identifier);
      } else {
        this.requests.set(identifier, validRequests);
      }
    }
  }
}

// DDoS protection utilities
export class DDoSProtection {
  private static suspiciousIPs = new Map<string, SuspiciousActivity>();
  private static blockedIPs = new Set<string>();
  
  private static readonly SUSPICIOUS_THRESHOLD = 100; // requests per minute
  private static readonly BLOCK_DURATION = 60 * 60 * 1000; // 1 hour
  private static readonly CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes

  static {
    // Periodic cleanup
    setInterval(() => {
      this.cleanup();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Check if IP should be blocked
   */
  static shouldBlock(ip: string): boolean {
    // Check if already blocked
    if (this.blockedIPs.has(ip)) {
      return true;
    }

    // Check for suspicious activity
    const activity = this.suspiciousIPs.get(ip);
    if (!activity) return false;

    const now = Date.now();
    const recentRequests = activity.requests.filter(
      timestamp => timestamp > now - 60 * 1000 // Last minute
    );

    if (recentRequests.length > this.SUSPICIOUS_THRESHOLD) {
      this.blockIP(ip);
      return true;
    }

    return false;
  }

  /**
   * Record request from IP
   */
  static recordRequest(ip: string): void {
    const now = Date.now();
    
    if (!this.suspiciousIPs.has(ip)) {
      this.suspiciousIPs.set(ip, {
        requests: [],
        firstSeen: now,
        lastSeen: now,
        totalRequests: 0
      });
    }

    const activity = this.suspiciousIPs.get(ip)!;
    activity.requests.push(now);
    activity.lastSeen = now;
    activity.totalRequests++;

    // Keep only last 5 minutes of requests
    activity.requests = activity.requests.filter(
      timestamp => timestamp > now - 5 * 60 * 1000
    );
  }

  /**
   * Block IP address
   */
  static blockIP(ip: string): void {
    this.blockedIPs.add(ip);
    console.warn(`Blocked suspicious IP: ${ip}`);
    
    // Auto-unblock after duration
    setTimeout(() => {
      this.unblockIP(ip);
    }, this.BLOCK_DURATION);
  }

  /**
   * Unblock IP address
   */
  static unblockIP(ip: string): void {
    this.blockedIPs.delete(ip);
    this.suspiciousIPs.delete(ip);
    console.info(`Unblocked IP: ${ip}`);
  }

  /**
   * Get blocked IPs
   */
  static getBlockedIPs(): string[] {
    return Array.from(this.blockedIPs);
  }

  /**
   * Get suspicious activity report
   */
  static getSuspiciousActivity(): SuspiciousActivityReport[] {
    const now = Date.now();
    return Array.from(this.suspiciousIPs.entries()).map(([ip, activity]) => ({
      ip,
      totalRequests: activity.totalRequests,
      recentRequests: activity.requests.filter(t => t > now - 60 * 1000).length,
      firstSeen: activity.firstSeen,
      lastSeen: activity.lastSeen,
      isBlocked: this.blockedIPs.has(ip)
    }));
  }

  /**
   * Cleanup old data
   */
  private static cleanup(): void {
    const now = Date.now();
    const cutoff = now - 24 * 60 * 60 * 1000; // 24 hours

    for (const [ip, activity] of this.suspiciousIPs.entries()) {
      if (activity.lastSeen < cutoff) {
        this.suspiciousIPs.delete(ip);
      }
    }
  }
}

// Request fingerprinting for additional security
export class RequestFingerprinting {
  /**
   * Generate request fingerprint based on various factors
   */
  static generateFingerprint(): string {
    const factors = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset().toString(),
      navigator.platform,
      navigator.cookieEnabled.toString()
    ];

    // Simple hash function
    let hash = 0;
    const str = factors.join('|');
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(36);
  }

  /**
   * Detect potential bot behavior
   */
  static detectBotBehavior(): BotDetectionResult {
    const checks = {
      hasWebDriver: 'webdriver' in navigator,
      hasPhantom: 'callPhantom' in window || '_phantom' in window,
      hasSelenium: 'selenium' in window || '__selenium_unwrapped' in window,
      suspiciousUserAgent: /headless|phantom|selenium|webdriver/i.test(navigator.userAgent),
      noPlugins: navigator.plugins.length === 0,
      suspiciousLanguages: navigator.languages.length === 0,
      automationFlags: 'automation' in navigator || 'webdriver' in navigator
    };

    const suspiciousCount = Object.values(checks).filter(Boolean).length;
    const riskScore = suspiciousCount / Object.keys(checks).length;

    return {
      isBot: riskScore > 0.3,
      riskScore,
      checks,
      fingerprint: this.generateFingerprint()
    };
  }
}

// Security middleware for API calls
export class SecurityMiddleware {
  /**
   * Apply security checks to API request
   */
  static async secureRequest(
    endpoint: string,
    options: RequestInit = {},
    rateLimitConfig?: RateLimitConfig
  ): Promise<Response> {
    // Get client IP (in real app, this would come from server)
    const clientIP = await this.getClientIP();
    
    // Check DDoS protection
    if (DDoSProtection.shouldBlock(clientIP)) {
      throw new Error('Request blocked due to suspicious activity');
    }

    // Record request
    DDoSProtection.recordRequest(clientIP);

    // Apply rate limiting if configured
    if (rateLimitConfig) {
      const rateLimiter = RateLimiter.getInstance(endpoint, rateLimitConfig);
      if (!rateLimiter.isAllowed(clientIP)) {
        const resetTime = rateLimiter.getResetTime(clientIP);
        throw new Error(`Rate limit exceeded. Try again at ${new Date(resetTime).toISOString()}`);
      }
    }

    // Bot detection
    const botDetection = RequestFingerprinting.detectBotBehavior();
    if (botDetection.isBot && botDetection.riskScore > 0.7) {
      console.warn('High-risk bot detected:', botDetection);
      // Could implement CAPTCHA challenge here
    }

    // Add security headers
    const secureOptions: RequestInit = {
      ...options,
      headers: {
        ...options.headers,
        'X-Request-Fingerprint': botDetection.fingerprint,
        'X-Client-IP': clientIP,
        'X-Timestamp': Date.now().toString()
      }
    };

    return fetch(endpoint, secureOptions);
  }

  /**
   * Get client IP address (simplified for demo)
   */
  private static async getClientIP(): Promise<string> {
    // TODO: Implement proper server-side IP detection
    return 'unknown-ip';
  }
}

// Types
interface SuspiciousActivity {
  requests: number[];
  firstSeen: number;
  lastSeen: number;
  totalRequests: number;
}

interface SuspiciousActivityReport {
  ip: string;
  totalRequests: number;
  recentRequests: number;
  firstSeen: number;
  lastSeen: number;
  isBlocked: boolean;
}

interface BotDetectionResult {
  isBot: boolean;
  riskScore: number;
  checks: Record<string, boolean>;
  fingerprint: string;
}