import React, { useState, useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  CreditCard, 
  Smartphone, 
  AlertCircle,
  Calendar,
  Filter,
  Download,
  RefreshCw
} from "lucide-react";
import { format, subDays } from "date-fns";
// import { PaymentStatusTracker } from "./PaymentStatusTracker";

interface PaymentDashboardProps {
  propertyId?: Id<"properties">;
  showFilters?: boolean;
}

export const PaymentDashboard: React.FC<PaymentDashboardProps> = ({
  propertyId,
  showFilters = true,
}) => {
  const [dateRange, setDateRange] = useState("30");
  const [statusFilter, setStatusFilter] = useState("all");
  const [methodFilter, setMethodFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch data
  const payments = useQuery(api.payments.getPaymentsByStatus, { 
    status: statusFilter === "all" ? undefined : statusFilter as any,
    limit: 1000 
  });
  // Mock invoices data for now since getInvoices doesn't exist yet
  const invoices: any[] = [];

  // Calculate date range
  const dateRangeStart = useMemo(() => {
    const days = parseInt(dateRange);
    return subDays(new Date(), days);
  }, [dateRange]);

  // Filter and process data
  const filteredData = useMemo(() => {
    if (!payments || !invoices) return { payments: [], invoices: [] };

    let filteredPayments = payments.filter((payment: any) => {
      const paymentDate = new Date(payment.createdAt);
      const withinDateRange = paymentDate >= dateRangeStart;
      const matchesMethod = methodFilter === "all" || payment.method === methodFilter;
      const matchesSearch = searchTerm === "" || 
        payment.transactionId.toLowerCase().includes(searchTerm.toLowerCase());

      return withinDateRange && matchesMethod && matchesSearch;
    });

    let filteredInvoices = invoices.filter((invoice: any) => {
      const invoiceDate = new Date(invoice.createdAt);
      return invoiceDate >= dateRangeStart;
    });

    return { payments: filteredPayments, invoices: filteredInvoices };
  }, [payments, invoices, dateRangeStart, methodFilter, searchTerm]);

  // Calculate metrics
  const metrics = useMemo(() => {
    const { payments: filteredPayments, invoices: filteredInvoices } = filteredData;

    const totalRevenue = filteredPayments
      .filter((p: any) => p.status === "completed")
      .reduce((sum: number, p: any) => sum + p.amount, 0);

    const totalPending = filteredPayments
      .filter((p: any) => p.status === "pending")
      .reduce((sum: number, p: any) => sum + p.amount, 0);

    const totalFailed = filteredPayments
      .filter((p: any) => p.status === "failed")
      .reduce((sum: number, p: any) => sum + p.amount, 0);

    const mpesaPayments = filteredPayments.filter((p: any) => p.method === "mpesa");
    const stripePayments = filteredPayments.filter((p: any) => p.method === "stripe");

    const mpesaRevenue = mpesaPayments
      .filter((p: any) => p.status === "completed")
      .reduce((sum: number, p: any) => sum + p.amount, 0);

    const stripeRevenue = stripePayments
      .filter((p: any) => p.status === "completed")
      .reduce((sum: number, p: any) => sum + p.amount, 0);

    const successRate = filteredPayments.length > 0 
      ? (filteredPayments.filter((p: any) => p.status === "completed").length / filteredPayments.length) * 100
      : 0;

    const averageTransactionValue = filteredPayments.length > 0
      ? totalRevenue / filteredPayments.filter((p: any) => p.status === "completed").length
      : 0;

    // Calculate previous period for comparison
    const previousPeriodStart = subDays(dateRangeStart, parseInt(dateRange));
    const previousPayments = payments?.filter((p: any) => {
      const paymentDate = new Date(p.createdAt);
      return paymentDate >= previousPeriodStart && paymentDate < dateRangeStart;
    }) || [];

    const previousRevenue = previousPayments
      .filter((p: any) => p.status === "completed")
      .reduce((sum: number, p: any) => sum + p.amount, 0);

    const revenueGrowth = previousRevenue > 0 
      ? ((totalRevenue - previousRevenue) / previousRevenue) * 100
      : 0;

    return {
      totalRevenue,
      totalPending,
      totalFailed,
      mpesaRevenue,
      stripeRevenue,
      successRate,
      averageTransactionValue,
      revenueGrowth,
      totalTransactions: filteredPayments.length,
      completedTransactions: filteredPayments.filter((p: any) => p.status === "completed").length,
      pendingTransactions: filteredPayments.filter((p: any) => p.status === "pending").length,
      failedTransactions: filteredPayments.filter((p: any) => p.status === "failed").length,
    };
  }, [filteredData, payments, dateRange, dateRangeStart]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (!payments || !invoices) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        Loading payment data...
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Payment Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor payment performance and analytics
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium">Date Range</label>
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">Last 7 days</SelectItem>
                    <SelectItem value="30">Last 30 days</SelectItem>
                    <SelectItem value="90">Last 90 days</SelectItem>
                    <SelectItem value="365">Last year</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium">Status</label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                    <SelectItem value="refunded">Refunded</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium">Payment Method</label>
                <Select value={methodFilter} onValueChange={setMethodFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Methods</SelectItem>
                    <SelectItem value="mpesa">M-PESA</SelectItem>
                    <SelectItem value="stripe">Card Payment</SelectItem>
                    <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium">Search</label>
                <Input
                  placeholder="Transaction ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(metrics.totalRevenue)}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {metrics.revenueGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1 text-red-600" />
              )}
              {formatPercentage(Math.abs(metrics.revenueGrowth))} from last period
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(metrics.successRate)}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.completedTransactions} of {metrics.totalTransactions} transactions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Transaction</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(metrics.averageTransactionValue)}</div>
            <p className="text-xs text-muted-foreground">
              Per successful transaction
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Amount</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(metrics.totalPending)}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.pendingTransactions} pending transactions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Payment Method Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Payment Methods</CardTitle>
            <CardDescription>Revenue breakdown by payment method</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Smartphone className="h-4 w-4 text-green-600" />
                <span>M-PESA</span>
              </div>
              <div className="text-right">
                <div className="font-medium">{formatCurrency(metrics.mpesaRevenue)}</div>
                <div className="text-sm text-muted-foreground">
                  {metrics.totalRevenue > 0 
                    ? formatPercentage((metrics.mpesaRevenue / metrics.totalRevenue) * 100)
                    : "0%"
                  }
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-blue-600" />
                <span>Card Payment</span>
              </div>
              <div className="text-right">
                <div className="font-medium">{formatCurrency(metrics.stripeRevenue)}</div>
                <div className="text-sm text-muted-foreground">
                  {metrics.totalRevenue > 0 
                    ? formatPercentage((metrics.stripeRevenue / metrics.totalRevenue) * 100)
                    : "0%"
                  }
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Transaction Status</CardTitle>
            <CardDescription>Current transaction status distribution</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-600 rounded-full" />
                <span>Completed</span>
              </div>
              <div className="text-right">
                <div className="font-medium">{metrics.completedTransactions}</div>
                <div className="text-sm text-muted-foreground">
                  {formatPercentage((metrics.completedTransactions / Math.max(metrics.totalTransactions, 1)) * 100)}
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-yellow-600 rounded-full" />
                <span>Pending</span>
              </div>
              <div className="text-right">
                <div className="font-medium">{metrics.pendingTransactions}</div>
                <div className="text-sm text-muted-foreground">
                  {formatPercentage((metrics.pendingTransactions / Math.max(metrics.totalTransactions, 1)) * 100)}
                </div>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-600 rounded-full" />
                <span>Failed</span>
              </div>
              <div className="text-right">
                <div className="font-medium">{metrics.failedTransactions}</div>
                <div className="text-sm text-muted-foreground">
                  {formatPercentage((metrics.failedTransactions / Math.max(metrics.totalTransactions, 1)) * 100)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
          <CardDescription>Latest payment transactions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredData.payments.slice(0, 10).map((payment: any) => (
              <div key={payment._id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {payment.method === "mpesa" ? (
                    <Smartphone className="h-4 w-4 text-green-600" />
                  ) : (
                    <CreditCard className="h-4 w-4 text-blue-600" />
                  )}
                  <div>
                    <p className="font-medium">{formatCurrency(payment.amount)}</p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(payment.createdAt), "MMM dd, yyyy HH:mm")}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge 
                    variant={
                      payment.status === "completed" ? "default" :
                      payment.status === "pending" ? "secondary" :
                      "destructive"
                    }
                  >
                    {payment.status}
                  </Badge>
                  <p className="text-xs text-muted-foreground mt-1">
                    {payment.method.toUpperCase()}
                  </p>
                </div>
              </div>
            ))}
            {filteredData.payments.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No transactions found for the selected filters
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};