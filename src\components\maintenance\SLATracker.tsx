import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Clock, AlertTriangle, CheckCircle, TrendingUp, TrendingDown } from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';

interface SLATrackerProps {
  propertyId: Id<"properties">;
  timeRange?: {
    startDate: number;
    endDate: number;
  };
}

export const SLATracker: React.FC<SLATrackerProps> = ({ propertyId, timeRange }) => {
  // Fetch SLA metrics
  const slaMetrics = useQuery(api.maintenance.getSLAMetrics, {
    propertyId,
    startDate: timeRange?.startDate,
    endDate: timeRange?.endDate
  });

  // Fetch tickets approaching SLA deadline
  const upcomingDeadlines = useQuery(api.maintenance.getTicketsApproachingSLA, {
    propertyId,
    hoursAhead: 24 // Next 24 hours
  });

  // Fetch overdue tickets
  const overdueTickets = useQuery(api.maintenance.getOverdueTickets, { propertyId });

  const getSLAStatus = (compliance: number) => {
    if (compliance >= 95) return { color: 'text-green-600', icon: CheckCircle, label: 'Excellent' };
    if (compliance >= 85) return { color: 'text-yellow-600', icon: Clock, label: 'Good' };
    return { color: 'text-red-600', icon: AlertTriangle, label: 'Needs Attention' };
  };

  const formatTimeRemaining = (deadline: number) => {
    const now = Date.now();
    const timeLeft = deadline - now;
    
    if (timeLeft <= 0) {
      return 'Overdue';
    }
    
    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'emergency': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  if (!slaMetrics) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const slaStatus = getSLAStatus(slaMetrics.slaCompliance);
  const StatusIcon = slaStatus.icon;

  return (
    <div className="space-y-6">
      {/* SLA Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SLA Compliance</CardTitle>
            <StatusIcon className={`h-4 w-4 ${slaStatus.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{slaMetrics.slaCompliance.toFixed(1)}%</div>
            <p className={`text-xs ${slaStatus.color}`}>
              {slaStatus.label}
            </p>
            <Progress 
              value={slaMetrics.slaCompliance} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Resolution Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{slaMetrics.averageResolutionTime.toFixed(1)}h</div>
            <p className="text-xs text-muted-foreground">
              Target: 24h
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue Tickets</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {overdueTickets?.length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Require immediate attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {slaMetrics.totalTickets > 0 
                ? ((slaMetrics.completedTickets / slaMetrics.totalTickets) * 100).toFixed(1)
                : 0
              }%
            </div>
            <p className="text-xs text-muted-foreground">
              {slaMetrics.completedTickets} of {slaMetrics.totalTickets} tickets
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Priority Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Tickets by Priority</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(slaMetrics.ticketsByPriority).map(([priority, count]) => (
              <div key={priority} className="text-center">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full ${getPriorityColor(priority)} text-white mb-2`}>
                  {count}
                </div>
                <div className="text-sm font-medium capitalize">{priority}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Upcoming SLA Deadlines */}
      {upcomingDeadlines && upcomingDeadlines.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-orange-500" />
              Upcoming SLA Deadlines (Next 24h)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {upcomingDeadlines.map((ticket: any) => (
                <div key={ticket._id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{ticket.title}</h4>
                      <Badge className={`${getPriorityColor(ticket.priority)} text-white`}>
                        {ticket.priority}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Category: {ticket.category}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className={`font-medium ${
                      Date.now() > ticket.slaDeadline ? 'text-red-600' : 'text-orange-600'
                    }`}>
                      {formatTimeRemaining(ticket.slaDeadline)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Due: {format(new Date(ticket.slaDeadline), 'MMM d, HH:mm')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overdue Tickets */}
      {overdueTickets && overdueTickets.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Overdue Tickets
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {overdueTickets.map((ticket: any) => (
                <div key={ticket._id} className="flex items-center justify-between p-3 border border-red-200 rounded-lg bg-red-50">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{ticket.title}</h4>
                      <Badge className={`${getPriorityColor(ticket.priority)} text-white`}>
                        {ticket.priority}
                      </Badge>
                      <Badge variant="destructive">
                        Overdue
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Category: {ticket.category} • Created: {formatDistanceToNow(new Date(ticket.createdAt), { addSuffix: true })}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-red-600">
                      {formatTimeRemaining(ticket.slaDeadline)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Was due: {format(new Date(ticket.slaDeadline), 'MMM d, HH:mm')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Status Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Tickets by Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(slaMetrics.ticketsByStatus).map(([status, count]) => {
              const percentage = slaMetrics.totalTickets > 0 
                ? ((count as number) / slaMetrics.totalTickets) * 100 
                : 0;
              
              return (
                <div key={status} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="capitalize font-medium">{status.replace('_', ' ')}</div>
                    <Badge variant="outline">{count as number}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-muted-foreground">
                      {percentage.toFixed(1)}%
                    </div>
                    <div className="w-20">
                      <Progress value={percentage} />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};