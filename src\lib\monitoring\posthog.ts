import posthog from 'posthog-js';

export interface PostHogConfig {
  apiKey: string;
  apiHost: string;
  environment: 'development' | 'staging' | 'production';
  enableSessionRecording: boolean;
  enableHeatmaps: boolean;
  capturePageViews: boolean;
}

const defaultConfig: PostHogConfig = {
  apiKey: process.env.VITE_POSTHOG_API_KEY || '',
  apiHost: process.env.VITE_POSTHOG_API_HOST || 'https://app.posthog.com',
  environment: (process.env.NODE_ENV as any) || 'development',
  enableSessionRecording: process.env.NODE_ENV === 'production',
  enableHeatmaps: process.env.NODE_ENV === 'production',
  capturePageViews: true,
};

export class PostHogAnalytics {
  private static instance: PostHogAnalytics;
  private config: PostHogConfig;
  private initialized = false;

  private constructor(config: Partial<PostHogConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  static getInstance(config?: Partial<PostHogConfig>): PostHogAnalytics {
    if (!PostHogAnalytics.instance) {
      PostHogAnalytics.instance = new PostHogAnalytics(config);
    }
    return PostHogAnalytics.instance;
  }

  initialize(): void {
    if (this.initialized || !this.config.apiKey) {
      return;
    }

    posthog.init(this.config.apiKey, {
      api_host: this.config.apiHost,
      capture_pageview: this.config.capturePageViews,
      session_recording: {
        enabled: this.config.enableSessionRecording,
      },
      autocapture: {
        dom_event_allowlist: ['click', 'change', 'submit'],
      },
      disable_session_recording: !this.config.enableSessionRecording,
      loaded: (posthog) => {
        if (this.config.environment === 'development') {
          posthog.debug();
        }
      },
    });

    this.initialized = true;
  }

  // User identification and properties
  identify(userId: string, properties?: Record<string, any>): void {
    if (!this.initialized) return;
    
    posthog.identify(userId, {
      environment: this.config.environment,
      ...properties,
    });
  }

  // Event tracking
  track(event: string, properties?: Record<string, any>): void {
    if (!this.initialized) return;
    
    posthog.capture(event, {
      timestamp: new Date(),
      ...properties,
    });
  }

  // Page view tracking
  trackPageView(path: string, properties?: Record<string, any>): void {
    if (!this.initialized) return;
    
    posthog.capture('$pageview', {
      $current_url: path,
      ...properties,
    });
  }

  // Property management specific events
  trackPropertyCreated(propertyId: string, propertyType: string): void {
    this.track('property_created', {
      property_id: propertyId,
      property_type: propertyType,
    });
  }

  trackLeaseCreated(leaseId: string, propertyId: string, duration: number): void {
    this.track('lease_created', {
      lease_id: leaseId,
      property_id: propertyId,
      lease_duration_months: duration,
    });
  }

  trackPaymentProcessed(paymentId: string, amount: number, method: string): void {
    this.track('payment_processed', {
      payment_id: paymentId,
      amount,
      payment_method: method,
    });
  }

  trackMaintenanceTicketCreated(ticketId: string, priority: string): void {
    this.track('maintenance_ticket_created', {
      ticket_id: ticketId,
      priority,
    });
  }

  // Performance tracking
  trackPerformanceMetric(metric: string, value: number, context?: Record<string, any>): void {
    this.track('performance_metric', {
      metric_name: metric,
      metric_value: value,
      ...context,
    });
  }

  trackLoadTime(page: string, loadTime: number): void {
    this.track('page_load_time', {
      page,
      load_time_ms: loadTime,
    });
  }

  trackDatabaseQuery(queryType: string, duration: number, success: boolean): void {
    this.track('database_query', {
      query_type: queryType,
      duration_ms: duration,
      success,
    });
  }

  // Feature usage tracking
  trackFeatureUsage(feature: string, action: string, context?: Record<string, any>): void {
    this.track('feature_usage', {
      feature,
      action,
      ...context,
    });
  }

  // Error tracking
  trackError(error: string, context?: Record<string, any>): void {
    this.track('error_occurred', {
      error_message: error,
      ...context,
    });
  }

  // User engagement
  trackUserEngagement(action: string, duration?: number): void {
    this.track('user_engagement', {
      action,
      duration_seconds: duration,
    });
  }

  // A/B testing support
  getFeatureFlag(flag: string): boolean | string {
    if (!this.initialized) return false;
    return posthog.getFeatureFlag(flag);
  }

  isFeatureEnabled(flag: string): boolean {
    if (!this.initialized) return false;
    return posthog.isFeatureEnabled(flag);
  }

  // Session management
  reset(): void {
    if (!this.initialized) return;
    posthog.reset();
  }

  // Group analytics (for properties/organizations)
  group(groupType: string, groupKey: string, properties?: Record<string, any>): void {
    if (!this.initialized) return;
    posthog.group(groupType, groupKey, properties);
  }

  // Funnel tracking for key workflows
  trackFunnelStep(funnel: string, step: string, properties?: Record<string, any>): void {
    this.track(`${funnel}_${step}`, {
      funnel_name: funnel,
      step_name: step,
      ...properties,
    });
  }

  // Revenue tracking
  trackRevenue(amount: number, currency: string = 'KES', properties?: Record<string, any>): void {
    this.track('revenue', {
      revenue: amount,
      currency,
      ...properties,
    });
  }
}

// Predefined event types for type safety
export const ANALYTICS_EVENTS = {
  // Authentication
  USER_LOGIN: 'user_login',
  USER_LOGOUT: 'user_logout',
  USER_REGISTERED: 'user_registered',
  
  // Property Management
  PROPERTY_CREATED: 'property_created',
  PROPERTY_UPDATED: 'property_updated',
  PROPERTY_DELETED: 'property_deleted',
  
  // Lease Management
  LEASE_CREATED: 'lease_created',
  LEASE_RENEWED: 'lease_renewed',
  LEASE_TERMINATED: 'lease_terminated',
  
  // Payments
  PAYMENT_INITIATED: 'payment_initiated',
  PAYMENT_COMPLETED: 'payment_completed',
  PAYMENT_FAILED: 'payment_failed',
  
  // Maintenance
  TICKET_CREATED: 'ticket_created',
  TICKET_ASSIGNED: 'ticket_assigned',
  TICKET_COMPLETED: 'ticket_completed',
  
  // Performance
  PAGE_LOAD: 'page_load',
  API_CALL: 'api_call',
  ERROR_OCCURRED: 'error_occurred',
} as const;

export default PostHogAnalytics;