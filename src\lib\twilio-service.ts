import { Twi<PERSON> } from 'twilio';

// Twilio configuration interface
interface TwilioConfig {
  accountSid: string;
  authToken: string;
  phoneNumber: string; // Twilio phone number for SMS
  whatsappNumber: string; // Twilio WhatsApp number
}

// Message template interface
interface MessageTemplate {
  content: string;
  variables: string[];
}

// Personalization data interface
interface PersonalizationData {
  [key: string]: string | number | boolean;
}

// Delivery status interface
interface DeliveryStatus {
  sid: string;
  status: 'queued' | 'sent' | 'delivered' | 'undelivered' | 'failed';
  errorCode?: string;
  errorMessage?: string;
  dateCreated: Date;
  dateUpdated?: Date;
  dateSent?: Date;
}

// Message result interface
interface MessageResult {
  sid: string;
  status: string;
  to: string;
  from: string;
  body: string;
  dateCreated: Date;
  errorCode?: string;
  errorMessage?: string;
}

// SMS and WhatsApp service class
export class TwilioService {
  private client: Twilio;
  private config: TwilioConfig;

  constructor(config: TwilioConfig) {
    this.config = config;
    this.client = new Twilio(config.accountSid, config.authToken);
  }

  /**
   * Send SMS message
   */
  async sendSMS(phoneNumber: string, message: string): Promise<MessageResult> {
    try {
      // Ensure phone number is in international format
      const formattedNumber = this.formatPhoneNumber(phoneNumber);
      
      const messageInstance = await this.client.messages.create({
        body: message,
        from: this.config.phoneNumber,
        to: formattedNumber,
      });

      return {
        sid: messageInstance.sid,
        status: messageInstance.status,
        to: messageInstance.to,
        from: messageInstance.from,
        body: messageInstance.body,
        dateCreated: messageInstance.dateCreated,
        errorCode: messageInstance.errorCode?.toString() || undefined,
        errorMessage: messageInstance.errorMessage || undefined,
      };
    } catch (error) {
      console.error('Failed to send SMS:', error);
      throw new Error(`SMS sending failed: ${(error as Error).message}`);
    }
  }

  /**
   * Send WhatsApp message
   */
  async sendWhatsApp(phoneNumber: string, message: string): Promise<MessageResult> {
    try {
      // Ensure phone number is in international format
      const formattedNumber = this.formatPhoneNumber(phoneNumber);
      
      const messageInstance = await this.client.messages.create({
        body: message,
        from: `whatsapp:${this.config.whatsappNumber}`,
        to: `whatsapp:${formattedNumber}`,
      });

      return {
        sid: messageInstance.sid,
        status: messageInstance.status,
        to: messageInstance.to,
        from: messageInstance.from,
        body: messageInstance.body,
        dateCreated: messageInstance.dateCreated,
        errorCode: messageInstance.errorCode?.toString() || undefined,
        errorMessage: messageInstance.errorMessage || undefined,
      };
    } catch (error) {
      console.error('Failed to send WhatsApp message:', error);
      throw new Error(`WhatsApp sending failed: ${(error as Error).message}`);
    }
  }

  /**
   * Get message status by SID
   */
  async getMessageStatus(messageSid: string): Promise<MessageResult> {
    try {
      const messageInstance = await this.client.messages(messageSid).fetch();
      
      return {
        sid: messageInstance.sid,
        status: messageInstance.status,
        to: messageInstance.to,
        from: messageInstance.from,
        body: messageInstance.body,
        dateCreated: messageInstance.dateCreated,
        errorCode: messageInstance.errorCode?.toString() || undefined,
        errorMessage: messageInstance.errorMessage || undefined,
      };
    } catch (error) {
      console.error('Failed to fetch message status:', error);
      throw new Error(`Status fetch failed: ${(error as Error).message}`);
    }
  }

  /**
   * Send bulk SMS messages
   */
  async sendBulkSMS(
    recipients: Array<{ phoneNumber: string; message: string }>
  ): Promise<Array<{ phoneNumber: string; result?: MessageResult; error?: string }>> {
    const results = [];
    
    for (const recipient of recipients) {
      try {
        const result = await this.sendSMS(recipient.phoneNumber, recipient.message);
        results.push({
          phoneNumber: recipient.phoneNumber,
          result,
        });
      } catch (error) {
        results.push({
          phoneNumber: recipient.phoneNumber,
          error: (error as Error).message,
        });
      }
    }
    
    return results;
  }

  /**
   * Send bulk WhatsApp messages
   */
  async sendBulkWhatsApp(
    recipients: Array<{ phoneNumber: string; message: string }>
  ): Promise<Array<{ phoneNumber: string; result?: MessageResult; error?: string }>> {
    const results = [];
    
    for (const recipient of recipients) {
      try {
        const result = await this.sendWhatsApp(recipient.phoneNumber, recipient.message);
        results.push({
          phoneNumber: recipient.phoneNumber,
          result,
        });
      } catch (error) {
        results.push({
          phoneNumber: recipient.phoneNumber,
          error: (error as Error).message,
        });
      }
    }
    
    return results;
  }

  /**
   * Process message template with personalization data
   */
  processTemplate(template: MessageTemplate, data: PersonalizationData): string {
    let processedContent = template.content;
    
    // Replace template variables with actual data
    for (const variable of template.variables) {
      const value = data[variable];
      if (value !== undefined) {
        const placeholder = new RegExp(`{{${variable}}}`, 'g');
        processedContent = processedContent.replace(placeholder, String(value));
      }
    }
    
    return processedContent;
  }

  /**
   * Send templated SMS with personalization
   */
  async sendTemplatedSMS(
    phoneNumber: string, 
    template: MessageTemplate, 
    data: PersonalizationData
  ): Promise<MessageResult> {
    const processedMessage = this.processTemplate(template, data);
    return this.sendSMS(phoneNumber, processedMessage);
  }

  /**
   * Send templated WhatsApp message with personalization
   */
  async sendTemplatedWhatsApp(
    phoneNumber: string, 
    template: MessageTemplate, 
    data: PersonalizationData
  ): Promise<MessageResult> {
    const processedMessage = this.processTemplate(template, data);
    return this.sendWhatsApp(phoneNumber, processedMessage);
  }

  /**
   * Send bulk templated messages with individual personalization
   */
  async sendBulkTemplatedMessages(
    type: 'sms' | 'whatsapp',
    recipients: Array<{ 
      phoneNumber: string; 
      personalizationData: PersonalizationData;
    }>,
    template: MessageTemplate
  ): Promise<Array<{ phoneNumber: string; result?: MessageResult; error?: string }>> {
    const results = [];
    
    for (const recipient of recipients) {
      try {
        let result: MessageResult;
        
        if (type === 'sms') {
          result = await this.sendTemplatedSMS(
            recipient.phoneNumber, 
            template, 
            recipient.personalizationData
          );
        } else {
          result = await this.sendTemplatedWhatsApp(
            recipient.phoneNumber, 
            template, 
            recipient.personalizationData
          );
        }
        
        results.push({
          phoneNumber: recipient.phoneNumber,
          result,
        });
      } catch (error) {
        results.push({
          phoneNumber: recipient.phoneNumber,
          error: (error as Error).message,
        });
      }
    }
    
    return results;
  }

  /**
   * Get detailed delivery status for a message
   */
  async getDeliveryStatus(messageSid: string): Promise<DeliveryStatus> {
    try {
      const messageInstance = await this.client.messages(messageSid).fetch();
      
      return {
        sid: messageInstance.sid,
        status: messageInstance.status as DeliveryStatus['status'],
        errorCode: messageInstance.errorCode?.toString(),
        errorMessage: messageInstance.errorMessage || undefined,
        dateCreated: messageInstance.dateCreated,
        dateUpdated: messageInstance.dateUpdated || undefined,
        dateSent: messageInstance.dateSent || undefined,
      };
    } catch (error) {
      console.error('Failed to fetch delivery status:', error);
      throw new Error(`Delivery status fetch failed: ${(error as Error).message}`);
    }
  }

  /**
   * Get delivery statuses for multiple messages
   */
  async getBulkDeliveryStatus(messageSids: string[]): Promise<DeliveryStatus[]> {
    const statuses = [];
    
    for (const sid of messageSids) {
      try {
        const status = await this.getDeliveryStatus(sid);
        statuses.push(status);
      } catch (error) {
        console.error(`Failed to get status for message ${sid}:`, error);
        // Continue with other messages even if one fails
      }
    }
    
    return statuses;
  }

  /**
   * Get delivery report for messages sent within a date range
   */
  async getDeliveryReport(
    startDate: Date, 
    endDate: Date, 
    limit: number = 100
  ): Promise<{
    totalMessages: number;
    delivered: number;
    failed: number;
    pending: number;
    deliveryRate: number;
    messages: DeliveryStatus[];
  }> {
    try {
      const messages = await this.client.messages.list({
        dateSentAfter: startDate,
        dateSentBefore: endDate,
        limit,
      });

      const deliveryStatuses: DeliveryStatus[] = messages.map(msg => ({
        sid: msg.sid,
        status: msg.status as DeliveryStatus['status'],
        errorCode: msg.errorCode?.toString(),
        errorMessage: msg.errorMessage || undefined,
        dateCreated: msg.dateCreated,
        dateUpdated: msg.dateUpdated || undefined,
        dateSent: msg.dateSent || undefined,
      }));

      const totalMessages = deliveryStatuses.length;
      const delivered = deliveryStatuses.filter(s => s.status === 'delivered').length;
      const failed = deliveryStatuses.filter(s => s.status === 'failed' || s.status === 'undelivered').length;
      const pending = deliveryStatuses.filter(s => s.status === 'queued' || s.status === 'sent').length;
      const deliveryRate = totalMessages > 0 ? (delivered / totalMessages) * 100 : 0;

      return {
        totalMessages,
        delivered,
        failed,
        pending,
        deliveryRate,
        messages: deliveryStatuses,
      };
    } catch (error) {
      console.error('Failed to generate delivery report:', error);
      throw new Error(`Delivery report generation failed: ${(error as Error).message}`);
    }
  }

  /**
   * Set up webhook for delivery status updates
   */
  async setupDeliveryWebhook(webhookUrl: string): Promise<void> {
    try {
      // This would typically be done through Twilio Console or API
      // For now, we'll just validate the URL format
      if (!webhookUrl.startsWith('https://')) {
        throw new Error('Webhook URL must use HTTPS');
      }
      
      console.log(`Delivery webhook should be configured to: ${webhookUrl}`);
      // In a real implementation, you would configure this through Twilio's API
    } catch (error) {
      console.error('Failed to setup delivery webhook:', error);
      throw new Error(`Webhook setup failed: ${(error as Error).message}`);
    }
  }

  /**
   * Validate phone number format
   */
  validatePhoneNumber(phoneNumber: string): boolean {
    // Basic validation for international format
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  /**
   * Format phone number to international format
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, '');
    
    // Handle Kenyan numbers (254 country code)
    if (cleaned.startsWith('0') && cleaned.length === 10) {
      // Convert 0********* to +254*********
      cleaned = '254' + cleaned.substring(1);
    } else if (cleaned.startsWith('254') && cleaned.length === 12) {
      // Already in correct format without +
      // Keep as is
    } else if (cleaned.startsWith('7') && cleaned.length === 9) {
      // Convert ********* to +254*********
      cleaned = '254' + cleaned;
    }
    
    // Add + prefix if not present
    if (!cleaned.startsWith('+')) {
      cleaned = '+' + cleaned;
    }
    
    return cleaned;
  }

  /**
   * Get account balance and usage
   */
  async getAccountInfo(): Promise<{
    balance: string;
    currency: string;
    accountSid: string;
    status: string;
  }> {
    try {
      const account = await this.client.api.accounts(this.config.accountSid).fetch();
      const balance = await this.client.api.accounts(this.config.accountSid).balance.fetch();
      
      return {
        balance: balance.balance,
        currency: balance.currency,
        accountSid: account.sid,
        status: account.status,
      };
    } catch (error) {
      console.error('Failed to fetch account info:', error);
      throw new Error(`Account info fetch failed: ${(error as Error).message}`);
    }
  }

  /**
   * List recent messages
   */
  async getRecentMessages(limit: number = 20): Promise<MessageResult[]> {
    try {
      const messages = await this.client.messages.list({ limit });
      
      return messages.map(msg => ({
        sid: msg.sid,
        status: msg.status,
        to: msg.to,
        from: msg.from,
        body: msg.body,
        dateCreated: msg.dateCreated,
        errorCode: msg.errorCode?.toString() || undefined,
        errorMessage: msg.errorMessage || undefined,
      }));
    } catch (error) {
      console.error('Failed to fetch recent messages:', error);
      throw new Error(`Recent messages fetch failed: ${(error as Error).message}`);
    }
  }
}

// Factory function to create TwilioService instance
export function createTwilioService(): TwilioService {
  const config: TwilioConfig = {
    accountSid: process.env.TWILIO_ACCOUNT_SID || '',
    authToken: process.env.TWILIO_AUTH_TOKEN || '',
    phoneNumber: process.env.TWILIO_PHONE_NUMBER || '',
    whatsappNumber: process.env.TWILIO_WHATSAPP_NUMBER || '',
  };

  // Validate required environment variables
  if (!config.accountSid || !config.authToken) {
    throw new Error('Twilio credentials not configured. Please set TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN environment variables.');
  }

  if (!config.phoneNumber) {
    throw new Error('Twilio phone number not configured. Please set TWILIO_PHONE_NUMBER environment variable.');
  }

  return new TwilioService(config);
}

// Export types
export type { 
  TwilioConfig, 
  MessageResult, 
  MessageTemplate, 
  PersonalizationData, 
  DeliveryStatus 
};