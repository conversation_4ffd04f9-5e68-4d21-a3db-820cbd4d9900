import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Generate rent roll for a property
export const generateRentRoll = mutation({
  args: {
    propertyId: v.id("properties"),
    month: v.number(), // 1-12
    year: v.number(),
    includeLateFees: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Property not found");
    }

    // Get all active leases for the property
    const activeLeases = await ctx.db
      .query("leases")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    const now = Date.now();
    const dueDate = new Date(args.year, args.month - 1, 1); // First day of the month
    const dueDateTimestamp = dueDate.getTime();

    const rentRollEntries = [];

    for (const lease of activeLeases) {
      // Check if lease is active during the specified month
      const leaseStartMonth = new Date(lease.startDate);
      const leaseEndMonth = new Date(lease.endDate);
      
      if (leaseStartMonth <= dueDate && leaseEndMonth >= dueDate) {
        // Get tenant and unit information
        const tenant = await ctx.db.get(lease.tenantId);
        const unit = await ctx.db.get(lease.unitId);

        if (!tenant || !unit) continue;

        // Check if invoice already exists for this month
        const existingInvoice = await ctx.db
          .query("invoices")
          .withIndex("by_lease", (q) => q.eq("leaseId", lease._id))
          .filter((q) => {
            const invoiceDueDate = new Date(q.field("dueDate"));
            return q.and(
              q.eq(invoiceDueDate.getFullYear(), args.year),
              q.eq(invoiceDueDate.getMonth(), args.month - 1),
              q.eq(q.field("type"), "rent")
            );
          })
          .first();

        let invoiceId = existingInvoice?._id;
        let invoiceStatus = existingInvoice?.status || "pending";
        let invoiceAmount = lease.monthlyRent;

        // Calculate late fees if applicable
        let lateFee = 0;
        if (args.includeLateFees && now > dueDateTimestamp + (lease.terms.gracePeriod * 24 * 60 * 60 * 1000)) {
          lateFee = (lease.monthlyRent * lease.terms.lateFeePercentage) / 100;
          invoiceAmount += lateFee;
        }

        // Create invoice if it doesn't exist
        if (!existingInvoice) {
          const invoiceItems = [
            {
              description: `Rent for ${dueDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`,
              amount: lease.monthlyRent,
              quantity: 1,
            }
          ];

          if (lateFee > 0) {
            invoiceItems.push({
              description: `Late fee (${lease.terms.lateFeePercentage}%)`,
              amount: lateFee,
              quantity: 1,
            });
          }

          invoiceId = await ctx.db.insert("invoices", {
            leaseId: lease._id,
            propertyId: args.propertyId,
            tenantId: lease.tenantId,
            amount: invoiceAmount,
            dueDate: dueDateTimestamp,
            status: "pending",
            type: "rent",
            items: invoiceItems,
            createdAt: now,
            updatedAt: now,
          });
        }

        rentRollEntries.push({
          leaseId: lease._id,
          invoiceId,
          tenant: {
            id: tenant._id,
            name: tenant.name,
            email: tenant.email,
          },
          unit: {
            id: unit._id,
            unitNumber: unit.unitNumber,
            type: unit.type,
          },
          rent: lease.monthlyRent,
          lateFee,
          totalAmount: invoiceAmount,
          dueDate: dueDateTimestamp,
          status: invoiceStatus,
          leaseStartDate: lease.startDate,
          leaseEndDate: lease.endDate,
        });
      }
    }

    // Calculate totals
    const totalRent = rentRollEntries.reduce((sum, entry) => sum + entry.rent, 0);
    const totalLateFees = rentRollEntries.reduce((sum, entry) => sum + entry.lateFee, 0);
    const totalAmount = rentRollEntries.reduce((sum, entry) => sum + entry.totalAmount, 0);
    const paidAmount = rentRollEntries
      .filter(entry => entry.status === "paid")
      .reduce((sum, entry) => sum + entry.totalAmount, 0);
    const pendingAmount = rentRollEntries
      .filter(entry => entry.status === "pending")
      .reduce((sum, entry) => sum + entry.totalAmount, 0);
    const overdueAmount = rentRollEntries
      .filter(entry => entry.status === "overdue")
      .reduce((sum, entry) => sum + entry.totalAmount, 0);

    return {
      propertyId: args.propertyId,
      month: args.month,
      year: args.year,
      generatedAt: now,
      entries: rentRollEntries,
      summary: {
        totalUnits: rentRollEntries.length,
        totalRent,
        totalLateFees,
        totalAmount,
        paidAmount,
        pendingAmount,
        overdueAmount,
        collectionRate: totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0,
      },
    };
  },
});

// Create individual invoice
export const createInvoice = mutation({
  args: {
    leaseId: v.id("leases"),
    amount: v.number(),
    dueDate: v.number(),
    type: v.union(v.literal("rent"), v.literal("deposit"), v.literal("maintenance"), v.literal("other")),
    items: v.array(v.object({
      description: v.string(),
      amount: v.number(),
      quantity: v.number(),
    })),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const lease = await ctx.db.get(args.leaseId);
    if (!lease) {
      throw new Error("Lease not found");
    }

    // Validate amount matches items total
    const itemsTotal = args.items.reduce((sum, item) => sum + (item.amount * item.quantity), 0);
    if (Math.abs(itemsTotal - args.amount) > 0.01) {
      throw new Error("Invoice amount does not match items total");
    }

    const now = Date.now();

    const invoiceId = await ctx.db.insert("invoices", {
      leaseId: args.leaseId,
      propertyId: lease.propertyId,
      tenantId: lease.tenantId,
      amount: args.amount,
      dueDate: args.dueDate,
      status: "pending",
      type: args.type,
      items: args.items,
      createdAt: now,
      updatedAt: now,
    });

    // Create notification for tenant
    await ctx.db.insert("notifications", {
      userId: lease.tenantId,
      title: "New Invoice Generated",
      message: `A new ${args.type} invoice for $${args.amount.toFixed(2)} has been generated and is due on ${new Date(args.dueDate).toLocaleDateString()}.`,
      type: "invoice",
      isRead: false,
      metadata: {
        invoiceId,
        leaseId: args.leaseId,
        amount: args.amount,
        dueDate: args.dueDate,
      },
      createdAt: now,
    });

    return invoiceId;
  },
});

// Update invoice status
export const updateInvoiceStatus = mutation({
  args: {
    invoiceId: v.id("invoices"),
    status: v.union(v.literal("pending"), v.literal("paid"), v.literal("overdue"), v.literal("cancelled")),
    paidAt: v.optional(v.number()),
    paymentMethod: v.optional(v.union(v.literal("mpesa"), v.literal("stripe"), v.literal("bank_transfer"))),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    const now = Date.now();
    const updates: any = {
      status: args.status,
      updatedAt: now,
    };

    if (args.paidAt) {
      updates.paidAt = args.paidAt;
    }

    if (args.paymentMethod) {
      updates.paymentMethod = args.paymentMethod;
    }

    await ctx.db.patch(args.invoiceId, updates);

    // Create notification based on status change
    if (args.status === "paid") {
      // Notify property manager of payment
      const lease = await ctx.db.get(invoice.leaseId);
      const property = lease ? await ctx.db.get(lease.propertyId) : null;
      
      if (property?.managerId) {
        await ctx.db.insert("notifications", {
          userId: property.managerId,
          title: "Payment Received",
          message: `Payment of $${invoice.amount.toFixed(2)} has been received for invoice #${invoice._id.slice(-8)}.`,
          type: "payment",
          isRead: false,
          metadata: {
            invoiceId: args.invoiceId,
            amount: invoice.amount,
            paymentMethod: args.paymentMethod,
          },
          createdAt: now,
        });
      }
    } else if (args.status === "overdue") {
      // Notify tenant of overdue invoice
      await ctx.db.insert("notifications", {
        userId: invoice.tenantId,
        title: "Invoice Overdue",
        message: `Your invoice for $${invoice.amount.toFixed(2)} is now overdue. Please make payment as soon as possible.`,
        type: "overdue",
        isRead: false,
        metadata: {
          invoiceId: args.invoiceId,
          amount: invoice.amount,
          dueDate: invoice.dueDate,
        },
        createdAt: now,
      });
    }

    return args.invoiceId;
  },
});

// Get invoices for a lease
export const getInvoicesByLease = query({
  args: { leaseId: v.id("leases") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("invoices")
      .withIndex("by_lease", (q) => q.eq("leaseId", args.leaseId))
      .order("desc")
      .collect();
  },
});

// Get invoices for a tenant
export const getInvoicesByTenant = query({
  args: { tenantId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("invoices")
      .withIndex("by_tenant", (q) => q.eq("tenantId", args.tenantId))
      .order("desc")
      .collect();
  },
});

// Get overdue invoices
export const getOverdueInvoices = query({
  args: {
    propertyId: v.optional(v.id("properties")),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    let query = ctx.db.query("invoices");
    
    const allInvoices = await query.collect();
    
    // Filter overdue invoices (due date passed and not paid)
    let overdueInvoices = allInvoices.filter(invoice => 
      invoice.dueDate < now && 
      invoice.status !== "paid" && 
      invoice.status !== "cancelled"
    );

    // Filter by property if specified
    if (args.propertyId) {
      overdueInvoices = overdueInvoices.filter(invoice => 
        invoice.propertyId === args.propertyId
      );
    }

    // Update status to overdue if not already
    for (const invoice of overdueInvoices) {
      if (invoice.status === "pending") {
        await ctx.db.patch(invoice._id, {
          status: "overdue",
          updatedAt: now,
        });
      }
    }

    // Get related data for each invoice
    const invoicesWithData = await Promise.all(
      overdueInvoices.map(async (invoice) => {
        const lease = await ctx.db.get(invoice.leaseId);
        const tenant = await ctx.db.get(invoice.tenantId);
        const property = await ctx.db.get(invoice.propertyId);
        const unit = lease ? await ctx.db.get(lease.unitId) : null;

        return {
          invoice,
          lease,
          tenant,
          property,
          unit,
          daysOverdue: Math.ceil((now - invoice.dueDate) / (24 * 60 * 60 * 1000)),
        };
      })
    );

    return invoicesWithData.sort((a, b) => b.daysOverdue - a.daysOverdue);
  },
});

// Generate recurring invoices (to be called by scheduled job)
export const generateRecurringInvoices = mutation({
  args: {
    targetDate: v.optional(v.number()), // If not provided, uses current date
  },
  handler: async (ctx, args) => {
    const targetDate = args.targetDate || Date.now();
    const date = new Date(targetDate);
    const month = date.getMonth() + 1; // 1-12
    const year = date.getFullYear();

    // Get all active leases
    const activeLeases = await ctx.db
      .query("leases")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .collect();

    const generatedInvoices = [];

    for (const lease of activeLeases) {
      // Check if lease is active during the target month
      const leaseStart = new Date(lease.startDate);
      const leaseEnd = new Date(lease.endDate);
      const monthStart = new Date(year, month - 1, 1);
      const monthEnd = new Date(year, month, 0);

      if (leaseStart <= monthEnd && leaseEnd >= monthStart) {
        // Check if invoice already exists for this month
        const existingInvoice = await ctx.db
          .query("invoices")
          .withIndex("by_lease", (q) => q.eq("leaseId", lease._id))
          .filter((q) => {
            const invoiceDueDate = new Date(q.field("dueDate"));
            return q.and(
              q.eq(invoiceDueDate.getFullYear(), year),
              q.eq(invoiceDueDate.getMonth(), month - 1),
              q.eq(q.field("type"), "rent")
            );
          })
          .first();

        if (!existingInvoice) {
          // Calculate prorated amount if lease starts/ends mid-month
          let rentAmount = lease.monthlyRent;
          const daysInMonth = new Date(year, month, 0).getDate();
          
          if (leaseStart > monthStart) {
            // Lease starts mid-month
            const daysFromStart = daysInMonth - leaseStart.getDate() + 1;
            rentAmount = (lease.monthlyRent / daysInMonth) * daysFromStart;
          } else if (leaseEnd < monthEnd) {
            // Lease ends mid-month
            const daysUntilEnd = leaseEnd.getDate();
            rentAmount = (lease.monthlyRent / daysInMonth) * daysUntilEnd;
          }

          const dueDate = new Date(year, month - 1, 1).getTime(); // First day of month

          const invoiceId = await ctx.db.insert("invoices", {
            leaseId: lease._id,
            propertyId: lease.propertyId,
            tenantId: lease.tenantId,
            amount: rentAmount,
            dueDate,
            status: "pending",
            type: "rent",
            items: [
              {
                description: `Rent for ${date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`,
                amount: rentAmount,
                quantity: 1,
              }
            ],
            createdAt: targetDate,
            updatedAt: targetDate,
          });

          generatedInvoices.push(invoiceId);

          // Create notification for tenant
          await ctx.db.insert("notifications", {
            userId: lease.tenantId,
            title: "Monthly Rent Invoice",
            message: `Your rent invoice for ${date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })} is now available. Amount due: $${rentAmount.toFixed(2)}.`,
            type: "invoice",
            isRead: false,
            metadata: {
              invoiceId,
              leaseId: lease._id,
              amount: rentAmount,
              dueDate,
            },
            createdAt: targetDate,
          });
        }
      }
    }

    return {
      generatedCount: generatedInvoices.length,
      invoiceIds: generatedInvoices,
      targetMonth: month,
      targetYear: year,
    };
  },
});

// Get invoice by ID with related data
export const getInvoiceById = query({
  args: { id: v.id("invoices") },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.id);
    if (!invoice) {
      return null;
    }

    const lease = await ctx.db.get(invoice.leaseId);
    const tenant = await ctx.db.get(invoice.tenantId);
    const property = await ctx.db.get(invoice.propertyId);
    const unit = lease ? await ctx.db.get(lease.unitId) : null;

    // Get payment history for this invoice
    const payments = await ctx.db
      .query("payments")
      .withIndex("by_invoice", (q) => q.eq("invoiceId", args.id))
      .collect();

    return {
      invoice,
      lease,
      tenant,
      property,
      unit,
      payments,
    };
  },
});

// Get financial summary for a property
export const getPropertyFinancialSummary = query({
  args: {
    propertyId: v.id("properties"),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const startDate = args.startDate || (now - (30 * 24 * 60 * 60 * 1000)); // 30 days ago
    const endDate = args.endDate || now;

    // Get all invoices for the property within date range
    const allInvoices = await ctx.db
      .query("invoices")
      .filter((q) => q.eq(q.field("propertyId"), args.propertyId))
      .collect();

    const invoicesInRange = allInvoices.filter(invoice => 
      invoice.createdAt >= startDate && invoice.createdAt <= endDate
    );

    // Calculate totals
    const totalInvoiced = invoicesInRange.reduce((sum, invoice) => sum + invoice.amount, 0);
    const totalPaid = invoicesInRange
      .filter(invoice => invoice.status === "paid")
      .reduce((sum, invoice) => sum + invoice.amount, 0);
    const totalPending = invoicesInRange
      .filter(invoice => invoice.status === "pending")
      .reduce((sum, invoice) => sum + invoice.amount, 0);
    const totalOverdue = invoicesInRange
      .filter(invoice => invoice.status === "overdue")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    // Get rent vs other income breakdown
    const rentIncome = invoicesInRange
      .filter(invoice => invoice.type === "rent" && invoice.status === "paid")
      .reduce((sum, invoice) => sum + invoice.amount, 0);
    const otherIncome = invoicesInRange
      .filter(invoice => invoice.type !== "rent" && invoice.status === "paid")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    // Calculate collection rate
    const collectionRate = totalInvoiced > 0 ? (totalPaid / totalInvoiced) * 100 : 0;

    // Get monthly breakdown
    const monthlyData = new Map();
    invoicesInRange.forEach(invoice => {
      const date = new Date(invoice.createdAt);
      const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;
      
      if (!monthlyData.has(monthKey)) {
        monthlyData.set(monthKey, {
          month: date.getMonth() + 1,
          year: date.getFullYear(),
          invoiced: 0,
          paid: 0,
          pending: 0,
          overdue: 0,
        });
      }
      
      const monthData = monthlyData.get(monthKey);
      monthData.invoiced += invoice.amount;
      
      if (invoice.status === "paid") monthData.paid += invoice.amount;
      else if (invoice.status === "pending") monthData.pending += invoice.amount;
      else if (invoice.status === "overdue") monthData.overdue += invoice.amount;
    });

    return {
      propertyId: args.propertyId,
      period: { startDate, endDate },
      summary: {
        totalInvoiced,
        totalPaid,
        totalPending,
        totalOverdue,
        collectionRate,
        rentIncome,
        otherIncome,
        totalIncome: totalPaid,
      },
      monthlyBreakdown: Array.from(monthlyData.values()).sort((a, b) => 
        a.year !== b.year ? a.year - b.year : a.month - b.month
      ),
      invoiceCount: {
        total: invoicesInRange.length,
        paid: invoicesInRange.filter(i => i.status === "paid").length,
        pending: invoicesInRange.filter(i => i.status === "pending").length,
        overdue: invoicesInRange.filter(i => i.status === "overdue").length,
      },
    };
  },
});

// Get pending invoices for tenant
export const getPendingInvoices = query({
  args: { tenantId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("invoices")
      .withIndex("by_tenant", (q) => q.eq("tenantId", args.tenantId))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .collect();
  },
});

// Get paid invoices for tenant
export const getPaidInvoices = query({
  args: { 
    tenantId: v.id("users"),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const invoices = await ctx.db
      .query("invoices")
      .withIndex("by_tenant", (q) => q.eq("tenantId", args.tenantId))
      .filter((q) => q.eq(q.field("status"), "paid"))
      .order("desc")
      .collect();
    
    return args.limit ? invoices.slice(0, args.limit) : invoices;
  },
});

// Get recent invoices for tenant
export const getRecentInvoices = query({
  args: { 
    tenantId: v.id("users"),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const invoices = await ctx.db
      .query("invoices")
      .withIndex("by_tenant", (q) => q.eq("tenantId", args.tenantId))
      .order("desc")
      .collect();
    
    return args.limit ? invoices.slice(0, args.limit) : invoices;
  },
});