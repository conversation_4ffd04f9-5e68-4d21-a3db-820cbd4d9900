import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { RegulatoryReporting } from '../RegulatoryReporting';

// Mock Convex hooks
const mockUseQuery = vi.fn();
const mockUseMutation = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: () => mockUseQuery(),
  useMutation: () => mockUseMutation(),
}));

// Mock API
vi.mock('../../../../convex/_generated/api', () => ({
  api: {
    compliance: {
      getRegulatoryReports: 'getRegulatoryReports',
      generateRegulatoryReport: 'generateRegulatoryReport',
    },
    properties: {
      getProperties: 'getProperties',
    },
  },
}));

describe('RegulatoryReporting', () => {
  const mockGenerateReport = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseMutation.mockReturnValue(mockGenerateReport);
  });

  it('renders regulatory reporting dashboard', () => {
    mockUseQuery.mockReturnValue([]);

    render(<RegulatoryReporting />);

    expect(screen.getByText('Regulatory Reporting')).toBeInTheDocument();
    expect(screen.getByText('Generate and manage regulatory compliance reports')).toBeInTheDocument();
  });

  it('displays report type cards', () => {
    mockUseQuery.mockReturnValue([]);

    render(<RegulatoryReporting />);

    expect(screen.getByText('ETIMS VAT Report')).toBeInTheDocument();
    expect(screen.getByText('ETIMS Income Tax Report')).toBeInTheDocument();
    expect(screen.getByText('Rental Income Report')).toBeInTheDocument();
    expect(screen.getByText('Tenant Registry Report')).toBeInTheDocument();
    expect(screen.getByText('Compliance Summary Report')).toBeInTheDocument();
    expect(screen.getByText('KYC Status Report')).toBeInTheDocument();
  });

  it('shows recent reports when available', () => {
    const mockReports = [
      {
        _id: 'report1',
        name: 'ETIMS VAT Report - January 2024',
        reportType: 'etims_vat',
        status: 'completed',
        propertyIds: ['prop1', 'prop2'],
        createdAt: Date.now(),
      },
      {
        _id: 'report2',
        name: 'Rental Income Report - December 2023',
        reportType: 'rental_income',
        status: 'generating',
        propertyIds: ['prop1'],
        createdAt: Date.now() - 1000,
      },
    ];

    mockUseQuery.mockImplementation((query) => {
      if (query === 'getRegulatoryReports') return mockReports;
      return [];
    });

    render(<RegulatoryReporting />);

    expect(screen.getByText('Recent Reports')).toBeInTheDocument();
    expect(screen.getByText('ETIMS VAT Report - January 2024')).toBeInTheDocument();
    expect(screen.getByText('Rental Income Report - December 2023')).toBeInTheDocument();
  });

  it('shows empty state when no reports exist', () => {
    mockUseQuery.mockImplementation((query) => {
      if (query === 'getRegulatoryReports') return [];
      return [];
    });

    render(<RegulatoryReporting />);

    expect(screen.getByText('No reports generated yet')).toBeInTheDocument();
    expect(screen.getByText('Generate Your First Report')).toBeInTheDocument();
  });

  it('opens create report modal when generate button is clicked', () => {
    mockUseQuery.mockReturnValue([]);

    render(<RegulatoryReporting />);

    const generateButton = screen.getByText('Generate Report');
    fireEvent.click(generateButton);

    expect(screen.getByText('Generate Regulatory Report')).toBeInTheDocument();
    expect(screen.getByText('Create a new compliance or regulatory report')).toBeInTheDocument();
  });

  it('displays report type options in modal', () => {
    mockUseQuery.mockReturnValue([]);

    render(<RegulatoryReporting />);

    // Open modal
    fireEvent.click(screen.getByText('Generate Report'));

    expect(screen.getByText('Report Type')).toBeInTheDocument();
    // The select component should contain all report types
  });

  it('handles report generation form submission', async () => {
    const mockProperties = [
      { _id: 'prop1', name: 'Property 1' },
      { _id: 'prop2', name: 'Property 2' },
    ];

    mockUseQuery.mockImplementation((query) => {
      if (query === 'getProperties') return mockProperties;
      return [];
    });

    render(<RegulatoryReporting />);

    // Open modal
    fireEvent.click(screen.getByText('Generate Report'));

    // Fill form (we'll simulate the form being filled)
    // In a real test, you'd interact with the form elements
    
    // Click generate button in modal
    const modalGenerateButton = screen.getAllByText('Generate Report')[1]; // Second one is in modal
    fireEvent.click(modalGenerateButton);

    // The form validation should prevent submission without required fields
    // In a complete implementation, you'd test the validation
  });

  it('displays correct status badges for reports', () => {
    const mockReports = [
      {
        _id: 'report1',
        name: 'Completed Report',
        reportType: 'etims_vat',
        status: 'completed',
        propertyIds: ['prop1'],
        createdAt: Date.now(),
      },
      {
        _id: 'report2',
        name: 'Generating Report',
        reportType: 'rental_income',
        status: 'generating',
        propertyIds: ['prop1'],
        createdAt: Date.now(),
      },
      {
        _id: 'report3',
        name: 'Failed Report',
        reportType: 'kyc_status',
        status: 'failed',
        propertyIds: ['prop1'],
        createdAt: Date.now(),
      },
    ];

    mockUseQuery.mockImplementation((query) => {
      if (query === 'getRegulatoryReports') return mockReports;
      return [];
    });

    render(<RegulatoryReporting />);

    expect(screen.getByText('completed')).toBeInTheDocument();
    expect(screen.getByText('generating')).toBeInTheDocument();
    expect(screen.getByText('failed')).toBeInTheDocument();
  });

  it('shows download button only for completed reports', () => {
    const mockReports = [
      {
        _id: 'report1',
        name: 'Completed Report',
        reportType: 'etims_vat',
        status: 'completed',
        propertyIds: ['prop1'],
        createdAt: Date.now(),
      },
      {
        _id: 'report2',
        name: 'Generating Report',
        reportType: 'rental_income',
        status: 'generating',
        propertyIds: ['prop1'],
        createdAt: Date.now(),
      },
    ];

    mockUseQuery.mockImplementation((query) => {
      if (query === 'getRegulatoryReports') return mockReports;
      return [];
    });

    render(<RegulatoryReporting />);

    const downloadButtons = screen.getAllByText('Download');
    expect(downloadButtons).toHaveLength(1); // Only one download button for completed report
  });

  it('handles property-specific reporting', () => {
    mockUseQuery.mockReturnValue([]);

    render(<RegulatoryReporting propertyId="property123" />);

    expect(screen.getByText('Regulatory Reporting')).toBeInTheDocument();
    // The component should filter reports by property ID
  });

  it('displays ETIMS information alert for ETIMS reports', () => {
    mockUseQuery.mockReturnValue([]);

    render(<RegulatoryReporting />);

    // Open modal
    fireEvent.click(screen.getByText('Generate Report'));

    // Select ETIMS VAT report type (this would require interacting with the select)
    // For now, we'll just verify the modal is open
    expect(screen.getByText('Generate Regulatory Report')).toBeInTheDocument();
  });

  it('allows adding and removing recipients', () => {
    mockUseQuery.mockReturnValue([]);

    render(<RegulatoryReporting />);

    // Open modal
    fireEvent.click(screen.getByText('Generate Report'));

    expect(screen.getByText('Report Recipients')).toBeInTheDocument();
    // Test would involve interacting with recipient form fields
  });

  it('closes modal when cancel is clicked', () => {
    mockUseQuery.mockReturnValue([]);

    render(<RegulatoryReporting />);

    // Open modal
    fireEvent.click(screen.getByText('Generate Report'));
    expect(screen.getByText('Generate Regulatory Report')).toBeInTheDocument();

    // Close modal
    fireEvent.click(screen.getByText('Cancel'));
    expect(screen.queryByText('Generate Regulatory Report')).not.toBeInTheDocument();
  });

  it('displays property count in report list', () => {
    const mockReports = [
      {
        _id: 'report1',
        name: 'Multi-Property Report',
        reportType: 'etims_vat',
        status: 'completed',
        propertyIds: ['prop1', 'prop2', 'prop3'],
        createdAt: Date.now(),
      },
    ];

    mockUseQuery.mockImplementation((query) => {
      if (query === 'getRegulatoryReports') return mockReports;
      return [];
    });

    render(<RegulatoryReporting />);

    expect(screen.getByText('3 properties')).toBeInTheDocument();
  });
});