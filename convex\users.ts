import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { validateTenantOnboardingData, sanitizeTenantData, validatePhoneNumber } from "./lib/validation";

// Get all users (with proper authorization) - legacy version
export const getUsersWithAuth = query({
  args: { sessionToken: v.string() },
  handler: async (ctx, _args) => {
    // Simplified auth check - would validate session in production
    const currentUser = await ctx.db.query("users").first();
    if (!currentUser || !["owner", "manager"].includes(currentUser.role)) {
      throw new Error("Not authorized to view all users");
    }

    return await ctx.db.query("users").collect();
  },
});

// Get user by ID
export const getUserById = query({
  args: { 
    sessionToken: v.string(),
    id: v.id("users") 
  },
  handler: async (ctx, args) => {
    // Simplified auth check - would validate session in production
    const currentUser = await ctx.db.query("users").first();
    if (!currentUser) {
      throw new Error("User not found");
    }

    // Users can view their own profile or owners/managers can view any profile
    if (currentUser._id !== args.id && !["owner", "manager"].includes(currentUser.role)) {
      throw new Error("Not authorized to view this user");
    }

    return await ctx.db.get(args.id);
  },
});

// Get user by ID (simplified version for internal use)
export const getUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.userId);
  },
});

// Get users with optional property filtering (simplified version)
export const getUsers = query({
  args: { 
    propertyId: v.optional(v.id("properties")),
    role: v.optional(v.union(v.literal("owner"), v.literal("manager"), v.literal("vendor"), v.literal("tenant")))
  },
  handler: async (ctx, args) => {
    let userQuery = ctx.db.query("users");

    if (args.role) {
      userQuery = userQuery.filter(q => q.eq(q.field("role"), args.role));
    }

    const users = await userQuery.collect();

    // If propertyId is specified, filter users who have access to that property
    if (args.propertyId) {
      return users.filter(user => 
        user.propertyAccess?.includes(args.propertyId) || 
        user.role === "owner" // Owners have access to all properties
      );
    }

    return users;
  },
});

// Get users by role
export const getUsersByRole = query({
  args: { 
    sessionToken: v.string(),
    role: v.union(v.literal("owner"), v.literal("manager"), v.literal("vendor"), v.literal("tenant")) 
  },
  handler: async (ctx, args) => {
    // Simplified auth check - would validate session in production
    const currentUser = await ctx.db.query("users").first();
    if (!currentUser) {
      throw new Error("Not authenticated");
    }

    return await ctx.db
      .query("users")
      .withIndex("by_role", (q) => q.eq("role", args.role))
      .collect();
  },
});

// Deactivate user account
export const deactivateUser = mutation({
  args: { 
    sessionToken: v.string(),
    userId: v.id("users") 
  },
  handler: async (ctx, args) => {
    // Simplified auth check - would validate session in production
    const currentUser = await ctx.db.query("users").first();
    if (!currentUser || currentUser.role !== "owner") {
      throw new Error("Only owners can deactivate users");
    }

    await ctx.db.patch(args.userId, {
      isActive: false,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Reactivate user account
export const reactivateUser = mutation({
  args: { 
    sessionToken: v.string(),
    userId: v.id("users") 
  },
  handler: async (ctx, args) => {
    // Simplified auth check - would validate session in production
    const currentUser = await ctx.db.query("users").first();
    if (!currentUser || currentUser.role !== "owner") {
      throw new Error("Only owners can reactivate users");
    }

    await ctx.db.patch(args.userId, {
      isActive: true,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Update KYC status
export const updateKYCStatus = mutation({
  args: {
    sessionToken: v.string(),
    userId: v.id("users"),
    status: v.union(v.literal("pending"), v.literal("verified"), v.literal("rejected")),
  },
  handler: async (ctx, args) => {
    // Simplified auth check - would validate session in production
    const currentUser = await ctx.db.query("users").first();
    if (!currentUser || !["owner", "manager"].includes(currentUser.role)) {
      throw new Error("Not authorized to update KYC status");
    }

    await ctx.db.patch(args.userId, {
      kycStatus: args.status,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Tenant onboarding functions

// Create tenant during onboarding process
export const createTenant = mutation({
  args: {
    email: v.string(),
    name: v.string(),
    phone: v.optional(v.string()),
    propertyId: v.id("properties"),
    kycDocuments: v.optional(v.array(v.object({
      type: v.string(),
      url: v.string(),
      name: v.string(),
    }))),
  },
  handler: async (ctx, args) => {
    // Validate and sanitize tenant data
    const sanitizedData = sanitizeTenantData(args);
    validateTenantOnboardingData({
      ...sanitizedData,
      kycStatus: "pending",
    });

    // Check if user with this email already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", sanitizedData.email))
      .first();

    if (existingUser) {
      throw new Error("User with this email already exists");
    }

    // Verify property exists
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Property not found");
    }

    const now = Date.now();

    // Create tenant user
    const tenantId = await ctx.db.insert("users", {
      email: sanitizedData.email,
      name: sanitizedData.name,
      role: "tenant",
      propertyAccess: [args.propertyId],
      kycStatus: "pending",
      phone: sanitizedData.phone,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    // Store KYC documents if provided
    if (args.kycDocuments && args.kycDocuments.length > 0) {
      for (const doc of args.kycDocuments) {
        await ctx.db.insert("documents", {
          name: doc.name,
          type: doc.type,
          url: doc.url,
          size: 0, // Would be set during actual file upload
          uploadedBy: tenantId,
          relatedTo: {
            type: "tenant",
            id: tenantId,
          },
          tags: ["kyc", "onboarding"],
          isPublic: false,
          createdAt: now,
        });
      }
    }

    // Create notification for property manager about new tenant
    const managerId = property.managerId || property.ownerId;
    await ctx.db.insert("notifications", {
      userId: managerId,
      title: "New Tenant Onboarded",
      message: `New tenant ${sanitizedData.name} has been onboarded for ${property.name}`,
      type: "system",
      isRead: false,
      createdAt: now,
    });

    return tenantId;
  },
});

// Update tenant profile during onboarding
export const updateTenantProfile = mutation({
  args: {
    tenantId: v.id("users"),
    name: v.optional(v.string()),
    phone: v.optional(v.string()),
    avatar: v.optional(v.string()),
    emergencyContact: v.optional(v.object({
      name: v.string(),
      phone: v.string(),
      relationship: v.string(),
    })),
  },
  handler: async (ctx, args) => {
    const tenant = await ctx.db.get(args.tenantId);
    if (!tenant) {
      throw new Error("Tenant not found");
    }

    if (tenant.role !== "tenant") {
      throw new Error("User is not a tenant");
    }

    const updates: any = {
      updatedAt: Date.now(),
    };

    if (args.name) {
      updates.name = args.name.trim();
    }

    if (args.phone) {
      if (!validatePhoneNumber(args.phone)) {
        throw new Error("Invalid phone number format");
      }
      updates.phone = args.phone.replace(/\s/g, '');
    }

    if (args.avatar) {
      updates.avatar = args.avatar;
    }

    // Store emergency contact as a document or in user metadata
    if (args.emergencyContact) {
      // For now, we'll store it as a document
      await ctx.db.insert("documents", {
        name: "Emergency Contact Information",
        type: "emergency_contact",
        url: "", // This would be a JSON document or stored differently
        size: 0,
        uploadedBy: args.tenantId,
        relatedTo: {
          type: "tenant",
          id: args.tenantId,
        },
        tags: ["emergency", "contact"],
        isPublic: false,
        createdAt: Date.now(),
      });
    }

    await ctx.db.patch(args.tenantId, updates);

    return args.tenantId;
  },
});

// Submit KYC documents for tenant
export const submitKYCDocuments = mutation({
  args: {
    tenantId: v.id("users"),
    documents: v.array(v.object({
      type: v.string(), // "id_card", "passport", "proof_of_income", etc.
      url: v.string(),
      name: v.string(),
      size: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    const tenant = await ctx.db.get(args.tenantId);
    if (!tenant) {
      throw new Error("Tenant not found");
    }

    if (tenant.role !== "tenant") {
      throw new Error("User is not a tenant");
    }

    const now = Date.now();

    // Store each document
    for (const doc of args.documents) {
      await ctx.db.insert("documents", {
        name: doc.name,
        type: doc.type,
        url: doc.url,
        size: doc.size,
        uploadedBy: args.tenantId,
        relatedTo: {
          type: "tenant",
          id: args.tenantId,
        },
        tags: ["kyc", "verification"],
        isPublic: false,
        createdAt: now,
      });
    }

    // Update tenant KYC status to pending review
    await ctx.db.patch(args.tenantId, {
      kycStatus: "pending",
      updatedAt: now,
    });

    // Notify property managers about KYC submission
    const propertyIds = tenant.propertyAccess;
    for (const propertyId of propertyIds) {
      const property = await ctx.db.get(propertyId);
      if (property) {
        const managerId = property.managerId || property.ownerId;
        await ctx.db.insert("notifications", {
          userId: managerId,
          title: "KYC Documents Submitted",
          message: `${tenant.name} has submitted KYC documents for review`,
          type: "system",
          isRead: false,
          createdAt: now,
        });
      }
    }

    return { success: true };
  },
});

// Get tenant onboarding status
export const getTenantOnboardingStatus = query({
  args: { tenantId: v.id("users") },
  handler: async (ctx, args) => {
    const tenant = await ctx.db.get(args.tenantId);
    if (!tenant) {
      throw new Error("Tenant not found");
    }

    if (tenant.role !== "tenant") {
      throw new Error("User is not a tenant");
    }

    // Get KYC documents
    const kycDocuments = await ctx.db
      .query("documents")
      .withIndex("by_related", (q) => q.eq("relatedTo.type", "tenant").eq("relatedTo.id", args.tenantId))
      .filter((q) => q.eq(q.field("tags"), ["kyc", "verification"]))
      .collect();

    // Get active leases
    const activeLeases = await ctx.db
      .query("leases")
      .withIndex("by_tenant", (q) => q.eq("tenantId", args.tenantId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    // Calculate onboarding completion percentage
    let completionPercentage = 0;
    const steps = {
      profileComplete: !!(tenant.name && tenant.phone),
      kycSubmitted: kycDocuments.length > 0,
      kycVerified: tenant.kycStatus === "verified",
      leaseActive: activeLeases.length > 0,
    };

    const completedSteps = Object.values(steps).filter(Boolean).length;
    completionPercentage = (completedSteps / Object.keys(steps).length) * 100;

    return {
      tenant,
      kycDocuments: kycDocuments.length,
      activeLeases: activeLeases.length,
      onboardingSteps: steps,
      completionPercentage,
      isFullyOnboarded: completionPercentage === 100,
    };
  },
});

// Get tenants by property for management
export const getTenantsByProperty = query({
  args: { 
    propertyId: v.id("properties"),
    kycStatus: v.optional(v.union(v.literal("pending"), v.literal("verified"), v.literal("rejected"))),
  },
  handler: async (ctx, args) => {
    // Get all users with tenant role who have access to this property
    const tenants = await ctx.db
      .query("users")
      .withIndex("by_role", (q) => q.eq("role", "tenant"))
      .filter((q) => q.eq(q.field("propertyAccess"), [args.propertyId]))
      .collect();

    // Filter by KYC status if specified
    let filteredTenants = tenants;
    if (args.kycStatus) {
      filteredTenants = tenants.filter(tenant => tenant.kycStatus === args.kycStatus);
    }

    // Get additional data for each tenant
    const tenantsWithData = await Promise.all(
      filteredTenants.map(async (tenant) => {
        // Get active leases
        const activeLeases = await ctx.db
          .query("leases")
          .withIndex("by_tenant", (q) => q.eq("tenantId", tenant._id))
          .filter((q) => q.eq(q.field("status"), "active"))
          .collect();

        // Get KYC documents count
        const kycDocuments = await ctx.db
          .query("documents")
          .withIndex("by_related", (q) => q.eq("relatedTo.type", "tenant").eq("relatedTo.id", tenant._id))
          .filter((q) => q.eq(q.field("tags"), ["kyc", "verification"]))
          .collect();

        return {
          tenant,
          activeLeases: activeLeases.length,
          kycDocumentsCount: kycDocuments.length,
          lastLogin: tenant.lastLogin,
        };
      })
    );

    return tenantsWithData.sort((a, b) => b.tenant.createdAt - a.tenant.createdAt);
  },
});

// Get escalation targets (managers and owners) for a property
export const getEscalationTargets = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!user) throw new Error("User not found");

    // Get property to find owner and manager
    const property = await ctx.db.get(args.propertyId);
    if (!property) throw new Error("Property not found");

    const escalationTargets = [];

    // Add property owner
    const owner = await ctx.db.get(property.ownerId);
    if (owner && owner.isActive) {
      escalationTargets.push(owner);
    }

    // Add property manager if different from owner
    if (property.managerId && property.managerId !== property.ownerId) {
      const manager = await ctx.db.get(property.managerId);
      if (manager && manager.isActive) {
        escalationTargets.push(manager);
      }
    }

    // Add other managers who have access to this property
    const managers = await ctx.db
      .query("users")
      .withIndex("by_role", (q) => q.eq("role", "manager"))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    for (const manager of managers) {
      if (manager.propertyAccess.includes(args.propertyId) && 
          !escalationTargets.find(t => t._id === manager._id)) {
        escalationTargets.push(manager);
      }
    }

    return escalationTargets;
  },
});

// Simple get user by ID for tenant portal (no auth required for portal access)
export const getById = query({
  args: { id: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});