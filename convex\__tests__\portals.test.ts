import { convexTest } from "convex-test";
import { describe, it, expect, beforeEach } from "vitest";
import { api } from "../_generated/api";
import schema from "../schema";

describe("Portal Management", () => {
  let t: any;

  beforeEach(async () => {
    t = convexTest(schema);
  });

  describe("createPortal", () => {
    it("should create a new portal with valid data", async () => {
      // Create a test property first
      const propertyId = await t.mutation(api.properties.createProperty, {
        name: "Test Property",
        type: "residential",
        address: {
          street: "123 Test St",
          city: "Test City",
          state: "Test State",
          country: "Test Country",
          postalCode: "12345",
        },
        ownerId: await t.run(async (ctx: any) => {
          return await ctx.db.insert("users", {
            email: "<EMAIL>",
            name: "Test Owner",
            role: "owner",
            propertyAccess: [],
            kycStatus: "verified",
            isActive: true,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          });
        }),
        branding: {
          primaryColor: "#3b82f6",
          secondaryColor: "#64748b",
        },
        settings: {
          currency: "USD",
          timezone: "UTC",
          language: "en",
          autoRentReminders: true,
          maintenanceSLA: 24,
        },
      });

      const portalId = await t.mutation(api.portals.createPortal, {
        propertyId,
        name: "Test Portal",
        subdomain: "test-portal",
        branding: {
          primaryColor: "#3b82f6",
          secondaryColor: "#64748b",
          accentColor: "#10b981",
          backgroundColor: "#ffffff",
          textColor: "#1f2937",
          fontFamily: "Inter, sans-serif",
        },
      });

      expect(portalId).toBeDefined();

      const portal = await t.query(api.portals.getPortalById, { portalId });
      expect(portal).toBeDefined();
      expect(portal.name).toBe("Test Portal");
      expect(portal.subdomain).toBe("test-portal");
      expect(portal.propertyId).toBe(propertyId);
      expect(portal.isActive).toBe(true);
      expect(portal.isPublished).toBe(false);
    });

    it("should reject duplicate subdomains", async () => {
      const propertyId = await t.run(async (ctx: any) => {
        const ownerId = await ctx.db.insert("users", {
          email: "<EMAIL>",
          name: "Test Owner",
          role: "owner",
          propertyAccess: [],
          kycStatus: "verified",
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert("properties", {
          name: "Test Property",
          type: "residential",
          address: {
            street: "123 Test St",
            city: "Test City",
            state: "Test State",
            country: "Test Country",
            postalCode: "12345",
          },
          ownerId,
          branding: {
            primaryColor: "#3b82f6",
            secondaryColor: "#64748b",
          },
          settings: {
            currency: "USD",
            timezone: "UTC",
            language: "en",
            autoRentReminders: true,
            maintenanceSLA: 24,
          },
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      // Create first portal
      await t.mutation(api.portals.createPortal, {
        propertyId,
        name: "First Portal",
        subdomain: "duplicate-test",
        branding: {
          primaryColor: "#3b82f6",
          secondaryColor: "#64748b",
          accentColor: "#10b981",
          backgroundColor: "#ffffff",
          textColor: "#1f2937",
          fontFamily: "Inter, sans-serif",
        },
      });

      // Try to create second portal with same subdomain
      await expect(
        t.mutation(api.portals.createPortal, {
          propertyId,
          name: "Second Portal",
          subdomain: "duplicate-test",
          branding: {
            primaryColor: "#3b82f6",
            secondaryColor: "#64748b",
            accentColor: "#10b981",
            backgroundColor: "#ffffff",
            textColor: "#1f2937",
            fontFamily: "Inter, sans-serif",
          },
        })
      ).rejects.toThrow("Subdomain is already taken");
    });

    it("should reject duplicate custom domains", async () => {
      const propertyId = await t.run(async (ctx: any) => {
        const ownerId = await ctx.db.insert("users", {
          email: "<EMAIL>",
          name: "Test Owner",
          role: "owner",
          propertyAccess: [],
          kycStatus: "verified",
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert("properties", {
          name: "Test Property",
          type: "residential",
          address: {
            street: "123 Test St",
            city: "Test City",
            state: "Test State",
            country: "Test Country",
            postalCode: "12345",
          },
          ownerId,
          branding: {
            primaryColor: "#3b82f6",
            secondaryColor: "#64748b",
          },
          settings: {
            currency: "USD",
            timezone: "UTC",
            language: "en",
            autoRentReminders: true,
            maintenanceSLA: 24,
          },
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      // Create first portal
      await t.mutation(api.portals.createPortal, {
        propertyId,
        name: "First Portal",
        subdomain: "first-portal",
        customDomain: "portal.example.com",
        branding: {
          primaryColor: "#3b82f6",
          secondaryColor: "#64748b",
          accentColor: "#10b981",
          backgroundColor: "#ffffff",
          textColor: "#1f2937",
          fontFamily: "Inter, sans-serif",
        },
      });

      // Try to create second portal with same custom domain
      await expect(
        t.mutation(api.portals.createPortal, {
          propertyId,
          name: "Second Portal",
          subdomain: "second-portal",
          customDomain: "portal.example.com",
          branding: {
            primaryColor: "#3b82f6",
            secondaryColor: "#64748b",
            accentColor: "#10b981",
            backgroundColor: "#ffffff",
            textColor: "#1f2937",
            fontFamily: "Inter, sans-serif",
          },
        })
      ).rejects.toThrow("Custom domain is already taken");
    });
  });

  describe("updatePortalBranding", () => {
    it("should update portal branding", async () => {
      const { portalId } = await createTestPortal(t);

      await t.mutation(api.portals.updatePortalBranding, {
        portalId,
        branding: {
          primaryColor: "#ff0000",
          secondaryColor: "#00ff00",
          accentColor: "#0000ff",
          backgroundColor: "#ffffff",
          textColor: "#000000",
          fontFamily: "Roboto, sans-serif",
          customCSS: ".custom { color: red; }",
        },
      });

      const portal = await t.query(api.portals.getPortalById, { portalId });
      expect(portal.branding.primaryColor).toBe("#ff0000");
      expect(portal.branding.customCSS).toBe(".custom { color: red; }");
    });
  });

  describe("updatePortalTheme", () => {
    it("should update portal theme", async () => {
      const { portalId } = await createTestPortal(t);

      await t.mutation(api.portals.updatePortalTheme, {
        portalId,
        theme: {
          layout: "minimal",
          headerStyle: "transparent",
          sidebarStyle: "collapsed",
          cardStyle: "flat",
          borderRadius: "large",
        },
      });

      const portal = await t.query(api.portals.getPortalById, { portalId });
      expect(portal.theme.layout).toBe("minimal");
      expect(portal.theme.headerStyle).toBe("transparent");
    });
  });

  describe("updateCustomDomain", () => {
    it("should update custom domain", async () => {
      const { portalId } = await createTestPortal(t);

      await t.mutation(api.portals.updateCustomDomain, {
        portalId,
        customDomain: "portal.newdomain.com",
      });

      const portal = await t.query(api.portals.getPortalById, { portalId });
      expect(portal.customDomain).toBe("portal.newdomain.com");
    });

    it("should reject duplicate custom domains", async () => {
      const { portalId: firstPortalId } = await createTestPortal(t);
      const { portalId: secondPortalId } = await createTestPortal(t, "second-portal");

      // Set custom domain for first portal
      await t.mutation(api.portals.updateCustomDomain, {
        portalId: firstPortalId,
        customDomain: "portal.example.com",
      });

      // Try to set same custom domain for second portal
      await expect(
        t.mutation(api.portals.updateCustomDomain, {
          portalId: secondPortalId,
          customDomain: "portal.example.com",
        })
      ).rejects.toThrow("Custom domain is already taken");
    });
  });

  describe("getPortalByProperty", () => {
    it("should retrieve portal by property ID", async () => {
      const { propertyId, portalId } = await createTestPortal(t);

      const portal = await t.query(api.portals.getPortalByProperty, { propertyId });
      expect(portal).toBeDefined();
      expect(portal._id).toBe(portalId);
    });

    it("should return null for property without portal", async () => {
      const propertyId = await t.run(async (ctx: any) => {
        const ownerId = await ctx.db.insert("users", {
          email: "<EMAIL>",
          name: "Test Owner",
          role: "owner",
          propertyAccess: [],
          kycStatus: "verified",
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });

        return await ctx.db.insert("properties", {
          name: "Test Property",
          type: "residential",
          address: {
            street: "123 Test St",
            city: "Test City",
            state: "Test State",
            country: "Test Country",
            postalCode: "12345",
          },
          ownerId,
          branding: {
            primaryColor: "#3b82f6",
            secondaryColor: "#64748b",
          },
          settings: {
            currency: "USD",
            timezone: "UTC",
            language: "en",
            autoRentReminders: true,
            maintenanceSLA: 24,
          },
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      });

      const portal = await t.query(api.portals.getPortalByProperty, { propertyId });
      expect(portal).toBeNull();
    });
  });

  describe("generatePortalCSS", () => {
    it("should generate CSS from portal branding", async () => {
      const { portalId } = await createTestPortal(t);

      const css = await t.query(api.portals.generatePortalCSS, { portalId });
      expect(css).toContain("--primary-color: #3b82f6");
      expect(css).toContain("--secondary-color: #64748b");
      expect(css).toContain("font-family: var(--font-family)");
    });
  });

  describe("checkSubdomainAvailability", () => {
    it("should return true for available subdomain", async () => {
      const available = await t.query(api.portals.checkSubdomainAvailability, {
        subdomain: "available-subdomain",
      });
      expect(available).toBe(true);
    });

    it("should return false for taken subdomain", async () => {
      await createTestPortal(t, "taken-subdomain");

      const available = await t.query(api.portals.checkSubdomainAvailability, {
        subdomain: "taken-subdomain",
      });
      expect(available).toBe(false);
    });
  });

  describe("checkCustomDomainAvailability", () => {
    it("should return true for available custom domain", async () => {
      const available = await t.query(api.portals.checkCustomDomainAvailability, {
        customDomain: "available.example.com",
      });
      expect(available).toBe(true);
    });

    it("should return false for taken custom domain", async () => {
      const { portalId } = await createTestPortal(t);
      
      await t.mutation(api.portals.updateCustomDomain, {
        portalId,
        customDomain: "taken.example.com",
      });

      const available = await t.query(api.portals.checkCustomDomainAvailability, {
        customDomain: "taken.example.com",
      });
      expect(available).toBe(false);
    });
  });
});

// Helper function to create a test portal
async function createTestPortal(t: any, subdomain = "test-portal") {
  const ownerId = await t.run(async (ctx: any) => {
    return await ctx.db.insert("users", {
      email: "<EMAIL>",
      name: "Test Owner",
      role: "owner",
      propertyAccess: [],
      kycStatus: "verified",
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  });

  const propertyId = await t.run(async (ctx: any) => {
    return await ctx.db.insert("properties", {
      name: "Test Property",
      type: "residential",
      address: {
        street: "123 Test St",
        city: "Test City",
        state: "Test State",
        country: "Test Country",
        postalCode: "12345",
      },
      ownerId,
      branding: {
        primaryColor: "#3b82f6",
        secondaryColor: "#64748b",
      },
      settings: {
        currency: "USD",
        timezone: "UTC",
        language: "en",
        autoRentReminders: true,
        maintenanceSLA: 24,
      },
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  });

  const portalId = await t.mutation(api.portals.createPortal, {
    propertyId,
    name: "Test Portal",
    subdomain,
    branding: {
      primaryColor: "#3b82f6",
      secondaryColor: "#64748b",
      accentColor: "#10b981",
      backgroundColor: "#ffffff",
      textColor: "#1f2937",
      fontFamily: "Inter, sans-serif",
    },
  });

  return { ownerId, propertyId, portalId };
}