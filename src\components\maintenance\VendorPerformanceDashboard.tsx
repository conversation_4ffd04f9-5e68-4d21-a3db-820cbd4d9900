import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
// import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  // LineChart,
  // Line,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import { 
  Star, 
  // Clock, 
  CheckCircle, 
  AlertTriangle, 
  // TrendingUp,
  // TrendingDown,
  Award,
  Users,
  // DollarSign
} from 'lucide-react';
import { subDays, subMonths } from 'date-fns';

interface VendorPerformanceDashboardProps {
  propertyId: Id<"properties">;
}

export const VendorPerformanceDashboard: React.FC<VendorPerformanceDashboardProps> = ({ propertyId }) => {
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedVendor, setSelectedVendor] = useState<string>('all');

  // Calculate date range
  const getDateRange = () => {
    const now = new Date();
    switch (timeRange) {
      case '7d':
        return { startDate: subDays(now, 7).getTime(), endDate: now.getTime() };
      case '30d':
        return { startDate: subDays(now, 30).getTime(), endDate: now.getTime() };
      case '90d':
        return { startDate: subDays(now, 90).getTime(), endDate: now.getTime() };
      case '6m':
        return { startDate: subMonths(now, 6).getTime(), endDate: now.getTime() };
      default:
        return { startDate: subDays(now, 30).getTime(), endDate: now.getTime() };
    }
  };

  const dateRange = getDateRange();

  // Fetch vendor performance data
  const vendorPerformance = useQuery(api.maintenance.getVendorPerformanceAnalytics, {
    propertyId,
    startDate: dateRange.startDate,
    endDate: dateRange.endDate
  });

  // Fetch all vendors for the property
  const allVendors = useQuery(api.vendors.getAllVendors, {
    isActive: true,
    isVerified: true
  });

  const timeRanges = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '6m', label: 'Last 6 months' }
  ];

  // const getPerformanceColor = (score: number) => {
  //   if (score >= 90) return 'text-green-600';
  //   if (score >= 75) return 'text-yellow-600';
  //   return 'text-red-600';
  // };

  const getPerformanceBadge = (score: number) => {
    if (score >= 90) return { label: 'Excellent', color: 'bg-green-500' };
    if (score >= 75) return { label: 'Good', color: 'bg-yellow-500' };
    return { label: 'Needs Improvement', color: 'bg-red-500' };
  };

  const calculateOverallScore = (vendor: any) => {
    return (vendor.averageRating * 20) * 0.3 + // Rating out of 5, converted to 100
           vendor.slaCompliance * 0.4 + // SLA compliance percentage
           Math.max(0, 100 - vendor.averageResponseTime * 2) * 0.3; // Response time factor
  };

  if (!vendorPerformance || !allVendors) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Prepare radar chart data for vendor comparison
  const radarData = vendorPerformance.topVendors.slice(0, 5).map((vendor: any) => ({
    vendor: vendor.companyName.substring(0, 10),
    rating: vendor.averageRating * 20, // Convert to 100 scale
    sla: vendor.slaCompliance,
    response: Math.max(0, 100 - vendor.averageResponseTime * 2),
    jobs: Math.min(100, vendor.completedJobs * 2) // Scale job count
  }));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Vendor Performance Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor and analyze vendor performance metrics
          </p>
        </div>
        
        <div className="flex gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {timeRanges.map((range) => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedVendor} onValueChange={setSelectedVendor}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="All Vendors" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Vendors</SelectItem>
              {vendorPerformance.topVendors.map((vendor: any) => (
                <SelectItem key={vendor.vendorId} value={vendor.vendorId}>
                  {vendor.companyName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Performance Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Vendors</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{vendorPerformance.topVendors.length}</div>
            <p className="text-xs text-muted-foreground">
              Currently working on tickets
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {vendorPerformance.topVendors.length > 0 
                ? (vendorPerformance.topVendors.reduce((sum: number, v: any) => sum + v.averageRating, 0) / vendorPerformance.topVendors.length).toFixed(1)
                : '0.0'
              }
            </div>
            <p className="text-xs text-muted-foreground">
              Out of 5.0 stars
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg SLA Compliance</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {vendorPerformance.topVendors.length > 0 
                ? (vendorPerformance.topVendors.reduce((sum: number, v: any) => sum + v.slaCompliance, 0) / vendorPerformance.topVendors.length).toFixed(1)
                : '0.0'
              }%
            </div>
            <p className="text-xs text-muted-foreground">
              Average across all vendors
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Jobs Completed</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {vendorPerformance.topVendors.reduce((sum: number, v: any) => sum + v.completedJobs, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all vendors
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Tabs */}
      <Tabs defaultValue="rankings" className="space-y-4">
        <TabsList>
          <TabsTrigger value="rankings">Rankings</TabsTrigger>
          <TabsTrigger value="comparison">Comparison</TabsTrigger>
          <TabsTrigger value="costs">Cost Analysis</TabsTrigger>
          <TabsTrigger value="utilization">Utilization</TabsTrigger>
        </TabsList>

        <TabsContent value="rankings" className="space-y-4">
          {/* Top Performers */}
          <Card>
            <CardHeader>
              <CardTitle>Vendor Performance Rankings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {vendorPerformance.topVendors.map((vendor: any, index: number) => {
                  const overallScore = calculateOverallScore(vendor);
                  const performanceBadge = getPerformanceBadge(overallScore);
                  
                  return (
                    <div key={vendor.vendorId} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-white ${
                          index === 0 ? 'bg-yellow-500' : 
                          index === 1 ? 'bg-gray-400' : 
                          index === 2 ? 'bg-orange-600' : 'bg-blue-500'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="font-semibold">{vendor.companyName}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={`${performanceBadge.color} text-white text-xs`}>
                              {performanceBadge.label}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              Score: {overallScore.toFixed(1)}/100
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div className="text-center">
                            <div className="font-semibold">{vendor.averageRating.toFixed(1)}</div>
                            <div className="text-muted-foreground">Rating</div>
                          </div>
                          <div className="text-center">
                            <div className="font-semibold">{vendor.slaCompliance.toFixed(1)}%</div>
                            <div className="text-muted-foreground">SLA</div>
                          </div>
                          <div className="text-center">
                            <div className="font-semibold">{vendor.averageResponseTime.toFixed(1)}h</div>
                            <div className="text-muted-foreground">Response</div>
                          </div>
                        </div>
                        <div className="mt-2">
                          <Progress value={overallScore} className="w-32" />
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Performance Metrics Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={vendorPerformance.topVendors.slice(0, 8)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="companyName" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="averageRating" fill="#8884d8" name="Rating (x20)" />
                  <Bar dataKey="slaCompliance" fill="#82ca9d" name="SLA Compliance %" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="comparison" className="space-y-4">
          {/* Radar Chart Comparison */}
          <Card>
            <CardHeader>
              <CardTitle>Multi-Dimensional Performance Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <RadarChart data={radarData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="vendor" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  <Radar name="Rating" dataKey="rating" stroke="#8884d8" fill="#8884d8" fillOpacity={0.1} />
                  <Radar name="SLA" dataKey="sla" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.1} />
                  <Radar name="Response" dataKey="response" stroke="#ffc658" fill="#ffc658" fillOpacity={0.1} />
                  <Tooltip />
                </RadarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Response Time Comparison */}
          <Card>
            <CardHeader>
              <CardTitle>Response Time Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={vendorPerformance.topVendors}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="companyName" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value} hours`, 'Response Time']} />
                  <Bar dataKey="averageResponseTime" fill="#ff7c7c" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="costs" className="space-y-4">
          {/* Cost by Vendor */}
          <Card>
            <CardHeader>
              <CardTitle>Cost Analysis by Vendor</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={vendorPerformance.costByVendor}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="companyName" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value}`, 'Total Cost']} />
                  <Bar dataKey="totalCost" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Cost Efficiency Metrics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Cost Efficiency Rankings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {vendorPerformance.costByVendor
                    .map((vendor: any) => {
                      const performanceVendor = vendorPerformance.topVendors.find((v: any) => v.vendorId === vendor.vendorId);
                      const costPerJob = performanceVendor ? vendor.totalCost / performanceVendor.completedJobs : 0;
                      return { ...vendor, costPerJob, completedJobs: performanceVendor?.completedJobs || 0 };
                    })
                    .sort((a: any, b: any) => a.costPerJob - b.costPerJob)
                    .slice(0, 5)
                    .map((vendor: any, index: number) => (
                      <div key={vendor.vendorId} className="flex items-center justify-between p-3 border rounded">
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-600 text-sm font-bold">
                            {index + 1}
                          </div>
                          <div>
                            <div className="font-medium">{vendor.companyName}</div>
                            <div className="text-sm text-muted-foreground">
                              {vendor.completedJobs} jobs completed
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold">${vendor.costPerJob.toFixed(2)}</div>
                          <div className="text-sm text-muted-foreground">per job</div>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Total Spending Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {vendorPerformance.costByVendor.map((vendor: any) => {
                    const totalSpending = vendorPerformance.costByVendor.reduce((sum: number, v: any) => sum + v.totalCost, 0);
                    const percentage = totalSpending > 0 ? (vendor.totalCost / totalSpending) * 100 : 0;
                    
                    return (
                      <div key={vendor.vendorId} className="space-y-2">
                        <div className="flex justify-between">
                          <span className="font-medium">{vendor.companyName}</span>
                          <span>${vendor.totalCost.toFixed(2)} ({percentage.toFixed(1)}%)</span>
                        </div>
                        <Progress value={percentage} />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="utilization" className="space-y-4">
          {/* Vendor Utilization */}
          <Card>
            <CardHeader>
              <CardTitle>Vendor Utilization Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={vendorPerformance.utilizationByVendor}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="jobCount" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Workload Balance */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Workload Balance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {vendorPerformance.utilizationByVendor
                    .sort((a: any, b: any) => b.jobCount - a.jobCount)
                    .map((vendor: any, _index: number) => {
                      const maxJobs = Math.max(...vendorPerformance.utilizationByVendor.map((v: any) => v.jobCount));
                      const utilizationPercentage = maxJobs > 0 ? (vendor.jobCount / maxJobs) * 100 : 0;
                      
                      return (
                        <div key={vendor.vendorId} className="space-y-2">
                          <div className="flex justify-between">
                            <span className="font-medium">{vendor.name}</span>
                            <span>{vendor.jobCount} jobs</span>
                          </div>
                          <Progress value={utilizationPercentage} />
                        </div>
                      );
                    })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Capacity Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {vendorPerformance.topVendors
                    .filter((vendor: any) => vendor.slaCompliance < 80 || vendor.averageResponseTime > 8)
                    .slice(0, 3)
                    .map((vendor: any) => (
                      <div key={vendor.vendorId} className="p-3 border border-orange-200 rounded-lg bg-orange-50">
                        <div className="flex items-start gap-2">
                          <AlertTriangle className="h-4 w-4 text-orange-600 mt-0.5" />
                          <div>
                            <h4 className="font-medium text-orange-800">{vendor.companyName}</h4>
                            <p className="text-sm text-orange-700">
                              {vendor.slaCompliance < 80 && "Low SLA compliance. "}
                              {vendor.averageResponseTime > 8 && "Slow response time. "}
                              Consider redistributing workload or finding additional vendors.
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  
                  {vendorPerformance.topVendors.every((v: any) => v.slaCompliance >= 80 && v.averageResponseTime <= 8) && (
                    <div className="p-3 border border-green-200 rounded-lg bg-green-50">
                      <div className="flex items-start gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-green-800">All vendors performing well</h4>
                          <p className="text-sm text-green-700">
                            Current vendor capacity and performance levels are optimal.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};