import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '../ui/tabs';
import { Badge } from '../ui/badge';
import { CommunicationCenter } from './CommunicationCenter';
import { MessageTemplates } from './MessageTemplates';
import { NotificationPreferences } from './NotificationPreferences';
import { 
  MessageSquare, 
  Phone, 
  Mail, 
  Users, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  Settings,
  FileText,
  Send
} from 'lucide-react';

interface CommunicationDashboardProps {
  propertyId?: Id<"properties">;
  userId: Id<"users">;
}

export function CommunicationDashboard({ propertyId, userId }: CommunicationDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview');

  // Queries for dashboard stats
  const recentMessages = useQuery(api.communications.getMessages, {
    propertyId,
    limit: 10,
  });

  const templates = useQuery(api.communications.getMessageTemplates, {
    propertyId,
    isActive: true,
  });

  // Calculate stats
  const messageStats = React.useMemo(() => {
    if (!recentMessages) return null;

    const total = recentMessages.length;
    const sent = recentMessages.filter(m => m.status === 'sent' || m.status === 'delivered').length;
    const failed = recentMessages.filter(m => m.status === 'failed').length;
    const pending = recentMessages.filter(m => m.status === 'pending').length;

    const smsCount = recentMessages.filter(m => m.type === 'sms').length;
    const whatsappCount = recentMessages.filter(m => m.type === 'whatsapp').length;
    const emailCount = recentMessages.filter(m => m.type === 'email').length;

    return {
      total,
      sent,
      failed,
      pending,
      successRate: total > 0 ? Math.round((sent / total) * 100) : 0,
      smsCount,
      whatsappCount,
      emailCount,
    };
  }, [recentMessages]);

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    description, 
    color = "text-gray-600" 
  }: {
    title: string;
    value: string | number;
    icon: React.ComponentType<{ className?: string }>;
    description?: string;
    color?: string;
  }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className={`text-2xl font-bold ${color}`}>{value}</p>
            {description && (
              <p className="text-xs text-gray-500 mt-1">{description}</p>
            )}
          </div>
          <Icon className={`h-8 w-8 ${color}`} />
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <MessageSquare className="h-6 w-6" />
        <h1 className="text-3xl font-bold">Communication Dashboard</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="send">Send Messages</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <StatCard
              title="Total Messages"
              value={messageStats?.total || 0}
              icon={MessageSquare}
              description="Last 50 messages"
            />
            <StatCard
              title="Success Rate"
              value={`${messageStats?.successRate || 0}%`}
              icon={TrendingUp}
              description="Delivered successfully"
              color="text-green-600"
            />
            <StatCard
              title="Active Templates"
              value={templates?.length || 0}
              icon={FileText}
              description="Ready to use"
            />
            <StatCard
              title="Pending"
              value={messageStats?.pending || 0}
              icon={Clock}
              description="Being processed"
              color="text-yellow-600"
            />
          </div>

          {/* Message Type Breakdown */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Message Types</CardTitle>
                <CardDescription>Distribution of recent messages by type</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4" />
                    <span>SMS</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ 
                          width: `${messageStats?.total ? (messageStats.smsCount / messageStats.total) * 100 : 0}%` 
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{messageStats?.smsCount || 0}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <MessageSquare className="h-4 w-4" />
                    <span>WhatsApp</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ 
                          width: `${messageStats?.total ? (messageStats.whatsappCount / messageStats.total) * 100 : 0}%` 
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{messageStats?.whatsappCount || 0}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4" />
                    <span>Email</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-purple-600 h-2 rounded-full" 
                        style={{ 
                          width: `${messageStats?.total ? (messageStats.emailCount / messageStats.total) * 100 : 0}%` 
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{messageStats?.emailCount || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Message Status</CardTitle>
                <CardDescription>Current status of recent messages</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Delivered</span>
                  </div>
                  <Badge className="bg-green-100 text-green-800">
                    {messageStats?.sent || 0}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    <span>Pending</span>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-800">
                    {messageStats?.pending || 0}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span>Failed</span>
                  </div>
                  <Badge className="bg-red-100 text-red-800">
                    {messageStats?.failed || 0}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Messages */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Messages</CardTitle>
              <CardDescription>Latest communication activity</CardDescription>
            </CardHeader>
            <CardContent>
              {recentMessages === undefined ? (
                <div className="text-center py-4">Loading messages...</div>
              ) : recentMessages.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No messages sent yet
                </div>
              ) : (
                <div className="space-y-3">
                  {recentMessages.slice(0, 5).map((message) => (
                    <div key={message._id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          {message.type === 'sms' && <Phone className="h-4 w-4" />}
                          {message.type === 'whatsapp' && <MessageSquare className="h-4 w-4" />}
                          {message.type === 'email' && <Mail className="h-4 w-4" />}
                          <span className="text-sm font-medium">{message.recipient}</span>
                        </div>
                        <div className="text-sm text-gray-600 max-w-md truncate">
                          {message.content}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge 
                          className={
                            message.status === 'sent' || message.status === 'delivered' 
                              ? 'bg-green-100 text-green-800'
                              : message.status === 'failed'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }
                        >
                          {message.status}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {new Date(message.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="send">
          <CommunicationCenter propertyId={propertyId} />
        </TabsContent>

        <TabsContent value="templates">
          <MessageTemplates propertyId={propertyId} userId={userId} />
        </TabsContent>

        <TabsContent value="preferences">
          <NotificationPreferences userId={userId} propertyId={propertyId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}