import { convexTest } from "convex-test";
import { expect, test, describe, beforeEach } from "vitest";
import { api } from "../_generated/api";
import schema from "../schema";
import { Id } from "../_generated/dataModel";

describe("Communications", () => {
  let t: any;
  let userId: Id<"users">;
  let propertyId: Id<"properties">;

  beforeEach(async () => {
    t = convexTest(schema);
    
    // Create test user
    userId = await t.mutation(api.users.create, {
      email: "<EMAIL>",
      name: "Test User",
      role: "manager",
      propertyAccess: [],
      kycStatus: "verified",
      isActive: true,
    });

    // Create test property
    propertyId = await t.mutation(api.properties.create, {
      name: "Test Property",
      type: "residential",
      address: {
        street: "123 Test St",
        city: "Test City",
        state: "Test State",
        country: "Kenya",
        postalCode: "12345",
      },
      ownerId: userId,
      branding: {
        primaryColor: "#000000",
        secondaryColor: "#ffffff",
      },
      settings: {
        currency: "KES",
        timezone: "Africa/Nairobi",
        language: "en",
        autoRentReminders: true,
        maintenanceSLA: 24,
      },
      isActive: true,
    });
  });

  describe("Message Templates", () => {
    test("should create message template", async () => {
      const templateId = await t.mutation(api.communications.createMessageTemplate, {
        name: "Payment Reminder",
        type: "sms",
        category: "payment_reminder",
        content: "Dear {{tenant_name}}, your rent of {{amount}} is due on {{due_date}}.",
        variables: ["tenant_name", "amount", "due_date"],
        isActive: true,
        propertyId,
        createdBy: userId,
      });

      expect(templateId).toBeDefined();

      const template = await t.query(api.communications.getMessageTemplates, {
        propertyId,
      });

      expect(template).toHaveLength(1);
      expect(template[0].name).toBe("Payment Reminder");
      expect(template[0].type).toBe("sms");
      expect(template[0].category).toBe("payment_reminder");
      expect(template[0].variables).toEqual(["tenant_name", "amount", "due_date"]);
    });

    test("should update message template", async () => {
      const templateId = await t.mutation(api.communications.createMessageTemplate, {
        name: "Original Template",
        type: "sms",
        category: "general",
        content: "Original content",
        variables: [],
        isActive: true,
        propertyId,
        createdBy: userId,
      });

      await t.mutation(api.communications.updateMessageTemplate, {
        templateId,
        name: "Updated Template",
        content: "Updated content with {{variable}}",
        variables: ["variable"],
      });

      const templates = await t.query(api.communications.getMessageTemplates, {
        propertyId,
      });

      expect(templates[0].name).toBe("Updated Template");
      expect(templates[0].content).toBe("Updated content with {{variable}}");
      expect(templates[0].variables).toEqual(["variable"]);
    });

    test("should delete message template", async () => {
      const templateId = await t.mutation(api.communications.createMessageTemplate, {
        name: "Template to Delete",
        type: "sms",
        category: "general",
        content: "Content",
        variables: [],
        isActive: true,
        propertyId,
        createdBy: userId,
      });

      await t.mutation(api.communications.deleteMessageTemplate, {
        templateId,
      });

      const templates = await t.query(api.communications.getMessageTemplates, {
        propertyId,
      });

      expect(templates).toHaveLength(0);
    });

    test("should filter templates by type and category", async () => {
      await t.mutation(api.communications.createMessageTemplate, {
        name: "SMS Payment",
        type: "sms",
        category: "payment_reminder",
        content: "SMS payment reminder",
        variables: [],
        isActive: true,
        propertyId,
        createdBy: userId,
      });

      await t.mutation(api.communications.createMessageTemplate, {
        name: "WhatsApp Maintenance",
        type: "whatsapp",
        category: "maintenance_update",
        content: "WhatsApp maintenance update",
        variables: [],
        isActive: true,
        propertyId,
        createdBy: userId,
      });

      const smsTemplates = await t.query(api.communications.getMessageTemplates, {
        propertyId,
        type: "sms",
      });

      const paymentTemplates = await t.query(api.communications.getMessageTemplates, {
        propertyId,
        category: "payment_reminder",
      });

      expect(smsTemplates).toHaveLength(1);
      expect(smsTemplates[0].name).toBe("SMS Payment");

      expect(paymentTemplates).toHaveLength(1);
      expect(paymentTemplates[0].category).toBe("payment_reminder");
    });
  });

  describe("Notification Preferences", () => {
    test("should save and retrieve notification preferences", async () => {
      const preferences = {
        sms: {
          enabled: true,
          paymentReminders: true,
          maintenanceUpdates: false,
          leaseNotifications: true,
          emergencyAlerts: true,
          generalAnnouncements: false,
        },
        whatsapp: {
          enabled: false,
          paymentReminders: false,
          maintenanceUpdates: false,
          leaseNotifications: false,
          emergencyAlerts: true,
          generalAnnouncements: false,
        },
        email: {
          enabled: true,
          paymentReminders: true,
          maintenanceUpdates: true,
          leaseNotifications: true,
          emergencyAlerts: true,
          generalAnnouncements: true,
          weeklyReports: true,
          monthlyStatements: true,
        },
        inApp: {
          enabled: true,
          paymentReminders: true,
          maintenanceUpdates: true,
          leaseNotifications: true,
          emergencyAlerts: true,
          generalAnnouncements: true,
        },
      };

      const quietHours = {
        enabled: true,
        startTime: "22:00",
        endTime: "08:00",
        timezone: "Africa/Nairobi",
      };

      await t.mutation(api.communications.saveNotificationPreferences, {
        userId,
        propertyId,
        preferences,
        quietHours,
        language: "en",
      });

      const savedPreferences = await t.query(api.communications.getNotificationPreferences, {
        userId,
        propertyId,
      });

      expect(savedPreferences.preferences.sms.enabled).toBe(true);
      expect(savedPreferences.preferences.sms.maintenanceUpdates).toBe(false);
      expect(savedPreferences.quietHours.enabled).toBe(true);
      expect(savedPreferences.quietHours.startTime).toBe("22:00");
      expect(savedPreferences.language).toBe("en");
    });

    test("should return default preferences when none exist", async () => {
      const preferences = await t.query(api.communications.getNotificationPreferences, {
        userId,
        propertyId,
      });

      expect(preferences.preferences.sms.enabled).toBe(true);
      expect(preferences.preferences.whatsapp.enabled).toBe(false);
      expect(preferences.preferences.email.enabled).toBe(true);
      expect(preferences.quietHours.enabled).toBe(false);
      expect(preferences.language).toBe("en");
    });

    test("should update existing preferences", async () => {
      // Create initial preferences
      await t.mutation(api.communications.saveNotificationPreferences, {
        userId,
        propertyId,
        preferences: {
          sms: {
            enabled: true,
            paymentReminders: true,
            maintenanceUpdates: true,
            leaseNotifications: true,
            emergencyAlerts: true,
            generalAnnouncements: true,
          },
          whatsapp: {
            enabled: false,
            paymentReminders: false,
            maintenanceUpdates: false,
            leaseNotifications: false,
            emergencyAlerts: false,
            generalAnnouncements: false,
          },
          email: {
            enabled: true,
            paymentReminders: true,
            maintenanceUpdates: true,
            leaseNotifications: true,
            emergencyAlerts: true,
            generalAnnouncements: true,
            weeklyReports: true,
            monthlyStatements: true,
          },
          inApp: {
            enabled: true,
            paymentReminders: true,
            maintenanceUpdates: true,
            leaseNotifications: true,
            emergencyAlerts: true,
            generalAnnouncements: true,
          },
        },
        quietHours: {
          enabled: false,
          startTime: "22:00",
          endTime: "08:00",
          timezone: "Africa/Nairobi",
        },
        language: "en",
      });

      // Update preferences
      await t.mutation(api.communications.saveNotificationPreferences, {
        userId,
        propertyId,
        preferences: {
          sms: {
            enabled: false, // Changed
            paymentReminders: true,
            maintenanceUpdates: true,
            leaseNotifications: true,
            emergencyAlerts: true,
            generalAnnouncements: true,
          },
          whatsapp: {
            enabled: true, // Changed
            paymentReminders: true,
            maintenanceUpdates: true,
            leaseNotifications: true,
            emergencyAlerts: true,
            generalAnnouncements: true,
          },
          email: {
            enabled: true,
            paymentReminders: true,
            maintenanceUpdates: true,
            leaseNotifications: true,
            emergencyAlerts: true,
            generalAnnouncements: true,
            weeklyReports: true,
            monthlyStatements: true,
          },
          inApp: {
            enabled: true,
            paymentReminders: true,
            maintenanceUpdates: true,
            leaseNotifications: true,
            emergencyAlerts: true,
            generalAnnouncements: true,
          },
        },
        quietHours: {
          enabled: true, // Changed
          startTime: "23:00", // Changed
          endTime: "07:00", // Changed
          timezone: "Africa/Nairobi",
        },
        language: "sw", // Changed
      });

      const updatedPreferences = await t.query(api.communications.getNotificationPreferences, {
        userId,
        propertyId,
      });

      expect(updatedPreferences.preferences.sms.enabled).toBe(false);
      expect(updatedPreferences.preferences.whatsapp.enabled).toBe(true);
      expect(updatedPreferences.quietHours.enabled).toBe(true);
      expect(updatedPreferences.quietHours.startTime).toBe("23:00");
      expect(updatedPreferences.quietHours.endTime).toBe("07:00");
      expect(updatedPreferences.language).toBe("sw");
    });
  });

  describe("Message Tracking", () => {
    test("should create and track message", async () => {
      const messageId = await t.mutation(api.communications.internal.communications.createMessage, {
        type: "sms",
        recipient: "+254712345678",
        content: "Test message",
        status: "pending",
        propertyId,
        userId,
      });

      expect(messageId).toBeDefined();

      const message = await t.query(api.communications.getMessageStatus, {
        messageId,
      });

      expect(message?.type).toBe("sms");
      expect(message?.recipient).toBe("+254712345678");
      expect(message?.content).toBe("Test message");
      expect(message?.status).toBe("pending");
    });

    test("should update message status", async () => {
      const messageId = await t.mutation(api.communications.internal.communications.createMessage, {
        type: "sms",
        recipient: "+254712345678",
        content: "Test message",
        status: "pending",
        propertyId,
        userId,
      });

      await t.mutation(api.communications.internal.communications.updateMessageStatus, {
        messageId,
        status: "sent",
        externalId: "SM123456789",
        sentAt: Date.now(),
      });

      const message = await t.query(api.communications.getMessageStatus, {
        messageId,
      });

      expect(message?.status).toBe("sent");
      expect(message?.externalId).toBe("SM123456789");
      expect(message?.sentAt).toBeDefined();
    });

    test("should retrieve messages with filters", async () => {
      // Create test messages
      await t.mutation(api.communications.internal.communications.createMessage, {
        type: "sms",
        recipient: "+254712345678",
        content: "SMS message",
        status: "sent",
        propertyId,
        userId,
      });

      await t.mutation(api.communications.internal.communications.createMessage, {
        type: "whatsapp",
        recipient: "+254712345678",
        content: "WhatsApp message",
        status: "delivered",
        propertyId,
        userId,
      });

      await t.mutation(api.communications.internal.communications.createMessage, {
        type: "sms",
        recipient: "+254723456789",
        content: "Another SMS",
        status: "failed",
        propertyId,
        userId,
      });

      // Test filtering by type
      const smsMessages = await t.query(api.communications.getMessages, {
        propertyId,
        type: "sms",
      });

      expect(smsMessages).toHaveLength(2);
      expect(smsMessages.every(m => m.type === "sms")).toBe(true);

      // Test filtering by property
      const propertyMessages = await t.query(api.communications.getMessages, {
        propertyId,
      });

      expect(propertyMessages).toHaveLength(3);

      // Test filtering by user
      const userMessages = await t.query(api.communications.getMessages, {
        userId,
      });

      expect(userMessages).toHaveLength(3);
    });
  });
});