import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Separator } from '../ui/separator';
import { ArrowLeft, Clock, User, MapPin, MessageSquare, AlertTriangle, Edit } from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { useToast } from '../ui/use-toast';

interface TicketDetailsProps {
  ticketId: Id<"maintenanceTickets">;
  onBack: () => void;
}

export const TicketDetails: React.FC<TicketDetailsProps> = ({ ticketId, onBack }) => {
  const { toast } = useToast();
  const [newNote, setNewNote] = useState('');
  const [newStatus, setNewStatus] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [showStatusUpdate, setShowStatusUpdate] = useState(false);

  // Fetch ticket details
  const ticket = useQuery(api.maintenance.getTicketById, { ticketId });
  
  // Mutations
  const updateTicketStatus = useMutation(api.maintenance.updateTicketStatus);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'emergency': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-500';
      case 'assigned': return 'bg-purple-500';
      case 'in_progress': return 'bg-orange-500';
      case 'completed': return 'bg-green-500';
      case 'closed': return 'bg-gray-500';
      case 'escalated': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const formatDate = (timestamp: number) => {
    return format(new Date(timestamp), 'PPP p');
  };

  const formatRelativeDate = (timestamp: number) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  const isOverdue = (ticket: any) => {
    return Date.now() > ticket.slaDeadline && !['completed', 'closed'].includes(ticket.status);
  };

  const getTimeUntilSLA = (slaDeadline: number) => {
    const now = Date.now();
    const timeLeft = slaDeadline - now;
    
    if (timeLeft <= 0) {
      return 'Overdue';
    }
    
    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h left`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m left`;
    } else {
      return `${minutes}m left`;
    }
  };

  const handleStatusUpdate = async () => {
    if (!newStatus) return;

    setIsUpdating(true);
    try {
      await updateTicketStatus({
        ticketId,
        status: newStatus as any,
        notes: newNote || undefined
      });

      toast({
        title: "Success",
        description: "Ticket status updated successfully",
      });

      setNewNote('');
      setNewStatus('');
      setShowStatusUpdate(false);
    } catch (error) {
      console.error('Error updating ticket:', error);
      toast({
        title: "Error",
        description: "Failed to update ticket status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusOptions = (currentStatus: string) => {
    const statusFlow = {
      'open': ['assigned', 'in_progress', 'closed'],
      'assigned': ['in_progress', 'open', 'escalated'],
      'in_progress': ['completed', 'escalated'],
      'completed': ['closed'],
      'escalated': ['assigned', 'in_progress'],
      'closed': []
    };

    return statusFlow[currentStatus as keyof typeof statusFlow] || [];
  };

  if (!ticket) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading ticket details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Tickets
        </Button>
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h1 className="text-3xl font-bold">{ticket.title}</h1>
            <Badge className={`${getPriorityColor(ticket.priority)} text-white`}>
              {ticket.priority}
            </Badge>
            <Badge className={`${getStatusColor(ticket.status)} text-white`}>
              {ticket.status.replace('_', ' ')}
            </Badge>
            {isOverdue(ticket) && (
              <Badge variant="destructive">
                Overdue
              </Badge>
            )}
          </div>
          <p className="text-muted-foreground">
            Ticket #{ticket._id.slice(-8)} • Created {formatRelativeDate(ticket.createdAt)}
          </p>
        </div>
        <Button onClick={() => setShowStatusUpdate(!showStatusUpdate)}>
          <Edit className="h-4 w-4 mr-2" />
          Update Status
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">{ticket.description}</p>
            </CardContent>
          </Card>

          {/* Images */}
          {ticket.images && ticket.images.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Images</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {ticket.images.map((image: any, index: number) => (
                    <div key={index} className="aspect-square">
                      <img
                        src={image}
                        alt={`Ticket image ${index + 1}`}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Status Update Form */}
          {showStatusUpdate && (
            <Card>
              <CardHeader>
                <CardTitle>Update Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Select value={newStatus} onValueChange={setNewStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select new status" />
                    </SelectTrigger>
                    <SelectContent>
                      {getStatusOptions(ticket.status).map((status) => (
                        <SelectItem key={status} value={status}>
                          {status.replace('_', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Textarea
                    placeholder="Add a note about this status change (optional)"
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    rows={3}
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button onClick={handleStatusUpdate} disabled={!newStatus || isUpdating}>
                    {isUpdating ? 'Updating...' : 'Update Status'}
                  </Button>
                  <Button variant="outline" onClick={() => setShowStatusUpdate(false)}>
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Activity Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Activity Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {ticket.notes.map((note: any, index: number) => (
                  <div key={index} className="flex gap-3">
                    <div className="flex-shrink-0">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        note.type === 'comment' ? 'bg-blue-100' :
                        note.type === 'status_change' ? 'bg-green-100' :
                        note.type === 'assignment' ? 'bg-purple-100' :
                        'bg-red-100'
                      }`}>
                        <MessageSquare className="h-4 w-4" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">User</span>
                        <Badge variant="outline" className="text-xs">
                          {note.type.replace('_', ' ')}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {formatRelativeDate(note.timestamp)}
                        </span>
                      </div>
                      <p className="text-sm">{note.message}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Ticket Info */}
          <Card>
            <CardHeader>
              <CardTitle>Ticket Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground">Category</div>
                <div className="capitalize">{ticket.category}</div>
              </div>
              
              <Separator />
              
              <div>
                <div className="text-sm font-medium text-muted-foreground">Created</div>
                <div>{formatDate(ticket.createdAt)}</div>
              </div>
              
              <Separator />
              
              <div>
                <div className="text-sm font-medium text-muted-foreground">Last Updated</div>
                <div>{formatDate(ticket.updatedAt)}</div>
              </div>
              
              {ticket.unitId && (
                <>
                  <Separator />
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Unit</div>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      <span>Unit specified</span>
                    </div>
                  </div>
                </>
              )}
              
              {ticket.vendorId && (
                <>
                  <Separator />
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Assigned Vendor</div>
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      <span>Vendor assigned</span>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* SLA Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                SLA Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground">SLA Deadline</div>
                <div className={`font-medium ${
                  isOverdue(ticket) ? 'text-red-600' : 'text-green-600'
                }`}>
                  {formatDate(ticket.slaDeadline)}
                </div>
              </div>
              
              <div>
                <div className="text-sm font-medium text-muted-foreground">Time Remaining</div>
                <div className={`font-medium ${
                  isOverdue(ticket) ? 'text-red-600' : 
                  Date.now() > ticket.slaDeadline * 0.8 ? 'text-orange-600' : 
                  'text-green-600'
                }`}>
                  {getTimeUntilSLA(ticket.slaDeadline)}
                </div>
              </div>
              
              {/* SLA Progress Bar */}
              <div>
                <div className="flex justify-between text-xs text-muted-foreground mb-1">
                  <span>Progress</span>
                  <span>{Math.min(100, Math.max(0, 
                    ((Date.now() - ticket.createdAt) / (ticket.slaDeadline - ticket.createdAt)) * 100
                  )).toFixed(0)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all ${
                      isOverdue(ticket) ? 'bg-red-500' :
                      Date.now() > ticket.slaDeadline * 0.8 ? 'bg-orange-500' :
                      'bg-green-500'
                    }`}
                    style={{
                      width: `${Math.min(100, Math.max(0, 
                        ((Date.now() - ticket.createdAt) / (ticket.slaDeadline - ticket.createdAt)) * 100
                      ))}%`
                    }}
                  />
                </div>
              </div>
              
              {isOverdue(ticket) && (
                <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm text-red-600 font-medium">
                    This ticket is overdue
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Cost Information */}
          {(ticket.estimatedCost || ticket.actualCost) && (
            <Card>
              <CardHeader>
                <CardTitle>Cost Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {ticket.estimatedCost && (
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Estimated Cost</div>
                    <div className="font-medium">${ticket.estimatedCost.toFixed(2)}</div>
                  </div>
                )}
                
                {ticket.actualCost && (
                  <>
                    <Separator />
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Actual Cost</div>
                      <div className="font-medium">${ticket.actualCost.toFixed(2)}</div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};