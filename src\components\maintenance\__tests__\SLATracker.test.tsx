import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { SLATracker } from '../SLATracker';

// Mock Convex hooks
vi.mock('convex/react', () => ({
  useQuery: vi.fn(() => [
    {
      _id: 'ticket1',
      title: 'Leaking faucet',
      priority: 'high',
      status: 'assigned',
      createdAt: Date.now() - 2 * 60 * 60 * 1000, // 2 hours ago
      slaDeadline: Date.now() + 22 * 60 * 60 * 1000, // 22 hours from now
      assignedVendor: { name: '<PERSON>' },
      unit: { unitNumber: 'A101' },
    },
    {
      _id: 'ticket2',
      title: 'Broken AC',
      priority: 'medium',
      status: 'in_progress',
      createdAt: Date.now() - 10 * 60 * 60 * 1000, // 10 hours ago
      slaDeadline: Date.now() - 2 * 60 * 60 * 1000, // 2 hours overdue
      assignedVendor: { name: 'HVAC Services' },
      unit: { unitNumber: 'B205' },
    },
    {
      _id: 'ticket3',
      title: 'Light bulb replacement',
      priority: 'low',
      status: 'completed',
      createdAt: Date.now() - 5 * 24 * 60 * 60 * 1000, // 5 days ago
      slaDeadline: Date.now() - 4 * 24 * 60 * 60 * 1000, // Completed on time
      completedAt: Date.now() - 4 * 24 * 60 * 60 * 1000,
      assignedVendor: { name: 'Maintenance Team' },
      unit: { unitNumber: 'C301' },
    },
  ]),
  useMutation: vi.fn(() => vi.fn()),
}));

describe('SLATracker', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders SLA tracker with tickets', () => {
    render(<SLATracker propertyId="property1" />);
    
    expect(screen.getByText('SLA Tracker')).toBeInTheDocument();
    expect(screen.getByText('Leaking faucet')).toBeInTheDocument();
    expect(screen.getByText('Broken AC')).toBeInTheDocument();
    expect(screen.getByText('Light bulb replacement')).toBeInTheDocument();
  });

  it('shows SLA status indicators correctly', () => {
    render(<SLATracker propertyId="property1" />);
    
    // On time ticket
    expect(screen.getByText('22h remaining')).toBeInTheDocument();
    
    // Overdue ticket
    expect(screen.getByText('2h overdue')).toBeInTheDocument();
    
    // Completed ticket
    expect(screen.getByText('Completed on time')).toBeInTheDocument();
  });

  it('displays priority badges correctly', () => {
    render(<SLATracker propertyId="property1" />);
    
    expect(screen.getByText('High')).toBeInTheDocument();
    expect(screen.getByText('Medium')).toBeInTheDocument();
    expect(screen.getByText('Low')).toBeInTheDocument();
  });

  it('shows status badges correctly', () => {
    render(<SLATracker propertyId="property1" />);
    
    expect(screen.getByText('Assigned')).toBeInTheDocument();
    expect(screen.getByText('In Progress')).toBeInTheDocument();
    expect(screen.getByText('Completed')).toBeInTheDocument();
  });

  it('filters tickets by SLA status', () => {
    render(<SLATracker propertyId="property1" />);
    
    const slaFilter = screen.getByRole('combobox', { name: /filter by sla status/i });
    fireEvent.click(slaFilter);
    
    const overdueOption = screen.getByText('Overdue');
    fireEvent.click(overdueOption);
    
    expect(screen.getByText('Broken AC')).toBeInTheDocument();
    expect(screen.queryByText('Leaking faucet')).not.toBeInTheDocument();
  });

  it('filters tickets by priority', () => {
    render(<SLATracker propertyId="property1" />);
    
    const priorityFilter = screen.getByRole('combobox', { name: /filter by priority/i });
    fireEvent.click(priorityFilter);
    
    const highOption = screen.getByText('High Priority');
    fireEvent.click(highOption);
    
    expect(screen.getByText('Leaking faucet')).toBeInTheDocument();
    expect(screen.queryByText('Broken AC')).not.toBeInTheDocument();
  });

  it('shows SLA metrics summary', () => {
    render(<SLATracker propertyId="property1" />);
    
    expect(screen.getByText(/sla compliance rate/i)).toBeInTheDocument();
    expect(screen.getByText(/average resolution time/i)).toBeInTheDocument();
    expect(screen.getByText(/overdue tickets/i)).toBeInTheDocument();
  });

  it('calculates SLA compliance rate correctly', () => {
    render(<SLATracker propertyId="property1" />);
    
    // 1 completed on time out of 1 completed = 100%
    // But with overdue tickets, overall compliance should be lower
    expect(screen.getByText(/67%/)).toBeInTheDocument(); // 2 out of 3 tickets meeting SLA
  });

  it('shows escalation alerts for overdue tickets', () => {
    render(<SLATracker propertyId="property1" />);
    
    expect(screen.getByText(/escalation required/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /escalate/i })).toBeInTheDocument();
  });

  it('handles ticket escalation', async () => {
    const mockEscalate = vi.fn().mockResolvedValue({ success: true });
    vi.mocked(require('convex/react').useMutation).mockReturnValue(mockEscalate);

    render(<SLATracker propertyId="property1" />);
    
    const escalateButton = screen.getByRole('button', { name: /escalate/i });
    fireEvent.click(escalateButton);
    
    await waitFor(() => {
      expect(mockEscalate).toHaveBeenCalledWith({
        ticketId: 'ticket2',
        reason: 'SLA deadline exceeded',
      });
    });
  });

  it('shows vendor performance metrics', () => {
    render(<SLATracker propertyId="property1" />);
    
    expect(screen.getByText('John Plumber')).toBeInTheDocument();
    expect(screen.getByText('HVAC Services')).toBeInTheDocument();
    expect(screen.getByText('Maintenance Team')).toBeInTheDocument();
  });

  it('exports SLA report', async () => {
    // Mock file download
    const createElementSpy = vi.spyOn(document, 'createElement');
    const mockAnchor = {
      href: '',
      download: '',
      click: vi.fn(),
    };
    createElementSpy.mockReturnValue(mockAnchor as any);

    render(<SLATracker propertyId="property1" />);
    
    const exportButton = screen.getByRole('button', { name: /export report/i });
    fireEvent.click(exportButton);
    
    await waitFor(() => {
      expect(mockAnchor.click).toHaveBeenCalled();
      expect(mockAnchor.download).toContain('sla-report');
    });
    
    createElementSpy.mockRestore();
  });

  it('shows time remaining in human-readable format', () => {
    render(<SLATracker propertyId="property1" />);
    
    expect(screen.getByText('22h remaining')).toBeInTheDocument();
    expect(screen.getByText('2h overdue')).toBeInTheDocument();
  });

  it('handles empty state when no tickets exist', () => {
    vi.mocked(require('convex/react').useQuery).mockReturnValue([]);
    
    render(<SLATracker propertyId="property1" />);
    
    expect(screen.getByText(/no maintenance tickets found/i)).toBeInTheDocument();
    expect(screen.getByText(/sla tracking will appear here/i)).toBeInTheDocument();
  });

  it('handles loading state', () => {
    vi.mocked(require('convex/react').useQuery).mockReturnValue(undefined);
    
    render(<SLATracker propertyId="property1" />);
    
    expect(screen.getByText(/loading sla data/i)).toBeInTheDocument();
  });

  it('shows SLA deadline warnings', () => {
    // Mock a ticket that's close to deadline
    const nearDeadlineTicket = {
      _id: 'ticket4',
      title: 'Urgent repair',
      priority: 'high',
      status: 'assigned',
      createdAt: Date.now() - 23 * 60 * 60 * 1000, // 23 hours ago
      slaDeadline: Date.now() + 1 * 60 * 60 * 1000, // 1 hour remaining
      assignedVendor: { name: 'Emergency Services' },
      unit: { unitNumber: 'D401' },
    };

    vi.mocked(require('convex/react').useQuery).mockReturnValue([nearDeadlineTicket]);
    
    render(<SLATracker propertyId="property1" />);
    
    expect(screen.getByText(/1h remaining/i)).toBeInTheDocument();
    expect(screen.getByText(/warning/i)).toBeInTheDocument();
  });

  it('groups tickets by SLA status', () => {
    render(<SLATracker propertyId="property1" />);
    
    expect(screen.getByText(/on time \(1\)/i)).toBeInTheDocument();
    expect(screen.getByText(/overdue \(1\)/i)).toBeInTheDocument();
    expect(screen.getByText(/completed \(1\)/i)).toBeInTheDocument();
  });

  it('shows detailed SLA breakdown', () => {
    render(<SLATracker propertyId="property1" />);
    
    const detailsButton = screen.getByRole('button', { name: /view details/i });
    fireEvent.click(detailsButton);
    
    expect(screen.getByText(/sla breakdown/i)).toBeInTheDocument();
    expect(screen.getByText(/response time/i)).toBeInTheDocument();
    expect(screen.getByText(/resolution time/i)).toBeInTheDocument();
  });
});