/**
 * Offline Sync Manager
 * Handles synchronization between local cache and server when online/offline
 */

import { offlineStorageManager } from './offline-storage';
import { offlineSyncReconciliation } from './offline-sync-reconciliation';
import { ConvexReactClient } from 'convex/react';

export interface SyncOptions {
  autoSync: boolean;
  syncInterval: number; // milliseconds
  batchSize: number;
  retryAttempts: number;
}

export interface SyncStatus {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime: number;
  pendingOperations: number;
  conflicts: number;
  error: string | null;
}

class OfflineSyncManager {
  private convexClient: ConvexReactClient | null = null;
  private syncInterval: NodeJS.Timeout | null = null;
  private isInitialized = false;
  private listeners: Set<(status: SyncStatus) => void> = new Set();
  
  private status: SyncStatus = {
    isOnline: navigator.onLine,
    isSyncing: false,
    lastSyncTime: 0,
    pendingOperations: 0,
    conflicts: 0,
    error: null
  };

  private options: SyncOptions = {
    autoSync: true,
    syncInterval: 30000, // 30 seconds
    batchSize: 50,
    retryAttempts: 3
  };

  /**
   * Initialize the sync manager
   */
  async initialize(convexClient: ConvexReactClient, options: Partial<SyncOptions> = {}) {
    if (this.isInitialized) return;

    this.convexClient = convexClient;
    this.options = { ...this.options, ...options };

    // Set up online/offline listeners
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);

    // Initialize storage
    await offlineStorageManager.initialize();

    // Update initial status
    await this.updateStatus();

    // Start auto-sync if enabled
    if (this.options.autoSync && this.status.isOnline) {
      this.startAutoSync();
    }

    this.isInitialized = true;
    console.log('Offline sync manager initialized');
  }

  /**
   * Add a status change listener
   */
  addStatusListener(listener: (status: SyncStatus) => void) {
    this.listeners.add(listener);
    // Immediately call with current status
    listener(this.status);
  }

  /**
   * Remove a status change listener
   */
  removeStatusListener(listener: (status: SyncStatus) => void) {
    this.listeners.delete(listener);
  }

  /**
   * Get current sync status
   */
  getStatus(): SyncStatus {
    return { ...this.status };
  }

  /**
   * Manually trigger sync
   */
  async sync(): Promise<void> {
    if (!this.convexClient || this.status.isSyncing) return;

    try {
      this.status.isSyncing = true;
      this.status.error = null;
      this.notifyListeners();

      const result = await offlineSyncReconciliation.reconcileOfflineChanges(
        this.convexClient,
        {
          strategy: 'last_write_wins',
          batchSize: this.options.batchSize,
          maxRetries: this.options.retryAttempts
        }
      );

      this.status.lastSyncTime = Date.now();
      this.status.conflicts = result.conflicts.length;
      
      if (!result.success) {
        this.status.error = result.errors.join('; ');
      }

    } catch (error) {
      console.error('Sync failed:', error);
      this.status.error = error instanceof Error ? error.message : 'Sync failed';
    } finally {
      this.status.isSyncing = false;
      await this.updateStatus();
    }
  }

  /**
   * Queue an operation for offline sync
   */
  async queueOperation(operation: {
    operation: 'create' | 'update' | 'delete';
    entityType: string;
    entityId?: string;
    data: any;
  }): Promise<void> {
    await offlineStorageManager.addToOfflineQueue(operation);
    await this.updateStatus();

    // Try immediate sync if online
    if (this.status.isOnline && this.options.autoSync) {
      this.sync();
    }
  }

  /**
   * Cache entity data locally
   */
  async cacheEntity(
    entityType: string,
    id: string,
    data: any,
    propertyId?: string
  ): Promise<void> {
    await offlineStorageManager.cacheEntity(
      entityType as any,
      id,
      data,
      propertyId
    );
  }

  /**
   * Get cached entity data
   */
  async getCachedEntity(entityType: string, id: string): Promise<any | null> {
    return await offlineStorageManager.getCachedEntity(entityType as any, id);
  }

  /**
   * Get all cached entities of a type
   */
  async getCachedEntities(entityType: string, propertyId?: string): Promise<any[]> {
    if (propertyId) {
      return await offlineStorageManager.getCachedEntitiesByProperty(
        entityType as any,
        propertyId
      );
    }
    return await offlineStorageManager.getAllCachedEntities(entityType as any);
  }

  /**
   * Start automatic synchronization
   */
  private startAutoSync() {
    if (this.syncInterval) return;

    this.syncInterval = setInterval(() => {
      if (this.status.isOnline && this.status.pendingOperations > 0) {
        this.sync();
      }
    }, this.options.syncInterval);
  }

  /**
   * Stop automatic synchronization
   */
  private stopAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * Handle coming online
   */
  private handleOnline = async () => {
    this.status.isOnline = true;
    this.status.error = null;
    
    if (this.options.autoSync) {
      this.startAutoSync();
      // Trigger immediate sync if there are pending operations
      if (this.status.pendingOperations > 0) {
        setTimeout(() => this.sync(), 1000); // Small delay to ensure connection is stable
      }
    }
    
    this.notifyListeners();
  };

  /**
   * Handle going offline
   */
  private handleOffline = () => {
    this.status.isOnline = false;
    this.stopAutoSync();
    this.notifyListeners();
  };

  /**
   * Update status with current data
   */
  private async updateStatus() {
    try {
      const queue = await offlineStorageManager.getOfflineQueue();
      this.status.pendingOperations = queue.length;
      this.notifyListeners();
    } catch (error) {
      console.error('Failed to update status:', error);
    }
  }

  /**
   * Notify all listeners of status changes
   */
  private notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.status);
      } catch (error) {
        console.error('Status listener error:', error);
      }
    });
  }

  /**
   * Clean up resources
   */
  async cleanup() {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
    this.stopAutoSync();
    this.listeners.clear();
    await offlineStorageManager.close();
    this.isInitialized = false;
  }
}

export const offlineSyncManager = new OfflineSyncManager();