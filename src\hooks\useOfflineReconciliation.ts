/**
 * React hook for managing offline sync reconciliation
 */

import { useState, useCallback, useEffect } from 'react';
import { useConvexAuth } from 'convex/react';
import { ConvexReactClient } from 'convex/react';
import { 
  offlineSyncReconciliation,
  ReconciliationResult,
  ConflictInfo,
  ReconciliationOptions,
  ConflictResolutionStrategy
} from '../lib/offline-sync-reconciliation';
import { offlineStorageManager } from '../lib/offline-storage';

export interface ReconciliationState {
  isReconciling: boolean;
  progress: number;
  currentOperation: string;
  result: ReconciliationResult | null;
  conflicts: ConflictInfo[];
  error: string | null;
}

export interface ReconciliationPreview {
  operationsToSync: number;
  potentialConflicts: ConflictInfo[];
  estimatedDuration: number;
}

export function useOfflineReconciliation(convexClient: ConvexReactClient) {
  const { isAuthenticated } = useConvexAuth();
  
  const [state, setState] = useState<ReconciliationState>({
    isReconciling: false,
    progress: 0,
    currentOperation: '',
    result: null,
    conflicts: [],
    error: null
  });

  const [preview, setPreview] = useState<ReconciliationPreview | null>(null);
  const [pendingOperationsCount, setPendingOperationsCount] = useState(0);

  // Check for pending operations on mount and auth change
  useEffect(() => {
    if (isAuthenticated) {
      checkPendingOperations();
    }
  }, [isAuthenticated]);

  /**
   * Check how many operations are pending sync
   */
  const checkPendingOperations = useCallback(async () => {
    try {
      const queue = await offlineStorageManager.getOfflineQueue();
      setPendingOperationsCount(queue.length);
    } catch (error) {
      console.error('Failed to check pending operations:', error);
    }
  }, []);

  /**
   * Preview what reconciliation would do
   */
  const previewReconciliation = useCallback(async (
    options: Partial<ReconciliationOptions> = {}
  ) => {
    if (!isAuthenticated) return;

    try {
      setState(prev => ({ ...prev, error: null }));
      
      const previewResult = await offlineSyncReconciliation.previewReconciliation(
        convexClient,
        options
      );
      
      setPreview(previewResult);
      return previewResult;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Preview failed';
      setState(prev => ({ ...prev, error: errorMessage }));
      throw error;
    }
  }, [convexClient, isAuthenticated]);

  /**
   * Start the reconciliation process
   */
  const startReconciliation = useCallback(async (
    options: Partial<ReconciliationOptions> = {}
  ) => {
    if (!isAuthenticated || state.isReconciling) return;

    setState(prev => ({
      ...prev,
      isReconciling: true,
      progress: 0,
      currentOperation: 'Initializing...',
      error: null,
      result: null
    }));

    try {
      // Get initial queue size for progress tracking
      const initialQueue = await offlineStorageManager.getOfflineQueue();
      const totalOperations = initialQueue.length;

      if (totalOperations === 0) {
        setState(prev => ({
          ...prev,
          isReconciling: false,
          progress: 100,
          currentOperation: 'No operations to sync',
          result: {
            success: true,
            conflicts: [],
            resolvedConflicts: 0,
            syncedOperations: 0,
            failedOperations: 0,
            errors: []
          }
        }));
        return;
      }

      // Update progress periodically during reconciliation
      const progressInterval = setInterval(async () => {
        try {
          const currentQueue = await offlineStorageManager.getOfflineQueue();
          const remaining = currentQueue.length;
          const completed = totalOperations - remaining;
          const progress = Math.min((completed / totalOperations) * 100, 95);
          
          setState(prev => ({
            ...prev,
            progress,
            currentOperation: `Processing ${completed}/${totalOperations} operations...`
          }));
        } catch (error) {
          console.warn('Failed to update progress:', error);
        }
      }, 1000);

      // Start reconciliation
      const result = await offlineSyncReconciliation.reconcileOfflineChanges(
        convexClient,
        options
      );

      clearInterval(progressInterval);

      setState(prev => ({
        ...prev,
        isReconciling: false,
        progress: 100,
        currentOperation: 'Reconciliation completed',
        result,
        conflicts: result.conflicts
      }));

      // Update pending operations count
      await checkPendingOperations();

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Reconciliation failed';
      
      setState(prev => ({
        ...prev,
        isReconciling: false,
        error: errorMessage,
        currentOperation: 'Failed'
      }));

      throw error;
    }
  }, [convexClient, isAuthenticated, state.isReconciling, checkPendingOperations]);

  /**
   * Resolve a specific conflict manually
   */
  const resolveConflict = useCallback(async (
    conflict: ConflictInfo,
    resolution: 'local' | 'server' | 'merge',
    mergedData?: any
  ) => {
    if (!isAuthenticated) return;

    try {
      await convexClient.mutation('realtimeSync:resolveConflict', {
        entityType: conflict.entityType,
        entityId: conflict.entityId,
        resolution,
        localData: conflict.localData,
        serverData: conflict.serverData,
        mergedData
      });

      // Update local cache with resolved data
      const finalData = resolution === 'local' ? conflict.localData :
                       resolution === 'server' ? conflict.serverData :
                       mergedData;

      await offlineStorageManager.cacheEntity(
        conflict.entityType as any,
        conflict.entityId,
        finalData
      );

      // Remove conflict from state
      setState(prev => ({
        ...prev,
        conflicts: prev.conflicts.filter(c => 
          c.entityId !== conflict.entityId || c.entityType !== conflict.entityType
        )
      }));

    } catch (error) {
      console.error('Failed to resolve conflict:', error);
      throw error;
    }
  }, [convexClient, isAuthenticated]);

  /**
   * Clear reconciliation state
   */
  const clearState = useCallback(() => {
    setState({
      isReconciling: false,
      progress: 0,
      currentOperation: '',
      result: null,
      conflicts: [],
      error: null
    });
    setPreview(null);
  }, []);

  /**
   * Auto-reconcile with default settings
   */
  const autoReconcile = useCallback(async (
    strategy: ConflictResolutionStrategy = 'last_write_wins'
  ) => {
    return startReconciliation({
      strategy,
      batchSize: 50,
      maxRetries: 3
    });
  }, [startReconciliation]);

  /**
   * Check if reconciliation is needed
   */
  const isReconciliationNeeded = pendingOperationsCount > 0;

  /**
   * Get reconciliation status summary
   */
  const getStatusSummary = useCallback(() => {
    if (state.isReconciling) {
      return {
        status: 'reconciling' as const,
        message: state.currentOperation,
        progress: state.progress
      };
    }

    if (state.error) {
      return {
        status: 'error' as const,
        message: state.error,
        progress: 0
      };
    }

    if (pendingOperationsCount > 0) {
      return {
        status: 'pending' as const,
        message: `${pendingOperationsCount} operations pending sync`,
        progress: 0
      };
    }

    if (state.conflicts.length > 0) {
      return {
        status: 'conflicts' as const,
        message: `${state.conflicts.length} conflicts need resolution`,
        progress: 0
      };
    }

    return {
      status: 'synced' as const,
      message: 'All changes synced',
      progress: 100
    };
  }, [state, pendingOperationsCount]);

  return {
    // State
    state,
    preview,
    pendingOperationsCount,
    isReconciliationNeeded,
    
    // Actions
    startReconciliation,
    previewReconciliation,
    resolveConflict,
    autoReconcile,
    clearState,
    checkPendingOperations,
    
    // Computed
    getStatusSummary
  };
}