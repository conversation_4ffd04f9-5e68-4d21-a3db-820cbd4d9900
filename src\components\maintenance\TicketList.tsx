import React from 'react';
import { Doc, Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Clock, User, MapPin, AlertTriangle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface TicketListProps {
  tickets: Doc<"maintenanceTickets">[];
  onTicketSelect: (ticketId: Id<"maintenanceTickets">) => void;
  getPriorityColor: (priority: string) => string;
  getStatusColor: (status: string) => string;
}

export const TicketList: React.FC<TicketListProps> = ({
  tickets,
  onTicketSelect,
  getPriorityColor,
  getStatusColor
}) => {
  const formatDate = (timestamp: number) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  const isOverdue = (ticket: Doc<"maintenanceTickets">) => {
    return Date.now() > ticket.slaDeadline && !['completed', 'closed'].includes(ticket.status);
  };

  const getTimeUntilSLA = (slaDeadline: number) => {
    const now = Date.now();
    const timeLeft = slaDeadline - now;
    
    if (timeLeft <= 0) {
      return 'Overdue';
    }
    
    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h left`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m left`;
    } else {
      return `${minutes}m left`;
    }
  };

  if (!tickets || tickets.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No tickets found</h3>
          <p className="text-muted-foreground text-center">
            No maintenance tickets match your current filters.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {tickets.map((ticket) => (
        <Card key={ticket._id} className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-lg font-semibold truncate">{ticket.title}</h3>
                  <Badge className={`${getPriorityColor(ticket.priority)} text-white`}>
                    {ticket.priority}
                  </Badge>
                  <Badge className={`${getStatusColor(ticket.status)} text-white`}>
                    {ticket.status.replace('_', ' ')}
                  </Badge>
                  {isOverdue(ticket) && (
                    <Badge variant="destructive">
                      Overdue
                    </Badge>
                  )}
                </div>
                
                <p className="text-muted-foreground mb-3 line-clamp-2">
                  {ticket.description}
                </p>
                
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>Created {formatDate(ticket.createdAt)}</span>
                  </div>
                  
                  {ticket.unitId && (
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      <span>Unit specified</span>
                    </div>
                  )}
                  
                  {ticket.vendorId && (
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      <span>Assigned</span>
                    </div>
                  )}
                </div>
                
                <div className="mt-2 flex items-center justify-between">
                  <div className="text-sm">
                    <span className="font-medium">Category:</span> {ticket.category}
                  </div>
                  
                  <div className={`text-sm font-medium ${
                    isOverdue(ticket) ? 'text-red-600' : 
                    Date.now() > ticket.slaDeadline * 0.8 ? 'text-orange-600' : 
                    'text-green-600'
                  }`}>
                    SLA: {getTimeUntilSLA(ticket.slaDeadline)}
                  </div>
                </div>
              </div>
              
              <div className="ml-4 flex flex-col gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onTicketSelect(ticket._id)}
                >
                  View Details
                </Button>
                
                {ticket.images && ticket.images.length > 0 && (
                  <div className="text-xs text-muted-foreground text-center">
                    {ticket.images.length} image{ticket.images.length > 1 ? 's' : ''}
                  </div>
                )}
              </div>
            </div>
            
            {/* Progress indicator for SLA */}
            <div className="mt-4">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>SLA Progress</span>
                <span>{getTimeUntilSLA(ticket.slaDeadline)}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all ${
                    isOverdue(ticket) ? 'bg-red-500' :
                    Date.now() > ticket.slaDeadline * 0.8 ? 'bg-orange-500' :
                    'bg-green-500'
                  }`}
                  style={{
                    width: `${Math.min(100, Math.max(0, 
                      ((Date.now() - ticket.createdAt) / (ticket.slaDeadline - ticket.createdAt)) * 100
                    ))}%`
                  }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};