import { test, expect } from '@playwright/test';

test.describe('Maintenance Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication as tenant
    await page.route('**/api/auth/verify', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          isAuthenticated: true,
          user: {
            _id: 'tenant1',
            email: '<EMAIL>',
            name: '<PERSON>',
            role: 'tenant'
          }
        })
      });
    });

    // Mock maintenance tickets
    await page.route('**/api/maintenance/tickets', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'ticket1',
            title: 'Leaking faucet in kitchen',
            description: 'The kitchen faucet has been leaking for 2 days',
            priority: 'medium',
            status: 'open',
            category: 'plumbing',
            createdAt: Date.now() - 2 * 24 * 60 * 60 * 1000,
            slaDeadline: Date.now() + 22 * 60 * 60 * 1000,
            unit: { unitNumber: 'A101' },
            property: { name: 'Sunset Apartments' }
          }
        ])
      });
    });

    await page.goto('/dashboard/maintenance');
  });

  test('should display maintenance tickets', async ({ page }) => {
    await expect(page.getByRole('heading', { name: /maintenance/i })).toBeVisible();
    await expect(page.getByText('Leaking faucet in kitchen')).toBeVisible();
    await expect(page.getByText('Medium')).toBeVisible();
    await expect(page.getByText('Open')).toBeVisible();
  });

  test('should create new maintenance request', async ({ page }) => {
    // Mock create ticket API
    await page.route('**/api/maintenance/tickets', async route => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            _id: 'ticket2',
            title: 'Broken air conditioning',
            status: 'open'
          })
        });
      }
    });

    await page.getByRole('button', { name: /create request/i }).click();
    
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(page.getByRole('heading', { name: /new maintenance request/i })).toBeVisible();
    
    await page.getByLabel(/title/i).fill('Broken air conditioning');
    await page.getByLabel(/description/i).fill('The AC unit in the living room is not cooling properly');
    await page.getByLabel(/priority/i).selectOption('high');
    await page.getByLabel(/category/i).selectOption('hvac');
    
    await page.getByRole('button', { name: /submit request/i }).click();
    
    await expect(page.getByText(/request submitted successfully/i)).toBeVisible();
  });

  test('should validate maintenance request form', async ({ page }) => {
    await page.getByRole('button', { name: /create request/i }).click();
    
    // Try to submit empty form
    await page.getByRole('button', { name: /submit request/i }).click();
    
    await expect(page.getByText(/title is required/i)).toBeVisible();
    await expect(page.getByText(/description is required/i)).toBeVisible();
  });

  test('should show ticket details', async ({ page }) => {
    await page.getByText('Leaking faucet in kitchen').click();
    
    await expect(page.getByRole('heading', { name: /ticket details/i })).toBeVisible();
    await expect(page.getByText(/the kitchen faucet has been leaking/i)).toBeVisible();
    await expect(page.getByText(/unit a101/i)).toBeVisible();
    await expect(page.getByText(/sunset apartments/i)).toBeVisible();
  });

  test('should filter tickets by status', async ({ page }) => {
    await page.getByRole('combobox', { name: /filter by status/i }).click();
    await page.getByText('Open').click();
    
    await expect(page.getByText('Leaking faucet in kitchen')).toBeVisible();
  });

  test('should filter tickets by priority', async ({ page }) => {
    await page.getByRole('combobox', { name: /filter by priority/i }).click();
    await page.getByText('Medium').click();
    
    await expect(page.getByText('Leaking faucet in kitchen')).toBeVisible();
  });

  test('should search tickets', async ({ page }) => {
    await page.getByPlaceholder(/search tickets/i).fill('faucet');
    
    await expect(page.getByText('Leaking faucet in kitchen')).toBeVisible();
  });

  test('should show SLA status', async ({ page }) => {
    await expect(page.getByText(/22h remaining/i)).toBeVisible();
  });

  test('should upload images for ticket', async ({ page }) => {
    await page.getByRole('button', { name: /create request/i }).click();
    
    await page.getByLabel(/title/i).fill('Broken window');
    await page.getByLabel(/description/i).fill('Window is cracked and needs replacement');
    
    // Mock file upload
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.getByLabel(/upload images/i).click();
    const fileChooser = await fileChooserPromise;
    
    // Create a mock file
    await fileChooser.setFiles({
      name: 'broken-window.jpg',
      mimeType: 'image/jpeg',
      buffer: Buffer.from('fake image data')
    });
    
    await expect(page.getByText('broken-window.jpg')).toBeVisible();
  });

  test('should show ticket timeline', async ({ page }) => {
    await page.getByText('Leaking faucet in kitchen').click();
    await page.getByRole('tab', { name: /timeline/i }).click();
    
    await expect(page.getByText(/ticket created/i)).toBeVisible();
    await expect(page.getByText(/2 days ago/i)).toBeVisible();
  });

  test('should allow tenant to add comments', async ({ page }) => {
    await page.getByText('Leaking faucet in kitchen').click();
    await page.getByRole('tab', { name: /comments/i }).click();
    
    await page.getByLabel(/add comment/i).fill('The leak is getting worse');
    await page.getByRole('button', { name: /post comment/i }).click();
    
    await expect(page.getByText('The leak is getting worse')).toBeVisible();
  });

  test('should show vendor assignment (manager view)', async ({ page }) => {
    // Switch to manager role
    await page.route('**/api/auth/verify', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          isAuthenticated: true,
          user: {
            _id: 'manager1',
            email: '<EMAIL>',
            name: 'Property Manager',
            role: 'manager'
          }
        })
      });
    });

    await page.reload();
    await page.getByText('Leaking faucet in kitchen').click();
    
    await expect(page.getByRole('button', { name: /assign vendor/i })).toBeVisible();
  });

  test('should assign vendor to ticket', async ({ page }) => {
    // Switch to manager role
    await page.route('**/api/auth/verify', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          isAuthenticated: true,
          user: {
            _id: 'manager1',
            email: '<EMAIL>',
            name: 'Property Manager',
            role: 'manager'
          }
        })
      });
    });

    // Mock vendors list
    await page.route('**/api/vendors', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'vendor1',
            name: 'ABC Plumbing Services',
            specialties: ['plumbing'],
            rating: 4.5
          }
        ])
      });
    });

    await page.reload();
    await page.getByText('Leaking faucet in kitchen').click();
    await page.getByRole('button', { name: /assign vendor/i }).click();
    
    await expect(page.getByRole('dialog')).toBeVisible();
    await page.getByText('ABC Plumbing Services').click();
    await page.getByRole('button', { name: /assign/i }).click();
    
    await expect(page.getByText(/vendor assigned successfully/i)).toBeVisible();
  });

  test('should escalate overdue ticket', async ({ page }) => {
    // Mock overdue ticket
    await page.route('**/api/maintenance/tickets', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'ticket3',
            title: 'Emergency repair needed',
            priority: 'high',
            status: 'assigned',
            slaDeadline: Date.now() - 2 * 60 * 60 * 1000, // 2 hours overdue
            unit: { unitNumber: 'B202' }
          }
        ])
      });
    });

    await page.reload();
    
    await expect(page.getByText(/2h overdue/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /escalate/i })).toBeVisible();
  });

  test('should show maintenance analytics', async ({ page }) => {
    await page.getByRole('tab', { name: /analytics/i }).click();
    
    await expect(page.getByText(/sla compliance/i)).toBeVisible();
    await expect(page.getByText(/average resolution time/i)).toBeVisible();
    await expect(page.getByText(/ticket volume/i)).toBeVisible();
  });

  test('should export maintenance report', async ({ page }) => {
    await page.getByRole('tab', { name: /analytics/i }).click();
    
    const downloadPromise = page.waitForEvent('download');
    await page.getByRole('button', { name: /export report/i }).click();
    
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toContain('maintenance-report');
  });
});