import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Create a new portal for a property
export const createPortal = mutation({
  args: {
    propertyId: v.id("properties"),
    name: v.string(),
    subdomain: v.string(),
    customDomain: v.optional(v.string()),
    branding: v.object({
      logo: v.optional(v.string()),
      favicon: v.optional(v.string()),
      primaryColor: v.string(),
      secondaryColor: v.string(),
      accentColor: v.string(),
      backgroundColor: v.string(),
      textColor: v.string(),
      fontFamily: v.string(),
      customCSS: v.optional(v.string()),
    }),
    theme: v.optional(v.object({
      layout: v.union(v.literal("modern"), v.literal("classic"), v.literal("minimal")),
      headerStyle: v.union(v.literal("fixed"), v.literal("static"), v.literal("transparent")),
      sidebarStyle: v.union(v.literal("expanded"), v.literal("collapsed"), v.literal("overlay")),
      cardStyle: v.union(v.literal("elevated"), v.literal("outlined"), v.literal("flat")),
      borderRadius: v.union(v.literal("none"), v.literal("small"), v.literal("medium"), v.literal("large")),
    })),
    features: v.optional(v.object({
      paymentPortal: v.boolean(),
      maintenanceRequests: v.boolean(),
      documentAccess: v.boolean(),
      leaseInformation: v.boolean(),
      communicationCenter: v.boolean(),
      announcementsBoard: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    // Check if subdomain is already taken
    const existingPortal = await ctx.db
      .query("portals")
      .withIndex("by_subdomain", (q) => q.eq("subdomain", args.subdomain))
      .first();

    if (existingPortal) {
      throw new Error("Subdomain is already taken");
    }

    // Check if custom domain is already taken (if provided)
    if (args.customDomain) {
      const existingCustomDomain = await ctx.db
        .query("portals")
        .withIndex("by_custom_domain", (q) => q.eq("customDomain", args.customDomain))
        .first();

      if (existingCustomDomain) {
        throw new Error("Custom domain is already taken");
      }
    }

    // Default theme settings
    const defaultTheme = {
      layout: "modern" as const,
      headerStyle: "fixed" as const,
      sidebarStyle: "expanded" as const,
      cardStyle: "elevated" as const,
      borderRadius: "medium" as const,
    };

    // Default feature settings
    const defaultFeatures = {
      paymentPortal: true,
      maintenanceRequests: true,
      documentAccess: true,
      leaseInformation: true,
      communicationCenter: true,
      announcementsBoard: true,
    };

    const portalId = await ctx.db.insert("portals", {
      propertyId: args.propertyId,
      name: args.name,
      subdomain: args.subdomain,
      customDomain: args.customDomain,
      branding: args.branding,
      theme: args.theme || defaultTheme,
      features: args.features || defaultFeatures,
      customization: {
        welcomeMessage: undefined,
        footerText: undefined,
        contactInfo: undefined,
        socialLinks: undefined,
        customPages: [],
      },
      seoSettings: {
        title: `${args.name} - Tenant Portal`,
        description: `Welcome to ${args.name} tenant portal. Manage your lease, payments, and maintenance requests.`,
        keywords: ["tenant portal", "property management", "rent payment", "maintenance"],
        ogImage: undefined,
      },
      analytics: {
        googleAnalyticsId: undefined,
        facebookPixelId: undefined,
        customTrackingCode: undefined,
      },
      isActive: true,
      isPublished: false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return portalId;
  },
});

// Update portal branding
export const updatePortalBranding = mutation({
  args: {
    portalId: v.id("portals"),
    branding: v.object({
      logo: v.optional(v.string()),
      favicon: v.optional(v.string()),
      primaryColor: v.string(),
      secondaryColor: v.string(),
      accentColor: v.string(),
      backgroundColor: v.string(),
      textColor: v.string(),
      fontFamily: v.string(),
      customCSS: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.portalId, {
      branding: args.branding,
      updatedAt: Date.now(),
    });
  },
});

// Update portal theme
export const updatePortalTheme = mutation({
  args: {
    portalId: v.id("portals"),
    theme: v.object({
      layout: v.union(v.literal("modern"), v.literal("classic"), v.literal("minimal")),
      headerStyle: v.union(v.literal("fixed"), v.literal("static"), v.literal("transparent")),
      sidebarStyle: v.union(v.literal("expanded"), v.literal("collapsed"), v.literal("overlay")),
      cardStyle: v.union(v.literal("elevated"), v.literal("outlined"), v.literal("flat")),
      borderRadius: v.union(v.literal("none"), v.literal("small"), v.literal("medium"), v.literal("large")),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.portalId, {
      theme: args.theme,
      updatedAt: Date.now(),
    });
  },
});

// Update portal customization
export const updatePortalCustomization = mutation({
  args: {
    portalId: v.id("portals"),
    customization: v.object({
      welcomeMessage: v.optional(v.string()),
      footerText: v.optional(v.string()),
      contactInfo: v.optional(v.object({
        phone: v.string(),
        email: v.string(),
        address: v.string(),
        hours: v.string(),
      })),
      socialLinks: v.optional(v.object({
        facebook: v.optional(v.string()),
        twitter: v.optional(v.string()),
        instagram: v.optional(v.string()),
        linkedin: v.optional(v.string()),
      })),
      customPages: v.array(v.object({
        title: v.string(),
        slug: v.string(),
        content: v.string(),
        isPublished: v.boolean(),
      })),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.portalId, {
      customization: args.customization,
      updatedAt: Date.now(),
    });
  },
});

// Update custom domain
export const updateCustomDomain = mutation({
  args: {
    portalId: v.id("portals"),
    customDomain: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if custom domain is already taken (if provided)
    if (args.customDomain) {
      const existingCustomDomain = await ctx.db
        .query("portals")
        .withIndex("by_custom_domain", (q) => q.eq("customDomain", args.customDomain))
        .first();

      if (existingCustomDomain && existingCustomDomain._id !== args.portalId) {
        throw new Error("Custom domain is already taken");
      }
    }

    await ctx.db.patch(args.portalId, {
      customDomain: args.customDomain,
      updatedAt: Date.now(),
    });
  },
});

// Toggle portal features
export const updatePortalFeatures = mutation({
  args: {
    portalId: v.id("portals"),
    features: v.object({
      paymentPortal: v.boolean(),
      maintenanceRequests: v.boolean(),
      documentAccess: v.boolean(),
      leaseInformation: v.boolean(),
      communicationCenter: v.boolean(),
      announcementsBoard: v.boolean(),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.portalId, {
      features: args.features,
      updatedAt: Date.now(),
    });
  },
});

// Publish/unpublish portal
export const togglePortalPublication = mutation({
  args: {
    portalId: v.id("portals"),
    isPublished: v.boolean(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.portalId, {
      isPublished: args.isPublished,
      updatedAt: Date.now(),
    });
  },
});

// Get portal by property ID
export const getPortalByProperty = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("portals")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .first();
  },
});

// Get portal by subdomain
export const getPortalBySubdomain = query({
  args: { subdomain: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("portals")
      .withIndex("by_subdomain", (q) => q.eq("subdomain", args.subdomain))
      .first();
  },
});

// Get portal by custom domain
export const getPortalByCustomDomain = query({
  args: { customDomain: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("portals")
      .withIndex("by_custom_domain", (q) => q.eq("customDomain", args.customDomain))
      .first();
  },
});

// Get portal by ID
export const getPortalById = query({
  args: { portalId: v.id("portals") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.portalId);
  },
});

// Get all portals for a property owner/manager
export const getPortalsByUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    // Get user's properties
    const properties = await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", args.userId))
      .collect();

    const managerProperties = await ctx.db
      .query("properties")
      .withIndex("by_manager", (q) => q.eq("managerId", args.userId))
      .collect();

    const allProperties = [...properties, ...managerProperties];
    const propertyIds = allProperties.map(p => p._id);

    // Get portals for these properties
    const portals = [];
    for (const propertyId of propertyIds) {
      const portal = await ctx.db
        .query("portals")
        .withIndex("by_property", (q) => q.eq("propertyId", propertyId))
        .first();
      
      if (portal) {
        const property = allProperties.find(p => p._id === propertyId);
        portals.push({
          ...portal,
          property: property,
        });
      }
    }

    return portals;
  },
});

// Generate CSS variables from branding
export const generatePortalCSS = query({
  args: { portalId: v.id("portals") },
  handler: async (ctx, args) => {
    const portal = await ctx.db.get(args.portalId);
    if (!portal) {
      throw new Error("Portal not found");
    }

    const { branding, theme } = portal;
    
    // Generate CSS custom properties
    const cssVariables = `
      :root {
        --primary-color: ${branding.primaryColor};
        --secondary-color: ${branding.secondaryColor};
        --accent-color: ${branding.accentColor};
        --background-color: ${branding.backgroundColor};
        --text-color: ${branding.textColor};
        --font-family: ${branding.fontFamily};
        --border-radius: ${theme.borderRadius === 'none' ? '0' : 
                         theme.borderRadius === 'small' ? '4px' : 
                         theme.borderRadius === 'medium' ? '8px' : '12px'};
      }
      
      body {
        font-family: var(--font-family);
        background-color: var(--background-color);
        color: var(--text-color);
      }
      
      .portal-header {
        ${theme.headerStyle === 'fixed' ? 'position: fixed; top: 0; z-index: 1000;' : ''}
        ${theme.headerStyle === 'transparent' ? 'background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);' : ''}
      }
      
      .portal-sidebar {
        ${theme.sidebarStyle === 'collapsed' ? 'width: 60px;' : 'width: 250px;'}
        ${theme.sidebarStyle === 'overlay' ? 'position: absolute; z-index: 999;' : ''}
      }
      
      .portal-card {
        border-radius: var(--border-radius);
        ${theme.cardStyle === 'elevated' ? 'box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);' : ''}
        ${theme.cardStyle === 'outlined' ? 'border: 1px solid rgba(0, 0, 0, 0.1);' : ''}
        ${theme.cardStyle === 'flat' ? 'box-shadow: none; border: none;' : ''}
      }
      
      .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
      }
      
      .btn-secondary {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
      }
      
      .btn-accent {
        background-color: var(--accent-color);
        border-color: var(--accent-color);
      }
      
      ${branding.customCSS || ''}
    `;

    return cssVariables;
  },
});

// Check subdomain availability
export const checkSubdomainAvailability = query({
  args: { subdomain: v.string() },
  handler: async (ctx, args) => {
    const existingPortal = await ctx.db
      .query("portals")
      .withIndex("by_subdomain", (q) => q.eq("subdomain", args.subdomain))
      .first();

    return !existingPortal;
  },
});

// Check custom domain availability
export const checkCustomDomainAvailability = query({
  args: { customDomain: v.string() },
  handler: async (ctx, args) => {
    const existingPortal = await ctx.db
      .query("portals")
      .withIndex("by_custom_domain", (q) => q.eq("customDomain", args.customDomain))
      .first();

    return !existingPortal;
  },
});

// Get portal by tenant (for tenant portal access)
export const getPortalByTenant = query({
  args: { tenantId: v.id("users") },
  handler: async (ctx, args) => {
    // Get tenant's active lease
    const activeLease = await ctx.db
      .query("leases")
      .withIndex("by_tenant", (q) => q.eq("tenantId", args.tenantId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (!activeLease) {
      return null;
    }

    // Get portal for the property
    return await ctx.db
      .query("portals")
      .withIndex("by_property", (q) => q.eq("propertyId", activeLease.propertyId))
      .first();
  },
});

// Get portal analytics
export const getPortalAnalytics = query({
  args: { 
    portalId: v.id("portals"),
    dateRange: v.string()
  },
  handler: async (ctx, args) => {
    // In a real implementation, this would aggregate analytics data
    // For now, return mock data structure
    const endDate = new Date();
    const startDate = new Date();
    
    switch (args.dateRange) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }

    // Get analytics data for the date range
    const analytics = await ctx.db
      .query("portalAnalytics")
      .withIndex("by_portal", (q) => q.eq("portalId", args.portalId))
      .filter((q) => q.gte(q.field("createdAt"), startDate.getTime()))
      .filter((q) => q.lte(q.field("createdAt"), endDate.getTime()))
      .collect();

    return analytics;
  },
});

// Get portal users (tenants with access)
export const getPortalUsers = query({
  args: { portalId: v.id("portals") },
  handler: async (ctx, args) => {
    const portal = await ctx.db.get(args.portalId);
    if (!portal) return [];

    // Get all tenants with active leases for this property
    const activeLeases = await ctx.db
      .query("leases")
      .withIndex("by_property", (q) => q.eq("propertyId", portal.propertyId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    const tenantIds = activeLeases.map(lease => lease.tenantId);
    const users = [];

    for (const tenantId of tenantIds) {
      const user = await ctx.db.get(tenantId);
      if (user) {
        users.push(user);
      }
    }

    return users;
  },
});

// Record portal analytics event
export const recordAnalyticsEvent = mutation({
  args: {
    portalId: v.id("portals"),
    eventType: v.string(),
    eventData: v.object({
      page: v.optional(v.string()),
      userId: v.optional(v.id("users")),
      sessionId: v.optional(v.string()),
      userAgent: v.optional(v.string()),
      ipAddress: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    
    // Get or create today's analytics record
    let analyticsRecord = await ctx.db
      .query("portalAnalytics")
      .withIndex("by_portal_date", (q) => 
        q.eq("portalId", args.portalId).eq("date", today)
      )
      .first();

    if (!analyticsRecord) {
      // Create new analytics record for today
      analyticsRecord = {
        _id: await ctx.db.insert("portalAnalytics", {
          portalId: args.portalId,
          date: today,
          metrics: {
            pageViews: 0,
            uniqueVisitors: 0,
            sessions: 0,
            averageSessionDuration: 0,
            bounceRate: 0,
            topPages: [],
            deviceBreakdown: {
              desktop: 0,
              mobile: 0,
              tablet: 0,
            },
            browserBreakdown: [],
          },
          tenantEngagement: {
            activeUsers: 0,
            paymentTransactions: 0,
            maintenanceRequests: 0,
            documentDownloads: 0,
            messagesSent: 0,
          },
          createdAt: Date.now(),
        }),
        portalId: args.portalId,
        date: today,
        metrics: {
          pageViews: 0,
          uniqueVisitors: 0,
          sessions: 0,
          averageSessionDuration: 0,
          bounceRate: 0,
          topPages: [],
          deviceBreakdown: {
            desktop: 0,
            mobile: 0,
            tablet: 0,
          },
          browserBreakdown: [],
        },
        tenantEngagement: {
          activeUsers: 0,
          paymentTransactions: 0,
          maintenanceRequests: 0,
          documentDownloads: 0,
          messagesSent: 0,
        },
        createdAt: Date.now(),
      };
    }

    // Update analytics based on event type
    const updates: any = {};
    
    switch (args.eventType) {
      case 'page_view':
        updates['metrics.pageViews'] = (analyticsRecord.metrics?.pageViews || 0) + 1;
        break;
      case 'payment_transaction':
        updates['tenantEngagement.paymentTransactions'] = 
          (analyticsRecord.tenantEngagement?.paymentTransactions || 0) + 1;
        break;
      case 'maintenance_request':
        updates['tenantEngagement.maintenanceRequests'] = 
          (analyticsRecord.tenantEngagement?.maintenanceRequests || 0) + 1;
        break;
      case 'document_download':
        updates['tenantEngagement.documentDownloads'] = 
          (analyticsRecord.tenantEngagement?.documentDownloads || 0) + 1;
        break;
      case 'message_sent':
        updates['tenantEngagement.messagesSent'] = 
          (analyticsRecord.tenantEngagement?.messagesSent || 0) + 1;
        break;
    }

    if (Object.keys(updates).length > 0) {
      await ctx.db.patch(analyticsRecord._id, updates);
    }

    return analyticsRecord._id;
  },
});