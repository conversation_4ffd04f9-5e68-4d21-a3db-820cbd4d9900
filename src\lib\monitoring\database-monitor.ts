import { ConvexReactClient } from 'convex/react';
import PerformanceMonitor from './performance-monitor';

export interface QueryMetrics {
  queryName: string;
  duration: number;
  success: boolean;
  timestamp: number;
  args?: any;
  result?: any;
  error?: string;
}

export interface QueryOptimizationSuggestion {
  queryName: string;
  issue: string;
  suggestion: string;
  severity: 'low' | 'medium' | 'high';
}

export class DatabaseMonitor {
  private static instance: DatabaseMonitor;
  private performanceMonitor: PerformanceMonitor;
  private queryMetrics: Map<string, QueryMetrics[]> = new Map();
  private slowQueryThreshold = 500; // milliseconds
  private verySlowQueryThreshold = 1000; // milliseconds

  private constructor() {
    this.performanceMonitor = PerformanceMonitor.getInstance();
  }

  static getInstance(): DatabaseMonitor {
    if (!DatabaseMonitor.instance) {
      DatabaseMonitor.instance = new DatabaseMonitor();
    }
    return DatabaseMonitor.instance;
  }

  // Wrap Convex queries with monitoring
  monitorQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    args?: any
  ): Promise<T> {
    return this.performanceMonitor.monitorDatabaseQuery(
      queryName,
      queryFn,
      { args }
    );
  }

  // Record query metrics
  recordQuery(metrics: QueryMetrics): void {
    if (!this.queryMetrics.has(metrics.queryName)) {
      this.queryMetrics.set(metrics.queryName, []);
    }

    const queries = this.queryMetrics.get(metrics.queryName)!;
    queries.push(metrics);

    // Keep only last 1000 queries per type
    if (queries.length > 1000) {
      queries.shift();
    }

    // Analyze for optimization opportunities
    this.analyzeQuery(metrics);
  }

  private analyzeQuery(metrics: QueryMetrics): void {
    const suggestions: QueryOptimizationSuggestion[] = [];

    // Check for slow queries
    if (metrics.duration > this.verySlowQueryThreshold) {
      suggestions.push({
        queryName: metrics.queryName,
        issue: `Very slow query: ${metrics.duration}ms`,
        suggestion: 'Consider adding indexes, reducing data fetched, or optimizing query logic',
        severity: 'high',
      });
    } else if (metrics.duration > this.slowQueryThreshold) {
      suggestions.push({
        queryName: metrics.queryName,
        issue: `Slow query: ${metrics.duration}ms`,
        suggestion: 'Review query efficiency and consider optimization',
        severity: 'medium',
      });
    }

    // Check for frequent queries
    const recentQueries = this.getRecentQueries(metrics.queryName, 60000); // Last minute
    if (recentQueries.length > 100) {
      suggestions.push({
        queryName: metrics.queryName,
        issue: `High frequency query: ${recentQueries.length} calls in last minute`,
        suggestion: 'Consider caching or batching this query',
        severity: 'medium',
      });
    }

    // Check for large result sets (if available)
    if (metrics.result && Array.isArray(metrics.result) && metrics.result.length > 1000) {
      suggestions.push({
        queryName: metrics.queryName,
        issue: `Large result set: ${metrics.result.length} items`,
        suggestion: 'Implement pagination or filtering to reduce data transfer',
        severity: 'medium',
      });
    }

    // Log suggestions
    suggestions.forEach(suggestion => {
      console.warn(`Database Optimization Suggestion:`, suggestion);
    });
  }

  private getRecentQueries(queryName: string, timeWindow: number): QueryMetrics[] {
    const queries = this.queryMetrics.get(queryName) || [];
    const cutoff = Date.now() - timeWindow;
    return queries.filter(q => q.timestamp > cutoff);
  }

  // Get query statistics
  getQueryStats(queryName?: string): Record<string, any> {
    if (queryName) {
      const queries = this.queryMetrics.get(queryName) || [];
      return this.calculateStats(queries);
    }

    const allStats: Record<string, any> = {};
    for (const [name, queries] of this.queryMetrics.entries()) {
      allStats[name] = this.calculateStats(queries);
    }
    return allStats;
  }

  private calculateStats(queries: QueryMetrics[]): any {
    if (queries.length === 0) {
      return { count: 0, avgDuration: 0, minDuration: 0, maxDuration: 0, successRate: 0 };
    }

    const durations = queries.map(q => q.duration);
    const successCount = queries.filter(q => q.success).length;

    return {
      count: queries.length,
      avgDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      successRate: (successCount / queries.length) * 100,
      slowQueries: queries.filter(q => q.duration > this.slowQueryThreshold).length,
      verySlowQueries: queries.filter(q => q.duration > this.verySlowQueryThreshold).length,
    };
  }

  // Get optimization suggestions
  getOptimizationSuggestions(): QueryOptimizationSuggestion[] {
    const suggestions: QueryOptimizationSuggestion[] = [];
    
    for (const [queryName, queries] of this.queryMetrics.entries()) {
      const stats = this.calculateStats(queries);
      
      // Suggest optimization for consistently slow queries
      if (stats.avgDuration > this.slowQueryThreshold) {
        suggestions.push({
          queryName,
          issue: `Consistently slow average duration: ${stats.avgDuration.toFixed(2)}ms`,
          suggestion: 'Review query logic and consider adding database indexes',
          severity: stats.avgDuration > this.verySlowQueryThreshold ? 'high' : 'medium',
        });
      }

      // Suggest optimization for queries with low success rate
      if (stats.successRate < 95 && stats.count > 10) {
        suggestions.push({
          queryName,
          issue: `Low success rate: ${stats.successRate.toFixed(1)}%`,
          suggestion: 'Review error handling and query reliability',
          severity: 'medium',
        });
      }

      // Suggest caching for frequently called queries
      if (stats.count > 1000) {
        suggestions.push({
          queryName,
          issue: `High call frequency: ${stats.count} calls`,
          suggestion: 'Consider implementing caching or query result memoization',
          severity: 'low',
        });
      }
    }

    return suggestions.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  }

  // Performance monitoring dashboard data
  getDashboardData(): any {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const oneDay = 24 * oneHour;

    const recentQueries = this.getAllQueriesInTimeRange(now - oneHour);
    const dailyQueries = this.getAllQueriesInTimeRange(now - oneDay);

    return {
      summary: {
        totalQueries: dailyQueries.length,
        recentQueries: recentQueries.length,
        avgDuration: this.calculateAverageDuration(recentQueries),
        slowQueries: recentQueries.filter(q => q.duration > this.slowQueryThreshold).length,
        errorRate: this.calculateErrorRate(recentQueries),
      },
      topSlowQueries: this.getTopSlowQueries(recentQueries, 10),
      mostFrequentQueries: this.getMostFrequentQueries(recentQueries, 10),
      optimizationSuggestions: this.getOptimizationSuggestions().slice(0, 5),
    };
  }

  private getAllQueriesInTimeRange(cutoff: number): QueryMetrics[] {
    const allQueries: QueryMetrics[] = [];
    for (const queries of this.queryMetrics.values()) {
      allQueries.push(...queries.filter(q => q.timestamp > cutoff));
    }
    return allQueries.sort((a, b) => b.timestamp - a.timestamp);
  }

  private calculateAverageDuration(queries: QueryMetrics[]): number {
    if (queries.length === 0) return 0;
    return queries.reduce((sum, q) => sum + q.duration, 0) / queries.length;
  }

  private calculateErrorRate(queries: QueryMetrics[]): number {
    if (queries.length === 0) return 0;
    const errorCount = queries.filter(q => !q.success).length;
    return (errorCount / queries.length) * 100;
  }

  private getTopSlowQueries(queries: QueryMetrics[], limit: number): QueryMetrics[] {
    return queries
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit);
  }

  private getMostFrequentQueries(queries: QueryMetrics[], limit: number): any[] {
    const frequency: Record<string, number> = {};
    queries.forEach(q => {
      frequency[q.queryName] = (frequency[q.queryName] || 0) + 1;
    });

    return Object.entries(frequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([queryName, count]) => ({ queryName, count }));
  }

  // Configuration
  setSlowQueryThreshold(threshold: number): void {
    this.slowQueryThreshold = threshold;
  }

  setVerySlowQueryThreshold(threshold: number): void {
    this.verySlowQueryThreshold = threshold;
  }

  // Clear metrics
  clearMetrics(queryName?: string): void {
    if (queryName) {
      this.queryMetrics.delete(queryName);
    } else {
      this.queryMetrics.clear();
    }
  }
}

// Hook for monitoring Convex queries
export function useMonitoredQuery<T>(
  queryName: string,
  queryFn: () => T,
  args?: any
): T {
  const monitor = DatabaseMonitor.getInstance();
  
  // For synchronous queries (useQuery), we can't easily wrap them
  // This would need to be implemented at the Convex client level
  // For now, we'll just return the query result
  return queryFn();
}

export default DatabaseMonitor;