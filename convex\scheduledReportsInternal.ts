import { internalMutation, internalQuery } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

// Process scheduled reports - called by cron job
export const processScheduledReports = internalMutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    const today = new Date(now);
    
    // Get all active report configurations with scheduling
    const reportConfigs = await ctx.db
      .query("reportConfigurations")
      .filter(q => q.eq(q.field("isActive"), true))
      .collect();

    const scheduledConfigs = reportConfigs.filter(config => config.schedule);

    for (const config of scheduledConfigs) {
      const schedule = config.schedule!;
      let shouldGenerate = false;

      // Check if report should be generated today
      switch (schedule.frequency) {
        case "weekly":
          if (schedule.dayOfWeek !== undefined && today.getDay() === schedule.dayOfWeek) {
            shouldGenerate = true;
          }
          break;
        
        case "monthly":
          if (schedule.dayOfMonth !== undefined && today.getDate() === schedule.dayOfMonth) {
            shouldGenerate = true;
          }
          break;
        
        case "quarterly":
          // Generate on the 1st of every quarter (Jan, Apr, Jul, Oct)
          if (today.getDate() === 1 && [0, 3, 6, 9].includes(today.getMonth())) {
            shouldGenerate = true;
          }
          break;
      }

      if (shouldGenerate) {
        try {
          // Generate report directly for now - can be moved to separate function later
          await generateAndDistributeReportHelper(ctx, config._id);
        } catch (error) {
          console.error(`Failed to generate report for config ${config._id}:`, error);
        }
      }
    }
  },
});

// Generate and distribute a specific report
export const generateAndDistributeReport = internalMutation({
  args: {
    configId: v.id("reportConfigurations"),
  },
  handler: async (ctx, args) => {
    const config = await ctx.db.get(args.configId);
    if (!config || !config.isActive || !config.schedule) {
      return;
    }

    try {
      // Calculate date range based on config
      const now = Date.now();
      let startDate: number, endDate: number;

      switch (config.filters.dateRange.type) {
        case "monthly":
          const lastMonth = new Date(now);
          lastMonth.setMonth(lastMonth.getMonth() - 1);
          lastMonth.setDate(1);
          lastMonth.setHours(0, 0, 0, 0);
          startDate = lastMonth.getTime();
          
          const endOfLastMonth = new Date(lastMonth);
          endOfLastMonth.setMonth(endOfLastMonth.getMonth() + 1);
          endOfLastMonth.setDate(0);
          endOfLastMonth.setHours(23, 59, 59, 999);
          endDate = endOfLastMonth.getTime();
          break;
        
        case "quarterly":
          const currentQuarter = Math.floor(new Date(now).getMonth() / 3);
          const quarterStart = new Date(new Date(now).getFullYear(), (currentQuarter - 1) * 3, 1);
          quarterStart.setHours(0, 0, 0, 0);
          startDate = quarterStart.getTime();
          
          const quarterEnd = new Date(quarterStart);
          quarterEnd.setMonth(quarterEnd.getMonth() + 3);
          quarterEnd.setDate(0);
          quarterEnd.setHours(23, 59, 59, 999);
          endDate = quarterEnd.getTime();
          break;
        
        case "annual":
          const yearStart = new Date(new Date(now).getFullYear() - 1, 0, 1);
          yearStart.setHours(0, 0, 0, 0);
          startDate = yearStart.getTime();
          
          const yearEnd = new Date(new Date(now).getFullYear() - 1, 11, 31);
          yearEnd.setHours(23, 59, 59, 999);
          endDate = yearEnd.getTime();
          break;
        
        default:
          startDate = now - (30 * 24 * 60 * 60 * 1000); // 30 days ago
          endDate = now;
      }

      // Generate the report data
      const reportData = await generateExecutiveSummaryInternal(ctx, {
        propertyIds: config.filters.propertyIds,
        startDate,
        endDate,
        reportType: config.filters.dateRange.type === "custom" ? "monthly" : config.filters.dateRange.type,
      });

      // Create report record
      const reportRecord = await ctx.db.insert("generatedReports", {
        configId: config._id,
        name: config.name,
        generatedAt: now,
        period: { startDate, endDate },
        data: reportData,
        status: "generated",
        recipients: config.schedule.recipients,
      });

      // Send notifications to recipients
      for (const recipient of config.schedule.recipients) {
        await sendReportNotificationHelper(ctx, {
          reportId: reportRecord,
          recipient,
          reportName: config.name,
        });
      }

      // Update config with last generation time
      await ctx.db.patch(config._id, {
        updatedAt: now,
      });

    } catch (error) {
      console.error(`Failed to generate report for config ${config._id}:`, error);
    }
  },
});

// Send report notification to recipient
export const sendReportNotification = internalMutation({
  args: {
    reportId: v.id("generatedReports"),
    recipient: v.string(),
    reportName: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Create notification message
      const message = `Your scheduled report "${args.reportName}" has been generated and is ready for download. Access it through your EstatePulse dashboard.`;
      
      // Insert notification (this would integrate with your existing notification system)
      await ctx.db.insert("notifications", {
        type: "report_generated",
        recipient: args.recipient,
        subject: `Report Ready: ${args.reportName}`,
        message,
        data: {
          reportId: args.reportId,
          reportName: args.reportName,
        },
        status: "pending",
        createdAt: Date.now(),
      });

    } catch (error) {
      console.error(`Failed to send notification for report ${args.reportId}:`, error);
    }
  },
});

// Internal version of executive summary generation
const generateExecutiveSummaryInternal = async (ctx: any, args: {
  propertyIds?: string[];
  startDate: number;
  endDate: number;
  reportType: "monthly" | "quarterly" | "annual";
}) => {
  const { startDate, endDate, propertyIds, reportType } = args;

  // Get all properties if none specified
  let properties = await ctx.db.query("properties").collect();
  if (propertyIds && propertyIds.length > 0) {
    properties = properties.filter((p: any) => propertyIds.includes(p._id));
  }

  // Get all relevant data
  const allInvoices = await ctx.db.query("invoices").collect();
  const allPayments = await ctx.db.query("payments").collect();
  const allTickets = await ctx.db.query("maintenanceTickets").collect();
  const allLeases = await ctx.db.query("leases").collect();
  const allUnits = await ctx.db.query("units").collect();

  // Filter data by date range and properties
  const filteredInvoices = allInvoices.filter((invoice: any) => 
    invoice.createdAt >= startDate && 
    invoice.createdAt <= endDate &&
    (!propertyIds || propertyIds.length === 0 || propertyIds.includes(invoice.propertyId))
  );

  const filteredPayments = allPayments.filter((payment: any) => 
    payment.createdAt >= startDate && payment.createdAt <= endDate
  );

  const filteredTickets = allTickets.filter((ticket: any) => 
    ticket.createdAt >= startDate && 
    ticket.createdAt <= endDate &&
    (!propertyIds || propertyIds.length === 0 || propertyIds.includes(ticket.propertyId))
  );

  const activeLeases = allLeases.filter((lease: any) => 
    lease.status === "active" &&
    (!propertyIds || propertyIds.length === 0 || propertyIds.includes(lease.propertyId))
  );

  // Calculate key metrics
  const totalRevenue = filteredInvoices
    .filter((invoice: any) => invoice.status === "paid")
    .reduce((sum: number, invoice: any) => sum + invoice.amount, 0);

  const totalExpenses = filteredTickets
    .filter((ticket: any) => ticket.actualCost && ticket.status === "completed")
    .reduce((sum: number, ticket: any) => sum + (ticket.actualCost || 0), 0);

  const netIncome = totalRevenue - totalExpenses;
  const profitMargin = totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0;

  // Calculate occupancy metrics
  const totalUnits = allUnits.filter((unit: any) => 
    !propertyIds || propertyIds.length === 0 || propertyIds.includes(unit.propertyId)
  ).length;

  const occupiedUnits = activeLeases.length;
  const occupancyRate = totalUnits > 0 ? (occupiedUnits / totalUnits) * 100 : 0;

  // Collection metrics
  const totalInvoiced = filteredInvoices.reduce((sum: number, invoice: any) => sum + invoice.amount, 0);
  const collectionRate = totalInvoiced > 0 ? (totalRevenue / totalInvoiced) * 100 : 0;

  const pendingAmount = filteredInvoices
    .filter((invoice: any) => invoice.status === "pending")
    .reduce((sum: number, invoice: any) => sum + invoice.amount, 0);

  const overdueAmount = filteredInvoices
    .filter((invoice: any) => invoice.status === "overdue")
    .reduce((sum: number, invoice: any) => sum + invoice.amount, 0);

  return {
    reportMetadata: {
      generatedAt: Date.now(),
      period: { startDate, endDate },
      reportType,
      propertyCount: properties.length,
      totalProperties: properties.length,
    },
    executiveSummary: {
      totalRevenue,
      totalExpenses,
      netIncome,
      profitMargin,
      revenueGrowth: 0, // Simplified for scheduled reports
      occupancyRate,
      collectionRate,
    },
    keyMetrics: {
      financial: {
        totalRevenue,
        totalExpenses,
        netIncome,
        profitMargin,
        pendingAmount,
        overdueAmount,
        collectionRate,
      },
      operational: {
        totalUnits,
        occupiedUnits,
        occupancyRate,
        totalTickets: filteredTickets.length,
        completedTickets: filteredTickets.filter((t: any) => t.status === "completed").length,
        avgResolutionTime: 0, // Simplified calculation
      },
      payments: {
        totalPayments: filteredPayments.length,
        mpesaPayments: filteredPayments.filter((p: any) => p.method === "mpesa").length,
        stripePayments: filteredPayments.filter((p: any) => p.method === "stripe").length,
        successRate: filteredPayments.length > 0 ? 
          (filteredPayments.filter((p: any) => p.status === "completed").length / filteredPayments.length) * 100 : 0,
      },
    },
  };
};

// Helper function to generate and distribute report
const generateAndDistributeReportHelper = async (ctx: any, configId: string) => {
  const config = await ctx.db.get(configId);
  if (!config || !config.isActive || !config.schedule) {
    return;
  }

  try {
    // Calculate date range based on config
    const now = Date.now();
    let startDate: number, endDate: number;

    switch (config.filters.dateRange.type) {
      case "monthly":
        const lastMonth = new Date(now);
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        lastMonth.setDate(1);
        lastMonth.setHours(0, 0, 0, 0);
        startDate = lastMonth.getTime();
        
        const endOfLastMonth = new Date(lastMonth);
        endOfLastMonth.setMonth(endOfLastMonth.getMonth() + 1);
        endOfLastMonth.setDate(0);
        endOfLastMonth.setHours(23, 59, 59, 999);
        endDate = endOfLastMonth.getTime();
        break;
      
      case "quarterly":
        const currentQuarter = Math.floor(new Date(now).getMonth() / 3);
        const quarterStart = new Date(new Date(now).getFullYear(), (currentQuarter - 1) * 3, 1);
        quarterStart.setHours(0, 0, 0, 0);
        startDate = quarterStart.getTime();
        
        const quarterEnd = new Date(quarterStart);
        quarterEnd.setMonth(quarterEnd.getMonth() + 3);
        quarterEnd.setDate(0);
        quarterEnd.setHours(23, 59, 59, 999);
        endDate = quarterEnd.getTime();
        break;
      
      case "annual":
        const yearStart = new Date(new Date(now).getFullYear() - 1, 0, 1);
        yearStart.setHours(0, 0, 0, 0);
        startDate = yearStart.getTime();
        
        const yearEnd = new Date(new Date(now).getFullYear() - 1, 11, 31);
        yearEnd.setHours(23, 59, 59, 999);
        endDate = yearEnd.getTime();
        break;
      
      default:
        startDate = now - (30 * 24 * 60 * 60 * 1000); // 30 days ago
        endDate = now;
    }

    // Generate the report data
    const reportData = await generateExecutiveSummaryInternal(ctx, {
      propertyIds: config.filters.propertyIds,
      startDate,
      endDate,
      reportType: config.filters.dateRange.type === "custom" ? "monthly" : config.filters.dateRange.type,
    });

    // Create report record
    const reportRecord = await ctx.db.insert("generatedReports", {
      configId: config._id,
      name: config.name,
      generatedAt: now,
      period: { startDate, endDate },
      data: reportData,
      status: "generated",
      recipients: config.schedule.recipients,
    });

    // Send notifications to recipients
    for (const recipient of config.schedule.recipients) {
      await sendReportNotificationHelper(ctx, {
        reportId: reportRecord,
        recipient,
        reportName: config.name,
      });
    }

    // Update config with last generation time
    await ctx.db.patch(config._id, {
      updatedAt: now,
    });

  } catch (error) {
    console.error(`Failed to generate report for config ${configId}:`, error);
  }
};

// Helper function to send report notification
const sendReportNotificationHelper = async (ctx: any, args: {
  reportId: string;
  recipient: string;
  reportName: string;
}) => {
  try {
    // Create notification message
    const message = `Your scheduled report "${args.reportName}" has been generated and is ready for download. Access it through your EstatePulse dashboard.`;
    
    // Insert notification
    await ctx.db.insert("notifications", {
      type: "report_generated",
      recipient: args.recipient,
      subject: `Report Ready: ${args.reportName}`,
      message,
      data: {
        reportId: args.reportId,
        reportName: args.reportName,
      },
      status: "pending",
      createdAt: Date.now(),
    });

  } catch (error) {
    console.error(`Failed to send notification for report ${args.reportId}:`, error);
  }
};
