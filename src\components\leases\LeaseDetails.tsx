import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { <PERSON><PERSON> } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { 
  FileText, 
  Calendar, 
  DollarSign, 
  User, 
  Building, 
  MapPin,
  Phone,
  Mail,
  Download,
  RefreshCw,
  XCircle,
  CheckCircle,
  AlertTriangle,
  Edit
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '../ui/alert-dialog';

interface LeaseDetailsProps {
  leaseId: Id<"leases">;
  onEdit?: () => void;
  onClose?: () => void;
}

export function LeaseDetails({ leaseId, onEdit, onClose }: LeaseDetailsProps) {
  const leaseData = useQuery(api.leases.getLeaseById, { id: leaseId });
  const updateLeaseStatus = useMutation(api.leases.updateLeaseStatus);
  const renewLease = useMutation(api.leases.renewLease);
  const terminateLease = useMutation(api.leases.terminateLease);

  const [isUpdating, setIsUpdating] = useState(false);
  const [showRenewalForm, setShowRenewalForm] = useState(false);

  if (!leaseData) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { lease, property, unit, tenant } = leaseData;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
        return 'bg-gray-100 text-gray-800';
      case 'terminated':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getESignatureStatusColor = (status: string) => {
    switch (status) {
      case 'signed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getDaysUntilExpiry = () => {
    const days = Math.ceil((lease.endDate - Date.now()) / (1000 * 60 * 60 * 24));
    return days;
  };

  const handleActivateLease = async () => {
    setIsUpdating(true);
    try {
      await updateLeaseStatus({
        id: leaseId,
        status: 'active',
      });
    } catch (error) {
      console.error('Error activating lease:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleTerminateLease = async () => {
    setIsUpdating(true);
    try {
      await terminateLease({
        id: leaseId,
        terminationDate: Date.now(),
        reason: 'Manual termination',
        earlyTermination: lease.endDate > Date.now(),
      });
    } catch (error) {
      console.error('Error terminating lease:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const daysUntilExpiry = getDaysUntilExpiry();
  const isExpiringSoon = daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  const isExpired = daysUntilExpiry <= 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-2xl font-bold">Lease Details</h2>
          <p className="text-gray-600">Lease #{lease._id.slice(-8)}</p>
        </div>
        <div className="flex gap-2">
          {onEdit && (
            <Button variant="outline" onClick={onEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
          {onClose && (
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
      </div>

      {/* Status and Actions */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Lease Status
            </CardTitle>
            <div className="flex gap-2">
              <Badge className={getStatusColor(lease.status)}>
                {lease.status}
              </Badge>
              <Badge className={getESignatureStatusColor(lease.eSignatureStatus)}>
                {lease.eSignatureStatus === 'signed' ? 'Signed' : 
                 lease.eSignatureStatus === 'pending' ? 'Pending Signature' : 'Signature Expired'}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Expiry Warning */}
          {isExpiringSoon && (
            <div className="flex items-center gap-2 p-3 bg-yellow-100 border border-yellow-200 rounded-md mb-4">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <p className="text-sm text-yellow-800">
                <strong>Expires in {daysUntilExpiry} days</strong> - Consider renewal or termination
              </p>
            </div>
          )}

          {isExpired && lease.status === 'active' && (
            <div className="flex items-center gap-2 p-3 bg-red-100 border border-red-200 rounded-md mb-4">
              <XCircle className="h-4 w-4 text-red-600" />
              <p className="text-sm text-red-800">
                <strong>Lease has expired</strong> - Update status or renew
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            {lease.status === 'pending' && lease.eSignatureStatus === 'signed' && (
              <Button onClick={handleActivateLease} disabled={isUpdating}>
                <CheckCircle className="h-4 w-4 mr-2" />
                Activate Lease
              </Button>
            )}

            {lease.status === 'active' && lease.terms.renewalOption && (
              <Button variant="outline" onClick={() => setShowRenewalForm(true)}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Renew Lease
              </Button>
            )}

            {lease.status === 'active' && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    <XCircle className="h-4 w-4 mr-2" />
                    Terminate Lease
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Terminate Lease</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to terminate this lease? This action cannot be undone.
                      The unit will be marked as vacant and the tenant will be notified.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleTerminateLease} disabled={isUpdating}>
                      Terminate
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}

            {lease.documentUrl && (
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download Document
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Lease Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Property & Unit Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Property & Unit
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {property && (
              <div>
                <h4 className="font-semibold mb-2">Property Details</h4>
                <div className="space-y-2 text-sm">
                  <p><strong>Name:</strong> {property.name}</p>
                  <p><strong>Type:</strong> {property.type}</p>
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 mt-0.5 text-gray-400" />
                    <div>
                      <p>{property.address.street}</p>
                      <p>{property.address.city}, {property.address.state} {property.address.postalCode}</p>
                      <p>{property.address.country}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <Separator />

            {unit && (
              <div>
                <h4 className="font-semibold mb-2">Unit Details</h4>
                <div className="space-y-2 text-sm">
                  <p><strong>Unit Number:</strong> {unit.unitNumber}</p>
                  <p><strong>Type:</strong> {unit.type}</p>
                  <p><strong>Size:</strong> {unit.size} sq ft</p>
                  {unit.bedrooms && <p><strong>Bedrooms:</strong> {unit.bedrooms}</p>}
                  {unit.bathrooms && <p><strong>Bathrooms:</strong> {unit.bathrooms}</p>}
                  <p><strong>Status:</strong> {unit.status}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tenant Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Tenant Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            {tenant && (
              <div className="space-y-3">
                <div>
                  <h4 className="font-semibold">{tenant.name}</h4>
                  <p className="text-sm text-gray-600">Tenant</p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">{tenant.email}</span>
                  </div>
                  {tenant.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">{tenant.phone}</span>
                    </div>
                  )}
                </div>

                <div className="pt-2">
                  <p className="text-sm">
                    <strong>KYC Status:</strong>{' '}
                    <Badge variant={tenant.kycStatus === 'verified' ? 'default' : 'secondary'}>
                      {tenant.kycStatus}
                    </Badge>
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Financial Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Financial Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <p className="text-sm text-gray-600">Monthly Rent</p>
              <p className="text-2xl font-bold">{formatCurrency(lease.monthlyRent)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Security Deposit</p>
              <p className="text-2xl font-bold">{formatCurrency(lease.deposit)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Lease Value</p>
              <p className="text-2xl font-bold">
                {formatCurrency(lease.monthlyRent * Math.ceil((lease.endDate - lease.startDate) / (1000 * 60 * 60 * 24 * 30)))}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Late Fee</p>
              <p className="text-2xl font-bold">{lease.terms.lateFeePercentage}%</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lease Terms */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Lease Terms
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">Lease Period</p>
                <p className="font-semibold">
                  {formatDate(lease.startDate)} - {formatDate(lease.endDate)}
                </p>
                <p className="text-sm text-gray-500">
                  {Math.ceil((lease.endDate - lease.startDate) / (1000 * 60 * 60 * 24))} days
                </p>
              </div>
              
              <div>
                <p className="text-sm text-gray-600">Notice Period</p>
                <p className="font-semibold">{lease.terms.noticePeriod} days</p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">Grace Period</p>
                <p className="font-semibold">{lease.terms.gracePeriod} days</p>
              </div>
              
              <div>
                <p className="text-sm text-gray-600">Renewal Option</p>
                <p className="font-semibold">
                  {lease.terms.renewalOption ? 'Available' : 'Not Available'}
                </p>
              </div>
            </div>
          </div>

          <Separator className="my-4" />

          <div className="text-sm text-gray-600">
            <p><strong>Created:</strong> {formatDate(lease.createdAt)}</p>
            <p><strong>Last Updated:</strong> {formatDate(lease.updatedAt)}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}