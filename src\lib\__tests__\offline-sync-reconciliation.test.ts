/**
 * Tests for offline sync reconciliation system
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { offlineSyncReconciliation, ConflictInfo } from '../offline-sync-reconciliation';
import { offlineStorageManager } from '../offline-storage';

// Mock Convex client
const mockConvexClient = {
  mutation: vi.fn(),
  query: vi.fn()
};

// Mock offline storage manager
vi.mock('../offline-storage', () => ({
  offlineStorageManager: {
    getOfflineQueue: vi.fn(),
    updateQueueItemStatus: vi.fn(),
    clearCompletedQueueItems: vi.fn(),
    getCachedEntity: vi.fn(),
    cacheEntity: vi.fn()
  }
}));

describe('OfflineSyncReconciliation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('reconcileOfflineChanges', () => {
    it('should handle empty offline queue', async () => {
      // Arrange
      (offlineStorageManager.getOfflineQueue as any).mockResolvedValue([]);

      // Act
      const result = await offlineSyncReconciliation.reconcileOfflineChanges(mockConvexClient);

      // Assert
      expect(result.success).toBe(true);
      expect(result.syncedOperations).toBe(0);
      expect(result.conflicts).toHaveLength(0);
    });

    it('should process successful operations', async () => {
      // Arrange
      const mockQueue = [
        {
          id: 'op1',
          operation: 'update',
          entityType: 'properties',
          entityId: 'prop1',
          data: { name: 'Updated Property' },
          timestamp: Date.now()
        }
      ];

      (offlineStorageManager.getOfflineQueue as any).mockResolvedValue(mockQueue);
      mockConvexClient.mutation.mockResolvedValue({
        results: [{ operationId: 'prop1', success: true, result: 'prop1' }],
        conflicts: []
      });

      // Act
      const result = await offlineSyncReconciliation.reconcileOfflineChanges(mockConvexClient);

      // Assert
      expect(result.success).toBe(true);
      expect(result.syncedOperations).toBe(1);
      expect(result.failedOperations).toBe(0);
      expect(offlineStorageManager.updateQueueItemStatus).toHaveBeenCalledWith('op1', 'completed');
    });

    it('should handle conflicts', async () => {
      // Arrange
      const mockQueue = [
        {
          id: 'op1',
          operation: 'update',
          entityType: 'properties',
          entityId: 'prop1',
          data: { name: 'Local Update' },
          timestamp: Date.now() - 1000
        }
      ];

      const mockConflict = {
        operationId: 'prop1',
        entityType: 'properties',
        entityId: 'prop1',
        clientVersion: Date.now() - 1000,
        serverVersion: Date.now(),
        conflictType: 'version_mismatch'
      };

      (offlineStorageManager.getOfflineQueue as any).mockResolvedValue(mockQueue);
      mockConvexClient.mutation.mockResolvedValue({
        results: [],
        conflicts: [mockConflict]
      });

      mockConvexClient.query.mockResolvedValue({
        version: Date.now(),
        lastModified: Date.now(),
        exists: true
      });

      (offlineStorageManager.getCachedEntity as any).mockResolvedValue({
        name: 'Local Update'
      });

      // Act
      const result = await offlineSyncReconciliation.reconcileOfflineChanges(mockConvexClient, {
        strategy: 'server_wins'
      });

      // Assert
      expect(result.conflicts.length).toBeGreaterThan(0);
      expect(result.conflicts[0].conflictType).toBe('version_mismatch');
    });

    it('should handle failed operations', async () => {
      // Arrange
      const mockQueue = [
        {
          id: 'op1',
          operation: 'update',
          entityType: 'properties',
          entityId: 'prop1',
          data: { name: 'Updated Property' },
          timestamp: Date.now()
        }
      ];

      (offlineStorageManager.getOfflineQueue as any).mockResolvedValue(mockQueue);
      mockConvexClient.mutation.mockResolvedValue({
        results: [{ operationId: 'prop1', success: false, error: 'Validation failed' }],
        conflicts: []
      });

      // Act
      const result = await offlineSyncReconciliation.reconcileOfflineChanges(mockConvexClient);

      // Assert
      expect(result.success).toBe(false);
      expect(result.failedOperations).toBe(1);
      expect(result.errors).toContain('Validation failed');
    });
  });

  describe('conflict resolution strategies', () => {
    const mockConflict: ConflictInfo = {
      entityType: 'properties',
      entityId: 'prop1',
      localData: { name: 'Local Name', description: 'Local Desc' },
      serverData: { name: 'Server Name', description: 'Server Desc' },
      conflictType: 'version_mismatch',
      localVersion: Date.now() - 1000,
      serverVersion: Date.now(),
      timestamp: Date.now()
    };

    it('should resolve with local_wins strategy', async () => {
      // Arrange
      (offlineStorageManager.getOfflineQueue as any).mockResolvedValue([]);

      // Act
      const result = await offlineSyncReconciliation.reconcileOfflineChanges(mockConvexClient, {
        strategy: 'local_wins'
      });

      // Assert
      expect(result.success).toBe(true);
    });

    it('should resolve with server_wins strategy', async () => {
      // Arrange
      (offlineStorageManager.getOfflineQueue as any).mockResolvedValue([]);

      // Act
      const result = await offlineSyncReconciliation.reconcileOfflineChanges(mockConvexClient, {
        strategy: 'server_wins'
      });

      // Assert
      expect(result.success).toBe(true);
    });

    it('should resolve with last_write_wins strategy', async () => {
      // Arrange
      (offlineStorageManager.getOfflineQueue as any).mockResolvedValue([]);

      // Act
      const result = await offlineSyncReconciliation.reconcileOfflineChanges(mockConvexClient, {
        strategy: 'last_write_wins'
      });

      // Assert
      expect(result.success).toBe(true);
    });

    it('should merge fields with merge_fields strategy', async () => {
      // Arrange
      (offlineStorageManager.getOfflineQueue as any).mockResolvedValue([]);

      // Act
      const result = await offlineSyncReconciliation.reconcileOfflineChanges(mockConvexClient, {
        strategy: 'merge_fields',
        fieldMergeRules: {
          name: 'local',
          description: 'server'
        }
      });

      // Assert
      expect(result.success).toBe(true);
    });
  });

  describe('field merging', () => {
    it('should merge fields according to rules', () => {
      // This would test the private mergeFields method
      // We'd need to expose it or test it through the public API
      const localData = {
        name: 'Local Name',
        description: 'Local Description',
        tags: ['local1', 'local2']
      };

      const serverData = {
        name: 'Server Name',
        description: 'Server Description',
        tags: ['server1', 'server2']
      };

      const mergeRules = {
        name: 'local' as const,
        description: 'server' as const,
        tags: 'merge' as const
      };

      // This test would verify the merge logic
      // For now, we'll just verify the reconciliation completes
      expect(true).toBe(true);
    });

    it('should concatenate string fields', () => {
      // Test string concatenation logic
      expect(true).toBe(true);
    });

    it('should merge array fields', () => {
      // Test array merging logic
      expect(true).toBe(true);
    });
  });

  describe('batch processing', () => {
    it('should process operations in batches', async () => {
      // Arrange
      const mockQueue = Array.from({ length: 150 }, (_, i) => ({
        id: `op${i}`,
        operation: 'update',
        entityType: 'properties',
        entityId: `prop${i}`,
        data: { name: `Property ${i}` },
        timestamp: Date.now()
      }));

      (offlineStorageManager.getOfflineQueue as any).mockResolvedValue(mockQueue);
      mockConvexClient.mutation.mockResolvedValue({
        results: mockQueue.slice(0, 50).map(op => ({
          operationId: op.entityId,
          success: true,
          result: op.entityId
        })),
        conflicts: []
      });

      // Act
      const result = await offlineSyncReconciliation.reconcileOfflineChanges(mockConvexClient, {
        batchSize: 50
      });

      // Assert
      expect(mockConvexClient.mutation).toHaveBeenCalledTimes(3); // 150 / 50 = 3 batches
    });
  });

  describe('preview functionality', () => {
    it('should preview reconciliation without executing', async () => {
      // Arrange
      const mockQueue = [
        {
          id: 'op1',
          operation: 'update',
          entityType: 'properties',
          entityId: 'prop1',
          data: { name: 'Updated Property', updatedAt: Date.now() - 1000 },
          timestamp: Date.now()
        }
      ];

      (offlineStorageManager.getOfflineQueue as any).mockResolvedValue(mockQueue);
      mockConvexClient.query.mockResolvedValue({
        version: Date.now(),
        exists: true
      });

      // Act
      const preview = await offlineSyncReconciliation.previewReconciliation(mockConvexClient);

      // Assert
      expect(preview.operationsToSync).toBe(1);
      expect(preview.potentialConflicts.length).toBeGreaterThanOrEqual(0);
      expect(preview.estimatedDuration).toBeGreaterThan(0);
    });
  });

  describe('error handling', () => {
    it('should handle network errors gracefully', async () => {
      // Arrange
      const mockQueue = [
        {
          id: 'op1',
          operation: 'update',
          entityType: 'properties',
          entityId: 'prop1',
          data: { name: 'Updated Property' },
          timestamp: Date.now()
        }
      ];

      (offlineStorageManager.getOfflineQueue as any).mockResolvedValue(mockQueue);
      mockConvexClient.mutation.mockRejectedValue(new Error('Network error'));

      // Act
      const result = await offlineSyncReconciliation.reconcileOfflineChanges(mockConvexClient);

      // Assert
      expect(result.success).toBe(false);
      expect(result.errors).toContain('Network error');
    });

    it('should handle storage errors gracefully', async () => {
      // Arrange
      (offlineStorageManager.getOfflineQueue as any).mockRejectedValue(new Error('Storage error'));

      // Act
      const result = await offlineSyncReconciliation.reconcileOfflineChanges(mockConvexClient);

      // Assert
      expect(result.success).toBe(false);
      expect(result.errors).toContain('Storage error');
    });
  });

  describe('retry logic', () => {
    it('should retry failed operations', async () => {
      // This would test retry logic if implemented
      expect(true).toBe(true);
    });

    it('should respect max retry attempts', async () => {
      // This would test max retry logic if implemented
      expect(true).toBe(true);
    });
  });
});