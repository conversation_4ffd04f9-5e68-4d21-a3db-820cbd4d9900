import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { useAuth } from '../../lib/auth-context';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select } from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { ImageUpload } from '../ui/image-upload';

interface PropertyFormData {
  name: string;
  type: 'residential' | 'commercial' | 'mixed';
  address: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  managerId?: Id<"users">;
  branding: {
    primaryColor: string;
    secondaryColor: string;
    customDomain?: string;
  };
  settings: {
    currency: string;
    timezone: string;
    language: string;
    autoRentReminders: boolean;
    maintenanceSLA: number;
  };
}

interface PropertyFormProps {
  propertyId?: Id<"properties">;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const PropertyForm: React.FC<PropertyFormProps> = ({
  propertyId,
  onSuccess,
  onCancel,
}) => {
  const { user } = useAuth();
  const createProperty = useMutation(api.properties.createProperty);
  const updateProperty = useMutation(api.properties.updateProperty);
  const existingProperty = useQuery(
    api.properties.getPropertyById,
    propertyId ? { id: propertyId } : "skip"
  );

  const [formData, setFormData] = useState<PropertyFormData>({
    name: '',
    type: 'residential',
    address: {
      street: '',
      city: '',
      state: '',
      country: 'Kenya',
      postalCode: '',
    },
    branding: {
      primaryColor: '#3b82f6',
      secondaryColor: '#1e40af',
      customDomain: '',
    },
    settings: {
      currency: 'KES',
      timezone: 'Africa/Nairobi',
      language: 'en',
      autoRentReminders: true,
      maintenanceSLA: 24,
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [logoFile, setLogoFile] = useState<File | null>(null);
  
  // TODO: Implement logo upload functionality
  console.log('Logo file selected:', logoFile);

  // Populate form with existing data when editing
  React.useEffect(() => {
    if (existingProperty) {
      setFormData({
        name: existingProperty.name,
        type: existingProperty.type,
        address: existingProperty.address,
        managerId: existingProperty.managerId,
        branding: existingProperty.branding || {
          primaryColor: '#3b82f6',
          secondaryColor: '#1e40af',
        },
        settings: existingProperty.settings || {
          currency: 'KES',
          timezone: 'Africa/Nairobi',
          language: 'en',
          autoRentReminders: true,
          maintenanceSLA: 24,
        },
      });
    }
  }, [existingProperty]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Property name is required';
    }

    if (!formData.address.street.trim()) {
      newErrors.street = 'Street address is required';
    }

    if (!formData.address.city.trim()) {
      newErrors.city = 'City is required';
    }

    if (!formData.address.state.trim()) {
      newErrors.state = 'State/Region is required';
    }

    if (!formData.address.postalCode.trim()) {
      newErrors.postalCode = 'Postal code is required';
    }

    if (formData.settings.maintenanceSLA < 1) {
      newErrors.maintenanceSLA = 'Maintenance SLA must be at least 1 hour';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      if (propertyId) {
        await updateProperty({
          id: propertyId,
          ...formData,
        });
      } else {
        if (!user?._id) {
          setErrors({ submit: 'User not authenticated' });
          return;
        }
        
        await createProperty({
          ...formData,
          ownerId: user._id as Id<"users">,
        });
      }
      
      onSuccess?.();
    } catch (error) {
      console.error('Error saving property:', error);
      setErrors({ submit: 'Failed to save property. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (path: string, value: any) => {
    setFormData(prev => {
      const keys = path.split('.');
      const newData = { ...prev };
      let current: any = newData;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newData;
    });
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>
          {propertyId ? 'Edit Property' : 'Create New Property'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Property Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => updateFormData('name', e.target.value)}
                placeholder="Enter property name"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name}</p>
              )}
            </div>

            <div>
              <Label htmlFor="type">Property Type *</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => updateFormData('type', value)}
              >
                <option value="residential">Residential</option>
                <option value="commercial">Commercial</option>
                <option value="mixed">Mixed Use</option>
              </Select>
            </div>
          </div>

          {/* Address Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Address Information</h3>
            
            <div>
              <Label htmlFor="street">Street Address *</Label>
              <Input
                id="street"
                value={formData.address.street}
                onChange={(e) => updateFormData('address.street', e.target.value)}
                placeholder="Enter street address"
                className={errors.street ? 'border-red-500' : ''}
              />
              {errors.street && (
                <p className="text-sm text-red-500 mt-1">{errors.street}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  value={formData.address.city}
                  onChange={(e) => updateFormData('address.city', e.target.value)}
                  placeholder="Enter city"
                  className={errors.city ? 'border-red-500' : ''}
                />
                {errors.city && (
                  <p className="text-sm text-red-500 mt-1">{errors.city}</p>
                )}
              </div>

              <div>
                <Label htmlFor="state">State/Region *</Label>
                <Input
                  id="state"
                  value={formData.address.state}
                  onChange={(e) => updateFormData('address.state', e.target.value)}
                  placeholder="Enter state/region"
                  className={errors.state ? 'border-red-500' : ''}
                />
                {errors.state && (
                  <p className="text-sm text-red-500 mt-1">{errors.state}</p>
                )}
              </div>

              <div>
                <Label htmlFor="postalCode">Postal Code *</Label>
                <Input
                  id="postalCode"
                  value={formData.address.postalCode}
                  onChange={(e) => updateFormData('address.postalCode', e.target.value)}
                  placeholder="Enter postal code"
                  className={errors.postalCode ? 'border-red-500' : ''}
                />
                {errors.postalCode && (
                  <p className="text-sm text-red-500 mt-1">{errors.postalCode}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="country">Country</Label>
              <Select
                value={formData.address.country}
                onValueChange={(value) => updateFormData('address.country', value)}
              >
                <option value="Kenya">Kenya</option>
                <option value="Uganda">Uganda</option>
                <option value="Tanzania">Tanzania</option>
                <option value="Rwanda">Rwanda</option>
              </Select>
            </div>
          </div>

          {/* Branding */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Branding</h3>
            
            <div>
              <Label>Property Logo</Label>
              <ImageUpload
                onImageSelect={setLogoFile}
                currentImage={existingProperty?.branding?.logo}
                className="mt-2"
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="primaryColor">Primary Color</Label>
                <Input
                  id="primaryColor"
                  type="color"
                  value={formData.branding.primaryColor}
                  onChange={(e) => updateFormData('branding.primaryColor', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="secondaryColor">Secondary Color</Label>
                <Input
                  id="secondaryColor"
                  type="color"
                  value={formData.branding.secondaryColor}
                  onChange={(e) => updateFormData('branding.secondaryColor', e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="customDomain">Custom Domain (Optional)</Label>
              <Input
                id="customDomain"
                value={formData.branding.customDomain || ''}
                onChange={(e) => updateFormData('branding.customDomain', e.target.value)}
                placeholder="e.g., myproperties.com"
              />
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Settings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="currency">Currency</Label>
                <Select
                  value={formData.settings.currency}
                  onValueChange={(value) => updateFormData('settings.currency', value)}
                >
                  <option value="KES">KES (Kenyan Shilling)</option>
                  <option value="UGX">UGX (Ugandan Shilling)</option>
                  <option value="TZS">TZS (Tanzanian Shilling)</option>
                  <option value="USD">USD (US Dollar)</option>
                </Select>
              </div>

              <div>
                <Label htmlFor="timezone">Timezone</Label>
                <Select
                  value={formData.settings.timezone}
                  onValueChange={(value) => updateFormData('settings.timezone', value)}
                >
                  <option value="Africa/Nairobi">Africa/Nairobi</option>
                  <option value="Africa/Kampala">Africa/Kampala</option>
                  <option value="Africa/Dar_es_Salaam">Africa/Dar_es_Salaam</option>
                  <option value="Africa/Kigali">Africa/Kigali</option>
                </Select>
              </div>

              <div>
                <Label htmlFor="maintenanceSLA">Maintenance SLA (hours)</Label>
                <Input
                  id="maintenanceSLA"
                  type="number"
                  min="1"
                  value={formData.settings.maintenanceSLA}
                  onChange={(e) => updateFormData('settings.maintenanceSLA', parseInt(e.target.value))}
                  className={errors.maintenanceSLA ? 'border-red-500' : ''}
                />
                {errors.maintenanceSLA && (
                  <p className="text-sm text-red-500 mt-1">{errors.maintenanceSLA}</p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="autoRentReminders"
                checked={formData.settings.autoRentReminders}
                onChange={(e) => updateFormData('settings.autoRentReminders', e.target.checked)}
                className="rounded border-gray-300"
              />
              <Label htmlFor="autoRentReminders">Enable automatic rent reminders</Label>
            </div>
          </div>

          {/* Error Display */}
          {errors.submit && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : propertyId ? 'Update Property' : 'Create Property'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};