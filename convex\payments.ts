import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { api } from "./_generated/api";
import { Id } from "./_generated/dataModel";

// M-PESA STK Push mutation
export const initiateMPESAPayment = mutation({
  args: {
    invoiceId: v.id("invoices"),
    phoneNumber: v.string(),
    amount: v.number(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get the invoice to validate
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    if (invoice.status === "paid") {
      throw new Error("Invoice is already paid");
    }

    // Validate amount matches invoice
    if (args.amount !== invoice.amount) {
      throw new Error("Payment amount does not match invoice amount");
    }

    // Create a pending payment record
    const paymentId = await ctx.db.insert("payments", {
      invoiceId: args.invoiceId,
      amount: args.amount,
      method: "mpesa",
      transactionId: `pending_${Date.now()}`, // Temporary ID until we get M-PESA response
      status: "pending",
      metadata: {
        phoneNumber: args.phoneNumber,
      },
      processedAt: Date.now(),
      createdAt: Date.now(),
    });

    return { paymentId, status: "initiated" };
  },
});

// Process M-PESA STK Push action
export const processMPESASTKPush = action({
  args: {
    paymentId: v.id("payments"),
    phoneNumber: v.string(),
    amount: v.number(),
    reference: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Get M-PESA credentials from environment
      const consumerKey = process.env.MPESA_CONSUMER_KEY;
      const consumerSecret = process.env.MPESA_CONSUMER_SECRET;
      const businessShortCode = process.env.MPESA_BUSINESS_SHORTCODE;
      const passkey = process.env.MPESA_PASSKEY;
      const callbackUrl = process.env.MPESA_CALLBACK_URL;

      if (!consumerKey || !consumerSecret || !businessShortCode || !passkey || !callbackUrl) {
        throw new Error("M-PESA configuration missing");
      }

      // Get OAuth token
      const authResponse = await fetch("https://sandbox-api.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials", {
        method: "GET",
        headers: {
          Authorization: `Basic ${Buffer.from(`${consumerKey}:${consumerSecret}`).toString("base64")}`,
        },
      });

      if (!authResponse.ok) {
        throw new Error("Failed to get M-PESA OAuth token");
      }

      const authData = await authResponse.json();
      const accessToken = authData.access_token;

      // Generate timestamp and password
      const timestamp = new Date().toISOString().replace(/[^0-9]/g, "").slice(0, -3);
      const password = Buffer.from(`${businessShortCode}${passkey}${timestamp}`).toString("base64");

      // Format phone number (ensure it starts with 254)
      let formattedPhone = args.phoneNumber.replace(/^\+/, "");
      if (formattedPhone.startsWith("0")) {
        formattedPhone = "254" + formattedPhone.slice(1);
      } else if (!formattedPhone.startsWith("254")) {
        formattedPhone = "254" + formattedPhone;
      }

      // Initiate STK Push
      const stkPushResponse = await fetch("https://sandbox-api.safaricom.co.ke/mpesa/stkpush/v1/processrequest", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          BusinessShortCode: businessShortCode,
          Password: password,
          Timestamp: timestamp,
          TransactionType: "CustomerPayBillOnline",
          Amount: Math.round(args.amount),
          PartyA: formattedPhone,
          PartyB: businessShortCode,
          PhoneNumber: formattedPhone,
          CallBackURL: callbackUrl,
          AccountReference: args.reference,
          TransactionDesc: `Payment for invoice ${args.reference}`,
        }),
      });

      const stkPushData = await stkPushResponse.json();

      if (stkPushData.ResponseCode === "0") {
        // Update payment with checkout request ID
        await ctx.runMutation(api.payments.updatePaymentTransaction, {
          paymentId: args.paymentId,
          transactionId: stkPushData.CheckoutRequestID,
          metadata: {
            phoneNumber: formattedPhone,
          },
        });

        return {
          success: true,
          checkoutRequestId: stkPushData.CheckoutRequestID,
          merchantRequestId: stkPushData.MerchantRequestID,
          message: "STK Push sent successfully",
        };
      } else {
        // Update payment status to failed
        await ctx.runMutation(api.payments.updatePaymentStatus, {
          paymentId: args.paymentId,
          status: "failed",
        });

        throw new Error(stkPushData.ResponseDescription || "STK Push failed");
      }
    } catch (error) {
      // Update payment status to failed
      await ctx.runMutation(api.payments.updatePaymentStatus, {
        paymentId: args.paymentId,
        status: "failed",
      });

      throw error;
    }
  },
});

// Update payment transaction ID
export const updatePaymentTransaction = mutation({
  args: {
    paymentId: v.id("payments"),
    transactionId: v.string(),
    metadata: v.optional(v.object({
      phoneNumber: v.optional(v.string()),
      checkoutRequestId: v.optional(v.string()),
      merchantRequestId: v.optional(v.string()),
      mpesaReceiptNumber: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const payment = await ctx.db.get(args.paymentId);
    if (!payment) {
      throw new Error("Payment not found");
    }

    await ctx.db.patch(args.paymentId, {
      transactionId: args.transactionId,
      metadata: {
        ...payment.metadata,
        ...args.metadata,
      },
    });
  },
});

// Update payment status
export const updatePaymentStatus = mutation({
  args: {
    paymentId: v.id("payments"),
    status: v.union(v.literal("pending"), v.literal("completed"), v.literal("failed"), v.literal("refunded")),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.paymentId, {
      status: args.status,
      processedAt: Date.now(),
    });
  },
});

// M-PESA callback handler
export const handleMPESACallback = mutation({
  args: {
    checkoutRequestId: v.string(),
    resultCode: v.number(),
    resultDesc: v.string(),
    mpesaReceiptNumber: v.optional(v.string()),
    transactionDate: v.optional(v.string()),
    phoneNumber: v.optional(v.string()),
    amount: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Find payment by checkout request ID
    const payment = await ctx.db
      .query("payments")
      .filter((q) => q.eq(q.field("transactionId"), args.checkoutRequestId))
      .first();

    if (!payment) {
      throw new Error("Payment not found for checkout request ID");
    }

    if (args.resultCode === 0) {
      // Payment successful
      await ctx.db.patch(payment._id, {
        status: "completed",
        metadata: {
          ...payment.metadata,
          mpesaReceiptNumber: args.mpesaReceiptNumber,
        },
        processedAt: Date.now(),
      });

      // Update invoice status to paid
      await ctx.runMutation(api.invoices.updateInvoiceStatus, {
        invoiceId: payment.invoiceId,
        status: "paid",
        paymentMethod: "mpesa",
      });

      // Create notification for successful payment
      const invoice = await ctx.db.get(payment.invoiceId);
      if (invoice) {
        const lease = await ctx.db.get(invoice.leaseId);
        const property = lease ? await ctx.db.get(lease.propertyId) : null;

        if (property?.managerId) {
          await ctx.db.insert("notifications", {
            userId: property.managerId,
            title: "Payment Received",
            message: `M-PESA payment of KES ${payment.amount.toFixed(2)} received for invoice #${invoice._id.slice(-8)}`,
            type: "payment_reminder",
            priority: "medium",
            isRead: false,
            metadata: {
              invoiceId: payment.invoiceId,
            },
            createdAt: Date.now(),
          });
        }

        // Notify tenant of successful payment
        await ctx.db.insert("notifications", {
          userId: invoice.tenantId,
          title: "Payment Confirmed",
          message: `Your M-PESA payment of KES ${payment.amount.toFixed(2)} has been confirmed. Receipt: ${args.mpesaReceiptNumber}`,
          type: "payment_reminder",
          priority: "medium",
          isRead: false,
          metadata: {
            invoiceId: payment.invoiceId,
          },
          createdAt: Date.now(),
        });
      }
    } else {
      // Payment failed
      await ctx.db.patch(payment._id, {
        status: "failed",
        processedAt: Date.now(),
      });

      // Notify tenant of failed payment
      const invoice = await ctx.db.get(payment.invoiceId);
      if (invoice) {
        await ctx.db.insert("notifications", {
          userId: invoice.tenantId,
          title: "Payment Failed",
          message: `Your M-PESA payment failed: ${args.resultDesc}. Please try again or use an alternative payment method.`,
          type: "payment_reminder",
          priority: "high",
          isRead: false,
          metadata: {
            invoiceId: payment.invoiceId,
          },
          createdAt: Date.now(),
        });
      }
    }

    return { success: true, status: args.resultCode === 0 ? "completed" : "failed" };
  },
});

// Query payment status
export const queryMPESAPaymentStatus = action({
  args: {
    checkoutRequestId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const consumerKey = process.env.MPESA_CONSUMER_KEY;
      const consumerSecret = process.env.MPESA_CONSUMER_SECRET;
      const businessShortCode = process.env.MPESA_BUSINESS_SHORTCODE;
      const passkey = process.env.MPESA_PASSKEY;

      if (!consumerKey || !consumerSecret || !businessShortCode || !passkey) {
        throw new Error("M-PESA configuration missing");
      }

      // Get OAuth token
      const authResponse = await fetch("https://sandbox-api.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials", {
        method: "GET",
        headers: {
          Authorization: `Basic ${Buffer.from(`${consumerKey}:${consumerSecret}`).toString("base64")}`,
        },
      });

      if (!authResponse.ok) {
        throw new Error("Failed to get M-PESA OAuth token");
      }

      const authData = await authResponse.json();
      const accessToken = authData.access_token;

      // Generate timestamp and password
      const timestamp = new Date().toISOString().replace(/[^0-9]/g, "").slice(0, -3);
      const password = Buffer.from(`${businessShortCode}${passkey}${timestamp}`).toString("base64");

      // Query transaction status
      const queryResponse = await fetch("https://sandbox-api.safaricom.co.ke/mpesa/stkpushquery/v1/query", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          BusinessShortCode: businessShortCode,
          Password: password,
          Timestamp: timestamp,
          CheckoutRequestID: args.checkoutRequestId,
        }),
      });

      const queryData = await queryResponse.json();

      return {
        success: true,
        resultCode: queryData.ResultCode,
        resultDesc: queryData.ResultDesc,
        responseCode: queryData.ResponseCode,
        responseDescription: queryData.ResponseDescription,
      };
    } catch (error) {
      throw error;
    }
  },
});

// Get payment by ID
export const getPayment = query({
  args: { paymentId: v.id("payments") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.paymentId);
  },
});

// Get payments for an invoice
export const getPaymentsByInvoice = query({
  args: { invoiceId: v.id("invoices") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("payments")
      .withIndex("by_invoice", (q) => q.eq("invoiceId", args.invoiceId))
      .collect();
  },
});

// Get payments by status
export const getPaymentsByStatus = query({
  args: { 
    status: v.optional(v.union(v.literal("pending"), v.literal("completed"), v.literal("failed"), v.literal("refunded"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("payments");
    
    if (args.status) {
      query = query.withIndex("by_status", (q) => q.eq("status", args.status));
    }
    
    query = query.order("desc");

    if (args.limit) {
      query = query.take(args.limit);
    }

    return await query.collect();
  },
});

// Verify payment by transaction ID
export const verifyPayment = query({
  args: { transactionId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("payments")
      .withIndex("by_transaction_id", (q) => q.eq("transactionId", args.transactionId))
      .first();
  },
});

// Stripe payment integration
export const initiateStripePayment = mutation({
  args: {
    invoiceId: v.id("invoices"),
    amount: v.number(),
    currency: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get the invoice to validate
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    if (invoice.status === "paid") {
      throw new Error("Invoice is already paid");
    }

    // Validate amount matches invoice
    if (args.amount !== invoice.amount) {
      throw new Error("Payment amount does not match invoice amount");
    }

    // Create a pending payment record
    const paymentId = await ctx.db.insert("payments", {
      invoiceId: args.invoiceId,
      amount: args.amount,
      method: "stripe",
      transactionId: `pending_stripe_${Date.now()}`, // Temporary ID until we get Stripe response
      status: "pending",
      metadata: {},
      processedAt: Date.now(),
      createdAt: Date.now(),
    });

    return { paymentId, status: "initiated" };
  },
});

// Process Stripe payment
export const processStripePayment = action({
  args: {
    paymentId: v.id("payments"),
    amount: v.number(),
    currency: v.string(),
    paymentMethodId: v.optional(v.string()),
    customerEmail: v.string(),
    description: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
      if (!stripeSecretKey) {
        throw new Error("Stripe configuration missing");
      }

      // Create payment intent with Stripe
      const paymentIntentResponse = await fetch("https://api.stripe.com/v1/payment_intents", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${stripeSecretKey}`,
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          amount: Math.round(args.amount * 100).toString(), // Convert to cents
          currency: args.currency.toLowerCase(),
          description: args.description,
          receipt_email: args.customerEmail,
          ...(args.paymentMethodId && { payment_method: args.paymentMethodId }),
          ...(args.paymentMethodId && { confirm: "true" }),
          automatic_payment_methods: JSON.stringify({ enabled: true }),
        }),
      });

      if (!paymentIntentResponse.ok) {
        const errorData = await paymentIntentResponse.json();
        throw new Error(errorData.error?.message || "Stripe payment failed");
      }

      const paymentIntent = await paymentIntentResponse.json();

      // Update payment with Stripe payment intent ID
      await ctx.runMutation(api.payments.updatePaymentTransaction, {
        paymentId: args.paymentId,
        transactionId: paymentIntent.id,
        metadata: {
          stripePaymentIntentId: paymentIntent.id,
        },
      });

      return {
        success: true,
        paymentIntentId: paymentIntent.id,
        clientSecret: paymentIntent.client_secret,
        status: paymentIntent.status,
        message: "Payment intent created successfully",
      };
    } catch (error) {
      // Update payment status to failed
      await ctx.runMutation(api.payments.updatePaymentStatus, {
        paymentId: args.paymentId,
        status: "failed",
      });

      throw error;
    }
  },
});

// Handle Stripe webhook
export const handleStripeWebhook = mutation({
  args: {
    paymentIntentId: v.string(),
    status: v.string(),
    amount: v.optional(v.number()),
    currency: v.optional(v.string()),
    receiptUrl: v.optional(v.string()),
    chargeId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Find payment by Stripe payment intent ID
    const payment = await ctx.db
      .query("payments")
      .filter((q) => q.eq(q.field("transactionId"), args.paymentIntentId))
      .first();

    if (!payment) {
      throw new Error("Payment not found for payment intent ID");
    }

    if (args.status === "succeeded") {
      // Payment successful
      await ctx.db.patch(payment._id, {
        status: "completed",
        metadata: {
          ...payment.metadata,
        },
        processedAt: Date.now(),
      });

      // Update invoice status to paid
      await ctx.runMutation(api.invoices.updateInvoiceStatus, {
        invoiceId: payment.invoiceId,
        status: "paid",
        paymentMethod: "stripe",
      });

      // Create notification for successful payment
      const invoice = await ctx.db.get(payment.invoiceId);
      if (invoice) {
        const lease = await ctx.db.get(invoice.leaseId);
        const property = lease ? await ctx.db.get(lease.propertyId) : null;

        if (property?.managerId) {
          await ctx.db.insert("notifications", {
            userId: property.managerId,
            title: "Payment Received",
            message: `Stripe payment of ${args.currency?.toUpperCase()} ${(payment.amount / 100).toFixed(2)} received for invoice #${invoice._id.slice(-8)}`,
            type: "payment_reminder",
            priority: "medium",
            isRead: false,
            metadata: {
              invoiceId: payment.invoiceId,
            },
            createdAt: Date.now(),
          });
        }

        // Notify tenant of successful payment
        await ctx.db.insert("notifications", {
          userId: invoice.tenantId,
          title: "Payment Confirmed",
          message: `Your payment of ${args.currency?.toUpperCase()} ${(payment.amount / 100).toFixed(2)} has been confirmed.`,
          type: "payment_reminder",
          priority: "medium",
          isRead: false,
          metadata: {
            invoiceId: payment.invoiceId,
          },
          createdAt: Date.now(),
        });
      }
    } else if (args.status === "payment_failed" || args.status === "canceled") {
      // Payment failed
      await ctx.db.patch(payment._id, {
        status: "failed",
        processedAt: Date.now(),
      });

      // Notify tenant of failed payment
      const invoice = await ctx.db.get(payment.invoiceId);
      if (invoice) {
        await ctx.db.insert("notifications", {
          userId: invoice.tenantId,
          title: "Payment Failed",
          message: `Your payment failed. Please try again or contact support.`,
          type: "payment_reminder",
          priority: "high",
          isRead: false,
          metadata: {
            invoiceId: payment.invoiceId,
          },
          createdAt: Date.now(),
        });
      }
    }

    return { success: true, status: args.status };
  },
});

// Create Stripe customer
export const createStripeCustomer = action({
  args: {
    email: v.string(),
    name: v.string(),
    phone: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
      if (!stripeSecretKey) {
        throw new Error("Stripe configuration missing");
      }

      const customerResponse = await fetch("https://api.stripe.com/v1/customers", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${stripeSecretKey}`,
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          email: args.email,
          name: args.name,
          ...(args.phone && { phone: args.phone }),
        }),
      });

      if (!customerResponse.ok) {
        const errorData = await customerResponse.json();
        throw new Error(errorData.error?.message || "Failed to create Stripe customer");
      }

      const customer = await customerResponse.json();

      return {
        success: true,
        customerId: customer.id,
        customer,
      };
    } catch (error) {
      throw error;
    }
  },
});

// Generic payment processing function
export const processPayment = mutation({
  args: {
    invoiceId: v.id("invoices"),
    amount: v.number(),
    method: v.union(v.literal("mpesa"), v.literal("stripe"), v.literal("bank_transfer")),
    metadata: v.optional(v.object({
      tenantId: v.optional(v.id("users")),
      portalId: v.optional(v.string()),
      phoneNumber: v.optional(v.string()),
      paymentIntentId: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get the invoice to validate
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    if (invoice.status === "paid") {
      throw new Error("Invoice is already paid");
    }

    // Validate amount matches invoice
    if (args.amount !== invoice.amount) {
      throw new Error("Payment amount does not match invoice amount");
    }

    // Create a payment record
    const paymentId = await ctx.db.insert("payments", {
      invoiceId: args.invoiceId,
      amount: args.amount,
      method: args.method,
      transactionId: `${args.method}_${Date.now()}`,
      status: "pending",
      metadata: args.metadata || {},
      processedAt: Date.now(),
      createdAt: Date.now(),
    });

    return { paymentId, status: "initiated" };
  },
});

// Refund Stripe payment
export const refundStripePayment = action({
  args: {
    paymentIntentId: v.string(),
    amount: v.optional(v.number()),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
      if (!stripeSecretKey) {
        throw new Error("Stripe configuration missing");
      }

      // Get the payment intent to find the charge
      const paymentIntentResponse = await fetch(`https://api.stripe.com/v1/payment_intents/${args.paymentIntentId}`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${stripeSecretKey}`,
        },
      });

      if (!paymentIntentResponse.ok) {
        throw new Error("Failed to retrieve payment intent");
      }

      const paymentIntent = await paymentIntentResponse.json();
      const chargeId = paymentIntent.latest_charge;

      if (!chargeId) {
        throw new Error("No charge found for this payment intent");
      }

      // Create refund
      const refundResponse = await fetch("https://api.stripe.com/v1/refunds", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${stripeSecretKey}`,
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          charge: chargeId,
          ...(args.amount && { amount: Math.round(args.amount * 100).toString() }),
          ...(args.reason && { reason: args.reason }),
        }),
      });

      if (!refundResponse.ok) {
        const errorData = await refundResponse.json();
        throw new Error(errorData.error?.message || "Refund failed");
      }

      const refund = await refundResponse.json();

      // Update payment status to refunded
      const payment = await ctx.runQuery(api.payments.verifyPayment, {
        transactionId: args.paymentIntentId,
      });

      if (payment) {
        await ctx.runMutation(api.payments.updatePaymentStatus, {
          paymentId: payment._id,
          status: "refunded",
        });
      }

      return {
        success: true,
        refundId: refund.id,
        amount: refund.amount / 100,
        status: refund.status,
      };
    } catch (error) {
      throw error;
    }
  },
});