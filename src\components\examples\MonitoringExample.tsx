import React, { useState, useEffect } from 'react';
import { useMonitoring, withMonitoring } from '../../hooks/useMonitoring';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

// Example component using the monitoring hook
function MonitoringExampleComponent() {
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(false);
  
  const {
    track,
    captureError,
    measurePerformance,
    recordMetric,
    monitorDatabaseQuery,
    isFeatureEnabled,
    trackComponentMount,
    trackComponentUnmount
  } = useMonitoring({
    autoTrackPageViews: true,
    autoTrackErrors: true
  });

  // Track component lifecycle
  useEffect(() => {
    trackComponentMount('MonitoringExample');
    return () => trackComponentUnmount('MonitoringExample');
  }, [trackComponentMount, trackComponentUnmount]);

  // Example: Track button clicks
  const handleButtonClick = () => {
    track('button_click', {
      button_type: 'increment',
      current_count: count,
      user_action: 'increment_counter'
    });
    setCount(prev => prev + 1);
  };

  // Example: Performance measurement
  const handlePerformanceTest = async () => {
    await measurePerformance('heavy_computation', async () => {
      setLoading(true);
      // Simulate heavy computation
      await new Promise(resolve => setTimeout(resolve, 2000));
      setLoading(false);
    });
  };

  // Example: Error tracking
  const handleErrorTest = () => {
    try {
      throw new Error('This is a test error for monitoring');
    } catch (error) {
      captureError(error as Error, {
        component: 'MonitoringExample',
        action: 'error_test',
        count: count
      });
    }
  };

  // Example: Database query monitoring
  const handleDatabaseTest = async () => {
    try {
      setLoading(true);
      await monitorDatabaseQuery('fetch_user_data', async () => {
        // Simulate database query
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { userId: '123', name: 'Test User' };
      }, {
        query_type: 'user_fetch',
        component: 'MonitoringExample'
      });
    } finally {
      setLoading(false);
    }
  };

  // Example: Custom metric recording
  const handleMetricTest = () => {
    recordMetric('user_engagement_score', Math.random() * 100, {
      component: 'MonitoringExample',
      interaction_type: 'metric_test'
    });
    track('custom_metric_recorded', {
      metric_name: 'user_engagement_score'
    });
  };

  // Feature flag example
  const showAdvancedFeatures = isFeatureEnabled('advanced_monitoring_features');

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Monitoring Integration Example</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span>Counter: {count}</span>
          <Button onClick={handleButtonClick}>
            Increment (Tracked)
          </Button>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Button 
            onClick={handlePerformanceTest} 
            disabled={loading}
            variant="outline"
          >
            {loading ? 'Testing...' : 'Performance Test'}
          </Button>

          <Button 
            onClick={handleErrorTest}
            variant="destructive"
          >
            Test Error Tracking
          </Button>

          <Button 
            onClick={handleDatabaseTest}
            disabled={loading}
            variant="secondary"
          >
            {loading ? 'Querying...' : 'Database Test'}
          </Button>

          <Button 
            onClick={handleMetricTest}
            variant="outline"
          >
            Record Custom Metric
          </Button>
        </div>

        {showAdvancedFeatures && (
          <div className="p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              🎉 Advanced monitoring features are enabled via feature flag!
            </p>
          </div>
        )}

        <div className="text-sm text-gray-600">
          <p>This component demonstrates:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Event tracking for user interactions</li>
            <li>Performance measurement for async operations</li>
            <li>Error capture and reporting</li>
            <li>Database query monitoring</li>
            <li>Custom metric recording</li>
            <li>Feature flag integration</li>
            <li>Component lifecycle tracking</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

// Example of using the HOC for automatic monitoring
const AutoMonitoredComponent = withMonitoring(
  ({ title }: { title: string }) => (
    <div className="p-4 border rounded">
      <h3 className="font-semibold">{title}</h3>
      <p>This component is automatically monitored via HOC</p>
    </div>
  ),
  'AutoMonitoredExample'
);

export default function MonitoringExample() {
  return (
    <div className="space-y-6 p-6">
      <MonitoringExampleComponent />
      <AutoMonitoredComponent title="HOC Monitored Component" />
    </div>
  );
}