import React, { useState } from 'react';
import { Id } from '../../../convex/_generated/dataModel';
import { LeaseList } from './LeaseList';
import { LeaseForm } from './LeaseForm';
import { LeaseDetails } from './LeaseDetails';
import { LeaseRenewal } from './LeaseRenewal';

type ViewMode = 'list' | 'create' | 'details' | 'edit' | 'renew';

interface LeaseManagementProps {
  propertyId?: Id<"properties">;
  tenantId?: Id<"users">;
}

export function LeaseManagement({ propertyId, tenantId }: LeaseManagementProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedLeaseId, setSelectedLeaseId] = useState<Id<"leases"> | null>(null);

  const handleCreateLease = () => {
    setViewMode('create');
  };

  const handleViewLease = (leaseId: Id<"leases">) => {
    setSelectedLeaseId(leaseId);
    setViewMode('details');
  };

  const handleEditLease = (leaseId: Id<"leases">) => {
    setSelectedLeaseId(leaseId);
    setViewMode('edit');
  };

  const handleRenewLease = (leaseId: Id<"leases">) => {
    setSelectedLeaseId(leaseId);
    setViewMode('renew');
  };

  const handleSuccess = () => {
    setViewMode('list');
    setSelectedLeaseId(null);
  };

  const handleCancel = () => {
    setViewMode('list');
    setSelectedLeaseId(null);
  };

  const renderContent = () => {
    switch (viewMode) {
      case 'create':
        return (
          <LeaseForm
            propertyId={propertyId}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        );

      case 'details':
        return selectedLeaseId ? (
          <LeaseDetails
            leaseId={selectedLeaseId}
            onEdit={() => handleEditLease(selectedLeaseId)}
            onClose={handleCancel}
          />
        ) : null;

      case 'edit':
        return selectedLeaseId ? (
          <LeaseForm
            propertyId={propertyId}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        ) : null;

      case 'renew':
        return selectedLeaseId ? (
          <LeaseRenewal
            leaseId={selectedLeaseId}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        ) : null;

      default:
        return (
          <LeaseList
            propertyId={propertyId}
            tenantId={tenantId}
            onViewLease={handleViewLease}
            onEditLease={handleEditLease}
            onCreateLease={handleCreateLease}
          />
        );
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {renderContent()}
    </div>
  );
}