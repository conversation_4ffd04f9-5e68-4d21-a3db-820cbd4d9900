import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../../convex/_generated/api';
import { Id } from '../../../../convex/_generated/dataModel';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { Separator } from '../../ui/separator';
import { 
  Home, 
  CreditCard, 
  Wrench, 
  FileText, 
  MessageSquare,
  // Calendar,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface TenantDashboardProps {
  tenantId: Id<"users">;
  portalId: Id<"portals">;
}

export const TenantDashboard: React.FC<TenantDashboardProps> = ({ tenantId, portalId }) => {
  const portal = useQuery(api.portals.getPortalById, { portalId });
  const tenant = useQuery(api.users.getById, { id: tenantId });
  const activeLease = useQuery(api.leases.getActiveLease, { tenantId });
  const recentInvoices = useQuery(api.invoices.getRecentInvoices, { tenantId, limit: 3 });
  const openTickets = useQuery(api.maintenance.getOpenTickets, { tenantId });
  const property = useQuery(api.properties.getById, 
    activeLease ? { id: activeLease.propertyId } : 'skip'
  );
  const unit = useQuery(api.units.getById, 
    activeLease ? { id: activeLease.unitId } : 'skip'
  );

  if (!portal || !tenant) {
    return <div>Loading...</div>;
  }

  const getNextRentDue = () => {
    if (!recentInvoices || recentInvoices.length === 0) return null;
    const unpaidInvoice = recentInvoices.find((invoice: any) => invoice.status === 'pending');
    return unpaidInvoice;
  };

  const nextRentDue = getNextRentDue();

  const QuickActionCard: React.FC<{
    icon: React.ReactNode;
    title: string;
    description: string;
    action: string;
    onClick: () => void;
    variant?: 'default' | 'urgent';
  }> = ({ icon, title, description, action, onClick, variant = 'default' }) => (
    <Card className={`cursor-pointer transition-all hover:shadow-md ${
      variant === 'urgent' ? 'border-orange-200 bg-orange-50' : ''
    }`} onClick={onClick}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg ${
            variant === 'urgent' ? 'bg-orange-100 text-orange-600' : 'bg-primary/10 text-primary'
          }`}>
            {icon}
          </div>
          <div className="flex-1">
            <h3 className="font-medium">{title}</h3>
            <p className="text-sm text-muted-foreground">{description}</p>
          </div>
          <Button variant="ghost" size="sm">
            {action}
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-primary to-primary/80 text-white rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Welcome back, {tenant.name}!</h1>
            <p className="text-white/80">
              {property?.name} - Unit {unit?.unitNumber}
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-white/80">Today</p>
            <p className="text-lg font-semibold">
              {new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {portal.features.paymentPortal && (
          <QuickActionCard
            icon={<CreditCard className="w-5 h-5" />}
            title="Pay Rent"
            description={nextRentDue ? `$${nextRentDue.amount} due ${new Date(nextRentDue.dueDate).toLocaleDateString()}` : "No pending payments"}
            action="Pay Now"
            onClick={() => {/* Navigate to payment */}}
            variant={nextRentDue ? 'urgent' : 'default'}
          />
        )}

        {portal.features.maintenanceRequests && (
          <QuickActionCard
            icon={<Wrench className="w-5 h-5" />}
            title="Maintenance Request"
            description={`${openTickets?.length || 0} open tickets`}
            action="Submit"
            onClick={() => {/* Navigate to maintenance */}}
          />
        )}

        {portal.features.documentAccess && (
          <QuickActionCard
            icon={<FileText className="w-5 h-5" />}
            title="Documents"
            description="View lease and property documents"
            action="Browse"
            onClick={() => {/* Navigate to documents */}}
          />
        )}

        {portal.features.communicationCenter && (
          <QuickActionCard
            icon={<MessageSquare className="w-5 h-5" />}
            title="Messages"
            description="Contact property management"
            action="Message"
            onClick={() => {/* Navigate to messages */}}
          />
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Lease Information */}
        <div className="lg:col-span-2 space-y-6">
          {portal.features.leaseInformation && activeLease && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Home className="w-5 h-5 mr-2" />
                  Lease Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Property</p>
                    <p className="font-medium">{property?.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Unit</p>
                    <p className="font-medium">Unit {unit?.unitNumber}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Lease Start</p>
                    <p className="font-medium">
                      {new Date(activeLease.startDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Lease End</p>
                    <p className="font-medium">
                      {new Date(activeLease.endDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Monthly Rent</p>
                    <p className="font-medium text-lg">${activeLease.monthlyRent}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                    <Badge variant={activeLease.status === 'active' ? 'default' : 'secondary'}>
                      {activeLease.status}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recent Payments */}
          {portal.features.paymentPortal && recentInvoices && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <DollarSign className="w-5 h-5 mr-2" />
                  Recent Payments
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentInvoices.map((invoice: any) => (
                    <div key={invoice._id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-full ${
                          invoice.status === 'paid' ? 'bg-green-100 text-green-600' :
                          invoice.status === 'pending' ? 'bg-yellow-100 text-yellow-600' :
                          'bg-red-100 text-red-600'
                        }`}>
                          {invoice.status === 'paid' ? <CheckCircle className="w-4 h-4" /> :
                           invoice.status === 'pending' ? <Clock className="w-4 h-4" /> :
                           <AlertCircle className="w-4 h-4" />}
                        </div>
                        <div>
                          <p className="font-medium">${invoice.amount}</p>
                          <p className="text-sm text-muted-foreground">
                            Due: {new Date(invoice.dueDate).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <Badge variant={
                        invoice.status === 'paid' ? 'default' :
                        invoice.status === 'pending' ? 'secondary' :
                        'destructive'
                      }>
                        {invoice.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Maintenance Tickets */}
          {portal.features.maintenanceRequests && openTickets && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Wrench className="w-5 h-5 mr-2" />
                  Open Tickets
                </CardTitle>
              </CardHeader>
              <CardContent>
                {openTickets.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">
                    No open maintenance tickets
                  </p>
                ) : (
                  <div className="space-y-3">
                    {openTickets.slice(0, 3).map((ticket: any) => (
                      <div key={ticket._id} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="font-medium text-sm">{ticket.title}</p>
                          <Badge variant={
                            ticket.priority === 'emergency' ? 'destructive' :
                            ticket.priority === 'high' ? 'destructive' :
                            ticket.priority === 'medium' ? 'secondary' :
                            'outline'
                          }>
                            {ticket.priority}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Status: {ticket.status}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Created: {new Date(ticket.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Quick Contact */}
          <Card>
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {portal.customization.contactInfo && (
                <div className="space-y-2 text-sm">
                  <p><strong>Phone:</strong> {portal.customization.contactInfo.phone}</p>
                  <p><strong>Email:</strong> {portal.customization.contactInfo.email}</p>
                  <p><strong>Hours:</strong> {portal.customization.contactInfo.hours}</p>
                </div>
              )}
              <Separator />
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full">
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Send Message
                </Button>
                <Button variant="outline" size="sm" className="w-full">
                  <Wrench className="w-4 h-4 mr-2" />
                  Report Issue
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Announcements */}
          {portal.features.announcementsBoard && (
            <Card>
              <CardHeader>
                <CardTitle>Announcements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-sm font-medium text-blue-900">
                      Pool Maintenance Scheduled
                    </p>
                    <p className="text-xs text-blue-700 mt-1">
                      The pool will be closed for maintenance on March 15th from 9 AM to 3 PM.
                    </p>
                  </div>
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-sm font-medium text-green-900">
                      New Parking Rules
                    </p>
                    <p className="text-xs text-green-700 mt-1">
                      Please ensure all vehicles are registered with management.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};