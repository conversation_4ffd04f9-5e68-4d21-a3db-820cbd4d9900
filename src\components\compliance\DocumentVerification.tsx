import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye, 
  Download, 
  FileText,
  User,
  Calendar,
  Hash,
  Tag
} from 'lucide-react';
import { useToast } from '../ui/use-toast';
import { Id } from '../../convex/_generated/dataModel';

interface DocumentVerificationProps {
  userId?: string;
  documentId?: string;
  showAllDocuments?: boolean;
}

interface ExtractedData {
  fullName?: string;
  idNumber?: string;
  dateOfBirth?: string;
  nationality?: string;
  address?: string;
}

export function DocumentVerification({ 
  userId, 
  documentId, 
  showAllDocuments = false 
}: DocumentVerificationProps) {
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>(documentId || '');
  const [verificationStatus, setVerificationStatus] = useState<'verified' | 'rejected'>('verified');
  const [verificationNotes, setVerificationNotes] = useState('');
  const [extractedData, setExtractedData] = useState<ExtractedData>({});
  const [isVerifying, setIsVerifying] = useState(false);

  const documents = useQuery(
    api.compliance.getUserKYCDocuments,
    userId ? { userId: userId as Id<"users"> } : "skip"
  );

  const selectedDocument = useQuery(
    api.compliance.getKYCDocumentById,
    selectedDocumentId ? { documentId: selectedDocumentId as Id<"kycDocuments"> } : "skip"
  );

  const verifyDocument = useMutation(api.compliance.verifyKYCDocument);
  const { toast } = useToast();

  const handleVerification = async () => {
    if (!selectedDocumentId) return;

    setIsVerifying(true);
    try {
      await verifyDocument({
        documentId: selectedDocumentId as Id<"kycDocuments">,
        verificationStatus,
        verificationNotes: verificationNotes || undefined,
        verifiedBy: userId as Id<"users">, // In real app, this would be the current user
        extractedData: Object.keys(extractedData).length > 0 ? extractedData : undefined,
      });

      toast({
        title: 'Document verified',
        description: `Document has been ${verificationStatus}.`,
      });

      // Reset form
      setVerificationNotes('');
      setExtractedData({});
    } catch (error) {
      console.error('Verification error:', error);
      toast({
        title: 'Verification failed',
        description: 'There was an error verifying the document.',
        variant: 'destructive',
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Verified</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>;
      case 'in_review':
        return <Badge className="bg-blue-100 text-blue-800"><Eye className="h-3 w-3 mr-1" />In Review</Badge>;
      case 'pending':
      default:
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Pending</Badge>;
    }
  };

  const formatDocumentType = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  if (showAllDocuments && documents) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Document Verification</h2>
        </div>

        <div className="grid gap-4">
          {documents.map((doc) => (
            <Card key={doc._id} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <FileText className="h-8 w-8 text-blue-500" />
                    <div>
                      <h3 className="font-semibold">{doc.fileName}</h3>
                      <p className="text-sm text-gray-500">
                        {formatDocumentType(doc.documentType)}
                      </p>
                      <p className="text-xs text-gray-400">
                        Uploaded {new Date(doc.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    {getStatusBadge(doc.verificationStatus)}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedDocumentId(doc._id)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Review
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {selectedDocumentId && (
          <DocumentVerification 
            documentId={selectedDocumentId} 
            userId={userId}
            showAllDocuments={false}
          />
        )}
      </div>
    );
  }

  if (!selectedDocument) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Select a document to verify</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Document Verification
        </CardTitle>
        <CardDescription>
          Review and verify the uploaded document
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="details" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="details">Document Details</TabsTrigger>
            <TabsTrigger value="verification">Verification</TabsTrigger>
            <TabsTrigger value="extracted">Extracted Data</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">File Name</Label>
                  <p className="text-sm">{selectedDocument.fileName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Document Type</Label>
                  <p className="text-sm">{formatDocumentType(selectedDocument.documentType)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">File Size</Label>
                  <p className="text-sm">{(selectedDocument.fileSize / 1024 / 1024).toFixed(2)} MB</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Upload Date</Label>
                  <p className="text-sm">{new Date(selectedDocument.createdAt).toLocaleString()}</p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Status</Label>
                  <div className="mt-1">{getStatusBadge(selectedDocument.verificationStatus)}</div>
                </div>
                {selectedDocument.documentNumber && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Document Number</Label>
                    <p className="text-sm">{selectedDocument.documentNumber}</p>
                  </div>
                )}
                {selectedDocument.tags.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Tags</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {selectedDocument.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          <Tag className="h-3 w-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-500" />
                  <span className="font-medium">Document Preview</span>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Document preview would be displayed here in a real implementation
              </p>
            </div>
          </TabsContent>

          <TabsContent value="verification" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="verification-status">Verification Decision</Label>
                <div className="flex gap-4 mt-2">
                  <Button
                    variant={verificationStatus === 'verified' ? 'default' : 'outline'}
                    onClick={() => setVerificationStatus('verified')}
                    className="flex items-center gap-2"
                  >
                    <CheckCircle className="h-4 w-4" />
                    Approve
                  </Button>
                  <Button
                    variant={verificationStatus === 'rejected' ? 'destructive' : 'outline'}
                    onClick={() => setVerificationStatus('rejected')}
                    className="flex items-center gap-2"
                  >
                    <XCircle className="h-4 w-4" />
                    Reject
                  </Button>
                </div>
              </div>

              <div>
                <Label htmlFor="verification-notes">Verification Notes</Label>
                <Textarea
                  id="verification-notes"
                  value={verificationNotes}
                  onChange={(e) => setVerificationNotes(e.target.value)}
                  placeholder="Add notes about the verification decision..."
                  rows={4}
                />
              </div>

              {selectedDocument.verificationStatus !== 'pending' && (
                <Alert>
                  <AlertDescription>
                    This document has already been {selectedDocument.verificationStatus}.
                    {selectedDocument.verifiedAt && (
                      <> Verified on {new Date(selectedDocument.verifiedAt).toLocaleString()}</>
                    )}
                    {selectedDocument.verificationNotes && (
                      <>
                        <br />
                        <strong>Notes:</strong> {selectedDocument.verificationNotes}
                      </>
                    )}
                  </AlertDescription>
                </Alert>
              )}

              <Button
                onClick={handleVerification}
                disabled={isVerifying}
                className="w-full"
              >
                {isVerifying ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Processing...
                  </>
                ) : (
                  <>
                    {verificationStatus === 'verified' ? (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    ) : (
                      <XCircle className="h-4 w-4 mr-2" />
                    )}
                    {verificationStatus === 'verified' ? 'Approve Document' : 'Reject Document'}
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="extracted" className="space-y-4">
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Extract and verify information from the document:
              </p>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="full-name">Full Name</Label>
                  <Input
                    id="full-name"
                    value={extractedData.fullName || ''}
                    onChange={(e) => setExtractedData(prev => ({ ...prev, fullName: e.target.value }))}
                    placeholder="Enter full name from document"
                  />
                </div>
                <div>
                  <Label htmlFor="id-number">ID Number</Label>
                  <Input
                    id="id-number"
                    value={extractedData.idNumber || ''}
                    onChange={(e) => setExtractedData(prev => ({ ...prev, idNumber: e.target.value }))}
                    placeholder="Enter ID number from document"
                  />
                </div>
                <div>
                  <Label htmlFor="date-of-birth">Date of Birth</Label>
                  <Input
                    id="date-of-birth"
                    value={extractedData.dateOfBirth || ''}
                    onChange={(e) => setExtractedData(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                    placeholder="YYYY-MM-DD"
                  />
                </div>
                <div>
                  <Label htmlFor="nationality">Nationality</Label>
                  <Input
                    id="nationality"
                    value={extractedData.nationality || ''}
                    onChange={(e) => setExtractedData(prev => ({ ...prev, nationality: e.target.value }))}
                    placeholder="Enter nationality"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  value={extractedData.address || ''}
                  onChange={(e) => setExtractedData(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="Enter address from document"
                  rows={3}
                />
              </div>

              {selectedDocument.extractedData && (
                <Alert>
                  <AlertDescription>
                    <strong>Previously extracted data:</strong>
                    <pre className="mt-2 text-xs bg-gray-100 p-2 rounded">
                      {JSON.stringify(selectedDocument.extractedData, null, 2)}
                    </pre>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}