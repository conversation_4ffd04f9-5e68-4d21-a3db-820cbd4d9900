"use node";

import { v } from "convex/values";
import { action } from "./_generated/server";

// Send SMS via Twilio
export const sendSMS = action({
  args: {
    phoneNumber: v.string(),
    message: v.string(),
    personalizedData: v.optional(v.record(v.string(), v.any())),
  },
  handler: async (_ctx, args) => {
    try {
      // Send via Twilio with personalization support
      const result = await sendSMSViaTwilio(args.phoneNumber, args.message, args.personalizedData);

      return {
        success: true,
        messageId: result.messageId,
        status: result.status,
      };
    } catch (error: unknown) {
      console.error("SMS sending failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

// Send WhatsApp message via Twilio
export const sendWhatsApp = action({
  args: {
    phoneNumber: v.string(),
    message: v.string(),
    personalizedData: v.optional(v.record(v.string(), v.any())),
  },
  handler: async (_ctx, args) => {
    try {
      // Send via Twilio WhatsApp API with personalization support
      const result = await sendWhatsAppViaTwilio(args.phoneNumber, args.message, args.personalizedData);

      return {
        success: true,
        messageId: result.messageId,
        status: result.status,
      };
    } catch (error: unknown) {
      console.error("WhatsApp sending failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

// Send bulk messages via Twilio
export const sendBulkMessages = action({
  args: {
    type: v.union(v.literal("sms"), v.literal("whatsapp")),
    recipients: v.array(v.object({
      phoneNumber: v.string(),
      personalizedData: v.optional(v.record(v.string(), v.any())),
    })),
    message: v.string(),
  },
  handler: async (_ctx, args) => {
    try {
      // Use enhanced bulk messaging with rate limiting
      const results = await sendBulkMessagesViaTwilio(
        args.type,
        args.recipients,
        args.message
      );

      return {
        success: true,
        results,
        totalSent: results.filter(r => r.success).length,
        totalFailed: results.filter(r => !r.success).length,
      };
    } catch (error: unknown) {
      console.error("Bulk messaging failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

// Twilio integration functions
async function sendSMSViaTwilio(phoneNumber: string, message: string, templateData?: Record<string, any>) {
  // Get Twilio credentials from environment variables
  const accountSid = process.env.TWILIO_ACCOUNT_SID;
  const authToken = process.env.TWILIO_AUTH_TOKEN;
  const twilioPhoneNumber = process.env.TWILIO_PHONE_NUMBER;

  if (!accountSid || !authToken || !twilioPhoneNumber) {
    throw new Error('Twilio credentials not configured');
  }

  // Import Twilio dynamically to avoid issues in Convex environment
  const { Twilio } = await import('twilio');
  const client = new Twilio(accountSid, authToken);

  try {
    // Process message with template data if provided
    let processedMessage = message;
    if (templateData) {
      Object.entries(templateData).forEach(([key, value]) => {
        processedMessage = processedMessage.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
      });
    }

    // Format phone number to international format
    const formattedNumber = formatPhoneNumber(phoneNumber);

    const messageInstance = await client.messages.create({
      body: processedMessage,
      from: twilioPhoneNumber,
      to: formattedNumber,
      statusCallback: process.env.TWILIO_STATUS_CALLBACK_URL, // For delivery status updates
    });

    return {
      messageId: messageInstance.sid,
      status: messageInstance.status,
      to: messageInstance.to,
      from: messageInstance.from,
    };
  } catch (error: unknown) {
    console.error('Twilio SMS error:', error);
    throw new Error(`Failed to send SMS: ${error instanceof Error ? error.message : String(error)}`);
  }
}

async function sendWhatsAppViaTwilio(phoneNumber: string, message: string, templateData?: Record<string, any>) {
  // Get Twilio credentials from environment variables
  const accountSid = process.env.TWILIO_ACCOUNT_SID;
  const authToken = process.env.TWILIO_AUTH_TOKEN;
  const twilioWhatsAppNumber = process.env.TWILIO_WHATSAPP_NUMBER;

  if (!accountSid || !authToken || !twilioWhatsAppNumber) {
    throw new Error('Twilio WhatsApp credentials not configured');
  }

  // Import Twilio dynamically to avoid issues in Convex environment
  const { Twilio } = await import('twilio');
  const client = new Twilio(accountSid, authToken);

  try {
    // Process message with template data if provided
    let processedMessage = message;
    if (templateData) {
      Object.entries(templateData).forEach(([key, value]) => {
        processedMessage = processedMessage.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
      });
    }

    // Format phone number to international format
    const formattedNumber = formatPhoneNumber(phoneNumber);

    const messageInstance = await client.messages.create({
      body: processedMessage,
      from: `whatsapp:${twilioWhatsAppNumber}`,
      to: `whatsapp:${formattedNumber}`,
      statusCallback: process.env.TWILIO_STATUS_CALLBACK_URL, // For delivery status updates
    });

    return {
      messageId: messageInstance.sid,
      status: messageInstance.status,
      to: messageInstance.to,
      from: messageInstance.from,
    };
  } catch (error: unknown) {
    console.error('Twilio WhatsApp error:', error);
    throw new Error(`Failed to send WhatsApp message: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// Enhanced bulk messaging with rate limiting and error handling
async function sendBulkMessagesViaTwilio(
  type: 'sms' | 'whatsapp',
  recipients: Array<{
    phoneNumber: string;
    personalizedData?: Record<string, any>;
  }>,
  message: string
) {
  const results = [];
  const BATCH_SIZE = 10; // Process in batches to avoid rate limits
  const DELAY_BETWEEN_BATCHES = 1000; // 1 second delay between batches

  for (let i = 0; i < recipients.length; i += BATCH_SIZE) {
    const batch = recipients.slice(i, i + BATCH_SIZE);

    const batchPromises = batch.map(async (recipient) => {
      try {
        let result;
        if (type === 'sms') {
          result = await sendSMSViaTwilio(recipient.phoneNumber, message, recipient.personalizedData);
        } else {
          result = await sendWhatsAppViaTwilio(recipient.phoneNumber, message, recipient.personalizedData);
        }

        return {
          phoneNumber: recipient.phoneNumber,
          success: true,
          messageId: result.messageId,
          status: result.status,
        };
      } catch (error) {
        return {
          phoneNumber: recipient.phoneNumber,
          success: false,
          error: error instanceof Error ? error.message : String(error),
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Add delay between batches (except for the last batch)
    if (i + BATCH_SIZE < recipients.length) {
      await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES));
    }
  }

  return results;
}

// Helper function to format phone numbers
function formatPhoneNumber(phoneNumber: string): string {
  // Remove all non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, '');

  // Handle Kenyan numbers (254 country code)
  if (cleaned.startsWith('0') && cleaned.length === 10) {
    // Convert 0712345678 to +254712345678
    cleaned = '254' + cleaned.substring(1);
  } else if (cleaned.startsWith('254') && cleaned.length === 12) {
    // Already in correct format without +
    // Keep as is
  } else if (cleaned.startsWith('7') && cleaned.length === 9) {
    // Convert 712345678 to +254712345678
    cleaned = '254' + cleaned;
  }

  // Add + prefix if not present
  if (!cleaned.startsWith('+')) {
    cleaned = '+' + cleaned;
  }

  return cleaned;
}