import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Alert, AlertDescription } from "../ui/alert";
import { Badge } from "../ui/badge";
import { Loader2, Smartphone, CheckCircle, XCircle, Clock } from "lucide-react";
import { useConvex } from "convex/react";
import { getMPESAService, MPESAService, MPESAPaymentResult, PaymentStatus } from "../../lib/mpesa-service";
import { Id } from "../../../convex/_generated/dataModel";

interface MPESAPaymentProps {
  invoiceId: Id<"invoices">;
  amount: number;
  reference: string;
  onPaymentSuccess?: (paymentId: Id<"payments">) => void;
  onPaymentFailure?: (error: string) => void;
}

export const MPESAPayment: React.FC<MPESAPaymentProps> = ({
  invoiceId,
  amount,
  reference,
  onPaymentSuccess,
  onPaymentFailure,
}) => {
  const convex = useConvex();
  const mpesaService = getMPESAService(convex);

  const [phoneNumber, setPhoneNumber] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentResult, setPaymentResult] = useState<MPESAPaymentResult | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null);
  const [statusCheckInterval, setStatusCheckInterval] = useState<NodeJS.Timeout | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Format phone number as user types
  const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/\D/g, ""); // Remove non-digits
    
    // Format as user types
    if (value.startsWith("254")) {
      value = value.slice(0, 12); // Limit to 12 digits
    } else if (value.startsWith("0")) {
      value = value.slice(0, 10); // Limit to 10 digits
    } else {
      value = value.slice(0, 9); // Limit to 9 digits
    }
    
    setPhoneNumber(value);
  };

  // Format phone number for display
  const formatPhoneForDisplay = (phone: string): string => {
    if (phone.startsWith("254")) {
      return `+${phone.slice(0, 3)} ${phone.slice(3, 6)} ${phone.slice(6, 9)} ${phone.slice(9)}`;
    } else if (phone.startsWith("0")) {
      return `${phone.slice(0, 4)} ${phone.slice(4, 7)} ${phone.slice(7)}`;
    }
    return phone;
  };

  // Initiate M-PESA payment
  const handlePayment = async () => {
    if (!phoneNumber.trim()) {
      setError("Please enter your phone number");
      return;
    }

    setIsProcessing(true);
    setError(null);
    setPaymentResult(null);
    setPaymentStatus(null);

    try {
      const result = await mpesaService.initiatePayment({
        invoiceId,
        phoneNumber,
        amount,
        reference,
      });

      setPaymentResult(result);

      if (result.success && result.checkoutRequestId) {
        // Start polling for payment status
        startStatusPolling(result.checkoutRequestId);
      } else {
        setError(result.error || result.message);
        setIsProcessing(false);
        onPaymentFailure?.(result.error || result.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Payment failed";
      setError(errorMessage);
      setIsProcessing(false);
      onPaymentFailure?.(errorMessage);
    }
  };

  // Start polling payment status
  const startStatusPolling = (checkoutRequestId: string) => {
    let attempts = 0;
    const maxAttempts = 30; // Poll for 5 minutes (30 * 10 seconds)

    const interval = setInterval(async () => {
      attempts++;

      try {
        const status = await mpesaService.checkPaymentStatus(checkoutRequestId);
        setPaymentStatus(status);

        if (status.status === "completed") {
          clearInterval(interval);
          setIsProcessing(false);
          if (paymentResult?.paymentId) {
            onPaymentSuccess?.(paymentResult.paymentId);
          }
        } else if (status.status === "failed" || attempts >= maxAttempts) {
          clearInterval(interval);
          setIsProcessing(false);
          const errorMsg = status.status === "failed" ? status.message || "Payment failed" : "Payment timeout";
          setError(errorMsg);
          onPaymentFailure?.(errorMsg);
        }
      } catch (err) {
        console.error("Status check failed:", err);
        if (attempts >= maxAttempts) {
          clearInterval(interval);
          setIsProcessing(false);
          setError("Unable to verify payment status");
          onPaymentFailure?.("Unable to verify payment status");
        }
      }
    }, 10000); // Check every 10 seconds

    setStatusCheckInterval(interval);
  };

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
      }
    };
  }, [statusCheckInterval]);

  // Get status badge
  const getStatusBadge = () => {
    if (!paymentStatus) return null;

    const statusConfig = {
      pending: { variant: "secondary" as const, icon: Clock, text: "Processing" },
      completed: { variant: "default" as const, icon: CheckCircle, text: "Completed" },
      failed: { variant: "destructive" as const, icon: XCircle, text: "Failed" },
      refunded: { variant: "outline" as const, icon: XCircle, text: "Refunded" },
    };

    const config = statusConfig[paymentStatus.status];
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.text}
      </Badge>
    );
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <div className="flex items-center justify-center mb-2">
          <Smartphone className="h-8 w-8 text-green-600" />
        </div>
        <CardTitle>M-PESA Payment</CardTitle>
        <CardDescription>
          Pay {MPESAService.formatAmount(amount)} via M-PESA
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!paymentResult && (
          <>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                placeholder="0712345678 or 254712345678"
                value={phoneNumber}
                onChange={handlePhoneNumberChange}
                disabled={isProcessing}
                className="text-center"
              />
              {phoneNumber && (
                <p className="text-sm text-muted-foreground text-center">
                  {formatPhoneForDisplay(phoneNumber)}
                </p>
              )}
            </div>

            <Button
              onClick={handlePayment}
              disabled={isProcessing || !phoneNumber.trim()}
              className="w-full"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Initiating Payment...
                </>
              ) : (
                `Pay ${MPESAService.formatAmount(amount)}`
              )}
            </Button>
          </>
        )}

        {paymentResult && paymentResult.success && (
          <div className="space-y-4">
            <Alert>
              <Smartphone className="h-4 w-4" />
              <AlertDescription>
                STK Push sent to {formatPhoneForDisplay(phoneNumber)}. 
                Please check your phone and enter your M-PESA PIN to complete the payment.
              </AlertDescription>
            </Alert>

            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Status:</span>
              {getStatusBadge()}
            </div>

            {isProcessing && (
              <div className="flex items-center justify-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Waiting for payment confirmation...</span>
              </div>
            )}

            {paymentStatus?.message && (
              <p className="text-sm text-center text-muted-foreground">
                {paymentStatus.message}
              </p>
            )}
          </div>
        )}

        {error && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {paymentResult && !paymentResult.success && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              {paymentResult.error || paymentResult.message}
            </AlertDescription>
          </Alert>
        )}

        <div className="text-xs text-muted-foreground text-center space-y-1">
          <p>Reference: {reference}</p>
          <p>Secure payment powered by M-PESA</p>
        </div>
      </CardContent>
    </Card>
  );
};