import { describe, it, expect } from "vitest";
import { 
  validateLeaseData,
  sanitizeLeaseData,
  validateLeaseStatus,
  validateESignatureStatus,
  validateLeaseTerms,
  validateTenantOnboardingData,
  sanitizeTenantData,
  validateLeaseRenewal,
  validateLeaseTermination,
  validateDateRange,
  validatePositiveNumber,
  validateNonNegativeNumber,
  validateEmail,
  validatePhoneNumber
} from "../lib/validation";

describe("Lease and Tenant Data Model Validation Tests", () => {

  describe("Lease Data Validation", () => {
    it("should validate lease data correctly", () => {
      const validData = {
        propertyId: "property123",
        unitId: "unit123",
        tenantId: "tenant123",
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: 50000,
        deposit: 100000,
      };

      expect(() => validateLeaseData(validData)).not.toThrow();
    });

    it("should throw error for missing property ID", () => {
      const invalidData = {
        unitId: "unit123",
        tenantId: "tenant123",
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: 50000,
        deposit: 100000,
      };

      expect(() => validateLeaseData(invalidData)).toThrow("Property ID is required");
    });

    it("should throw error for invalid date range", () => {
      const now = Date.now();
      const invalidData = {
        propertyId: "property123",
        unitId: "unit123",
        tenantId: "tenant123",
        startDate: now,
        endDate: now - 1000, // End date before start date
        monthlyRent: 50000,
        deposit: 100000,
      };

      expect(() => validateLeaseData(invalidData)).toThrow("End date must be after start date");
    });

    it("should throw error for invalid rent amount", () => {
      const invalidData = {
        propertyId: "property123",
        unitId: "unit123",
        tenantId: "tenant123",
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: -1000,
        deposit: 100000,
      };

      expect(() => validateLeaseData(invalidData)).toThrow("Monthly rent must be greater than 0");
    });

    it("should sanitize lease data correctly", () => {
      const dirtyData = {
        documentUrl: "  https://example.com/lease.pdf  ",
      };

      const sanitized = sanitizeLeaseData(dirtyData);
      expect(sanitized.documentUrl).toBe("https://example.com/lease.pdf");
    });
  });

  describe("Lease Status Validation", () => {
    it("should validate lease status correctly", () => {
      const validStatuses = ["active", "expired", "terminated", "pending"];
      
      validStatuses.forEach(status => {
        expect(() => validateLeaseStatus(status)).not.toThrow();
      });
    });

    it("should throw error for invalid lease status", () => {
      expect(() => validateLeaseStatus("invalid")).toThrow("Invalid lease status");
    });

    it("should validate e-signature status correctly", () => {
      const validStatuses = ["pending", "signed", "expired"];
      
      validStatuses.forEach(status => {
        expect(() => validateESignatureStatus(status)).not.toThrow();
      });
    });

    it("should throw error for invalid e-signature status", () => {
      expect(() => validateESignatureStatus("invalid")).toThrow("Invalid e-signature status");
    });
  });

  describe("Lease Terms Validation", () => {
    it("should validate lease terms correctly", () => {
      const validTerms = {
        noticePeriod: 30,
        lateFeePercentage: 5,
        gracePeriod: 5,
        renewalOption: true,
      };

      expect(() => validateLeaseTerms(validTerms)).not.toThrow();
    });

    it("should throw error for negative notice period", () => {
      const invalidTerms = {
        noticePeriod: -10,
        lateFeePercentage: 5,
        gracePeriod: 5,
        renewalOption: true,
      };

      expect(() => validateLeaseTerms(invalidTerms)).toThrow("Notice period cannot be negative");
    });

    it("should throw error for invalid late fee percentage", () => {
      const invalidTerms = {
        noticePeriod: 30,
        lateFeePercentage: 150, // Over 100%
        gracePeriod: 5,
        renewalOption: true,
      };

      expect(() => validateLeaseTerms(invalidTerms)).toThrow("Late fee percentage must be between 0 and 100");
    });

    it("should throw error for negative grace period", () => {
      const invalidTerms = {
        noticePeriod: 30,
        lateFeePercentage: 5,
        gracePeriod: -5,
        renewalOption: true,
      };

      expect(() => validateLeaseTerms(invalidTerms)).toThrow("Grace period cannot be negative");
    });

    it("should throw error for invalid renewal option", () => {
      const invalidTerms = {
        noticePeriod: 30,
        lateFeePercentage: 5,
        gracePeriod: 5,
        renewalOption: "yes", // Should be boolean
      };

      expect(() => validateLeaseTerms(invalidTerms)).toThrow("Renewal option must be a boolean value");
    });
  });

  describe("Tenant Onboarding Validation", () => {
    it("should validate tenant onboarding data correctly", () => {
      const validData = {
        email: "<EMAIL>",
        name: "John Doe",
        phone: "+254712345678",
        kycStatus: "pending",
      };

      expect(() => validateTenantOnboardingData(validData)).not.toThrow();
    });

    it("should throw error for invalid email", () => {
      const invalidData = {
        email: "invalid-email",
        name: "John Doe",
        kycStatus: "pending",
      };

      expect(() => validateTenantOnboardingData(invalidData)).toThrow("Valid email address is required");
    });

    it("should throw error for missing name", () => {
      const invalidData = {
        email: "<EMAIL>",
        name: "",
        kycStatus: "pending",
      };

      expect(() => validateTenantOnboardingData(invalidData)).toThrow("Tenant name is required");
    });

    it("should throw error for invalid phone number", () => {
      const invalidData = {
        email: "<EMAIL>",
        name: "John Doe",
        phone: "invalid-phone",
        kycStatus: "pending",
      };

      expect(() => validateTenantOnboardingData(invalidData)).toThrow("Valid phone number is required");
    });

    it("should sanitize tenant data correctly", () => {
      const dirtyData = {
        name: "  John Doe  ",
        email: "  <EMAIL>  ",
        phone: "+254 712 345 678",
      };

      const sanitized = sanitizeTenantData(dirtyData);
      expect(sanitized.name).toBe("John Doe");
      expect(sanitized.email).toBe("<EMAIL>");
      expect(sanitized.phone).toBe("+254712345678");
    });
  });

  describe("Lease Renewal Validation", () => {
    it("should validate lease renewal correctly", () => {
      const currentLease = {
        status: "active",
        terms: { renewalOption: true },
        endDate: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days from now
      };

      const renewalData = {
        newEndDate: Date.now() + 395 * 24 * 60 * 60 * 1000, // 395 days from now
        newMonthlyRent: 55000,
      };

      expect(() => validateLeaseRenewal(currentLease, renewalData)).not.toThrow();
    });

    it("should throw error for inactive lease renewal", () => {
      const currentLease = {
        status: "expired",
        terms: { renewalOption: true },
        endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      };

      const renewalData = {
        newEndDate: Date.now() + 395 * 24 * 60 * 60 * 1000,
      };

      expect(() => validateLeaseRenewal(currentLease, renewalData)).toThrow("Only active leases can be renewed");
    });

    it("should throw error for lease without renewal option", () => {
      const currentLease = {
        status: "active",
        terms: { renewalOption: false },
        endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      };

      const renewalData = {
        newEndDate: Date.now() + 395 * 24 * 60 * 60 * 1000,
      };

      expect(() => validateLeaseRenewal(currentLease, renewalData)).toThrow("This lease does not have a renewal option");
    });

    it("should throw error for invalid new end date", () => {
      const currentLease = {
        status: "active",
        terms: { renewalOption: true },
        endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      };

      const renewalData = {
        newEndDate: Date.now() + 15 * 24 * 60 * 60 * 1000, // Before current end date
      };

      expect(() => validateLeaseRenewal(currentLease, renewalData)).toThrow("New end date must be after current end date");
    });
  });

  describe("Lease Termination Validation", () => {
    it("should validate lease termination correctly", () => {
      const lease = {
        status: "active",
        endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      };

      const terminationData = {
        terminationDate: Date.now() + 15 * 24 * 60 * 60 * 1000,
        reason: "Tenant requested termination",
        earlyTermination: true,
      };

      expect(() => validateLeaseTermination(lease, terminationData)).not.toThrow();
    });

    it("should throw error for inactive lease termination", () => {
      const lease = {
        status: "expired",
        endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      };

      const terminationData = {
        terminationDate: Date.now() + 15 * 24 * 60 * 60 * 1000,
        reason: "Tenant requested termination",
        earlyTermination: true,
      };

      expect(() => validateLeaseTermination(lease, terminationData)).toThrow("Only active leases can be terminated");
    });

    it("should throw error for missing termination reason", () => {
      const lease = {
        status: "active",
        endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      };

      const terminationData = {
        terminationDate: Date.now() + 15 * 24 * 60 * 60 * 1000,
        reason: "",
        earlyTermination: true,
      };

      expect(() => validateLeaseTermination(lease, terminationData)).toThrow("Termination reason is required");
    });

    it("should throw error for early termination without flag", () => {
      const lease = {
        status: "active",
        endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      };

      const terminationData = {
        terminationDate: Date.now() + 15 * 24 * 60 * 60 * 1000, // Before lease end
        reason: "Tenant requested termination",
        earlyTermination: false, // Should be true for early termination
      };

      expect(() => validateLeaseTermination(lease, terminationData)).toThrow("Early termination flag must be set for termination before lease end date");
    });
  });

  describe("Utility Validation Functions", () => {
    it("should validate date ranges correctly", () => {
      const startDate = Date.now();
      const endDate = startDate + 365 * 24 * 60 * 60 * 1000;

      expect(() => validateDateRange(startDate, endDate)).not.toThrow();
    });

    it("should throw error for invalid date range", () => {
      const startDate = Date.now();
      const endDate = startDate - 1000; // Before start date

      expect(() => validateDateRange(startDate, endDate)).toThrow("End date must be after start date");
    });

    it("should validate positive numbers correctly", () => {
      expect(() => validatePositiveNumber(100, "Test value")).not.toThrow();
    });

    it("should throw error for non-positive numbers", () => {
      expect(() => validatePositiveNumber(0, "Test value")).toThrow("Test value must be greater than 0");
      expect(() => validatePositiveNumber(-10, "Test value")).toThrow("Test value must be greater than 0");
    });

    it("should validate non-negative numbers correctly", () => {
      expect(() => validateNonNegativeNumber(0, "Test value")).not.toThrow();
      expect(() => validateNonNegativeNumber(100, "Test value")).not.toThrow();
    });

    it("should throw error for negative numbers", () => {
      expect(() => validateNonNegativeNumber(-10, "Test value")).toThrow("Test value cannot be negative");
    });

    it("should validate email addresses correctly", () => {
      const validEmails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    it("should reject invalid email addresses", () => {
      const invalidEmails = [
        "invalid-email",
        "@domain.com",
        "user@",
        "user <EMAIL>",
      ];

      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false);
      });
    });

    it("should validate Kenyan phone numbers correctly", () => {
      const validPhones = [
        "+254712345678",
        "0712345678",
        "+254 712 345 678",
        "0 712 345 678",
      ];

      validPhones.forEach(phone => {
        expect(validatePhoneNumber(phone)).toBe(true);
      });
    });

    it("should reject invalid phone numbers", () => {
      const invalidPhones = [
        "712345678", // Missing country code or leading 0
        "+254812345678", // Invalid operator code
        "+254712345", // Too short
        "invalid-phone",
      ];

      invalidPhones.forEach(phone => {
        expect(validatePhoneNumber(phone)).toBe(false);
      });
    });
  });
});