import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select } from '../ui/select';
import { Textarea } from '../ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

interface UnitFormData {
  unitNumber: string;
  type: 'apartment' | 'office' | 'retail' | 'parking';
  size: number;
  bedrooms?: number;
  bathrooms?: number;
  rent: number;
  deposit: number;
  amenities: string[];
  description?: string;
}

interface UnitFormProps {
  propertyId: Id<"properties">;
  unitId?: Id<"units">;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const UnitForm: React.FC<UnitFormProps> = ({
  propertyId,
  unitId,
  onSuccess,
  onCancel,
}) => {
  const createUnit = useMutation(api.units.createUnit);
  const updateUnit = useMutation(api.units.updateUnit);
  const existingUnit = useQuery(
    api.units.getUnitById,
    unitId ? { id: unitId } : "skip"
  );

  const [formData, setFormData] = useState<UnitFormData>({
    unitNumber: '',
    type: 'apartment',
    size: 0,
    bedrooms: 1,
    bathrooms: 1,
    rent: 0,
    deposit: 0,
    amenities: [],
    description: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [amenityInput, setAmenityInput] = useState('');

  // Populate form with existing data when editing
  React.useEffect(() => {
    if (existingUnit) {
      setFormData({
        unitNumber: existingUnit.unitNumber,
        type: existingUnit.type,
        size: existingUnit.size,
        bedrooms: existingUnit.bedrooms,
        bathrooms: existingUnit.bathrooms,
        rent: existingUnit.rent,
        deposit: existingUnit.deposit,
        amenities: existingUnit.amenities || [],
        description: existingUnit.description || '',
      });
    }
  }, [existingUnit]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.unitNumber.trim()) {
      newErrors.unitNumber = 'Unit number is required';
    }

    if (formData.size <= 0) {
      newErrors.size = 'Size must be greater than 0';
    }

    if (formData.rent <= 0) {
      newErrors.rent = 'Rent must be greater than 0';
    }

    if (formData.deposit < 0) {
      newErrors.deposit = 'Deposit cannot be negative';
    }

    if (formData.type === 'apartment') {
      if (!formData.bedrooms || formData.bedrooms <= 0) {
        newErrors.bedrooms = 'Bedrooms is required for apartments';
      }
      if (!formData.bathrooms || formData.bathrooms <= 0) {
        newErrors.bathrooms = 'Bathrooms is required for apartments';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      if (unitId) {
        await updateUnit({
          id: unitId,
          ...formData,
        });
      } else {
        await createUnit({
          propertyId,
          ...formData,
        });
      }
      
      onSuccess?.();
    } catch (error) {
      console.error('Error saving unit:', error);
      setErrors({ submit: 'Failed to save unit. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (field: keyof UnitFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const addAmenity = () => {
    if (amenityInput.trim() && !formData.amenities.includes(amenityInput.trim())) {
      setFormData(prev => ({
        ...prev,
        amenities: [...prev.amenities, amenityInput.trim()],
      }));
      setAmenityInput('');
    }
  };

  const removeAmenity = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.filter(a => a !== amenity),
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addAmenity();
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>
          {unitId ? 'Edit Unit' : 'Create New Unit'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="unitNumber">Unit Number *</Label>
              <Input
                id="unitNumber"
                value={formData.unitNumber}
                onChange={(e) => updateFormData('unitNumber', e.target.value)}
                placeholder="e.g., A101, Office 205"
                className={errors.unitNumber ? 'border-red-500' : ''}
              />
              {errors.unitNumber && (
                <p className="text-sm text-red-500 mt-1">{errors.unitNumber}</p>
              )}
            </div>

            <div>
              <Label htmlFor="type">Unit Type *</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => updateFormData('type', value)}
              >
                <option value="apartment">Apartment</option>
                <option value="office">Office</option>
                <option value="retail">Retail</option>
                <option value="parking">Parking</option>
              </Select>
            </div>
          </div>

          {/* Size and Layout */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="size">Size (sq ft) *</Label>
              <Input
                id="size"
                type="number"
                min="1"
                value={formData.size || ''}
                onChange={(e) => updateFormData('size', parseInt(e.target.value) || 0)}
                placeholder="Enter size in square feet"
                className={errors.size ? 'border-red-500' : ''}
              />
              {errors.size && (
                <p className="text-sm text-red-500 mt-1">{errors.size}</p>
              )}
            </div>

            {formData.type === 'apartment' && (
              <>
                <div>
                  <Label htmlFor="bedrooms">Bedrooms *</Label>
                  <Input
                    id="bedrooms"
                    type="number"
                    min="0"
                    value={formData.bedrooms || ''}
                    onChange={(e) => updateFormData('bedrooms', parseInt(e.target.value) || 0)}
                    className={errors.bedrooms ? 'border-red-500' : ''}
                  />
                  {errors.bedrooms && (
                    <p className="text-sm text-red-500 mt-1">{errors.bedrooms}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="bathrooms">Bathrooms *</Label>
                  <Input
                    id="bathrooms"
                    type="number"
                    min="0"
                    step="0.5"
                    value={formData.bathrooms || ''}
                    onChange={(e) => updateFormData('bathrooms', parseFloat(e.target.value) || 0)}
                    className={errors.bathrooms ? 'border-red-500' : ''}
                  />
                  {errors.bathrooms && (
                    <p className="text-sm text-red-500 mt-1">{errors.bathrooms}</p>
                  )}
                </div>
              </>
            )}
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="rent">Monthly Rent *</Label>
              <Input
                id="rent"
                type="number"
                min="0"
                value={formData.rent || ''}
                onChange={(e) => updateFormData('rent', parseInt(e.target.value) || 0)}
                placeholder="Enter monthly rent amount"
                className={errors.rent ? 'border-red-500' : ''}
              />
              {errors.rent && (
                <p className="text-sm text-red-500 mt-1">{errors.rent}</p>
              )}
            </div>

            <div>
              <Label htmlFor="deposit">Security Deposit *</Label>
              <Input
                id="deposit"
                type="number"
                min="0"
                value={formData.deposit || ''}
                onChange={(e) => updateFormData('deposit', parseInt(e.target.value) || 0)}
                placeholder="Enter security deposit amount"
                className={errors.deposit ? 'border-red-500' : ''}
              />
              {errors.deposit && (
                <p className="text-sm text-red-500 mt-1">{errors.deposit}</p>
              )}
            </div>
          </div>

          {/* Amenities */}
          <div>
            <Label>Amenities</Label>
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Input
                  value={amenityInput}
                  onChange={(e) => setAmenityInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Add amenity (e.g., Air Conditioning, Balcony)"
                />
                <Button
                  type="button"
                  onClick={addAmenity}
                  variant="outline"
                >
                  Add
                </Button>
              </div>
              
              {formData.amenities.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.amenities.map((amenity, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                    >
                      {amenity}
                      <button
                        type="button"
                        onClick={() => removeAmenity(amenity)}
                        className="ml-2 text-blue-600 hover:text-blue-800"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => updateFormData('description', e.target.value)}
              placeholder="Enter unit description, special features, etc."
              rows={3}
            />
          </div>

          {/* Error Display */}
          {errors.submit && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : unitId ? 'Update Unit' : 'Create Unit'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};