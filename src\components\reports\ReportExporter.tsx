import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Checkbox } from '../ui/checkbox';
import { Label } from '../ui/label';
import { 
  FileText, 
  Download, 
  FileSpreadsheet, 
  FileImage,
  Calendar,
  Building,
  DollarSign
} from 'lucide-react';

interface ReportExporterProps {
  reportData: any;
  onClose: () => void;
}

export const ReportExporter: React.FC<ReportExporterProps> = ({ reportData, onClose }) => {
  const [exportFormat, setExportFormat] = useState<'pdf' | 'excel' | 'csv'>('pdf');
  const [selectedSections, setSelectedSections] = useState<string[]>([
    'executive-summary',
    'financial-overview',
    'property-performance',
    'operational-metrics'
  ]);
  const [isExporting, setIsExporting] = useState(false);

  const reportConfigs = useQuery(api.executiveReports.getReportConfigurations);

  const availableSections = [
    { id: 'executive-summary', name: 'Executive Summary', description: 'High-level KPIs and trends' },
    { id: 'financial-overview', name: 'Financial Overview', description: 'Revenue, expenses, and profitability' },
    { id: 'property-performance', name: 'Property Performance', description: 'Property-by-property breakdown' },
    { id: 'operational-metrics', name: 'Operational Metrics', description: 'Maintenance and occupancy data' },
    { id: 'payment-analytics', name: 'Payment Analytics', description: 'Payment methods and success rates' },
    { id: 'trend-analysis', name: 'Trend Analysis', description: 'Period-over-period comparisons' },
  ];

  const formatOptions = [
    {
      value: 'pdf',
      label: 'PDF Report',
      description: 'Professional formatted report for presentations',
      icon: FileText,
      color: 'text-red-600'
    },
    {
      value: 'excel',
      label: 'Excel Workbook',
      description: 'Detailed data with multiple sheets for analysis',
      icon: FileSpreadsheet,
      color: 'text-green-600'
    },
    {
      value: 'csv',
      label: 'CSV Data',
      description: 'Raw data export for custom analysis',
      icon: FileImage,
      color: 'text-blue-600'
    }
  ];

  const handleSectionToggle = (sectionId: string) => {
    setSelectedSections(prev => 
      prev.includes(sectionId)
        ? prev.filter(s => s !== sectionId)
        : [...prev, sectionId]
    );
  };

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // Create export data structure
      const exportData = {
        metadata: {
          generatedAt: new Date().toISOString(),
          format: exportFormat,
          sections: selectedSections,
          reportPeriod: reportData.reportMetadata.period,
        },
        data: {
          executiveSummary: selectedSections.includes('executive-summary') ? reportData.executiveSummary : null,
          keyMetrics: selectedSections.includes('financial-overview') ? reportData.keyMetrics : null,
          propertyPerformance: selectedSections.includes('property-performance') ? reportData.propertyPerformance : null,
          trends: selectedSections.includes('trend-analysis') ? reportData.trends : null,
        }
      };

      // Handle different export formats
      switch (exportFormat) {
        case 'pdf':
          await exportToPDF(exportData);
          break;
        case 'excel':
          await exportToExcel(exportData);
          break;
        case 'csv':
          await exportToCSV(exportData);
          break;
      }
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const exportToPDF = async (data: any) => {
    // Create PDF content
    const pdfContent = generatePDFContent(data);
    
    // Create blob and download
    const blob = new Blob([pdfContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `executive-report-${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const exportToExcel = async (data: any) => {
    // Create Excel-compatible CSV with multiple sheets simulation
    const csvContent = generateExcelContent(data);
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `executive-report-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const exportToCSV = async (data: any) => {
    const csvContent = generateCSVContent(data);
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `executive-report-data-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const generatePDFContent = (data: any) => {
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('en-KE', {
        style: 'currency',
        currency: 'KES',
        minimumFractionDigits: 0,
      }).format(amount);
    };

    const formatPercentage = (value: number) => {
      return `${value.toFixed(1)}%`;
    };

    return `
<!DOCTYPE html>
<html>
<head>
    <title>Executive Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 40px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .section { margin-bottom: 30px; }
        .kpi-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .kpi-card { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        .kpi-value { font-size: 24px; font-weight: bold; color: #2563eb; }
        .kpi-label { color: #666; font-size: 14px; }
        .property-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .property-table th, .property-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        .property-table th { background-color: #f5f5f5; }
        .positive { color: #16a34a; }
        .negative { color: #dc2626; }
        @media print { body { margin: 20px; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>Executive Report</h1>
        <p>Generated on ${new Date().toLocaleDateString()}</p>
        <p>Period: ${new Date(data.metadata.reportPeriod.startDate).toLocaleDateString()} - ${new Date(data.metadata.reportPeriod.endDate).toLocaleDateString()}</p>
    </div>

    ${data.data.executiveSummary ? `
    <div class="section">
        <h2>Executive Summary</h2>
        <div class="kpi-grid">
            <div class="kpi-card">
                <div class="kpi-value">${formatCurrency(data.data.executiveSummary.totalRevenue)}</div>
                <div class="kpi-label">Total Revenue</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">${formatCurrency(data.data.executiveSummary.netIncome)}</div>
                <div class="kpi-label">Net Income</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">${formatPercentage(data.data.executiveSummary.occupancyRate)}</div>
                <div class="kpi-label">Occupancy Rate</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">${formatPercentage(data.data.executiveSummary.collectionRate)}</div>
                <div class="kpi-label">Collection Rate</div>
            </div>
        </div>
    </div>
    ` : ''}

    ${data.data.propertyPerformance ? `
    <div class="section">
        <h2>Property Performance</h2>
        <table class="property-table">
            <thead>
                <tr>
                    <th>Property</th>
                    <th>Revenue</th>
                    <th>Expenses</th>
                    <th>Net Income</th>
                    <th>Occupancy</th>
                </tr>
            </thead>
            <tbody>
                ${data.data.propertyPerformance.map((property: any) => `
                <tr>
                    <td>${property.propertyName}</td>
                    <td>${formatCurrency(property.revenue)}</td>
                    <td>${formatCurrency(property.expenses)}</td>
                    <td class="${property.netIncome >= 0 ? 'positive' : 'negative'}">${formatCurrency(property.netIncome)}</td>
                    <td>${formatPercentage(property.occupancyRate)}</td>
                </tr>
                `).join('')}
            </tbody>
        </table>
    </div>
    ` : ''}

    <div class="section">
        <p><em>This report was generated automatically by EstatePulse on ${new Date().toLocaleString()}</em></p>
    </div>
</body>
</html>`;
  };

  const generateExcelContent = (data: any) => {
    let csvContent = "Executive Report Data\n\n";
    
    if (data.data.executiveSummary) {
      csvContent += "Executive Summary\n";
      csvContent += "Metric,Value\n";
      csvContent += `Total Revenue,${data.data.executiveSummary.totalRevenue}\n`;
      csvContent += `Net Income,${data.data.executiveSummary.netIncome}\n`;
      csvContent += `Occupancy Rate,${data.data.executiveSummary.occupancyRate}%\n`;
      csvContent += `Collection Rate,${data.data.executiveSummary.collectionRate}%\n\n`;
    }

    if (data.data.propertyPerformance) {
      csvContent += "Property Performance\n";
      csvContent += "Property Name,Revenue,Expenses,Net Income,Occupancy Rate,Units,Occupied Units\n";
      data.data.propertyPerformance.forEach((property: any) => {
        csvContent += `${property.propertyName},${property.revenue},${property.expenses},${property.netIncome},${property.occupancyRate}%,${property.unitCount},${property.occupiedUnits}\n`;
      });
    }

    return csvContent;
  };

  const generateCSVContent = (data: any) => {
    let csvContent = "";
    
    if (data.data.propertyPerformance) {
      csvContent += "Property Name,Revenue,Expenses,Net Income,Occupancy Rate,Units,Occupied Units\n";
      data.data.propertyPerformance.forEach((property: any) => {
        csvContent += `"${property.propertyName}",${property.revenue},${property.expenses},${property.netIncome},${property.occupancyRate},${property.unitCount},${property.occupiedUnits}\n`;
      });
    }

    return csvContent;
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Export Report</DialogTitle>
          <DialogDescription>
            Choose format and sections to include in your export
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div>
            <Label className="text-base font-medium">Export Format</Label>
            <div className="grid grid-cols-1 gap-3 mt-3">
              {formatOptions.map((option) => {
                const IconComponent = option.icon;
                return (
                  <Card 
                    key={option.value}
                    className={`cursor-pointer transition-colors ${
                      exportFormat === option.value ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => setExportFormat(option.value as 'pdf' | 'excel' | 'csv')}
                  >
                    <CardContent className="flex items-center p-4">
                      <IconComponent className={`h-8 w-8 mr-4 ${option.color}`} />
                      <div className="flex-1">
                        <div className="font-medium">{option.label}</div>
                        <div className="text-sm text-gray-600">{option.description}</div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Section Selection */}
          <div>
            <Label className="text-base font-medium">Report Sections</Label>
            <div className="grid grid-cols-1 gap-2 mt-3">
              {availableSections.map((section) => (
                <div key={section.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                  <Checkbox
                    id={section.id}
                    checked={selectedSections.includes(section.id)}
                    onCheckedChange={() => handleSectionToggle(section.id)}
                  />
                  <div className="flex-1">
                    <Label htmlFor={section.id} className="font-medium cursor-pointer">
                      {section.name}
                    </Label>
                    <p className="text-sm text-gray-600 mt-1">{section.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Report Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Report Preview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Period:</span>
                <span>
                  {new Date(reportData.reportMetadata.period.startDate).toLocaleDateString()} - {' '}
                  {new Date(reportData.reportMetadata.period.endDate).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Properties:</span>
                <span>{reportData.reportMetadata.propertyCount} properties</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Sections:</span>
                <span>{selectedSections.length} sections</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Format:</span>
                <Badge variant="outline">{exportFormat.toUpperCase()}</Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleExport} 
            disabled={selectedSections.length === 0 || isExporting}
          >
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
