/**
 * Update Channel Management
 * Handles different update channels (stable, beta, alpha)
 */

export type UpdateChannel = 'stable' | 'beta' | 'alpha'

export interface ChannelConfig {
  name: string
  description: string
  checkInterval: number // in milliseconds
  autoDownload: boolean
  autoInstall: boolean
}

export const UPDATE_CHANNELS: Record<UpdateChannel, ChannelConfig> = {
  stable: {
    name: 'Stable',
    description: 'Stable releases with full testing',
    checkInterval: 4 * 60 * 60 * 1000, // 4 hours
    autoDownload: false,
    autoInstall: false
  },
  beta: {
    name: 'Beta',
    description: 'Beta releases with new features',
    checkInterval: 2 * 60 * 60 * 1000, // 2 hours
    autoDownload: false,
    autoInstall: false
  },
  alpha: {
    name: 'Alpha',
    description: 'Alpha releases with experimental features',
    checkInterval: 1 * 60 * 60 * 1000, // 1 hour
    autoDownload: false,
    autoInstall: false
  }
}

export class UpdateChannelManager {
  private static instance: UpdateChannelManager
  private currentChannel: UpdateChannel = 'stable'

  private constructor() {
    this.loadChannelFromStorage()
  }

  public static getInstance(): UpdateChannelManager {
    if (!UpdateChannelManager.instance) {
      UpdateChannelManager.instance = new UpdateChannelManager()
    }
    return UpdateChannelManager.instance
  }

  private loadChannelFromStorage(): void {
    try {
      const stored = localStorage.getItem('update-channel')
      if (stored && this.isValidChannel(stored)) {
        this.currentChannel = stored as UpdateChannel
      }
    } catch (error) {
      console.warn('Failed to load update channel from storage:', error)
    }
  }

  private saveChannelToStorage(): void {
    try {
      localStorage.setItem('update-channel', this.currentChannel)
    } catch (error) {
      console.warn('Failed to save update channel to storage:', error)
    }
  }

  private isValidChannel(channel: string): boolean {
    return Object.keys(UPDATE_CHANNELS).includes(channel)
  }

  public getCurrentChannel(): UpdateChannel {
    return this.currentChannel
  }

  public setChannel(channel: UpdateChannel): void {
    if (!this.isValidChannel(channel)) {
      throw new Error(`Invalid update channel: ${channel}`)
    }
    
    this.currentChannel = channel
    this.saveChannelToStorage()
  }

  public getChannelConfig(channel?: UpdateChannel): ChannelConfig {
    const targetChannel = channel || this.currentChannel
    return UPDATE_CHANNELS[targetChannel]
  }

  public getAllChannels(): Array<{ channel: UpdateChannel; config: ChannelConfig }> {
    return Object.entries(UPDATE_CHANNELS).map(([channel, config]) => ({
      channel: channel as UpdateChannel,
      config
    }))
  }

  public getUpdateFeedUrl(channel?: UpdateChannel): string {
    const targetChannel = channel || this.currentChannel
    
    // In production, these would point to different release channels
    const baseUrl = 'https://api.github.com/repos/estate-pulse/estate-pulse-app/releases'
    
    switch (targetChannel) {
      case 'stable':
        return `${baseUrl}/latest`
      case 'beta':
        return `${baseUrl}?prerelease=true`
      case 'alpha':
        return `${baseUrl}?prerelease=true&per_page=1`
      default:
        return `${baseUrl}/latest`
    }
  }

  public shouldAutoCheck(): boolean {
    const config = this.getChannelConfig()
    return config.checkInterval > 0
  }

  public getCheckInterval(): number {
    return this.getChannelConfig().checkInterval
  }

  public shouldAutoDownload(): boolean {
    return this.getChannelConfig().autoDownload
  }

  public shouldAutoInstall(): boolean {
    return this.getChannelConfig().autoInstall
  }
}

// Export singleton instance
export const updateChannelManager = UpdateChannelManager.getInstance()

// Hook for React components
export const useUpdateChannel = () => {
  const [channel, setChannelState] = React.useState<UpdateChannel>(
    updateChannelManager.getCurrentChannel()
  )

  const setChannel = (newChannel: UpdateChannel) => {
    updateChannelManager.setChannel(newChannel)
    setChannelState(newChannel)
  }

  return {
    channel,
    setChannel,
    config: updateChannelManager.getChannelConfig(),
    allChannels: updateChannelManager.getAllChannels()
  }
}

// Add React import for the hook
import React from 'react'