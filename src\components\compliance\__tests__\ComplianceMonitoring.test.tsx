import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ComplianceMonitoring } from '../ComplianceMonitoring';

// Mock Convex hooks
const mockUseQuery = vi.fn();
const mockUseMutation = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: () => mockUseQuery(),
  useMutation: () => mockUseMutation(),
}));

// Mock API
vi.mock('../../../../convex/_generated/api', () => ({
  api: {
    compliance: {
      getComplianceChecklists: 'getComplianceChecklists',
      getComplianceAlerts: 'getComplianceAlerts',
      getAuditTrail: 'getAuditTrail',
      runComplianceCheck: 'runComplianceCheck',
      scheduleComplianceReview: 'scheduleComplianceReview',
    },
  },
}));

describe('ComplianceMonitoring', () => {
  const mockRunComplianceCheck = vi.fn();
  const mockScheduleReview = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseMutation.mockReturnValue(mockRunComplianceCheck);
  });

  it('renders compliance monitoring dashboard', () => {
    mockUseQuery.mockReturnValue([]);

    render(<ComplianceMonitoring />);

    expect(screen.getByText('Compliance Monitoring')).toBeInTheDocument();
    expect(screen.getByText('Monitor compliance status, track alerts, and manage regulatory requirements')).toBeInTheDocument();
  });

  it('displays alert summary cards', () => {
    const mockAlerts = [
      {
        entityId: 'entity1',
        entityType: 'tenant',
        severity: 'critical',
        message: 'Missing KYC documents',
        createdAt: Date.now(),
      },
      {
        entityId: 'entity2',
        entityType: 'vendor',
        severity: 'error',
        message: 'Expired license',
        createdAt: Date.now(),
      },
      {
        entityId: 'entity3',
        entityType: 'tenant',
        severity: 'warning',
        message: 'Document review needed',
        createdAt: Date.now(),
      },
    ];

    mockUseQuery.mockImplementation((query) => {
      if (query === 'getComplianceAlerts') return mockAlerts;
      return [];
    });

    render(<ComplianceMonitoring />);

    expect(screen.getByText('Total Alerts')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument(); // Total alerts
    expect(screen.getByText('Critical')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // Critical alerts
  });

  it('shows critical alerts banner when critical alerts exist', () => {
    const mockAlerts = [
      {
        entityId: 'entity1',
        entityType: 'tenant',
        severity: 'critical',
        message: 'Critical compliance issue',
        createdAt: Date.now(),
      },
    ];

    mockUseQuery.mockImplementation((query) => {
      if (query === 'getComplianceAlerts') return mockAlerts;
      return [];
    });

    render(<ComplianceMonitoring />);

    expect(screen.getByText(/You have 1 critical compliance alert/)).toBeInTheDocument();
  });

  it('displays compliance overview with metrics', () => {
    mockUseQuery.mockReturnValue([]);

    render(<ComplianceMonitoring />);

    expect(screen.getByText('Compliance Status Overview')).toBeInTheDocument();
    expect(screen.getByText('Overall Compliance Rate')).toBeInTheDocument();
    expect(screen.getByText('85%')).toBeInTheDocument();
    expect(screen.getByText('142')).toBeInTheDocument(); // Compliant count
    expect(screen.getByText('25')).toBeInTheDocument(); // Non-compliant count
  });

  it('switches between tabs correctly', () => {
    mockUseQuery.mockReturnValue([]);

    render(<ComplianceMonitoring />);

    // Click on alerts tab
    fireEvent.click(screen.getByText('Alerts'));
    expect(screen.getByText('Compliance Alerts')).toBeInTheDocument();

    // Click on checklists tab
    fireEvent.click(screen.getByText('Checklists'));
    expect(screen.getByText('Compliance Checklists')).toBeInTheDocument();

    // Click on audit tab
    fireEvent.click(screen.getByText('Audit Trail'));
    expect(screen.getByText('Compliance Audit Trail')).toBeInTheDocument();
  });

  it('displays alerts with correct severity styling', () => {
    const mockAlerts = [
      {
        entityId: 'entity1',
        entityType: 'tenant',
        severity: 'critical',
        message: 'Critical issue',
        createdAt: Date.now(),
      },
      {
        entityId: 'entity2',
        entityType: 'vendor',
        severity: 'warning',
        message: 'Warning issue',
        createdAt: Date.now(),
      },
    ];

    mockUseQuery.mockImplementation((query) => {
      if (query === 'getComplianceAlerts') return mockAlerts;
      return [];
    });

    render(<ComplianceMonitoring />);

    // Switch to alerts tab
    fireEvent.click(screen.getByText('Alerts'));

    expect(screen.getByText('Critical issue')).toBeInTheDocument();
    expect(screen.getByText('Warning issue')).toBeInTheDocument();
  });

  it('handles running compliance check', async () => {
    const mockAlerts = [
      {
        entityId: 'entity1',
        entityType: 'tenant',
        severity: 'critical',
        message: 'Critical issue',
        createdAt: Date.now(),
      },
    ];

    mockUseQuery.mockImplementation((query) => {
      if (query === 'getComplianceAlerts') return mockAlerts;
      return [];
    });

    render(<ComplianceMonitoring />);

    // Switch to alerts tab
    fireEvent.click(screen.getByText('Alerts'));

    // Click review button
    const reviewButton = screen.getByText('Review');
    fireEvent.click(reviewButton);

    await waitFor(() => {
      expect(mockRunComplianceCheck).toHaveBeenCalled();
    });
  });

  it('displays audit trail entries', () => {
    const mockAuditTrail = [
      {
        _id: 'audit1',
        details: { description: 'Document uploaded' },
        action: 'created',
        entityType: 'kyc_document',
        timestamp: Date.now(),
      },
      {
        _id: 'audit2',
        details: { description: 'Compliance status updated' },
        action: 'updated',
        entityType: 'compliance_status',
        timestamp: Date.now() - 1000,
      },
    ];

    mockUseQuery.mockImplementation((query) => {
      if (query === 'getAuditTrail') return mockAuditTrail;
      return [];
    });

    render(<ComplianceMonitoring />);

    expect(screen.getByText('Document uploaded')).toBeInTheDocument();
    expect(screen.getByText('Compliance status updated')).toBeInTheDocument();
  });

  it('shows empty state when no alerts exist', () => {
    mockUseQuery.mockImplementation((query) => {
      if (query === 'getComplianceAlerts') return [];
      return [];
    });

    render(<ComplianceMonitoring />);

    // Switch to alerts tab
    fireEvent.click(screen.getByText('Alerts'));

    expect(screen.getByText('No active compliance alerts')).toBeInTheDocument();
  });

  it('refreshes data when refresh button is clicked', () => {
    mockUseQuery.mockReturnValue([]);

    render(<ComplianceMonitoring />);

    const refreshButton = screen.getByText('Refresh');
    fireEvent.click(refreshButton);

    // In a real implementation, this would trigger a data refresh
    // For now, we just verify the button exists and is clickable
    expect(refreshButton).toBeInTheDocument();
  });

  it('displays quick actions in overview tab', () => {
    mockUseQuery.mockReturnValue([]);

    render(<ComplianceMonitoring />);

    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    expect(screen.getByText('View All Alerts')).toBeInTheDocument();
    expect(screen.getByText('Manage Checklists')).toBeInTheDocument();
    expect(screen.getAllByText('Audit Trail')).toHaveLength(2); // One in tab, one in quick actions
  });

  it('handles property-specific monitoring', () => {
    mockUseQuery.mockReturnValue([]);

    render(<ComplianceMonitoring propertyId="property123" />);

    expect(screen.getByText('Compliance Monitoring')).toBeInTheDocument();
    // The component should filter data by property ID
  });
});