import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import PerformanceMonitor from '@/lib/monitoring/performance-monitor';
import DatabaseMonitor from '@/lib/monitoring/database-monitor';
import { AlertTriangle, TrendingUp, Database, Clock, Cpu, Zap } from 'lucide-react';

interface PerformanceStats {
  avg: number;
  min: number;
  max: number;
  count: number;
}

interface DashboardData {
  summary: {
    totalQueries: number;
    recentQueries: number;
    avgDuration: number;
    slowQueries: number;
    errorRate: number;
  };
  topSlowQueries: any[];
  mostFrequentQueries: any[];
  optimizationSuggestions: any[];
}

export const PerformanceDashboard: React.FC = () => {
  const [performanceStats, setPerformanceStats] = useState<Record<string, PerformanceStats>>({});
  const [databaseData, setDatabaseData] = useState<DashboardData | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const performanceMonitor = PerformanceMonitor.getInstance();
  const databaseMonitor = DatabaseMonitor.getInstance();

  useEffect(() => {
    refreshData();
    const interval = setInterval(refreshData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      const stats = performanceMonitor.getPerformanceSummary();
      const dbData = databaseMonitor.getDashboardData();
      
      setPerformanceStats(stats);
      setDatabaseData(dbData);
    } catch (error) {
      console.error('Failed to refresh performance data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatBytes = (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  };

  const getSeverityColor = (severity: string): string => {
    switch (severity) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'default';
    }
  };

  const getPerformanceScore = (): number => {
    if (!performanceStats['page-load']) return 0;
    
    const pageLoad = performanceStats['page-load'].avg;
    if (pageLoad < 1000) return 100;
    if (pageLoad < 2000) return 80;
    if (pageLoad < 3000) return 60;
    if (pageLoad < 5000) return 40;
    return 20;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Performance Monitoring</h2>
          <p className="text-muted-foreground">
            Monitor application performance, database queries, and optimization opportunities
          </p>
        </div>
        <Button onClick={refreshData} disabled={isRefreshing}>
          {isRefreshing ? 'Refreshing...' : 'Refresh Data'}
        </Button>
      </div>

      {/* Performance Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Performance Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Progress value={getPerformanceScore()} className="h-3" />
            </div>
            <div className="text-2xl font-bold">
              {getPerformanceScore()}/100
            </div>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Based on page load times and core web vitals
          </p>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance Metrics</TabsTrigger>
          <TabsTrigger value="database">Database Queries</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Page Load Time</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {performanceStats['page-load'] 
                    ? formatDuration(performanceStats['page-load'].avg)
                    : 'N/A'
                  }
                </div>
                <p className="text-xs text-muted-foreground">
                  Average load time
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Database Queries</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {databaseData?.summary.recentQueries || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Last hour
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
                <Cpu className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {performanceStats['memory-usage']
                    ? formatBytes(performanceStats['memory-usage'].avg)
                    : 'N/A'
                  }
                </div>
                <p className="text-xs text-muted-foreground">
                  Average usage
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {databaseData?.summary.errorRate?.toFixed(1) || 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Query error rate
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {Object.entries(performanceStats).map(([metric, stats]) => (
              <Card key={metric}>
                <CardHeader>
                  <CardTitle className="text-lg capitalize">
                    {metric.replace('-', ' ')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Average:</span>
                      <span className="font-medium">
                        {metric.includes('memory') || metric.includes('bundle')
                          ? formatBytes(stats.avg)
                          : formatDuration(stats.avg)
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Min:</span>
                      <span className="font-medium">
                        {metric.includes('memory') || metric.includes('bundle')
                          ? formatBytes(stats.min)
                          : formatDuration(stats.min)
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Max:</span>
                      <span className="font-medium">
                        {metric.includes('memory') || metric.includes('bundle')
                          ? formatBytes(stats.max)
                          : formatDuration(stats.max)
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Count:</span>
                      <span className="font-medium">{stats.count}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="database" className="space-y-4">
          {databaseData && (
            <>
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Slowest Queries</CardTitle>
                    <CardDescription>
                      Queries with the highest execution time
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {databaseData.topSlowQueries.slice(0, 5).map((query, index) => (
                        <div key={index} className="flex justify-between items-center">
                          <span className="text-sm font-medium truncate">
                            {query.queryName}
                          </span>
                          <Badge variant={query.duration > 1000 ? 'destructive' : 'secondary'}>
                            {formatDuration(query.duration)}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Most Frequent Queries</CardTitle>
                    <CardDescription>
                      Queries called most often in the last hour
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {databaseData.mostFrequentQueries.slice(0, 5).map((query, index) => (
                        <div key={index} className="flex justify-between items-center">
                          <span className="text-sm font-medium truncate">
                            {query.queryName}
                          </span>
                          <Badge variant="outline">
                            {query.count} calls
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </TabsContent>

        <TabsContent value="optimization" className="space-y-4">
          {databaseData?.optimizationSuggestions && databaseData.optimizationSuggestions.length > 0 ? (
            <div className="space-y-4">
              {databaseData.optimizationSuggestions.map((suggestion, index) => (
                <Alert key={index}>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">{suggestion.queryName}</span>
                          <Badge variant={getSeverityColor(suggestion.severity) as any}>
                            {suggestion.severity}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {suggestion.issue}
                        </p>
                        <p className="text-sm">
                          <strong>Suggestion:</strong> {suggestion.suggestion}
                        </p>
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-muted-foreground">
                  <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No optimization suggestions at this time.</p>
                  <p className="text-sm">Your application is performing well!</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PerformanceDashboard;