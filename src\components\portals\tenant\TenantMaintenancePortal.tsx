import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../../convex/_generated/api';
import { Id } from '../../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import { Textarea } from '../../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { Badge } from '../../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs';
import { Alert, AlertDescription } from '../../ui/alert';
import { useToast } from '../../ui/use-toast';
import { 
  Wrench, 
  Plus, 
  Clock, 
  CheckCircle,
  AlertCircle,
  MessageSquare,
  Camera,
  Upload,
  // Calendar,
  User
} from 'lucide-react';

interface TenantMaintenancePortalProps {
  tenantId: Id<"users">;
  portalId: Id<"portals">;
}

interface MaintenanceFormData {
  title: string;
  description: string;
  category: string;
  priority: string;
  images: string[];
}

const MAINTENANCE_CATEGORIES = [
  { value: 'plumbing', label: 'Plumbing' },
  { value: 'electrical', label: 'Electrical' },
  { value: 'hvac', label: 'HVAC/Air Conditioning' },
  { value: 'appliance', label: 'Appliances' },
  { value: 'structural', label: 'Structural' },
  { value: 'cleaning', label: 'Cleaning' },
  { value: 'security', label: 'Security' },
  { value: 'other', label: 'Other' },
];

const PRIORITY_LEVELS = [
  { value: 'low', label: 'Low', description: 'Non-urgent, can wait a few days' },
  { value: 'medium', label: 'Medium', description: 'Should be addressed within 24-48 hours' },
  { value: 'high', label: 'High', description: 'Needs attention within 24 hours' },
  { value: 'emergency', label: 'Emergency', description: 'Immediate attention required' },
];

export const TenantMaintenancePortal: React.FC<TenantMaintenancePortalProps> = ({ tenantId, portalId }) => {
  const { toast } = useToast();
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<MaintenanceFormData>({
    title: '',
    description: '',
    category: '',
    priority: 'medium',
    images: [],
  });

  const portal = useQuery(api.portals.getPortalById, { portalId });
  const tenant = useQuery(api.users.getById, { id: tenantId });
  const activeLease = useQuery(api.leases.getActiveLease, { tenantId });
  const tickets = useQuery(api.maintenance.getTicketsByTenant, { tenantId });

  const createTicket = useMutation(api.maintenance.createTicket);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!activeLease) {
      toast({
        title: "Error",
        description: "No active lease found",
        variant: "destructive",
      });
      return;
    }

    try {
      await createTicket({
        propertyId: activeLease.propertyId,
        unitId: activeLease.unitId,
        tenantId,
        title: formData.title,
        description: formData.description,
        category: formData.category as any,
        priority: formData.priority as any,
        images: formData.images,
      });

      toast({
        title: "Request Submitted",
        description: "Your maintenance request has been submitted successfully.",
      });

      // Reset form
      setFormData({
        title: '',
        description: '',
        category: '',
        priority: 'medium',
        images: [],
      });
      setShowForm(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit maintenance request",
        variant: "destructive",
      });
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      // In a real implementation, you would upload these files to storage
      // and get back URLs to store in the images array
      const imageUrls = Array.from(files).map(file => URL.createObjectURL(file));
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, ...imageUrls],
      }));
    }
  };

  const TicketCard: React.FC<{ ticket: any }> = ({ ticket }) => (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${
              ticket.status === 'completed' ? 'bg-green-100 text-green-600' :
              ticket.status === 'in_progress' ? 'bg-blue-100 text-blue-600' :
              ticket.status === 'assigned' ? 'bg-yellow-100 text-yellow-600' :
              'bg-gray-100 text-gray-600'
            }`}>
              {ticket.status === 'completed' ? <CheckCircle className="w-4 h-4" /> :
               ticket.status === 'in_progress' ? <Wrench className="w-4 h-4" /> :
               <Clock className="w-4 h-4" />}
            </div>
            <div>
              <h3 className="font-medium">{ticket.title}</h3>
              <p className="text-sm text-muted-foreground">
                {ticket.category.charAt(0).toUpperCase() + ticket.category.slice(1)}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={
              ticket.priority === 'emergency' ? 'destructive' :
              ticket.priority === 'high' ? 'destructive' :
              ticket.priority === 'medium' ? 'secondary' :
              'outline'
            }>
              {ticket.priority}
            </Badge>
            <Badge variant={
              ticket.status === 'completed' ? 'default' :
              ticket.status === 'in_progress' ? 'secondary' :
              'outline'
            }>
              {ticket.status.replace('_', ' ')}
            </Badge>
          </div>
        </div>

        <p className="text-sm text-muted-foreground mb-3">
          {ticket.description}
        </p>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Created:</span>
            <p className="text-muted-foreground">
              {new Date(ticket.createdAt).toLocaleDateString()}
            </p>
          </div>
          <div>
            <span className="font-medium">SLA Deadline:</span>
            <p className="text-muted-foreground">
              {new Date(ticket.slaDeadline).toLocaleDateString()}
            </p>
          </div>
        </div>

        {ticket.vendorId && (
          <div className="mt-3 pt-3 border-t">
            <div className="flex items-center space-x-2">
              <User className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">Assigned to vendor</span>
            </div>
          </div>
        )}

        {ticket.notes && ticket.notes.length > 0 && (
          <div className="mt-3 pt-3 border-t">
            <div className="flex items-center space-x-2 mb-2">
              <MessageSquare className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Recent Updates</span>
            </div>
            <div className="space-y-2">
              {ticket.notes.slice(-2).map((note: any, index: number) => (
                <div key={index} className="bg-muted p-2 rounded text-sm">
                  <p>{note.message}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    {new Date(note.timestamp).toLocaleDateString()}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  const MaintenanceForm: React.FC = () => (
    <Card>
      <CardHeader>
        <CardTitle>Submit Maintenance Request</CardTitle>
        <CardDescription>
          Describe the issue you're experiencing and we'll get it resolved quickly.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Issue Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Brief description of the issue"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {MAINTENANCE_CATEGORIES.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">Priority *</Label>
              <Select
                value={formData.priority}
                onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
                required
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PRIORITY_LEVELS.map((priority) => (
                    <SelectItem key={priority.value} value={priority.value}>
                      <div>
                        <div className="font-medium">{priority.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {priority.description}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Detailed Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Please provide as much detail as possible about the issue, including when it started, what you've tried, and any other relevant information."
              rows={4}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="images">Photos (Optional)</Label>
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
              <Camera className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm text-muted-foreground mb-2">
                Upload photos to help us understand the issue better
              </p>
              <Input
                id="images"
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageUpload}
                className="hidden"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => document.getElementById('images')?.click()}
              >
                <Upload className="w-4 h-4 mr-2" />
                Choose Photos
              </Button>
            </div>
            {formData.images.length > 0 && (
              <div className="grid grid-cols-3 gap-2 mt-2">
                {formData.images.map((image, index) => (
                  <img
                    key={index}
                    src={image}
                    alt={`Upload ${index + 1}`}
                    className="w-full h-20 object-cover rounded border"
                  />
                ))}
              </div>
            )}
          </div>

          {formData.priority === 'emergency' && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                For true emergencies (gas leaks, electrical hazards, flooding), 
                please also call our emergency hotline immediately.
              </AlertDescription>
            </Alert>
          )}

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowForm(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              Submit Request
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );

  if (!portal || !tenant) {
    return <div>Loading...</div>;
  }

  const openTickets = tickets?.filter((ticket: any) => 
    ['open', 'assigned', 'in_progress'].includes(ticket.status)
  ) || [];
  
  const closedTickets = tickets?.filter((ticket: any) => 
    ['completed', 'closed'].includes(ticket.status)
  ) || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Maintenance Requests</h1>
          <p className="text-muted-foreground">
            Submit and track maintenance requests for your unit
          </p>
        </div>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          New Request
        </Button>
      </div>

      {showForm && <MaintenanceForm />}

      <Tabs defaultValue="open" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="open" className="flex items-center">
            <Clock className="w-4 h-4 mr-2" />
            Open ({openTickets.length})
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center">
            <CheckCircle className="w-4 h-4 mr-2" />
            History ({closedTickets.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="open" className="space-y-4">
          {openTickets.length > 0 ? (
            <div className="grid gap-4">
              {openTickets.map((ticket: any) => (
                <TicketCard key={ticket._id} ticket={ticket} />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-600" />
                <h3 className="text-lg font-medium mb-2">No Open Requests</h3>
                <p className="text-muted-foreground mb-4">
                  You don't have any open maintenance requests at this time.
                </p>
                <Button onClick={() => setShowForm(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Submit New Request
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          {closedTickets.length > 0 ? (
            <div className="grid gap-4">
              {closedTickets.map((ticket: any) => (
                <TicketCard key={ticket._id} ticket={ticket} />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Wrench className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No History</h3>
                <p className="text-muted-foreground">
                  Your completed maintenance requests will appear here.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};