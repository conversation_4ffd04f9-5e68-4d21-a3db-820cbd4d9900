import { test, expect } from '@playwright/test';

test.describe('Property Management', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/verify', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          isAuthenticated: true,
          user: {
            _id: 'user1',
            email: '<EMAIL>',
            name: 'Property Owner',
            role: 'owner'
          }
        })
      });
    });

    // Mock properties data
    await page.route('**/api/properties', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'prop1',
            name: 'Sunset Apartments',
            type: 'residential',
            address: {
              street: '123 Main Street',
              city: 'Nairobi',
              state: 'Nairobi County',
              postalCode: '00100',
              country: 'Kenya'
            },
            ownerId: 'user1',
            totalUnits: 50,
            occupiedUnits: 35,
            occupancyRate: 70
          }
        ])
      });
    });

    await page.goto('/dashboard/properties');
  });

  test('should display properties list', async ({ page }) => {
    await expect(page.getByRole('heading', { name: /properties/i })).toBeVisible();
    await expect(page.getByText('Sunset Apartments')).toBeVisible();
    await expect(page.getByText('70% occupied')).toBeVisible();
  });

  test('should open create property dialog', async ({ page }) => {
    await page.getByRole('button', { name: /add property/i }).click();
    
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(page.getByRole('heading', { name: /create property/i })).toBeVisible();
    await expect(page.getByLabel(/property name/i)).toBeVisible();
    await expect(page.getByLabel(/property type/i)).toBeVisible();
  });

  test('should create new property', async ({ page }) => {
    // Mock create property API
    await page.route('**/api/properties', async route => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            _id: 'prop2',
            name: 'Ocean View Towers',
            type: 'residential'
          })
        });
      }
    });

    await page.getByRole('button', { name: /add property/i }).click();
    
    await page.getByLabel(/property name/i).fill('Ocean View Towers');
    await page.getByLabel(/property type/i).selectOption('residential');
    await page.getByLabel(/street address/i).fill('456 Ocean Drive');
    await page.getByLabel(/city/i).fill('Mombasa');
    await page.getByLabel(/state/i).fill('Mombasa County');
    await page.getByLabel(/postal code/i).fill('80100');
    
    await page.getByRole('button', { name: /create property/i }).click();
    
    await expect(page.getByText(/property created successfully/i)).toBeVisible();
  });

  test('should show property details', async ({ page }) => {
    await page.getByText('Sunset Apartments').click();
    
    await expect(page.getByRole('heading', { name: /sunset apartments/i })).toBeVisible();
    await expect(page.getByText(/123 main street/i)).toBeVisible();
    await expect(page.getByText(/nairobi/i)).toBeVisible();
  });

  test('should edit property', async ({ page }) => {
    // Mock update property API
    await page.route('**/api/properties/prop1', async route => {
      if (route.request().method() === 'PATCH') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true
          })
        });
      }
    });

    await page.getByRole('button', { name: /edit/i }).first().click();
    
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(page.getByDisplayValue('Sunset Apartments')).toBeVisible();
    
    await page.getByLabel(/property name/i).fill('Sunset Apartments Updated');
    await page.getByRole('button', { name: /update property/i }).click();
    
    await expect(page.getByText(/property updated successfully/i)).toBeVisible();
  });

  test('should filter properties by type', async ({ page }) => {
    await page.getByRole('combobox', { name: /filter by type/i }).click();
    await page.getByText('Residential').click();
    
    await expect(page.getByText('Sunset Apartments')).toBeVisible();
  });

  test('should search properties', async ({ page }) => {
    await page.getByPlaceholder(/search properties/i).fill('Sunset');
    
    await expect(page.getByText('Sunset Apartments')).toBeVisible();
  });

  test('should show property analytics', async ({ page }) => {
    await page.getByText('Sunset Apartments').click();
    await page.getByRole('tab', { name: /analytics/i }).click();
    
    await expect(page.getByText(/occupancy rate/i)).toBeVisible();
    await expect(page.getByText(/revenue/i)).toBeVisible();
    await expect(page.getByText(/expenses/i)).toBeVisible();
  });

  test('should manage units within property', async ({ page }) => {
    await page.getByText('Sunset Apartments').click();
    await page.getByRole('tab', { name: /units/i }).click();
    
    await expect(page.getByRole('heading', { name: /units/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /add unit/i })).toBeVisible();
  });

  test('should delete property with confirmation', async ({ page }) => {
    // Mock delete property API
    await page.route('**/api/properties/prop1', async route => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true
          })
        });
      }
    });

    await page.getByRole('button', { name: /delete/i }).first().click();
    
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(page.getByText(/are you sure/i)).toBeVisible();
    
    await page.getByRole('button', { name: /confirm delete/i }).click();
    
    await expect(page.getByText(/property deleted successfully/i)).toBeVisible();
  });
});