import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { 
  History, 
  Search, 
  Calendar,
  MessageSquare,
  Phone,
  Mail,
  Bell,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

interface NotificationHistoryProps {
  userId: Id<"users">;
  propertyId?: Id<"properties">;
}

export function NotificationHistory({ userId, propertyId }: NotificationHistoryProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<string>('30');

  // Calculate date range
  const fromDate = new Date();
  fromDate.setDate(fromDate.getDate() - parseInt(dateRange));

  // Query notification history
  const notificationHistory = useQuery(api.communications.getNotificationHistory, {
    userId,
    propertyId,
    fromDate: fromDate.getTime(),
    limit: 100,
  });

  const getChannelIcon = (type: string) => {
    switch (type) {
      case 'sms':
        return <Phone className="h-4 w-4" />;
      case 'whatsapp':
        return <MessageSquare className="h-4 w-4" />;
      case 'email':
        return <Mail className="h-4 w-4" />;
      case 'in_app':
        return <Bell className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'scheduled':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'payment_reminder':
        return 'bg-purple-100 text-purple-800';
      case 'maintenance_update':
        return 'bg-orange-100 text-orange-800';
      case 'lease_expiry':
        return 'bg-blue-100 text-blue-800';
      case 'sla_warning':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Filter notifications
  const filteredHistory = notificationHistory?.filter(notification => {
    const matchesSearch = notification.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (notification.subject && notification.subject.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === 'all' || notification.status === statusFilter;
    const matchesType = typeFilter === 'all' || notification.type === typeFilter;
    return matchesSearch && matchesStatus && matchesType;
  });

  // Group notifications by date
  const groupedHistory = filteredHistory?.reduce((groups, notification) => {
    const date = new Date(notification.createdAt).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(notification);
    return groups;
  }, {} as Record<string, typeof filteredHistory>);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <History className="h-5 w-5" />
          <span>Notification History</span>
        </CardTitle>
        <CardDescription>
          View the history of all sent notifications and their delivery status
        </CardDescription>
        
        {/* Filters */}
        <div className="flex flex-wrap items-center gap-4 pt-4">
          <div className="relative flex-1 min-w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search notifications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="sent">Sent</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
              <SelectItem value="scheduled">Scheduled</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="sms">SMS</SelectItem>
              <SelectItem value="whatsapp">WhatsApp</SelectItem>
              <SelectItem value="email">Email</SelectItem>
              <SelectItem value="in_app">In-App</SelectItem>
            </SelectContent>
          </Select>

          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent>
        {notificationHistory === undefined ? (
          <div className="text-center py-8">Loading notification history...</div>
        ) : !groupedHistory || Object.keys(groupedHistory).length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No notification history found for the selected criteria
          </div>
        ) : (
          <div className="space-y-6">
            {Object.entries(groupedHistory)
              .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
              .map(([date, notifications]) => (
                <div key={date}>
                  <div className="flex items-center space-x-2 mb-3">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <h3 className="font-medium text-gray-900">{date}</h3>
                    <div className="flex-1 border-t border-gray-200"></div>
                  </div>
                  
                  <div className="space-y-3">
                    {notifications.map((notification) => (
                      <div key={notification._id} className="border rounded-lg p-4 bg-white">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3 flex-1">
                            <div className="flex items-center space-x-1">
                              {getChannelIcon(notification.type)}
                              {getStatusIcon(notification.status)}
                            </div>
                            
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <Badge variant="outline" className="uppercase text-xs">
                                  {notification.type}
                                </Badge>
                                <Badge className={getStatusColor(notification.status)}>
                                  {notification.status}
                                </Badge>
                                {notification.metadata?.relatedEntityType && (
                                  <Badge className={getTypeColor(notification.metadata.relatedEntityType)}>
                                    {notification.metadata.relatedEntityType.replace('_', ' ')}
                                  </Badge>
                                )}
                              </div>
                              
                              {notification.subject && (
                                <h4 className="font-medium mb-1">{notification.subject}</h4>
                              )}
                              
                              <p className="text-sm text-gray-600 mb-2">
                                {notification.content.length > 150 
                                  ? `${notification.content.substring(0, 150)}...`
                                  : notification.content
                                }
                              </p>
                              
                              <div className="flex items-center space-x-4 text-xs text-gray-500">
                                <span>
                                  {new Date(notification.createdAt).toLocaleTimeString()}
                                </span>
                                {notification.sentAt && (
                                  <span>
                                    Sent: {new Date(notification.sentAt).toLocaleTimeString()}
                                  </span>
                                )}
                                {notification.metadata?.deliveryAttempts && (
                                  <span>
                                    Attempts: {notification.metadata.deliveryAttempts}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          <div className="text-right">
                            {notification.status === 'failed' && notification.metadata?.errorMessage && (
                              <div className="text-xs text-red-600 max-w-48 truncate" title={notification.metadata.errorMessage}>
                                Error: {notification.metadata.errorMessage}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}