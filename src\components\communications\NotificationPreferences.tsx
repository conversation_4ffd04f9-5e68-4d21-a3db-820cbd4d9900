import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Separator } from '../ui/separator';
import { useToast } from '../ui/use-toast';
import { Settings, Bell, MessageSquare, Phone, Mail, Clock } from 'lucide-react';

interface NotificationPreferencesProps {
  userId: Id<"users">;
  propertyId?: Id<"properties">;
}

interface NotificationSettings {
  sms: {
    enabled: boolean;
    paymentReminders: boolean;
    maintenanceUpdates: boolean;
    leaseNotifications: boolean;
    emergencyAlerts: boolean;
    generalAnnouncements: boolean;
  };
  whatsapp: {
    enabled: boolean;
    paymentReminders: boolean;
    maintenanceUpdates: boolean;
    leaseNotifications: boolean;
    emergencyAlerts: boolean;
    generalAnnouncements: boolean;
  };
  email: {
    enabled: boolean;
    paymentReminders: boolean;
    maintenanceUpdates: boolean;
    leaseNotifications: boolean;
    emergencyAlerts: boolean;
    generalAnnouncements: boolean;
    weeklyReports: boolean;
    monthlyStatements: boolean;
  };
  inApp: {
    enabled: boolean;
    paymentReminders: boolean;
    maintenanceUpdates: boolean;
    leaseNotifications: boolean;
    emergencyAlerts: boolean;
    generalAnnouncements: boolean;
  };
}

interface QuietHours {
  enabled: boolean;
  startTime: string;
  endTime: string;
  timezone: string;
}

export function NotificationPreferences({ userId, propertyId }: NotificationPreferencesProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  
  // Default preferences
  const defaultPreferences: NotificationSettings = {
    sms: {
      enabled: true,
      paymentReminders: true,
      maintenanceUpdates: true,
      leaseNotifications: true,
      emergencyAlerts: true,
      generalAnnouncements: false,
    },
    whatsapp: {
      enabled: false,
      paymentReminders: false,
      maintenanceUpdates: false,
      leaseNotifications: false,
      emergencyAlerts: true,
      generalAnnouncements: false,
    },
    email: {
      enabled: true,
      paymentReminders: true,
      maintenanceUpdates: true,
      leaseNotifications: true,
      emergencyAlerts: true,
      generalAnnouncements: true,
      weeklyReports: true,
      monthlyStatements: true,
    },
    inApp: {
      enabled: true,
      paymentReminders: true,
      maintenanceUpdates: true,
      leaseNotifications: true,
      emergencyAlerts: true,
      generalAnnouncements: true,
    },
  };

  const defaultQuietHours: QuietHours = {
    enabled: false,
    startTime: '22:00',
    endTime: '08:00',
    timezone: 'Africa/Nairobi',
  };

  const [preferences, setPreferences] = useState<NotificationSettings>(defaultPreferences);
  const [quietHours, setQuietHours] = useState<QuietHours>(defaultQuietHours);
  const [language, setLanguage] = useState('en');

  // Query existing preferences
  const existingPreferences = useQuery(api.communications.getNotificationPreferences, {
    userId,
    propertyId,
  });

  // Mutation to save preferences
  const savePreferences = useMutation(api.communications.saveNotificationPreferences);

  // Load existing preferences when available
  useEffect(() => {
    if (existingPreferences) {
      setPreferences(existingPreferences.preferences);
      setQuietHours(existingPreferences.quietHours);
      setLanguage(existingPreferences.language);
    }
  }, [existingPreferences]);

  const handlePreferenceChange = (
    channel: keyof NotificationSettings,
    setting: string,
    value: boolean
  ) => {
    setPreferences(prev => ({
      ...prev,
      [channel]: {
        ...prev[channel],
        [setting]: value,
      },
    }));
  };

  const handleQuietHoursChange = (field: keyof QuietHours, value: string | boolean) => {
    setQuietHours(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await savePreferences({
        userId,
        propertyId,
        preferences,
        quietHours,
        language,
      });

      toast({
        title: "Preferences Saved",
        description: "Your notification preferences have been updated successfully",
      });
    } catch (error) {
      toast({
        title: "Save Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const NotificationChannelCard = ({ 
    channel, 
    title, 
    description, 
    icon: Icon,
    settings 
  }: {
    channel: keyof NotificationSettings;
    title: string;
    description: string;
    icon: React.ComponentType<{ className?: string }>;
    settings: Array<{ key: string; label: string; description?: string }>;
  }) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Icon className="h-5 w-5" />
          <span>{title}</span>
        </CardTitle>
        <CardDescription>{description}</CardDescription>
        <div className="flex items-center space-x-2">
          <Switch
            checked={preferences[channel].enabled}
            onCheckedChange={(checked) => handlePreferenceChange(channel, 'enabled', checked)}
          />
          <span className="text-sm">
            {preferences[channel].enabled ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      </CardHeader>
      {preferences[channel].enabled && (
        <CardContent className="space-y-4">
          {settings.map((setting) => (
            <div key={setting.key} className="flex items-center justify-between">
              <div className="space-y-0.5">
                <div className="text-sm font-medium">{setting.label}</div>
                {setting.description && (
                  <div className="text-xs text-gray-500">{setting.description}</div>
                )}
              </div>
              <Switch
                checked={preferences[channel][setting.key as keyof typeof preferences[typeof channel]]}
                onCheckedChange={(checked) => handlePreferenceChange(channel, setting.key, checked)}
              />
            </div>
          ))}
        </CardContent>
      )}
    </Card>
  );

  const commonSettings = [
    { key: 'paymentReminders', label: 'Payment Reminders', description: 'Rent due dates and overdue notices' },
    { key: 'maintenanceUpdates', label: 'Maintenance Updates', description: 'Ticket status changes and assignments' },
    { key: 'leaseNotifications', label: 'Lease Notifications', description: 'Lease renewals and expirations' },
    { key: 'emergencyAlerts', label: 'Emergency Alerts', description: 'Urgent maintenance and security issues' },
    { key: 'generalAnnouncements', label: 'General Announcements', description: 'Property news and updates' },
  ];

  const emailSettings = [
    ...commonSettings,
    { key: 'weeklyReports', label: 'Weekly Reports', description: 'Property performance summaries' },
    { key: 'monthlyStatements', label: 'Monthly Statements', description: 'Financial statements and invoices' },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Settings className="h-6 w-6" />
        <h2 className="text-2xl font-bold">Notification Preferences</h2>
      </div>

      <div className="grid gap-6">
        <NotificationChannelCard
          channel="sms"
          title="SMS Notifications"
          description="Receive notifications via text message"
          icon={Phone}
          settings={commonSettings}
        />

        <NotificationChannelCard
          channel="whatsapp"
          title="WhatsApp Notifications"
          description="Receive notifications via WhatsApp"
          icon={MessageSquare}
          settings={commonSettings}
        />

        <NotificationChannelCard
          channel="email"
          title="Email Notifications"
          description="Receive notifications via email"
          icon={Mail}
          settings={emailSettings}
        />

        <NotificationChannelCard
          channel="inApp"
          title="In-App Notifications"
          description="Receive notifications within the application"
          icon={Bell}
          settings={commonSettings}
        />

        {/* Quiet Hours */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>Quiet Hours</span>
            </CardTitle>
            <CardDescription>
              Set times when you don't want to receive non-emergency notifications
            </CardDescription>
            <div className="flex items-center space-x-2">
              <Switch
                checked={quietHours.enabled}
                onCheckedChange={(checked) => handleQuietHoursChange('enabled', checked)}
              />
              <span className="text-sm">
                {quietHours.enabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </CardHeader>
          {quietHours.enabled && (
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Start Time</label>
                  <Input
                    type="time"
                    value={quietHours.startTime}
                    onChange={(e) => handleQuietHoursChange('startTime', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">End Time</label>
                  <Input
                    type="time"
                    value={quietHours.endTime}
                    onChange={(e) => handleQuietHoursChange('endTime', e.target.value)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Timezone</label>
                <Select value={quietHours.timezone} onValueChange={(value) => handleQuietHoursChange('timezone', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Africa/Nairobi">Africa/Nairobi (EAT)</SelectItem>
                    <SelectItem value="UTC">UTC</SelectItem>
                    <SelectItem value="America/New_York">America/New_York (EST)</SelectItem>
                    <SelectItem value="Europe/London">Europe/London (GMT)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          )}
        </Card>

        {/* Language Preferences */}
        <Card>
          <CardHeader>
            <CardTitle>Language Preferences</CardTitle>
            <CardDescription>
              Choose your preferred language for notifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <label className="text-sm font-medium">Language</label>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="sw">Swahili</SelectItem>
                  <SelectItem value="fr">French</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator />

      <div className="flex justify-end">
        <Button onClick={handleSave} disabled={isLoading}>
          {isLoading ? 'Saving...' : 'Save Preferences'}
        </Button>
      </div>
    </div>
  );
}