import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Alert, AlertDescription } from '../ui/alert';
import { useToast } from '../ui/use-toast';
import { PortalAnalytics } from './PortalAnalytics';
import { 
  Globe, 
  Users, 
  Settings, 
  // BarChart3, 
  Eye, 
  // EyeOff,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Copy,
  RefreshCw,
  Zap,
  Shield
} from 'lucide-react';

interface PortalManagementDashboardProps {
  propertyId: Id<"properties">;
}

export const PortalManagementDashboard: React.FC<PortalManagementDashboardProps> = ({ propertyId }) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');

  const property = useQuery(api.properties.getById, { id: propertyId });
  const portal = useQuery(api.portals.getPortalByProperty, { propertyId });
  const portalUsers = useQuery(api.portals.getPortalUsers, 
    portal ? { portalId: portal._id } : 'skip'
  );

  const togglePortalPublication = useMutation(api.portals.togglePortalPublication);
  const updatePortalFeatures = useMutation(api.portals.updatePortalFeatures);

  const handleTogglePublication = async () => {
    if (!portal) return;

    try {
      await togglePortalPublication({
        portalId: portal._id,
        isPublished: !portal.isPublished,
      });

      toast({
        title: portal.isPublished ? "Portal Unpublished" : "Portal Published",
        description: portal.isPublished 
          ? "Your portal is now offline and inaccessible to tenants"
          : "Your portal is now live and accessible to tenants",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update portal status",
        variant: "destructive",
      });
    }
  };

  const handleFeatureToggle = async (feature: string, enabled: boolean) => {
    if (!portal) return;

    try {
      await updatePortalFeatures({
        portalId: portal._id,
        features: {
          ...portal.features,
          [feature]: enabled,
        },
      });

      toast({
        title: "Feature Updated",
        description: `${feature} has been ${enabled ? 'enabled' : 'disabled'}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update feature",
        variant: "destructive",
      });
    }
  };

  const copyPortalUrl = () => {
    if (!portal) return;
    
    const url = portal.customDomain 
      ? `https://${portal.customDomain}`
      : `https://${portal.subdomain}.estatepulse.com`;
    
    navigator.clipboard.writeText(url);
    toast({
      title: "URL Copied",
      description: "Portal URL has been copied to clipboard",
    });
  };

  if (!property) {
    return <div>Loading...</div>;
  }

  if (!portal) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Globe className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-medium mb-2">No Portal Created</h3>
          <p className="text-muted-foreground mb-4">
            Create a tenant portal for {property.name} to get started.
          </p>
          <Button>Create Portal</Button>
        </CardContent>
      </Card>
    );
  }

  const portalUrl = portal.customDomain 
    ? `https://${portal.customDomain}`
    : `https://${portal.subdomain}.estatepulse.com`;

  const getStatusColor = () => {
    if (!portal.isActive) return 'destructive';
    if (!portal.isPublished) return 'secondary';
    return 'default';
  };

  const getStatusText = () => {
    if (!portal.isActive) return 'Inactive';
    if (!portal.isPublished) return 'Draft';
    return 'Live';
  };

  const OverviewTab: React.FC = () => (
    <div className="space-y-6">
      {/* Portal Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Portal Status</span>
            <Badge variant={getStatusColor()}>
              {getStatusText()}
            </Badge>
          </CardTitle>
          <CardDescription>
            Current status and accessibility of your tenant portal
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="published">Portal Published</Label>
              <p className="text-sm text-muted-foreground">
                Make the portal accessible to tenants
              </p>
            </div>
            <Switch
              id="published"
              checked={portal.isPublished}
              onCheckedChange={handleTogglePublication}
            />
          </div>

          {portal.isPublished && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Your portal is live and accessible at{' '}
                <a href={portalUrl} target="_blank" rel="noopener noreferrer" className="underline">
                  {portalUrl}
                </a>
              </AlertDescription>
            </Alert>
          )}

          {!portal.isPublished && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Your portal is currently offline and not accessible to tenants.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Portal Information */}
      <Card>
        <CardHeader>
          <CardTitle>Portal Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Portal Name</Label>
              <p className="text-sm text-muted-foreground">{portal.name}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Property</Label>
              <p className="text-sm text-muted-foreground">{property.name}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Subdomain</Label>
              <div className="flex items-center space-x-2">
                <p className="text-sm text-muted-foreground font-mono">
                  {portal.subdomain}.estatepulse.com
                </p>
                <Button variant="ghost" size="sm" onClick={copyPortalUrl}>
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium">Custom Domain</Label>
              <p className="text-sm text-muted-foreground font-mono">
                {portal.customDomain || 'Not configured'}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Created</Label>
              <p className="text-sm text-muted-foreground">
                {new Date(portal.createdAt).toLocaleDateString()}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Last Updated</Label>
              <p className="text-sm text-muted-foreground">
                {new Date(portal.updatedAt).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2 pt-4 border-t">
            <Button variant="outline" asChild>
              <a href={portalUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="w-4 h-4 mr-2" />
                View Portal
              </a>
            </Button>
            <Button variant="outline" onClick={copyPortalUrl}>
              <Copy className="w-4 h-4 mr-2" />
              Copy URL
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 text-blue-600 rounded-lg">
                <Users className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Users</p>
                <p className="text-2xl font-bold">{portalUsers?.length || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 text-green-600 rounded-lg">
                <Eye className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Page Views (30d)</p>
                <p className="text-2xl font-bold">1,250</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 text-purple-600 rounded-lg">
                <Zap className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Engagement Rate</p>
                <p className="text-2xl font-bold">78%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const FeaturesTab: React.FC = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Portal Features</CardTitle>
          <CardDescription>
            Enable or disable specific features for your tenant portal
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {Object.entries(portal.features).map(([feature, enabled]) => {
            const featureInfo = getFeatureInfo(feature);
            return (
              <div key={feature} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${enabled ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
                    {featureInfo.icon}
                  </div>
                  <div>
                    <h4 className="font-medium">{featureInfo.name}</h4>
                    <p className="text-sm text-muted-foreground">{featureInfo.description}</p>
                  </div>
                </div>
                <Switch
                  checked={Boolean(enabled)}
                  onCheckedChange={(checked: boolean) => handleFeatureToggle(feature, checked)}
                />
              </div>
            );
          })}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Feature Usage Analytics</CardTitle>
          <CardDescription>
            See how tenants are using different portal features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { feature: 'Payment Portal', usage: 89, sessions: 156 },
              { feature: 'Maintenance Requests', usage: 67, sessions: 89 },
              { feature: 'Document Access', usage: 54, sessions: 67 },
              { feature: 'Communication Center', usage: 32, sessions: 34 },
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium">{item.feature}</span>
                    <span className="text-sm text-muted-foreground">
                      {item.sessions} sessions
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="h-2 bg-primary rounded-full"
                      style={{ width: `${item.usage}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const SecurityTab: React.FC = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            Security Settings
          </CardTitle>
          <CardDescription>
            Manage security and access controls for your portal
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">SSL Certificate</h4>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm">Active and Valid</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Your portal is secured with a valid SSL certificate
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Access Logs</h4>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm">Enabled</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                All portal access is logged for security monitoring
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Data Encryption</h4>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm">AES-256</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                All sensitive data is encrypted at rest and in transit
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Backup Status</h4>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm">Daily Backups</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Portal data is backed up daily with 30-day retention
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Security Events</CardTitle>
          <CardDescription>
            Monitor security-related activities on your portal
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              {
                event: 'Successful login',
                user: '<EMAIL>',
                time: '2 minutes ago',
                status: 'success'
              },
              {
                event: 'Failed login attempt',
                user: '<EMAIL>',
                time: '1 hour ago',
                status: 'warning'
              },
              {
                event: 'Password reset',
                user: '<EMAIL>',
                time: '3 hours ago',
                status: 'info'
              },
            ].map((event, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${
                    event.status === 'success' ? 'bg-green-500' :
                    event.status === 'warning' ? 'bg-yellow-500' :
                    'bg-blue-500'
                  }`} />
                  <div>
                    <p className="text-sm font-medium">{event.event}</p>
                    <p className="text-xs text-muted-foreground">{event.user}</p>
                  </div>
                </div>
                <span className="text-xs text-muted-foreground">{event.time}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Portal Management</h1>
          <p className="text-muted-foreground">
            Manage and monitor your tenant portal for {property.name}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" asChild>
            <a href={portalUrl} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="w-4 h-4 mr-2" />
              View Portal
            </a>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <OverviewTab />
        </TabsContent>

        <TabsContent value="features">
          <FeaturesTab />
        </TabsContent>

        <TabsContent value="analytics">
          <PortalAnalytics portalId={portal._id} />
        </TabsContent>

        <TabsContent value="security">
          <SecurityTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

function getFeatureInfo(feature: string) {
  const features: Record<string, { name: string; description: string; icon: React.ReactNode }> = {
    paymentPortal: {
      name: 'Payment Portal',
      description: 'Allow tenants to make rent payments online',
      icon: <Settings className="w-4 h-4" />,
    },
    maintenanceRequests: {
      name: 'Maintenance Requests',
      description: 'Enable maintenance request submission and tracking',
      icon: <Settings className="w-4 h-4" />,
    },
    documentAccess: {
      name: 'Document Access',
      description: 'Provide access to lease documents and files',
      icon: <Settings className="w-4 h-4" />,
    },
    leaseInformation: {
      name: 'Lease Information',
      description: 'Display lease details and terms',
      icon: <Settings className="w-4 h-4" />,
    },
    communicationCenter: {
      name: 'Communication Center',
      description: 'Enable messaging between tenants and management',
      icon: <Settings className="w-4 h-4" />,
    },
    announcementsBoard: {
      name: 'Announcements Board',
      description: 'Show property announcements and updates',
      icon: <Settings className="w-4 h-4" />,
    },
  };

  return features[feature] || { 
    name: feature, 
    description: 'Feature configuration', 
    icon: <Settings className="w-4 h-4" /> 
  };
}