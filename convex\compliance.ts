import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// KYC Document Management

export const uploadKYCDocument = mutation({
  args: {
    userId: v.id("users"),
    documentType: v.union(
      v.literal("national_id"),
      v.literal("passport"),
      v.literal("drivers_license"),
      v.literal("birth_certificate"),
      v.literal("proof_of_income"),
      v.literal("bank_statement"),
      v.literal("employment_letter"),
      v.literal("business_registration"),
      v.literal("tax_certificate"),
      v.literal("other")
    ),
    documentNumber: v.optional(v.string()),
    documentUrl: v.string(),
    fileName: v.string(),
    fileSize: v.number(),
    mimeType: v.string(),
    tags: v.array(v.string()),
    uploadedBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Check if there's an existing active document of the same type
    const existingDoc = await ctx.db
      .query("kycDocuments")
      .withIndex("by_user_type", (q) => 
        q.eq("userId", args.userId).eq("documentType", args.documentType)
      )
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    let version = 1;
    let previousVersionId = undefined;

    if (existingDoc) {
      // Deactivate the existing document
      await ctx.db.patch(existingDoc._id, { isActive: false });
      version = existingDoc.version + 1;
      previousVersionId = existingDoc._id;
    }

    const documentId = await ctx.db.insert("kycDocuments", {
      ...args,
      verificationStatus: "pending",
      version,
      previousVersionId,
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Create audit trail
    await ctx.db.insert("auditTrails", {
      entityId: documentId,
      entityType: "kyc_document",
      action: "created",
      performedBy: args.uploadedBy,
      details: {
        description: `Uploaded ${args.documentType} document: ${args.fileName}`,
      },
      metadata: {
        documentType: args.documentType,
      },
      timestamp: Date.now(),
    });

    return documentId;
  },
});

export const verifyKYCDocument = mutation({
  args: {
    documentId: v.id("kycDocuments"),
    verificationStatus: v.union(
      v.literal("verified"),
      v.literal("rejected")
    ),
    verificationNotes: v.optional(v.string()),
    verifiedBy: v.id("users"),
    extractedData: v.optional(v.object({
      fullName: v.optional(v.string()),
      idNumber: v.optional(v.string()),
      dateOfBirth: v.optional(v.string()),
      nationality: v.optional(v.string()),
      address: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const document = await ctx.db.get(args.documentId);
    if (!document) {
      throw new Error("Document not found");
    }

    await ctx.db.patch(args.documentId, {
      verificationStatus: args.verificationStatus,
      verificationNotes: args.verificationNotes,
      verifiedBy: args.verifiedBy,
      verifiedAt: Date.now(),
      extractedData: args.extractedData,
      updatedAt: Date.now(),
    });

    // Create audit trail
    await ctx.db.insert("auditTrails", {
      entityId: args.documentId,
      entityType: "kyc_document",
      action: "verified",
      performedBy: args.verifiedBy,
      details: {
        description: `Document ${args.verificationStatus}: ${document.fileName}`,
        newValues: { verificationStatus: args.verificationStatus },
      },
      metadata: {
        documentType: document.documentType,
        verificationResult: args.verificationStatus,
      },
      timestamp: Date.now(),
    });

    // Update user KYC status if all required documents are verified
    await updateUserKYCStatus(ctx, document.userId);

    return args.documentId;
  },
});

export const getUserKYCDocuments = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("kycDocuments")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();
  },
});

export const getKYCDocumentById = query({
  args: { documentId: v.id("kycDocuments") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.documentId);
  },
});

// Compliance Checklist Management

export const createComplianceChecklist = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    entityType: v.union(v.literal("tenant"), v.literal("vendor"), v.literal("property"), v.literal("lease")),
    propertyId: v.optional(v.id("properties")),
    requirements: v.array(v.object({
      id: v.string(),
      title: v.string(),
      description: v.string(),
      category: v.union(
        v.literal("kyc"),
        v.literal("legal"),
        v.literal("financial"),
        v.literal("regulatory"),
        v.literal("safety"),
        v.literal("insurance")
      ),
      priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("critical")),
      requiredDocuments: v.array(v.string()),
      isRequired: v.boolean(),
      automationRules: v.optional(v.object({
        autoCheck: v.boolean(),
        checkFrequency: v.optional(v.union(v.literal("daily"), v.literal("weekly"), v.literal("monthly"))),
        conditions: v.optional(v.string()),
      })),
    })),
    createdBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("complianceChecklists", {
      ...args,
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  },
});

export const getComplianceChecklists = query({
  args: {
    entityType: v.optional(v.union(v.literal("tenant"), v.literal("vendor"), v.literal("property"), v.literal("lease"))),
    propertyId: v.optional(v.id("properties")),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("complianceChecklists");
    
    if (args.entityType) {
      query = query.withIndex("by_entity_type", (q) => q.eq("entityType", args.entityType));
    }
    
    let results = await query.filter((q) => q.eq(q.field("isActive"), true)).collect();
    
    if (args.propertyId) {
      results = results.filter(checklist => 
        !checklist.propertyId || checklist.propertyId === args.propertyId
      );
    }
    
    return results;
  },
});

export const updateComplianceStatus = mutation({
  args: {
    entityId: v.string(),
    entityType: v.union(v.literal("tenant"), v.literal("vendor"), v.literal("property"), v.literal("lease")),
    checklistId: v.id("complianceChecklists"),
    propertyId: v.optional(v.id("properties")),
    requirementStatuses: v.array(v.object({
      requirementId: v.string(),
      status: v.union(
        v.literal("compliant"),
        v.literal("non_compliant"),
        v.literal("pending"),
        v.literal("not_applicable")
      ),
      completedAt: v.optional(v.number()),
      notes: v.optional(v.string()),
      documentIds: v.array(v.id("kycDocuments")),
      reviewedBy: v.optional(v.id("users")),
    })),
    reviewedBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Calculate overall score and status
    const totalRequirements = args.requirementStatuses.length;
    const compliantRequirements = args.requirementStatuses.filter(
      req => req.status === "compliant" || req.status === "not_applicable"
    ).length;
    
    const overallScore = totalRequirements > 0 ? (compliantRequirements / totalRequirements) * 100 : 0;
    
    let overallStatus: "compliant" | "non_compliant" | "pending" | "in_review" | "expired";
    if (overallScore === 100) {
      overallStatus = "compliant";
    } else if (overallScore >= 80) {
      overallStatus = "in_review";
    } else if (args.requirementStatuses.some(req => req.status === "pending")) {
      overallStatus = "pending";
    } else {
      overallStatus = "non_compliant";
    }

    // Generate alerts for missing or expired documents
    const alerts = [];
    for (const req of args.requirementStatuses) {
      if (req.status === "non_compliant") {
        alerts.push({
          type: "missing_document" as const,
          message: `Requirement "${req.requirementId}" is not compliant`,
          severity: "error" as const,
          createdAt: Date.now(),
        });
      }
    }

    // Check if status already exists
    const existingStatus = await ctx.db
      .query("complianceStatus")
      .withIndex("by_entity", (q) => 
        q.eq("entityId", args.entityId).eq("entityType", args.entityType)
      )
      .filter((q) => q.eq(q.field("checklistId"), args.checklistId))
      .first();

    const statusData = {
      entityId: args.entityId,
      entityType: args.entityType,
      checklistId: args.checklistId,
      propertyId: args.propertyId,
      overallStatus,
      overallScore,
      requirementStatuses: args.requirementStatuses,
      lastReviewDate: Date.now(),
      nextReviewDate: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days from now
      reviewedBy: args.reviewedBy,
      alerts,
      updatedAt: Date.now(),
    };

    let statusId;
    if (existingStatus) {
      await ctx.db.patch(existingStatus._id, statusData);
      statusId = existingStatus._id;
    } else {
      statusId = await ctx.db.insert("complianceStatus", {
        ...statusData,
        createdAt: Date.now(),
      });
    }

    // Create audit trail
    await ctx.db.insert("auditTrails", {
      entityId: args.entityId,
      entityType: "compliance_status",
      action: existingStatus ? "updated" : "created",
      performedBy: args.reviewedBy,
      details: {
        description: `Compliance status ${existingStatus ? 'updated' : 'created'} for ${args.entityType}`,
        newValues: { overallStatus, overallScore },
      },
      metadata: {
        complianceScore: overallScore,
      },
      timestamp: Date.now(),
    });

    return statusId;
  },
});

export const getComplianceStatus = query({
  args: {
    entityId: v.string(),
    entityType: v.union(v.literal("tenant"), v.literal("vendor"), v.literal("property"), v.literal("lease")),
    checklistId: v.optional(v.id("complianceChecklists")),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("complianceStatus")
      .withIndex("by_entity", (q) => 
        q.eq("entityId", args.entityId).eq("entityType", args.entityType)
      );

    if (args.checklistId) {
      const results = await query.collect();
      return results.filter(status => status.checklistId === args.checklistId);
    }

    return await query.collect();
  },
});

// Regulatory Reporting

export const generateRegulatoryReport = mutation({
  args: {
    name: v.string(),
    reportType: v.union(
      v.literal("etims_vat"),
      v.literal("etims_income_tax"),
      v.literal("rental_income"),
      v.literal("tenant_registry"),
      v.literal("compliance_summary"),
      v.literal("kyc_status"),
      v.literal("custom")
    ),
    propertyIds: v.array(v.id("properties")),
    reportPeriod: v.object({
      startDate: v.number(),
      endDate: v.number(),
      frequency: v.union(v.literal("monthly"), v.literal("quarterly"), v.literal("annual")),
    }),
    fileFormat: v.union(v.literal("pdf"), v.literal("excel"), v.literal("csv"), v.literal("xml")),
    recipients: v.array(v.object({
      email: v.string(),
      name: v.string(),
      role: v.string(),
    })),
    generatedBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Generate report data based on type
    let reportData: any = {};
    
    switch (args.reportType) {
      case "etims_vat":
        reportData = await generateETIMSVATReport(ctx, args.propertyIds, args.reportPeriod);
        break;
      case "etims_income_tax":
        reportData = await generateETIMSIncomeTaxReport(ctx, args.propertyIds, args.reportPeriod);
        break;
      case "rental_income":
        reportData = await generateRentalIncomeReport(ctx, args.propertyIds, args.reportPeriod);
        break;
      case "tenant_registry":
        reportData = await generateTenantRegistryReport(ctx, args.propertyIds, args.reportPeriod);
        break;
      case "compliance_summary":
        reportData = await generateComplianceSummaryReport(ctx, args.propertyIds, args.reportPeriod);
        break;
      case "kyc_status":
        reportData = await generateKYCStatusReport(ctx, args.propertyIds, args.reportPeriod);
        break;
      default:
        reportData = { message: "Custom report type - implement specific logic" };
    }

    const reportId = await ctx.db.insert("regulatoryReports", {
      ...args,
      status: "completed",
      isAutoGenerated: false,
      reportData,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Create audit trail
    await ctx.db.insert("auditTrails", {
      entityId: reportId,
      entityType: "regulatory_report",
      action: "generated",
      performedBy: args.generatedBy,
      details: {
        description: `Generated ${args.reportType} report: ${args.name}`,
      },
      metadata: {
        reportType: args.reportType,
        propertyCount: args.propertyIds.length,
      },
      timestamp: Date.now(),
    });

    return reportId;
  },
});

export const getRegulatoryReports = query({
  args: {
    reportType: v.optional(v.union(
      v.literal("etims_vat"),
      v.literal("etims_income_tax"),
      v.literal("rental_income"),
      v.literal("tenant_registry"),
      v.literal("compliance_summary"),
      v.literal("kyc_status"),
      v.literal("custom")
    )),
    propertyId: v.optional(v.id("properties")),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("regulatoryReports");
    
    if (args.reportType) {
      query = query.withIndex("by_type", (q) => q.eq("reportType", args.reportType));
    }
    
    let results = await query.collect();
    
    if (args.propertyId) {
      results = results.filter(report => 
        report.propertyIds.includes(args.propertyId)
      );
    }
    
    return results.sort((a, b) => b.createdAt - a.createdAt);
  },
});

// Audit Trail

export const getAuditTrail = query({
  args: {
    entityId: v.optional(v.string()),
    entityType: v.optional(v.union(
      v.literal("kyc_document"),
      v.literal("compliance_status"),
      v.literal("user"),
      v.literal("property"),
      v.literal("lease")
    )),
    performedBy: v.optional(v.id("users")),
    action: v.optional(v.union(
      v.literal("created"),
      v.literal("updated"),
      v.literal("deleted"),
      v.literal("verified"),
      v.literal("rejected"),
      v.literal("reviewed"),
      v.literal("accessed"),
      v.literal("downloaded")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("auditTrails");
    
    if (args.entityId && args.entityType) {
      query = query.withIndex("by_entity", (q) => 
        q.eq("entityId", args.entityId).eq("entityType", args.entityType)
      );
    } else if (args.performedBy) {
      query = query.withIndex("by_performer", (q) => q.eq("performedBy", args.performedBy));
    } else if (args.action) {
      query = query.withIndex("by_action", (q) => q.eq("action", args.action));
    } else {
      query = query.withIndex("by_timestamp");
    }
    
    let results = await query.collect();
    
    // Sort by timestamp descending
    results.sort((a, b) => b.timestamp - a.timestamp);
    
    if (args.limit) {
      results = results.slice(0, args.limit);
    }
    
    return results;
  },
});

// Compliance Monitoring and Automation

export const runComplianceCheck = mutation({
  args: {
    entityId: v.string(),
    entityType: v.union(v.literal("tenant"), v.literal("vendor"), v.literal("property"), v.literal("lease")),
    checklistId: v.optional(v.id("complianceChecklists")),
    propertyId: v.optional(v.id("properties")),
    triggeredBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Get applicable checklists
    let checklists;
    if (args.checklistId) {
      const checklist = await ctx.db.get(args.checklistId);
      checklists = checklist ? [checklist] : [];
    } else {
      checklists = await ctx.db
        .query("complianceChecklists")
        .withIndex("by_entity_type", (q) => q.eq("entityType", args.entityType))
        .filter((q) => q.eq(q.field("isActive"), true))
        .collect();
    }

    const results = [];

    for (const checklist of checklists) {
      // Auto-check requirements where possible
      const requirementStatuses = [];
      
      for (const requirement of checklist.requirements) {
        let status: "compliant" | "non_compliant" | "pending" | "not_applicable" = "pending";
        let documentIds: string[] = [];
        
        if (requirement.automationRules?.autoCheck) {
          // Perform automated compliance check
          const autoCheckResult = await performAutomatedCheck(ctx, args.entityId, args.entityType, requirement);
          status = autoCheckResult.status;
          documentIds = autoCheckResult.documentIds;
        }
        
        requirementStatuses.push({
          requirementId: requirement.id,
          status,
          completedAt: status === "compliant" ? Date.now() : undefined,
          notes: status === "compliant" ? "Automatically verified" : undefined,
          documentIds,
          reviewedBy: args.triggeredBy,
        });
      }

      // Update compliance status
      const statusId = await updateComplianceStatus(ctx, {
        entityId: args.entityId,
        entityType: args.entityType,
        checklistId: checklist._id,
        propertyId: args.propertyId,
        requirementStatuses,
        reviewedBy: args.triggeredBy,
      });

      results.push({ checklistId: checklist._id, statusId });
    }

    return results;
  },
});

export const scheduleComplianceReview = mutation({
  args: {
    entityId: v.string(),
    entityType: v.union(v.literal("tenant"), v.literal("vendor"), v.literal("property"), v.literal("lease")),
    reviewDate: v.number(),
    reviewType: v.union(v.literal("periodic"), v.literal("triggered"), v.literal("manual")),
    notes: v.optional(v.string()),
    scheduledBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("notifications", {
      userId: args.scheduledBy,
      title: "Compliance Review Scheduled",
      message: `Compliance review scheduled for ${args.entityType} ${args.entityId}`,
      type: "system",
      priority: "medium",
      isRead: false,
      scheduledFor: args.reviewDate,
      createdAt: Date.now(),
    });
  },
});

export const getComplianceAlerts = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    severity: v.optional(v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("critical"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Get compliance statuses with alerts
    let query = ctx.db.query("complianceStatus");
    
    let results = await query.collect();
    
    if (args.propertyId) {
      results = results.filter(status => status.propertyId === args.propertyId);
    }

    // Extract alerts and filter by severity
    const alerts = results.flatMap(status => 
      status.alerts?.map(alert => ({
        ...alert,
        entityId: status.entityId,
        entityType: status.entityType,
        checklistId: status.checklistId,
        statusId: status._id,
      })) || []
    );

    let filteredAlerts = alerts;
    if (args.severity) {
      filteredAlerts = alerts.filter(alert => alert.severity === args.severity);
    }

    // Sort by creation date (newest first)
    filteredAlerts.sort((a, b) => b.createdAt - a.createdAt);

    if (args.limit) {
      filteredAlerts = filteredAlerts.slice(0, args.limit);
    }

    return filteredAlerts;
  },
});

// Report Generation Helper Functions

async function generateETIMSVATReport(ctx: any, propertyIds: string[], period: any) {
  const startDate = period.startDate;
  const endDate = period.endDate;

  // Get all invoices for the period
  const invoices = await ctx.db
    .query("invoices")
    .filter((q) => 
      q.and(
        q.gte(q.field("createdAt"), startDate),
        q.lte(q.field("createdAt"), endDate),
        q.or(...propertyIds.map(id => q.eq(q.field("propertyId"), id)))
      )
    )
    .collect();

  const vatRate = 0.16; // 16% VAT in Kenya
  let totalRevenue = 0;
  let totalVAT = 0;

  const transactions = invoices.map(invoice => {
    const revenue = invoice.amount;
    const vat = revenue * vatRate;
    totalRevenue += revenue;
    totalVAT += vat;

    return {
      date: new Date(invoice.createdAt).toISOString().split('T')[0],
      invoiceNumber: invoice._id,
      description: `Rent - ${invoice.type}`,
      amount: revenue,
      vatAmount: vat,
      totalAmount: revenue + vat,
    };
  });

  return {
    reportPeriod: {
      startDate: new Date(startDate).toISOString().split('T')[0],
      endDate: new Date(endDate).toISOString().split('T')[0],
    },
    summary: {
      totalRevenue,
      totalVAT,
      totalAmount: totalRevenue + totalVAT,
      transactionCount: transactions.length,
    },
    transactions,
    etimsFormat: {
      // ETIMS-specific formatting
      supplierInfo: {
        tin: "P000000000A", // Property TIN
        name: "Property Management Company",
        address: "Nairobi, Kenya",
      },
      vatSummary: {
        standardRate: vatRate,
        standardRateAmount: totalVAT,
        totalVAT,
      },
    },
  };
}

async function generateETIMSIncomeTaxReport(ctx: any, propertyIds: string[], period: any) {
  const startDate = period.startDate;
  const endDate = period.endDate;

  // Get rental income
  const invoices = await ctx.db
    .query("invoices")
    .filter((q) => 
      q.and(
        q.gte(q.field("createdAt"), startDate),
        q.lte(q.field("createdAt"), endDate),
        q.or(...propertyIds.map(id => q.eq(q.field("propertyId"), id))),
        q.eq(q.field("status"), "paid")
      )
    )
    .collect();

  // Get maintenance expenses
  const maintenanceTickets = await ctx.db
    .query("maintenanceTickets")
    .filter((q) => 
      q.and(
        q.gte(q.field("createdAt"), startDate),
        q.lte(q.field("createdAt"), endDate),
        q.or(...propertyIds.map(id => q.eq(q.field("propertyId"), id))),
        q.neq(q.field("actualCost"), undefined)
      )
    )
    .collect();

  const totalIncome = invoices.reduce((sum, invoice) => sum + invoice.amount, 0);
  const totalExpenses = maintenanceTickets.reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0);
  const netIncome = totalIncome - totalExpenses;
  const incomeTax = netIncome * 0.30; // 30% corporate tax rate

  return {
    reportPeriod: {
      startDate: new Date(startDate).toISOString().split('T')[0],
      endDate: new Date(endDate).toISOString().split('T')[0],
    },
    incomeStatement: {
      grossIncome: totalIncome,
      allowableDeductions: totalExpenses,
      netIncome,
      taxableIncome: Math.max(0, netIncome),
      incomeTax: Math.max(0, incomeTax),
    },
    details: {
      incomeBreakdown: invoices.map(invoice => ({
        date: new Date(invoice.createdAt).toISOString().split('T')[0],
        description: `Rent - ${invoice.type}`,
        amount: invoice.amount,
      })),
      expenseBreakdown: maintenanceTickets.map(ticket => ({
        date: new Date(ticket.createdAt).toISOString().split('T')[0],
        description: ticket.title,
        amount: ticket.actualCost || 0,
        category: ticket.category,
      })),
    },
  };
}

async function generateRentalIncomeReport(ctx: any, propertyIds: string[], period: any) {
  const properties = await Promise.all(
    propertyIds.map(id => ctx.db.get(id))
  );

  const reportData = [];

  for (const property of properties.filter(Boolean)) {
    const invoices = await ctx.db
      .query("invoices")
      .withIndex("by_property", (q) => q.eq("propertyId", property._id))
      .filter((q) => 
        q.and(
          q.gte(q.field("createdAt"), period.startDate),
          q.lte(q.field("createdAt"), period.endDate)
        )
      )
      .collect();

    const units = await ctx.db
      .query("units")
      .withIndex("by_property", (q) => q.eq("propertyId", property._id))
      .collect();

    const totalIncome = invoices.reduce((sum, invoice) => sum + invoice.amount, 0);
    const paidIncome = invoices
      .filter(invoice => invoice.status === "paid")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    reportData.push({
      propertyId: property._id,
      propertyName: property.name,
      totalUnits: units.length,
      occupiedUnits: units.filter(unit => unit.status === "occupied").length,
      totalIncome,
      paidIncome,
      outstandingAmount: totalIncome - paidIncome,
      occupancyRate: units.length > 0 ? (units.filter(unit => unit.status === "occupied").length / units.length) * 100 : 0,
    });
  }

  return {
    reportPeriod: {
      startDate: new Date(period.startDate).toISOString().split('T')[0],
      endDate: new Date(period.endDate).toISOString().split('T')[0],
    },
    properties: reportData,
    summary: {
      totalProperties: reportData.length,
      totalIncome: reportData.reduce((sum, prop) => sum + prop.totalIncome, 0),
      totalPaidIncome: reportData.reduce((sum, prop) => sum + prop.paidIncome, 0),
      totalOutstanding: reportData.reduce((sum, prop) => sum + prop.outstandingAmount, 0),
      averageOccupancyRate: reportData.length > 0 
        ? reportData.reduce((sum, prop) => sum + prop.occupancyRate, 0) / reportData.length 
        : 0,
    },
  };
}

async function generateTenantRegistryReport(ctx: any, propertyIds: string[], period: any) {
  const tenants = [];

  for (const propertyId of propertyIds) {
    const leases = await ctx.db
      .query("leases")
      .withIndex("by_property", (q) => q.eq("propertyId", propertyId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    for (const lease of leases) {
      const tenant = await ctx.db.get(lease.tenantId);
      const unit = await ctx.db.get(lease.unitId);
      const property = await ctx.db.get(lease.propertyId);

      if (tenant && unit && property) {
        const kycDocuments = await ctx.db
          .query("kycDocuments")
          .withIndex("by_user", (q) => q.eq("userId", tenant._id))
          .filter((q) => q.eq(q.field("isActive"), true))
          .collect();

        tenants.push({
          tenantId: tenant._id,
          tenantName: tenant.name,
          tenantEmail: tenant.email,
          tenantPhone: tenant.phone,
          propertyName: property.name,
          unitNumber: unit.unitNumber,
          leaseStartDate: new Date(lease.startDate).toISOString().split('T')[0],
          leaseEndDate: new Date(lease.endDate).toISOString().split('T')[0],
          monthlyRent: lease.monthlyRent,
          kycStatus: tenant.kycStatus,
          documentsCount: kycDocuments.length,
          verifiedDocuments: kycDocuments.filter(doc => doc.verificationStatus === "verified").length,
        });
      }
    }
  }

  return {
    reportPeriod: {
      startDate: new Date(period.startDate).toISOString().split('T')[0],
      endDate: new Date(period.endDate).toISOString().split('T')[0],
    },
    tenants,
    summary: {
      totalTenants: tenants.length,
      verifiedTenants: tenants.filter(t => t.kycStatus === "verified").length,
      pendingTenants: tenants.filter(t => t.kycStatus === "pending").length,
      rejectedTenants: tenants.filter(t => t.kycStatus === "rejected").length,
    },
  };
}

async function generateComplianceSummaryReport(ctx: any, propertyIds: string[], period: any) {
  const complianceData = [];

  for (const propertyId of propertyIds) {
    const property = await ctx.db.get(propertyId);
    if (!property) continue;

    // Get compliance statuses for this property
    const complianceStatuses = await ctx.db
      .query("complianceStatus")
      .filter((q) => q.eq(q.field("propertyId"), propertyId))
      .collect();

    const totalEntities = complianceStatuses.length;
    const compliantEntities = complianceStatuses.filter(status => status.overallStatus === "compliant").length;
    const nonCompliantEntities = complianceStatuses.filter(status => status.overallStatus === "non_compliant").length;
    const pendingEntities = complianceStatuses.filter(status => status.overallStatus === "pending").length;

    // Get alerts
    const alerts = complianceStatuses.flatMap(status => status.alerts || []);
    const criticalAlerts = alerts.filter(alert => alert.severity === "critical").length;
    const highAlerts = alerts.filter(alert => alert.severity === "error").length;

    complianceData.push({
      propertyId,
      propertyName: property.name,
      totalEntities,
      compliantEntities,
      nonCompliantEntities,
      pendingEntities,
      complianceRate: totalEntities > 0 ? (compliantEntities / totalEntities) * 100 : 0,
      criticalAlerts,
      highAlerts,
      lastReviewDate: Math.max(...complianceStatuses.map(s => s.lastReviewDate || 0)),
    });
  }

  return {
    reportPeriod: {
      startDate: new Date(period.startDate).toISOString().split('T')[0],
      endDate: new Date(period.endDate).toISOString().split('T')[0],
    },
    properties: complianceData,
    summary: {
      totalProperties: complianceData.length,
      averageComplianceRate: complianceData.length > 0 
        ? complianceData.reduce((sum, prop) => sum + prop.complianceRate, 0) / complianceData.length 
        : 0,
      totalCriticalAlerts: complianceData.reduce((sum, prop) => sum + prop.criticalAlerts, 0),
      totalHighAlerts: complianceData.reduce((sum, prop) => sum + prop.highAlerts, 0),
    },
  };
}

async function generateKYCStatusReport(ctx: any, propertyIds: string[], period: any) {
  const kycData = [];

  // Get all users associated with the properties
  const allUsers = await ctx.db.query("users").collect();
  const relevantUsers = allUsers.filter(user => 
    user.propertyAccess.some(propId => propertyIds.includes(propId))
  );

  for (const user of relevantUsers) {
    const kycDocuments = await ctx.db
      .query("kycDocuments")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const verifiedDocs = kycDocuments.filter(doc => doc.verificationStatus === "verified");
    const pendingDocs = kycDocuments.filter(doc => doc.verificationStatus === "pending");
    const rejectedDocs = kycDocuments.filter(doc => doc.verificationStatus === "rejected");

    kycData.push({
      userId: user._id,
      userName: user.name,
      userEmail: user.email,
      userRole: user.role,
      kycStatus: user.kycStatus,
      totalDocuments: kycDocuments.length,
      verifiedDocuments: verifiedDocs.length,
      pendingDocuments: pendingDocs.length,
      rejectedDocuments: rejectedDocs.length,
      lastDocumentUpload: Math.max(...kycDocuments.map(doc => doc.createdAt), 0),
    });
  }

  return {
    reportPeriod: {
      startDate: new Date(period.startDate).toISOString().split('T')[0],
      endDate: new Date(period.endDate).toISOString().split('T')[0],
    },
    users: kycData,
    summary: {
      totalUsers: kycData.length,
      verifiedUsers: kycData.filter(u => u.kycStatus === "verified").length,
      pendingUsers: kycData.filter(u => u.kycStatus === "pending").length,
      rejectedUsers: kycData.filter(u => u.kycStatus === "rejected").length,
      totalDocuments: kycData.reduce((sum, u) => sum + u.totalDocuments, 0),
      verifiedDocuments: kycData.reduce((sum, u) => sum + u.verifiedDocuments, 0),
    },
  };
}

// Automated compliance checking
async function performAutomatedCheck(ctx: any, entityId: string, entityType: string, requirement: any) {
  let status: "compliant" | "non_compliant" | "pending" | "not_applicable" = "pending";
  let documentIds: string[] = [];

  // Example automated checks based on requirement category
  switch (requirement.category) {
    case "kyc":
      if (entityType === "tenant" || entityType === "vendor") {
        const user = await ctx.db.get(entityId);
        if (user) {
          const userDocs = await ctx.db
            .query("kycDocuments")
            .withIndex("by_user", (q) => q.eq("userId", entityId))
            .filter((q) => q.eq(q.field("isActive"), true))
            .collect();

          const verifiedDocs = userDocs.filter(doc => doc.verificationStatus === "verified");
          const hasRequiredDocs = requirement.requiredDocuments.every((docType: string) =>
            verifiedDocs.some(doc => doc.documentType === docType)
          );

          status = hasRequiredDocs ? "compliant" : "non_compliant";
          documentIds = verifiedDocs.map(doc => doc._id);
        }
      }
      break;

    case "legal":
      // Check for legal document compliance
      status = "pending"; // Requires manual review
      break;

    case "financial":
      // Check payment history, financial standing
      if (entityType === "tenant") {
        const invoices = await ctx.db
          .query("invoices")
          .withIndex("by_tenant", (q) => q.eq("tenantId", entityId))
          .collect();

        const overdueInvoices = invoices.filter(inv => 
          inv.status === "overdue" && inv.dueDate < Date.now()
        );

        status = overdueInvoices.length === 0 ? "compliant" : "non_compliant";
      }
      break;

    default:
      status = "pending";
  }

  return { status, documentIds };
}

// Helper function to update user KYC status
async function updateUserKYCStatus(ctx: any, userId: string) {
  const userDocuments = await ctx.db
    .query("kycDocuments")
    .withIndex("by_user", (q) => q.eq("userId", userId))
    .filter((q) => q.eq(q.field("isActive"), true))
    .collect();

  // Define required document types for KYC completion
  const requiredDocTypes = ["national_id", "proof_of_income"];
  
  const verifiedDocs = userDocuments.filter(doc => doc.verificationStatus === "verified");
  const hasAllRequired = requiredDocTypes.every(type => 
    verifiedDocs.some(doc => doc.documentType === type)
  );

  let kycStatus: "pending" | "verified" | "rejected";
  if (hasAllRequired) {
    kycStatus = "verified";
  } else if (userDocuments.some(doc => doc.verificationStatus === "rejected")) {
    kycStatus = "rejected";
  } else {
    kycStatus = "pending";
  }

  await ctx.db.patch(userId, { kycStatus });
}