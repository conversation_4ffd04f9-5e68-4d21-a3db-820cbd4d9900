import React from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Separator } from "../ui/separator";
import { CheckCircle, XCircle, Clock, CreditCard, Smartphone, Building } from "lucide-react";
import { format } from "date-fns";

interface PaymentStatusTrackerProps {
  invoiceId: Id<"invoices">;
}

export const PaymentStatusTracker: React.FC<PaymentStatusTrackerProps> = ({ invoiceId }) => {
  const payments = useQuery(api.payments.getPaymentsByInvoice, { invoiceId });
  // Mock invoice data for now since getInvoice doesn't exist yet
  const invoice = useQuery(api.invoices.getInvoiceById, { id: invoiceId });

  if (!payments || !invoice) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Clock className="h-4 w-4 animate-spin mr-2" />
            Loading payment information...
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "refunded":
        return <XCircle className="h-4 w-4 text-orange-600" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: "secondary" as const,
      completed: "default" as const,
      failed: "destructive" as const,
      refunded: "outline" as const,
    };

    return (
      <Badge variant={variants[status as keyof typeof variants] || "secondary"}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case "mpesa":
        return <Smartphone className="h-4 w-4 text-green-600" />;
      case "stripe":
        return <CreditCard className="h-4 w-4 text-blue-600" />;
      case "bank_transfer":
        return <Building className="h-4 w-4 text-gray-600" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat("en-KE", {
      style: "currency",
      currency: "KES",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const totalPaid = payments
    .filter((p: any) => p.status === "completed")
    .reduce((sum: number, p: any) => sum + p.amount, 0);

  const remainingAmount = invoice?.invoice.amount - totalPaid;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Payment Status
          {getStatusBadge(invoice?.invoice.status)}
        </CardTitle>
        <CardDescription>
          Invoice #{invoice._id.slice(-8)} - Due {format(new Date(invoice.dueDate), "MMM dd, yyyy")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Payment Summary */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-sm text-muted-foreground">Total Amount</p>
            <p className="font-semibold">{formatAmount(invoice.amount)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Paid</p>
            <p className="font-semibold text-green-600">{formatAmount(totalPaid)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Remaining</p>
            <p className="font-semibold text-red-600">{formatAmount(remainingAmount)}</p>
          </div>
        </div>

        <Separator />

        {/* Payment History */}
        <div className="space-y-3">
          <h4 className="font-medium">Payment History</h4>
          {payments.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-4">
              No payments recorded yet
            </p>
          ) : (
            <div className="space-y-3">
              {payments
                .sort((a: any, b: any) => b.createdAt - a.createdAt)
                .map((payment: any) => (
                  <div
                    key={payment._id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(payment.status)}
                      {getMethodIcon(payment.method)}
                      <div>
                        <p className="font-medium">{formatAmount(payment.amount)}</p>
                        <p className="text-sm text-muted-foreground">
                          {format(new Date(payment.createdAt), "MMM dd, yyyy 'at' HH:mm")}
                        </p>
                        {payment.metadata?.mpesaReceiptNumber && (
                          <p className="text-xs text-muted-foreground">
                            Receipt: {payment.metadata.mpesaReceiptNumber}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      {getStatusBadge(payment.status)}
                      <p className="text-xs text-muted-foreground mt-1">
                        {payment.method.toUpperCase()}
                      </p>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </div>

        {/* Payment Progress Bar */}
        {totalPaid > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Payment Progress</span>
              <span>{Math.round((totalPaid / invoice.amount) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min((totalPaid / invoice.amount) * 100, 100)}%` }}
              />
            </div>
          </div>
        )}

        {/* Additional Information */}
        {invoice.paymentMethod && (
          <div className="text-sm text-muted-foreground">
            <p>Last payment method: {invoice.paymentMethod.toUpperCase()}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};