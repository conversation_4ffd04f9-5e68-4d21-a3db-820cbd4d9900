/**
 * React hook for offline sync functionality
 */

import { useState, useEffect, useCallback } from 'react';
import { useConvex } from 'convex/react';
import { offlineSyncManager, SyncStatus, SyncOptions } from '../lib/offline-sync';

export function useOfflineSync(options: Partial<SyncOptions> = {}) {
  const convex = useConvex();
  const [status, setStatus] = useState<SyncStatus>({
    isOnline: navigator.onLine,
    isSyncing: false,
    lastSyncTime: 0,
    pendingOperations: 0,
    conflicts: 0,
    error: null
  });
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize sync manager
  useEffect(() => {
    let mounted = true;

    const initialize = async () => {
      try {
        await offlineSyncManager.initialize(convex, options);
        if (mounted) {
          setIsInitialized(true);
        }
      } catch (error) {
        console.error('Failed to initialize offline sync:', error);
      }
    };

    if (convex) {
      initialize();
    }

    return () => {
      mounted = false;
    };
  }, [convex]);

  // Set up status listener
  useEffect(() => {
    if (!isInitialized) return;

    const handleStatusChange = (newStatus: SyncStatus) => {
      setStatus(newStatus);
    };

    offlineSyncManager.addStatusListener(handleStatusChange);

    return () => {
      offlineSyncManager.removeStatusListener(handleStatusChange);
    };
  }, [isInitialized]);

  // Manual sync trigger
  const sync = useCallback(async () => {
    if (!isInitialized) return;
    await offlineSyncManager.sync();
  }, [isInitialized]);

  // Queue operation for offline sync
  const queueOperation = useCallback(async (operation: {
    operation: 'create' | 'update' | 'delete';
    entityType: string;
    entityId?: string;
    data: any;
  }) => {
    if (!isInitialized) return;
    await offlineSyncManager.queueOperation(operation);
  }, [isInitialized]);

  // Cache entity locally
  const cacheEntity = useCallback(async (
    entityType: string,
    id: string,
    data: any,
    propertyId?: string
  ) => {
    if (!isInitialized) return;
    await offlineSyncManager.cacheEntity(entityType, id, data, propertyId);
  }, [isInitialized]);

  // Get cached entity
  const getCachedEntity = useCallback(async (
    entityType: string,
    id: string
  ) => {
    if (!isInitialized) return null;
    return await offlineSyncManager.getCachedEntity(entityType, id);
  }, [isInitialized]);

  // Get cached entities
  const getCachedEntities = useCallback(async (
    entityType: string,
    propertyId?: string
  ) => {
    if (!isInitialized) return [];
    return await offlineSyncManager.getCachedEntities(entityType, propertyId);
  }, [isInitialized]);

  return {
    // Status
    status,
    isInitialized,
    isOnline: status.isOnline,
    isSyncing: status.isSyncing,
    hasPendingOperations: status.pendingOperations > 0,
    hasConflicts: status.conflicts > 0,
    hasError: !!status.error,
    
    // Actions
    sync,
    queueOperation,
    cacheEntity,
    getCachedEntity,
    getCachedEntities,
    
    // Computed values
    lastSyncText: status.lastSyncTime > 0 
      ? `Last synced ${new Date(status.lastSyncTime).toLocaleTimeString()}`
      : 'Never synced',
    statusText: getStatusText(status)
  };
}

function getStatusText(status: SyncStatus): string {
  if (!status.isOnline) {
    return 'Offline';
  }
  
  if (status.isSyncing) {
    return 'Syncing...';
  }
  
  if (status.error) {
    return 'Sync error';
  }
  
  if (status.conflicts > 0) {
    return `${status.conflicts} conflicts`;
  }
  
  if (status.pendingOperations > 0) {
    return `${status.pendingOperations} pending`;
  }
  
  return 'Synced';
}

// Hook for offline-first data operations
export function useOfflineData<T>(
  entityType: string,
  entityId?: string,
  propertyId?: string
) {
  const { getCachedEntity, getCachedEntities, cacheEntity, queueOperation } = useOfflineSync();
  const [data, setData] = useState<T | T[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load cached data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        let cachedData;
        if (entityId) {
          cachedData = await getCachedEntity(entityType, entityId);
        } else {
          cachedData = await getCachedEntities(entityType, propertyId);
        }

        setData(cachedData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [entityType, entityId, propertyId, getCachedEntity, getCachedEntities]);

  // Update data locally and queue for sync
  const updateData = useCallback(async (newData: Partial<T>, id?: string) => {
    try {
      const targetId = id || entityId;
      if (!targetId) throw new Error('No entity ID provided');

      // Update local cache
      await cacheEntity(entityType, targetId, newData, propertyId);
      
      // Queue for sync
      await queueOperation({
        operation: 'update',
        entityType,
        entityId: targetId,
        data: newData
      });

      // Update local state
      if (entityId) {
        setData(prev => ({ ...prev as T, ...newData }));
      } else {
        // For arrays, update the specific item
        setData(prev => {
          if (Array.isArray(prev)) {
            return prev.map(item => 
              (item as any).id === targetId ? { ...item, ...newData } : item
            );
          }
          return prev;
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update data');
      throw err;
    }
  }, [entityType, entityId, propertyId, cacheEntity, queueOperation]);

  // Create new data locally and queue for sync
  const createData = useCallback(async (newData: Omit<T, 'id'>) => {
    try {
      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const dataWithId = { ...newData, id: tempId } as T;

      // Cache locally
      await cacheEntity(entityType, tempId, dataWithId, propertyId);
      
      // Queue for sync
      await queueOperation({
        operation: 'create',
        entityType,
        data: dataWithId
      });

      // Update local state
      if (Array.isArray(data)) {
        setData(prev => [...(prev as T[]), dataWithId]);
      } else {
        setData(dataWithId);
      }

      return tempId;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create data');
      throw err;
    }
  }, [entityType, propertyId, data, cacheEntity, queueOperation]);

  // Delete data locally and queue for sync
  const deleteData = useCallback(async (id?: string) => {
    try {
      const targetId = id || entityId;
      if (!targetId) throw new Error('No entity ID provided');

      // Queue for sync
      await queueOperation({
        operation: 'delete',
        entityType,
        entityId: targetId,
        data: {}
      });

      // Update local state
      if (Array.isArray(data)) {
        setData(prev => (prev as T[]).filter(item => (item as any).id !== targetId));
      } else {
        setData(null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete data');
      throw err;
    }
  }, [entityType, entityId, data, queueOperation]);

  return {
    data,
    loading,
    error,
    updateData,
    createData,
    deleteData,
    refetch: () => {
      // Trigger reload by changing a dependency
      setLoading(true);
    }
  };
}