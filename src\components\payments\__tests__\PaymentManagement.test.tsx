import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { PaymentManagement } from '../PaymentManagement';

// Mock Convex hooks
const mockProcessPayment = vi.fn();
const mockRefundPayment = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: vi.fn(() => [
    {
      _id: 'payment1',
      invoiceId: 'invoice1',
      amount: 50000,
      method: 'mpesa',
      transactionId: 'MPESA123456',
      status: 'completed',
      processedAt: Date.now(),
      invoice: {
        leaseId: 'lease1',
        dueDate: Date.now(),
        lease: {
          tenant: { name: '<PERSON>', email: '<EMAIL>' },
          unit: { unitNumber: 'A101' },
        },
      },
    },
    {
      _id: 'payment2',
      invoiceId: 'invoice2',
      amount: 45000,
      method: 'stripe',
      transactionId: 'STRIPE789012',
      status: 'pending',
      processedAt: Date.now(),
      invoice: {
        leaseId: 'lease2',
        dueDate: Date.now(),
        lease: {
          tenant: { name: '<PERSON>', email: '<EMAIL>' },
          unit: { unitNumber: 'A102' },
        },
      },
    },
  ]),
  useMutation: vi.fn((mutation) => {
    if (mutation.toString().includes('process')) return mockProcessPayment;
    if (mutation.toString().includes('refund')) return mockRefundPayment;
    return vi.fn();
  }),
}));

// Mock auth context
vi.mock('../../../lib/auth-context', () => ({
  useAuth: vi.fn(() => ({
    user: {
      _id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'manager',
    },
  })),
}));

describe('PaymentManagement', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders payment list with payments', () => {
    render(<PaymentManagement propertyId="property1" />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('KES 50,000')).toBeInTheDocument();
    expect(screen.getByText('KES 45,000')).toBeInTheDocument();
  });

  it('shows payment status badges correctly', () => {
    render(<PaymentManagement propertyId="property1" />);
    
    expect(screen.getByText('Completed')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
  });

  it('shows payment method badges', () => {
    render(<PaymentManagement propertyId="property1" />);
    
    expect(screen.getByText('M-PESA')).toBeInTheDocument();
    expect(screen.getByText('Stripe')).toBeInTheDocument();
  });

  it('filters payments by status', () => {
    render(<PaymentManagement propertyId="property1" />);
    
    const statusFilter = screen.getByRole('combobox', { name: /filter by status/i });
    fireEvent.click(statusFilter);
    
    const completedOption = screen.getByText('Completed');
    fireEvent.click(completedOption);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
  });

  it('filters payments by method', () => {
    render(<PaymentManagement propertyId="property1" />);
    
    const methodFilter = screen.getByRole('combobox', { name: /filter by method/i });
    fireEvent.click(methodFilter);
    
    const mpesaOption = screen.getByText('M-PESA');
    fireEvent.click(mpesaOption);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
  });

  it('searches payments by tenant name', () => {
    render(<PaymentManagement propertyId="property1" />);
    
    const searchInput = screen.getByPlaceholderText(/search payments/i);
    fireEvent.change(searchInput, { target: { value: 'John' } });
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
  });

  it('shows payment details when view button is clicked', () => {
    render(<PaymentManagement propertyId="property1" />);
    
    const viewButtons = screen.getAllByRole('button', { name: /view/i });
    fireEvent.click(viewButtons[0]);
    
    expect(screen.getByText(/payment details/i)).toBeInTheDocument();
    expect(screen.getByText('MPESA123456')).toBeInTheDocument();
  });

  it('processes refund with confirmation', async () => {
    mockRefundPayment.mockResolvedValue({ success: true, refundId: 'refund123' });
    
    // Mock window.confirm and prompt
    const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(true);
    const promptSpy = vi.spyOn(window, 'prompt').mockReturnValue('25000');
    
    render(<PaymentManagement propertyId="property1" />);
    
    const refundButtons = screen.getAllByRole('button', { name: /refund/i });
    fireEvent.click(refundButtons[0]);
    
    await waitFor(() => {
      expect(confirmSpy).toHaveBeenCalledWith(
        'Are you sure you want to process a refund for this payment?'
      );
      expect(promptSpy).toHaveBeenCalledWith(
        'Enter refund amount (max: KES 50,000):',
        '50000'
      );
      expect(mockRefundPayment).toHaveBeenCalledWith({
        paymentId: 'payment1',
        amount: 25000,
        reason: 'Manual refund',
      });
    });
    
    confirmSpy.mockRestore();
    promptSpy.mockRestore();
  });

  it('shows payment analytics summary', () => {
    render(<PaymentManagement propertyId="property1" />);
    
    expect(screen.getByText(/total payments/i)).toBeInTheDocument();
    expect(screen.getByText(/completed payments/i)).toBeInTheDocument();
    expect(screen.getByText(/pending payments/i)).toBeInTheDocument();
  });

  it('exports payment data', async () => {
    const mockExport = vi.fn();
    
    // Mock file download
    const createElementSpy = vi.spyOn(document, 'createElement');
    const mockAnchor = {
      href: '',
      download: '',
      click: vi.fn(),
    };
    createElementSpy.mockReturnValue(mockAnchor as any);
    
    render(<PaymentManagement propertyId="property1" />);
    
    const exportButton = screen.getByRole('button', { name: /export/i });
    fireEvent.click(exportButton);
    
    await waitFor(() => {
      expect(mockAnchor.click).toHaveBeenCalled();
    });
    
    createElementSpy.mockRestore();
  });

  it('shows empty state when no payments exist', () => {
    vi.mocked(require('convex/react').useQuery).mockReturnValue([]);
    
    render(<PaymentManagement propertyId="property1" />);
    
    expect(screen.getByText(/no payments found/i)).toBeInTheDocument();
    expect(screen.getByText(/payments will appear here/i)).toBeInTheDocument();
  });

  it('handles loading state', () => {
    vi.mocked(require('convex/react').useQuery).mockReturnValue(undefined);
    
    render(<PaymentManagement propertyId="property1" />);
    
    expect(screen.getByText(/loading payments/i)).toBeInTheDocument();
  });

  it('shows error message on refund failure', async () => {
    mockRefundPayment.mockRejectedValue(new Error('Refund processing failed'));
    
    const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(true);
    const promptSpy = vi.spyOn(window, 'prompt').mockReturnValue('25000');
    
    render(<PaymentManagement propertyId="property1" />);
    
    const refundButtons = screen.getAllByRole('button', { name: /refund/i });
    fireEvent.click(refundButtons[0]);
    
    await waitFor(() => {
      expect(screen.getByText(/refund processing failed/i)).toBeInTheDocument();
    });
    
    confirmSpy.mockRestore();
    promptSpy.mockRestore();
  });

  it('shows payment reconciliation status', () => {
    render(<PaymentManagement propertyId="property1" />);
    
    expect(screen.getByText(/reconciliation status/i)).toBeInTheDocument();
  });

  it('handles bulk payment operations', async () => {
    render(<PaymentManagement propertyId="property1" />);
    
    // Select multiple payments
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[0]);
    fireEvent.click(checkboxes[1]);
    
    expect(screen.getByText(/2 selected/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /bulk actions/i })).toBeInTheDocument();
  });
});