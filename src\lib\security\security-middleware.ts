import { InputVali<PERSON><PERSON>, InputSanitizer, RateLimitConfigs } from './input-validation';
import { SecurityMiddleware as SecurityMiddlewareCore, DDoSProtection } from './rate-limiting';
import { SessionManager, TokenSecurity, SecurityEventLogger, SecurityEventType } from './session-management';
import { auditLogger, AuditEventType, AuditSeverity } from './audit-logging';

// Security context interface
export interface SecurityContext {
  userId?: string;
  sessionId?: string;
  role?: string;
  permissions?: string[];
  ipAddress?: string;
  userAgent?: string;
  fingerprint?: string;
}

// Security middleware for React components and API calls
export class SecurityMiddleware {
  private static sessionManager = SessionManager.getInstance();

  /**
   * Secure API request wrapper
   */
  static async secureApiCall<T>(
    endpoint: string,
    options: RequestInit = {},
    requireAuth: boolean = true,
    rateLimitKey?: string
  ): Promise<T> {
    try {
      // Get security context
      const context = await this.getSecurityContext();
      
      // Check authentication if required
      if (requireAuth && !context.userId) {
        throw new Error('Authentication required');
      }

      // Apply rate limiting
      if (rateLimitKey && RateLimitConfigs[rateLimitKey as keyof typeof RateLimitConfigs]) {
        const config = RateLimitConfigs[rateLimitKey as keyof typeof RateLimitConfigs];
        await SecurityMiddlewareCore.secureRequest(endpoint, options, config);
      }

      // Sanitize request body if present
      if (options.body) {
        options.body = this.sanitizeRequestBody(options.body);
      }

      // Add security headers
      const secureOptions: RequestInit = {
        ...options,
        headers: {
          ...options.headers,
          'Authorization': context.userId ? `Bearer ${TokenSecurity.retrieveToken('accessToken')}` : '',
          'X-Session-ID': context.sessionId || '',
          'X-Request-ID': crypto.randomUUID(),
          'X-Timestamp': Date.now().toString(),
          'Content-Type': 'application/json'
        }
      };

      // Make the request
      const response = await fetch(endpoint, secureOptions);

      // Log the API call
      await auditLogger.logEvent({
        type: this.getAuditEventTypeForEndpoint(endpoint),
        severity: AuditSeverity.LOW,
        userId: context.userId,
        sessionId: context.sessionId,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        resource: endpoint,
        resourceType: 'api_endpoint',
        action: `API call to ${endpoint}`,
        details: {
          method: options.method || 'GET',
          statusCode: response.status
        },
        success: response.ok
      });

      if (!response.ok) {
        throw new Error(`API call failed: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      // Log security event for failed API calls
      SecurityEventLogger.logEvent({
        type: SecurityEventType.PERMISSION_DENIED,
        userId: (await this.getSecurityContext()).userId,
        details: { endpoint, error: error instanceof Error ? error.message : 'Unknown error' },
        severity: 'medium'
      });

      throw error;
    }
  }

  /**
   * Validate user permissions for action
   */
  static async validatePermissions(
    requiredPermissions: string[],
    resource?: string
  ): Promise<boolean> {
    const context = await this.getSecurityContext();
    
    if (!context.userId || !context.permissions) {
      return false;
    }

    // Check if user has all required permissions
    const hasPermissions = requiredPermissions.every(permission => 
      context.permissions!.includes(permission)
    );

    if (!hasPermissions) {
      // Log permission denied event
      await auditLogger.logSecurityEvent(
        AuditEventType.SUSPICIOUS_LOGIN_ATTEMPT,
        AuditSeverity.MEDIUM,
        context.userId,
        context.sessionId,
        context.ipAddress || 'unknown',
        `Permission denied for ${requiredPermissions.join(', ')}`,
        { resource, requiredPermissions, userPermissions: context.permissions }
      );
    }

    return hasPermissions;
  }

  /**
   * Secure form submission
   */
  static async secureFormSubmit<T>(
    formData: Record<string, any>,
    validationSchema: any,
    endpoint: string,
    auditEventType: AuditEventType
  ): Promise<T> {
    try {
      // Validate and sanitize input
      const sanitizedData = this.sanitizeFormData(formData);
      const validatedData = InputValidator.validateInput(validationSchema, sanitizedData);

      // Submit securely
      const result = await this.secureApiCall<T>(endpoint, {
        method: 'POST',
        body: JSON.stringify(validatedData)
      });

      // Log successful operation
      const context = await this.getSecurityContext();
      await auditLogger.logDataModification(
        auditEventType,
        context.userId!,
        context.sessionId!,
        endpoint,
        'form_submission',
        'Form submitted successfully',
        undefined,
        validatedData
      );

      return result;
    } catch (error) {
      // Log failed operation
      const context = await this.getSecurityContext();
      await auditLogger.logEvent({
        type: auditEventType,
        severity: AuditSeverity.MEDIUM,
        userId: context.userId,
        sessionId: context.sessionId,
        action: 'Form submission failed',
        details: { endpoint, error: error instanceof Error ? error.message : 'Unknown error' },
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });

      throw error;
    }
  }

  /**
   * Secure file upload
   */
  static async secureFileUpload(
    file: File,
    endpoint: string,
    allowedTypes: string[] = ['image/jpeg', 'image/png', 'application/pdf'],
    maxSize: number = 50 * 1024 * 1024
  ): Promise<any> {
    try {
      // Validate file
      InputValidator.validateFileUpload(file, allowedTypes, maxSize);

      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      // Upload securely
      const result = await this.secureApiCall(endpoint, {
        method: 'POST',
        body: formData
      }, true, 'fileUpload');

      // Log file upload
      const context = await this.getSecurityContext();
      await auditLogger.logEvent({
        type: AuditEventType.DOCUMENT_UPLOADED,
        severity: AuditSeverity.MEDIUM,
        userId: context.userId!,
        sessionId: context.sessionId!,
        action: 'File uploaded',
        details: {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          endpoint
        },
        success: true
      });

      return result;
    } catch (error) {
      // Log failed upload
      const context = await this.getSecurityContext();
      await auditLogger.logEvent({
        type: AuditEventType.DOCUMENT_UPLOADED,
        severity: AuditSeverity.HIGH,
        userId: context.userId,
        sessionId: context.sessionId,
        action: 'File upload failed',
        details: {
          fileName: file.name,
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });

      throw error;
    }
  }

  /**
   * Secure financial operation
   */
  static async secureFinancialOperation<T>(
    operation: string,
    amount: number,
    currency: string,
    endpoint: string,
    data: Record<string, any>
  ): Promise<T> {
    const context = await this.getSecurityContext();

    // Require recent authentication for financial operations
    if (!this.sessionManager.verifyRecentAuth(context.sessionId!, 5 * 60 * 1000)) {
      throw new Error('Recent authentication required for financial operations');
    }

    try {
      // Validate amount
      if (amount <= 0 || amount > 999999999.99) {
        throw new Error('Invalid amount');
      }

      // Submit operation
      const result = await this.secureApiCall<T>(endpoint, {
        method: 'POST',
        body: JSON.stringify(data)
      }, true, 'payment');

      // Log financial operation
      await auditLogger.logFinancialOperation(
        AuditEventType.PAYMENT_PROCESSED,
        context.userId!,
        context.sessionId!,
        amount,
        currency,
        endpoint,
        operation,
        true,
        data
      );

      return result;
    } catch (error) {
      // Log failed financial operation
      await auditLogger.logFinancialOperation(
        AuditEventType.PAYMENT_PROCESSED,
        context.userId!,
        context.sessionId!,
        amount,
        currency,
        endpoint,
        operation,
        false,
        data,
        error instanceof Error ? error.message : 'Unknown error'
      );

      throw error;
    }
  }

  /**
   * Get current security context
   */
  static async getSecurityContext(): Promise<SecurityContext> {
    const accessToken = TokenSecurity.retrieveToken('accessToken');
    
    if (!accessToken) {
      return {};
    }

    const session = await this.sessionManager.validateSession(accessToken);
    
    if (!session) {
      return {};
    }

    return {
      userId: session.userId,
      sessionId: session.sessionId,
      role: session.role,
      permissions: session.permissions,
      ipAddress: session.ipAddress,
      userAgent: session.deviceInfo.userAgent,
      fingerprint: session.deviceInfo.fingerprint
    };
  }

  /**
   * Check if current session is valid
   */
  static async isSessionValid(): Promise<boolean> {
    const context = await this.getSecurityContext();
    return !!context.userId;
  }

  /**
   * Require authentication for component
   */
  static async requireAuth(): Promise<SecurityContext> {
    const context = await this.getSecurityContext();
    
    if (!context.userId) {
      throw new Error('Authentication required');
    }

    return context;
  }

  /**
   * Sanitize form data
   */
  private static sanitizeFormData(data: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        sanitized[key] = InputSanitizer.sanitizeText(value);
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeFormData(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Sanitize request body
   */
  private static sanitizeRequestBody(body: string | FormData): string | FormData {
    if (typeof body === 'string') {
      try {
        const parsed = JSON.parse(body);
        const sanitized = this.sanitizeFormData(parsed);
        return JSON.stringify(sanitized);
      } catch {
        return InputSanitizer.sanitizeText(body);
      }
    }

    return body; // FormData is handled separately
  }

  /**
   * Get audit event type for endpoint
   */
  private static getAuditEventTypeForEndpoint(endpoint: string): AuditEventType {
    if (endpoint.includes('/auth/')) return AuditEventType.USER_LOGIN;
    if (endpoint.includes('/properties/')) return AuditEventType.PROPERTY_UPDATED;
    if (endpoint.includes('/leases/')) return AuditEventType.LEASE_UPDATED;
    if (endpoint.includes('/payments/')) return AuditEventType.PAYMENT_PROCESSED;
    if (endpoint.includes('/documents/')) return AuditEventType.DOCUMENT_UPLOADED;
    
    return AuditEventType.SYSTEM_BACKUP_CREATED; // Generic system operation
  }
}

// React hook for security context
export function useSecurityContext() {
  const [context, setContext] = React.useState<SecurityContext>({});
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const loadContext = async () => {
      try {
        const securityContext = await SecurityMiddleware.getSecurityContext();
        setContext(securityContext);
      } catch (error) {
        console.error('Failed to load security context:', error);
      } finally {
        setLoading(false);
      }
    };

    loadContext();
  }, []);

  return { context, loading };
}

// React hook for permission checking
export function usePermissions() {
  const { context } = useSecurityContext();

  const hasPermission = React.useCallback((permission: string): boolean => {
    return context.permissions?.includes(permission) || false;
  }, [context.permissions]);

  const hasAnyPermission = React.useCallback((permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  }, [hasPermission]);

  const hasAllPermissions = React.useCallback((permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  }, [hasPermission]);

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    permissions: context.permissions || []
  };
}

// Security-aware component wrapper
export function withSecurity<P extends object>(
  Component: React.ComponentType<P>,
  requiredPermissions: string[] = []
) {
  return function SecurityWrappedComponent(props: P) {
    const { context, loading } = useSecurityContext();
    const { hasAllPermissions } = usePermissions();

    if (loading) {
      return React.createElement('div', null, 'Loading...');
    }

    if (!context.userId) {
      return React.createElement('div', null, 'Authentication required');
    }

    if (requiredPermissions.length > 0 && !hasAllPermissions(requiredPermissions)) {
      return React.createElement('div', null, 'Insufficient permissions');
    }

    return React.createElement(Component, props);
  };
}

// Import React for hooks
import React from 'react';