import { 
  app, 
  BrowserWindow, 
  Menu, 
  shell, 
  ipcMain, 
  dialog, 
  Tray, 
  nativeImage, 
  Notification,
  globalShortcut,
  webContents
} from 'electron'
import { autoUpdater } from 'electron-updater'
import log from 'electron-log'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import { promises as fs } from 'fs'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// The built directory structure
//
// ├─┬ dist
// │ ├─┬ main
// │ │ └── main.js
// │ ├─┬ preload
// │ │ └── preload.js
// │ └─┬ renderer
// │   └── index.html

process.env.DIST = join(__dirname, '../dist')
process.env.VITE_PUBLIC = app.isPackaged
  ? process.env.DIST!
  : join(process.env.DIST!, '../public')

let win: BrowserWindow | null
let tray: Tray | null = null

// Configure logging
log.transports.file.level = 'info'
autoUpdater.logger = log

// Configure auto-updater
autoUpdater.checkForUpdatesAndNotify = autoUpdater.checkForUpdatesAndNotify
autoUpdater.autoDownload = false // We'll handle download manually
autoUpdater.autoInstallOnAppQuit = true

// Auto-updater configuration
if (process.env.NODE_ENV === 'production') {
  // In production, use GitHub releases or your update server
  autoUpdater.setFeedURL({
    provider: 'github',
    owner: 'estate-pulse',
    repo: 'estate-pulse-app',
    private: false
  })
} else {
  // In development, disable auto-updater
  autoUpdater.updateConfigPath = null
}

function createWindow() {
  win = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    icon: join(process.env.VITE_PUBLIC!, 'electron-vite.svg'),
    webPreferences: {
      preload: join(__dirname, '../preload/preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
    },
    titleBarStyle: 'default',
    show: false,
  })

  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', new Date().toLocaleString())
  })

  if (process.env.VITE_DEV_SERVER_URL) {
    win.loadURL(process.env.VITE_DEV_SERVER_URL)
    win.webContents.openDevTools()
  } else {
    win.loadFile(join(process.env.DIST!, 'renderer/index.html'))
  }

  // Make all links open with the browser, not with the application
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('https:')) shell.openExternal(url)
    return { action: 'deny' }
  })

  // Show window when ready to prevent visual flash
  win.once('ready-to-show', () => {
    win?.show()
  })
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
app.whenReady().then(() => {
  createWindow()
  createTray()
  createMenu()
  registerGlobalShortcuts()

  app.on('activate', () => {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.

// Handle app protocol for deep linking
app.setAsDefaultProtocolClient('estate-pulse')

// Security: Prevent new window creation
app.on('web-contents-created', (_, contents) => {
  contents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
})

// IPC handlers for secure communication
ipcMain.handle('app-version', () => {
  return app.getVersion()
})

ipcMain.handle('platform', () => {
  return process.platform
})

// Native file system access
ipcMain.handle('show-open-dialog', async (_, options) => {
  const result = await dialog.showOpenDialog(win!, options)
  return result
})

ipcMain.handle('show-save-dialog', async (_, options) => {
  const result = await dialog.showSaveDialog(win!, options)
  return result
})

ipcMain.handle('read-file', async (_, filePath: string) => {
  try {
    const content = await fs.readFile(filePath, 'utf-8')
    return { success: true, content }
  } catch (error) {
    return { success: false, error: (error as Error).message }
  }
})

ipcMain.handle('write-file', async (_, filePath: string, content: string) => {
  try {
    await fs.writeFile(filePath, content, 'utf-8')
    return { success: true }
  } catch (error) {
    return { success: false, error: (error as Error).message }
  }
})

// System notifications
ipcMain.handle('show-notification', (_, options: { title: string; body: string; icon?: string }) => {
  if (Notification.isSupported()) {
    const notification = new Notification({
      title: options.title,
      body: options.body,
      icon: options.icon ? join(process.env.VITE_PUBLIC!, options.icon) : undefined
    })
    notification.show()
    return true
  }
  return false
})

// Print functionality
ipcMain.handle('print-document', async (_, html: string, options?: any) => {
  try {
    // Create a hidden window for printing
    const printWindow = new BrowserWindow({
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    })

    await printWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(html)}`)
    
    const printOptions = {
      silent: false,
      printBackground: true,
      color: true,
      margin: {
        marginType: 'printableArea'
      },
      landscape: false,
      pagesPerSheet: 1,
      collate: false,
      copies: 1,
      ...options
    }

    const result = await printWindow.webContents.print(printOptions)
    printWindow.close()
    return { success: true, result }
  } catch (error) {
    return { success: false, error: (error as Error).message }
  }
})

// Print to PDF
ipcMain.handle('print-to-pdf', async (_, html: string, options?: any) => {
  try {
    const printWindow = new BrowserWindow({
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    })

    await printWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(html)}`)
    
    const pdfOptions = {
      marginsType: 0,
      pageSize: 'A4',
      printBackground: true,
      printSelectionOnly: false,
      landscape: false,
      ...options
    }

    const pdfBuffer = await printWindow.webContents.printToPDF(pdfOptions)
    printWindow.close()
    return { success: true, buffer: Array.from(pdfBuffer) }
  } catch (error) {
    return { success: false, error: (error as Error).message }
  }
})

// Window management
ipcMain.handle('minimize-window', () => {
  win?.minimize()
})

ipcMain.handle('maximize-window', () => {
  if (win?.isMaximized()) {
    win.unmaximize()
  } else {
    win?.maximize()
  }
})

ipcMain.handle('close-window', () => {
  win?.close()
})

ipcMain.handle('hide-window', () => {
  win?.hide()
})

ipcMain.handle('show-window', () => {
  win?.show()
  win?.focus()
})

// Auto-updater IPC handlers
ipcMain.handle('check-for-updates', async () => {
  try {
    const result = await autoUpdater.checkForUpdates()
    return { success: true, updateInfo: result?.updateInfo }
  } catch (error) {
    log.error('Check for updates failed:', error)
    return { success: false, error: (error as Error).message }
  }
})

ipcMain.handle('download-update', async () => {
  try {
    await autoUpdater.downloadUpdate()
    return { success: true }
  } catch (error) {
    log.error('Download update failed:', error)
    return { success: false, error: (error as Error).message }
  }
})

ipcMain.handle('install-update', () => {
  autoUpdater.quitAndInstall()
})

ipcMain.handle('get-app-version', () => {
  return app.getVersion()
})

ipcMain.handle('set-auto-launch', async (_, enable: boolean) => {
  try {
    if (enable) {
      await app.setLoginItemSettings({
        openAtLogin: true,
        name: 'EstatePulse'
      })
    } else {
      await app.setLoginItemSettings({
        openAtLogin: false
      })
    }
    return { success: true }
  } catch (error) {
    return { success: false, error: (error as Error).message }
  }
})

ipcMain.handle('get-auto-launch', () => {
  const settings = app.getLoginItemSettings()
  return settings.openAtLogin
})

// Crash reporting
ipcMain.handle('report-crash', (_, crashData: any) => {
  log.error('Application crash reported:', crashData)
  // In a real app, you'd send this to a crash reporting service
  return { success: true }
})

// Create system tray
function createTray() {
  const trayIcon = nativeImage.createFromPath(join(process.env.VITE_PUBLIC!, 'electron-vite.svg'))
  tray = new Tray(trayIcon.resize({ width: 16, height: 16 }))
  
  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Show EstatePulse',
      click: () => {
        win?.show()
        win?.focus()
      }
    },
    {
      label: 'Hide EstatePulse',
      click: () => {
        win?.hide()
      }
    },
    { type: 'separator' },
    {
      label: 'New Property',
      accelerator: 'CmdOrCtrl+N',
      click: () => {
        win?.webContents.send('menu-action', 'new-property')
      }
    },
    {
      label: 'New Lease',
      accelerator: 'CmdOrCtrl+Shift+L',
      click: () => {
        win?.webContents.send('menu-action', 'new-lease')
      }
    },
    {
      label: 'New Maintenance Ticket',
      accelerator: 'CmdOrCtrl+Shift+M',
      click: () => {
        win?.webContents.send('menu-action', 'new-ticket')
      }
    },
    { type: 'separator' },
    {
      label: 'Quit',
      accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
      click: () => {
        app.quit()
      }
    }
  ])
  
  tray.setContextMenu(contextMenu)
  tray.setToolTip('EstatePulse - Property Management')
  
  // Double click to show/hide window
  tray.on('double-click', () => {
    if (win?.isVisible()) {
      win.hide()
    } else {
      win?.show()
      win?.focus()
    }
  })
}

// Create application menu
function createMenu() {
  const isMac = process.platform === 'darwin'
  
  const template: Electron.MenuItemConstructorOptions[] = [
    // App menu (macOS)
    ...(isMac ? [{
      label: app.getName(),
      submenu: [
        { role: 'about' as const },
        { type: 'separator' as const },
        { role: 'services' as const },
        { type: 'separator' as const },
        { role: 'hide' as const },
        { role: 'hideOthers' as const },
        { role: 'unhide' as const },
        { type: 'separator' as const },
        { role: 'quit' as const }
      ]
    }] : []),
    
    // File menu
    {
      label: 'File',
      submenu: [
        {
          label: 'New Property',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            win?.webContents.send('menu-action', 'new-property')
          }
        },
        {
          label: 'New Lease',
          accelerator: 'CmdOrCtrl+Shift+L',
          click: () => {
            win?.webContents.send('menu-action', 'new-lease')
          }
        },
        {
          label: 'New Maintenance Ticket',
          accelerator: 'CmdOrCtrl+Shift+M',
          click: () => {
            win?.webContents.send('menu-action', 'new-ticket')
          }
        },
        { type: 'separator' },
        {
          label: 'Import Documents',
          accelerator: 'CmdOrCtrl+I',
          click: () => {
            win?.webContents.send('menu-action', 'import-documents')
          }
        },
        {
          label: 'Export Report',
          accelerator: 'CmdOrCtrl+E',
          click: () => {
            win?.webContents.send('menu-action', 'export-report')
          }
        },
        { type: 'separator' },
        isMac ? { role: 'close' } : { role: 'quit' }
      ]
    },
    
    // Edit menu
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        ...(isMac ? [
          { role: 'pasteAndMatchStyle' as const },
          { role: 'delete' as const },
          { role: 'selectAll' as const },
          { type: 'separator' as const },
          {
            label: 'Speech',
            submenu: [
              { role: 'startSpeaking' as const },
              { role: 'stopSpeaking' as const }
            ]
          }
        ] : [
          { role: 'delete' as const },
          { type: 'separator' as const },
          { role: 'selectAll' as const }
        ])
      ]
    },
    
    // View menu
    {
      label: 'View',
      submenu: [
        {
          label: 'Dashboard',
          accelerator: 'CmdOrCtrl+1',
          click: () => {
            win?.webContents.send('menu-action', 'navigate-dashboard')
          }
        },
        {
          label: 'Properties',
          accelerator: 'CmdOrCtrl+2',
          click: () => {
            win?.webContents.send('menu-action', 'navigate-properties')
          }
        },
        {
          label: 'Leases',
          accelerator: 'CmdOrCtrl+3',
          click: () => {
            win?.webContents.send('menu-action', 'navigate-leases')
          }
        },
        {
          label: 'Maintenance',
          accelerator: 'CmdOrCtrl+4',
          click: () => {
            win?.webContents.send('menu-action', 'navigate-maintenance')
          }
        },
        {
          label: 'Payments',
          accelerator: 'CmdOrCtrl+5',
          click: () => {
            win?.webContents.send('menu-action', 'navigate-payments')
          }
        },
        { type: 'separator' },
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    
    // Window menu
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'zoom' },
        ...(isMac ? [
          { type: 'separator' as const },
          { role: 'front' as const },
          { type: 'separator' as const },
          { role: 'window' as const }
        ] : [
          { role: 'close' as const }
        ])
      ]
    },
    
    // Help menu
    {
      role: 'help',
      submenu: [
        {
          label: 'About EstatePulse',
          click: () => {
            win?.webContents.send('menu-action', 'show-about')
          }
        },
        {
          label: 'User Guide',
          accelerator: 'F1',
          click: () => {
            win?.webContents.send('menu-action', 'show-help')
          }
        },
        {
          label: 'Keyboard Shortcuts',
          accelerator: 'CmdOrCtrl+/',
          click: () => {
            win?.webContents.send('menu-action', 'show-shortcuts')
          }
        },
        { type: 'separator' },
        {
          label: 'Report Issue',
          click: () => {
            shell.openExternal('https://github.com/estate-pulse/issues')
          }
        }
      ]
    }
  ]
  
  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// Register global shortcuts
function registerGlobalShortcuts() {
  // Quick actions
  globalShortcut.register('CmdOrCtrl+Shift+N', () => {
    win?.webContents.send('menu-action', 'quick-new')
  })
  
  // Search
  globalShortcut.register('CmdOrCtrl+K', () => {
    win?.webContents.send('menu-action', 'global-search')
  })
  
  // Toggle window visibility
  globalShortcut.register('CmdOrCtrl+Shift+H', () => {
    if (win?.isVisible()) {
      win.hide()
    } else {
      win?.show()
      win?.focus()
    }
  })
}

// Cleanup on app quit
app.on('before-quit', () => {
  globalShortcut.unregisterAll()
})

app.on('will-quit', () => {
  globalShortcut.unregisterAll()
})

// Auto-updater event handlers
autoUpdater.on('checking-for-update', () => {
  log.info('Checking for update...')
  win?.webContents.send('update-status', { type: 'checking' })
})

autoUpdater.on('update-available', (info) => {
  log.info('Update available:', info)
  win?.webContents.send('update-status', { 
    type: 'available', 
    version: info.version,
    releaseNotes: info.releaseNotes,
    releaseDate: info.releaseDate
  })
  
  // Show notification
  if (Notification.isSupported()) {
    const notification = new Notification({
      title: 'Update Available',
      body: `EstatePulse ${info.version} is available for download`,
      icon: join(process.env.VITE_PUBLIC!, 'electron-vite.svg')
    })
    
    notification.on('click', () => {
      win?.webContents.send('update-action', 'show-update-dialog')
    })
    
    notification.show()
  }
})

autoUpdater.on('update-not-available', (info) => {
  log.info('Update not available:', info)
  win?.webContents.send('update-status', { type: 'not-available' })
})

autoUpdater.on('error', (err) => {
  log.error('Auto-updater error:', err)
  win?.webContents.send('update-status', { 
    type: 'error', 
    error: err.message 
  })
})

autoUpdater.on('download-progress', (progressObj) => {
  const logMessage = `Download speed: ${progressObj.bytesPerSecond} - Downloaded ${progressObj.percent}% (${progressObj.transferred}/${progressObj.total})`
  log.info(logMessage)
  
  win?.webContents.send('update-status', { 
    type: 'download-progress',
    percent: progressObj.percent,
    bytesPerSecond: progressObj.bytesPerSecond,
    transferred: progressObj.transferred,
    total: progressObj.total
  })
})

autoUpdater.on('update-downloaded', (info) => {
  log.info('Update downloaded:', info)
  win?.webContents.send('update-status', { 
    type: 'downloaded',
    version: info.version
  })
  
  // Show notification
  if (Notification.isSupported()) {
    const notification = new Notification({
      title: 'Update Ready',
      body: `EstatePulse ${info.version} has been downloaded and is ready to install`,
      icon: join(process.env.VITE_PUBLIC!, 'electron-vite.svg')
    })
    
    notification.on('click', () => {
      win?.webContents.send('update-action', 'install-update')
    })
    
    notification.show()
  }
})

// Check for updates when app is ready (only in production)
app.whenReady().then(() => {
  if (process.env.NODE_ENV === 'production') {
    // Check for updates 5 seconds after app start
    setTimeout(() => {
      autoUpdater.checkForUpdatesAndNotify()
    }, 5000)
    
    // Check for updates every 4 hours
    setInterval(() => {
      autoUpdater.checkForUpdatesAndNotify()
    }, 4 * 60 * 60 * 1000)
  }
})

// Handle app crashes
process.on('uncaughtException', (error) => {
  log.error('Uncaught Exception:', error)
  // In a real app, you'd send this to a crash reporting service
})

process.on('unhandledRejection', (reason, promise) => {
  log.error('Unhandled Rejection at:', promise, 'reason:', reason)
  // In a real app, you'd send this to a crash reporting service
})