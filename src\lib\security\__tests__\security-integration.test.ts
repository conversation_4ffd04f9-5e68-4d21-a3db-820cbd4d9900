import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  InputValidator,
  InputSanitizer,
  ValidationSchemas,
  RateLimiter,
  SessionManager,
  auditLogger,
  AuditEventType,
  DataEncryption,
  KeyManager,
  dataRetentionManager,
  consentManager,
  ConsentType,
  privacyControlsManager,
  PrivacyControlType
} from '../index';

// Mock crypto for testing
Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: vi.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    }),
    randomUUID: vi.fn(() => 'test-uuid-' + Math.random().toString(36).substr(2, 9)),
    subtle: {
      generateKey: vi.fn(() => Promise.resolve({} as CryptoKey)),
      exportKey: vi.fn(() => Promise.resolve(new ArrayBuffer(32))),
      importKey: vi.fn(() => Promise.resolve({} as CryptoKey)),
      encrypt: vi.fn(() => Promise.resolve(new ArrayBuffer(16))),
      decrypt: vi.fn(() => Promise.resolve(new ArrayBuffer(16))),
      digest: vi.fn(() => Promise.resolve(new ArrayBuffer(32)))
    }
  }
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};
Object.defineProperty(global, 'localStorage', { value: localStorageMock });

describe('Security Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('Input Validation and Sanitization', () => {
    it('should sanitize HTML input', () => {
      const maliciousInput = '<script>alert("xss")</script><p>Safe content</p>';
      const sanitized = InputSanitizer.sanitizeHtml(maliciousInput);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('<p>Safe content</p>');
    });

    it('should validate email addresses', () => {
      expect(() => {
        InputValidator.validateInput(ValidationSchemas.email, '<EMAIL>');
      }).not.toThrow();

      expect(() => {
        InputValidator.validateInput(ValidationSchemas.email, 'invalid-email');
      }).toThrow();
    });

    it('should validate password strength', () => {
      expect(() => {
        InputValidator.validateInput(ValidationSchemas.password, 'StrongP@ss123');
      }).not.toThrow();

      expect(() => {
        InputValidator.validateInput(ValidationSchemas.password, 'weak');
      }).toThrow();
    });

    it('should sanitize file names', () => {
      const maliciousFileName = '../../../etc/passwd';
      const sanitized = InputSanitizer.sanitizeFileName(maliciousFileName);
      
      expect(sanitized).not.toContain('../');
      expect(sanitized).not.toContain('/');
    });
  });

  describe('Rate Limiting', () => {
    it('should allow requests within limit', () => {
      const rateLimiter = new RateLimiter({
        windowMs: 60000,
        maxRequests: 5
      });

      const identifier = 'test-user';
      
      // First 5 requests should be allowed
      for (let i = 0; i < 5; i++) {
        expect(rateLimiter.isAllowed(identifier)).toBe(true);
      }
      
      // 6th request should be blocked
      expect(rateLimiter.isAllowed(identifier)).toBe(false);
    });

    it('should reset rate limit after window', () => {
      vi.useFakeTimers();
      
      const rateLimiter = new RateLimiter({
        windowMs: 1000,
        maxRequests: 1
      });

      const identifier = 'test-user';
      
      expect(rateLimiter.isAllowed(identifier)).toBe(true);
      expect(rateLimiter.isAllowed(identifier)).toBe(false);
      
      // Advance time past window
      vi.advanceTimersByTime(1001);
      
      expect(rateLimiter.isAllowed(identifier)).toBe(true);
      
      vi.useRealTimers();
    });
  });

  describe('Session Management', () => {
    it('should create and validate sessions', async () => {
      const sessionManager = SessionManager.getInstance();
      
      const { accessToken, sessionId } = await sessionManager.createSession(
        'user123',
        '<EMAIL>',
        'tenant',
        ['read:properties'],
        {
          userAgent: 'test-agent',
          platform: 'test',
          browser: 'test',
          fingerprint: 'test-fingerprint'
        },
        '127.0.0.1'
      );

      expect(accessToken).toBeDefined();
      expect(sessionId).toBeDefined();

      const session = await sessionManager.validateSession(accessToken);
      expect(session).toBeDefined();
      expect(session?.userId).toBe('user123');
    });

    it('should handle session expiration', async () => {
      vi.useFakeTimers();
      
      const sessionManager = SessionManager.getInstance({
        maxAge: 1000, // 1 second
        renewalThreshold: 500,
        maxConcurrentSessions: 3,
        requireReauth: []
      });

      const { accessToken } = await sessionManager.createSession(
        'user123',
        '<EMAIL>',
        'tenant',
        ['read:properties'],
        {
          userAgent: 'test-agent',
          platform: 'test',
          browser: 'test',
          fingerprint: 'test-fingerprint'
        },
        '127.0.0.1'
      );

      // Session should be valid initially
      let session = await sessionManager.validateSession(accessToken);
      expect(session).toBeDefined();

      // Advance time past expiration
      vi.advanceTimersByTime(2000);

      // Session should be expired
      session = await sessionManager.validateSession(accessToken);
      expect(session).toBeNull();
      
      vi.useRealTimers();
    });
  });

  describe('Audit Logging', () => {
    it('should log audit events', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      await auditLogger.logEvent({
        type: AuditEventType.USER_LOGIN,
        severity: 'low' as any,
        userId: 'user123',
        action: 'User logged in',
        details: { method: 'password' },
        success: true
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        'Audit Event:',
        expect.objectContaining({
          type: AuditEventType.USER_LOGIN,
          userId: 'user123',
          action: 'User logged in'
        })
      );
      
      consoleSpy.mockRestore();
    });

    it('should filter audit events', async () => {
      await auditLogger.logEvent({
        type: AuditEventType.USER_LOGIN,
        severity: 'low' as any,
        userId: 'user123',
        action: 'User logged in',
        details: {},
        success: true
      });

      await auditLogger.logEvent({
        type: AuditEventType.USER_LOGOUT,
        severity: 'low' as any,
        userId: 'user123',
        action: 'User logged out',
        details: {},
        success: true
      });

      const loginEvents = auditLogger.getEvents({ type: AuditEventType.USER_LOGIN });
      expect(loginEvents).toHaveLength(1);
      expect(loginEvents[0].type).toBe(AuditEventType.USER_LOGIN);
    });
  });

  describe('Data Encryption', () => {
    it('should encrypt and decrypt data', async () => {
      const key = await DataEncryption.generateKey();
      const originalData = 'sensitive information';
      
      const encrypted = await DataEncryption.encrypt(originalData, key);
      expect(encrypted.data).toBeDefined();
      expect(encrypted.iv).toBeDefined();
      
      const decrypted = await DataEncryption.decrypt(encrypted, key);
      expect(decrypted).toBe(originalData);
    });

    it('should manage encryption keys', async () => {
      const keyId = 'test-key';
      const key = await KeyManager.generateAndStoreKey(keyId);
      
      expect(key).toBeDefined();
      
      const retrievedKey = await KeyManager.getKey(keyId);
      expect(retrievedKey).toBeDefined();
    });
  });

  describe('Data Retention', () => {
    it('should create retention policies', async () => {
      const policyId = await dataRetentionManager.createPolicy({
        name: 'Test Policy',
        description: 'Test retention policy',
        dataType: 'test_data',
        retentionPeriod: 30,
        deletionMethod: 'soft_delete' as any,
        isActive: true
      });

      expect(policyId).toBeDefined();
    });

    it('should register data for retention tracking', async () => {
      // First create a policy
      await dataRetentionManager.createPolicy({
        name: 'Test Policy',
        description: 'Test retention policy',
        dataType: 'test_data',
        retentionPeriod: 30,
        deletionMethod: 'soft_delete' as any,
        isActive: true
      });

      // Then register data
      await dataRetentionManager.registerData(
        'data123',
        'test_data',
        Date.now()
      );

      // Should not throw
      expect(true).toBe(true);
    });
  });

  describe('Consent Management', () => {
    it('should record user consent', async () => {
      const consentId = await consentManager.recordConsent(
        'user123',
        ConsentType.DATA_PROCESSING,
        true,
        '127.0.0.1',
        'test-agent'
      );

      expect(consentId).toBeDefined();
      
      const hasConsent = consentManager.hasConsent('user123', ConsentType.DATA_PROCESSING);
      expect(hasConsent).toBe(true);
    });

    it('should withdraw consent', async () => {
      // First grant consent
      await consentManager.recordConsent(
        'user123',
        ConsentType.MARKETING,
        true,
        '127.0.0.1',
        'test-agent'
      );

      expect(consentManager.hasConsent('user123', ConsentType.MARKETING)).toBe(true);

      // Then withdraw it
      await consentManager.withdrawConsent('user123', ConsentType.MARKETING);
      
      expect(consentManager.hasConsent('user123', ConsentType.MARKETING)).toBe(false);
    });

    it('should generate consent summary', async () => {
      await consentManager.recordConsent(
        'user123',
        ConsentType.DATA_PROCESSING,
        true,
        '127.0.0.1',
        'test-agent'
      );

      const summary = consentManager.getConsentSummary('user123');
      
      expect(summary.userId).toBe('user123');
      expect(summary.grantedConsents).toBeGreaterThan(0);
    });
  });

  describe('Privacy Controls', () => {
    it('should submit privacy requests', async () => {
      const requestId = await privacyControlsManager.submitRequest(
        'user123',
        PrivacyControlType.DATA_EXPORT,
        'I want to export my data'
      );

      expect(requestId).toBeDefined();
      
      const request = privacyControlsManager.getRequest(requestId);
      expect(request).toBeDefined();
      expect(request?.userId).toBe('user123');
      expect(request?.type).toBe(PrivacyControlType.DATA_EXPORT);
    });

    it('should export user data', async () => {
      const exportData = await privacyControlsManager.exportUserData(
        'user123',
        'json' as any
      );

      expect(exportData.userId).toBe('user123');
      expect(exportData.format).toBe('json');
      expect(exportData.data).toBeDefined();
    });

    it('should generate privacy dashboard', async () => {
      const dashboard = await privacyControlsManager.getPrivacyDashboard('user123');
      
      expect(dashboard.userId).toBe('user123');
      expect(dashboard.dataCategories).toBeDefined();
      expect(dashboard.privacyScore).toBeGreaterThanOrEqual(0);
      expect(dashboard.privacyScore).toBeLessThanOrEqual(100);
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle complete user data lifecycle', async () => {
      const userId = 'integration-user';
      
      // 1. Record consent
      await consentManager.recordConsent(
        userId,
        ConsentType.DATA_PROCESSING,
        true,
        '127.0.0.1',
        'test-agent'
      );

      // 2. Register data for retention
      await dataRetentionManager.registerData(
        `${userId}-profile`,
        'profile',
        Date.now()
      );

      // 3. Submit data export request
      const exportRequestId = await privacyControlsManager.submitRequest(
        userId,
        PrivacyControlType.DATA_EXPORT,
        'Export my data'
      );

      // 4. Process the request
      await privacyControlsManager.processRequest(exportRequestId, 'system');

      // 5. Verify request was completed
      const request = privacyControlsManager.getRequest(exportRequestId);
      expect(request?.status).toBe('completed');

      // 6. Generate privacy dashboard
      const dashboard = await privacyControlsManager.getPrivacyDashboard(userId);
      expect(dashboard.recentRequests).toHaveLength(1);
    });

    it('should enforce security policies across modules', async () => {
      const userId = 'security-test-user';
      
      // Test input validation
      expect(() => {
        InputValidator.validateInput(ValidationSchemas.email, 'invalid-email');
      }).toThrow();

      // Test rate limiting
      const rateLimiter = new RateLimiter({
        windowMs: 60000,
        maxRequests: 1
      });
      
      expect(rateLimiter.isAllowed(userId)).toBe(true);
      expect(rateLimiter.isAllowed(userId)).toBe(false);

      // Test audit logging
      await auditLogger.logEvent({
        type: AuditEventType.SECURITY_BREACH_DETECTED,
        severity: 'critical' as any,
        userId,
        action: 'Security policy violation detected',
        details: { violation: 'rate_limit_exceeded' },
        success: false
      });

      const securityEvents = auditLogger.getSecurityEvents('critical' as any);
      expect(securityEvents.length).toBeGreaterThan(0);
    });
  });
});