import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Badge } from '../ui/badge';
import { PortalBrandingEngine } from './PortalBrandingEngine';
import { DomainManager } from './DomainManager';
import { CSSCustomizer } from './CSSCustomizer';
import { 
  Palette, 
  Globe, 
  Code, 
  Settings, 
  // Eye, 
  ExternalLink,
  Plus,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface PortalManagementProps {
  propertyId: Id<"properties">;
}

export const PortalManagement: React.FC<PortalManagementProps> = ({ propertyId }) => {
  const [activeTab, setActiveTab] = useState('branding');

  const property = useQuery(api.properties.getById, { id: propertyId });
  const portal = useQuery(api.portals.getPortalByProperty, { propertyId });

  const getPortalStatus = () => {
    if (!portal) return { status: 'not_created', label: 'Not Created', variant: 'secondary' as const };
    if (!portal.isPublished) return { status: 'draft', label: 'Draft', variant: 'secondary' as const };
    if (portal.isActive) return { status: 'active', label: 'Active', variant: 'default' as const };
    return { status: 'inactive', label: 'Inactive', variant: 'destructive' as const };
  };

  const portalStatus = getPortalStatus();

  const getPortalUrl = () => {
    if (!portal) return null;
    return portal.customDomain 
      ? `https://${portal.customDomain}`
      : `https://${portal.subdomain}.estatepulse.com`;
  };

  const portalUrl = getPortalUrl();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Portal Management</h1>
          <p className="text-muted-foreground">
            Configure and customize your white-label tenant portal for {property?.name}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant={portalStatus.variant} className="flex items-center">
            {portalStatus.status === 'active' ? (
              <CheckCircle className="w-3 h-3 mr-1" />
            ) : (
              <AlertCircle className="w-3 h-3 mr-1" />
            )}
            {portalStatus.label}
          </Badge>
          {portalUrl && (
            <Button variant="outline" asChild>
              <a href={portalUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="w-4 h-4 mr-2" />
                View Portal
              </a>
            </Button>
          )}
        </div>
      </div>

      {/* Portal Overview */}
      {portal ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Globe className="w-5 h-5 mr-2" />
              Portal Overview
            </CardTitle>
            <CardDescription>
              Current configuration and status of your tenant portal
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Portal Name</h4>
                <p className="text-sm text-muted-foreground">{portal.name}</p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Subdomain</h4>
                <p className="text-sm text-muted-foreground font-mono">
                  {portal.subdomain}.estatepulse.com
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Custom Domain</h4>
                <p className="text-sm text-muted-foreground font-mono">
                  {portal.customDomain || 'Not configured'}
                </p>
              </div>
            </div>
            
            <div className="mt-4 pt-4 border-t">
              <h4 className="font-medium mb-2">Enabled Features</h4>
              <div className="flex flex-wrap gap-2">
                {Object.entries(portal.features).map(([feature, enabled]) => (
                  <Badge 
                    key={feature} 
                    variant={enabled ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {feature.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Plus className="w-5 h-5 mr-2" />
              Create Portal
            </CardTitle>
            <CardDescription>
              No portal has been created for this property yet. Use the branding tab to create and configure your portal.
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      {/* Configuration Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="branding" className="flex items-center">
            <Palette className="w-4 h-4 mr-2" />
            Branding
          </TabsTrigger>
          <TabsTrigger value="domain" className="flex items-center" disabled={!portal}>
            <Globe className="w-4 h-4 mr-2" />
            Domain
          </TabsTrigger>
          <TabsTrigger value="css" className="flex items-center" disabled={!portal}>
            <Code className="w-4 h-4 mr-2" />
            Custom CSS
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center" disabled={!portal}>
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="branding">
          <PortalBrandingEngine propertyId={propertyId} />
        </TabsContent>

        <TabsContent value="domain">
          {portal ? (
            <DomainManager portalId={portal._id} />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <p className="text-center text-muted-foreground">
                  Create a portal first in the Branding tab to configure domain settings.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="css">
          {portal ? (
            <CSSCustomizer portalId={portal._id} />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <p className="text-center text-muted-foreground">
                  Create a portal first in the Branding tab to add custom CSS.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="settings">
          {portal ? (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Portal Settings</CardTitle>
                  <CardDescription>
                    Configure portal features and advanced settings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">Portal Status</h4>
                        <p className="text-sm text-muted-foreground">
                          Control whether the portal is accessible to tenants
                        </p>
                      </div>
                      <Badge variant={portal.isPublished ? "default" : "secondary"}>
                        {portal.isPublished ? 'Published' : 'Draft'}
                      </Badge>
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="font-medium">SEO Settings</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Title:</span> {portal.seoSettings.title}
                        </div>
                        <div>
                          <span className="font-medium">Description:</span> {portal.seoSettings.description}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Feature Configuration</CardTitle>
                  <CardDescription>
                    Enable or disable specific portal features
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(portal.features).map(([feature, enabled]) => (
                      <div key={feature} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <h5 className="font-medium">
                            {feature.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </h5>
                          <p className="text-xs text-muted-foreground">
                            {getFeatureDescription(feature)}
                          </p>
                        </div>
                        <Badge variant={enabled ? "default" : "secondary"}>
                          {enabled ? 'Enabled' : 'Disabled'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <p className="text-center text-muted-foreground">
                  Create a portal first in the Branding tab to configure settings.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

function getFeatureDescription(feature: string): string {
  const descriptions: Record<string, string> = {
    paymentPortal: 'Allow tenants to make rent payments online',
    maintenanceRequests: 'Enable maintenance request submission',
    documentAccess: 'Provide access to lease documents and files',
    leaseInformation: 'Display lease details and terms',
    communicationCenter: 'Enable messaging between tenants and management',
    announcementsBoard: 'Show property announcements and updates',
  };
  
  return descriptions[feature] || 'Feature configuration';
}