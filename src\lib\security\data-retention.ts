import { auditLogger, AuditEventType, AuditSeverity } from './audit-logging';

// Data retention policy configuration
export interface RetentionPolicy {
  id: string;
  name: string;
  description: string;
  dataType: string;
  retentionPeriod: number; // in days
  deletionMethod: DeletionMethod;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
  conditions?: RetentionCondition[];
}

export enum DeletionMethod {
  SOFT_DELETE = 'soft_delete', // Mark as deleted but keep data
  HARD_DELETE = 'hard_delete', // Permanently remove data
  ARCHIVE = 'archive', // Move to archive storage
  ANONYMIZE = 'anonymize' // Remove PII but keep anonymized data
}

export interface RetentionCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains';
  value: any;
}

export interface DataRetentionRecord {
  id: string;
  dataId: string;
  dataType: string;
  policyId: string;
  createdAt: number;
  scheduledDeletionAt: number;
  status: RetentionStatus;
  lastChecked?: number;
  deletedAt?: number;
  deletedBy?: string;
  metadata?: Record<string, any>;
}

export enum RetentionStatus {
  ACTIVE = 'active',
  SCHEDULED_FOR_DELETION = 'scheduled_for_deletion',
  DELETED = 'deleted',
  ARCHIVED = 'archived',
  ANONYMIZED = 'anonymized',
  EXEMPT = 'exempt'
}

// Data retention manager
export class DataRetentionManager {
  private static instance: DataRetentionManager;
  private policies = new Map<string, RetentionPolicy>();
  private records = new Map<string, DataRetentionRecord>();
  private readonly STORAGE_KEY = 'data_retention_policies';
  private readonly RECORDS_KEY = 'data_retention_records';

  constructor() {
    this.loadPolicies();
    this.loadRecords();
    this.startRetentionScheduler();
  }

  static getInstance(): DataRetentionManager {
    if (!this.instance) {
      this.instance = new DataRetentionManager();
    }
    return this.instance;
  }

  /**
   * Create a new retention policy
   */
  async createPolicy(policy: Omit<RetentionPolicy, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const policyId = crypto.randomUUID();
    const now = Date.now();

    const newPolicy: RetentionPolicy = {
      ...policy,
      id: policyId,
      createdAt: now,
      updatedAt: now
    };

    this.policies.set(policyId, newPolicy);
    await this.savePolicies();

    // Log policy creation
    await auditLogger.logEvent({
      type: AuditEventType.CONFIGURATION_CHANGED,
      severity: AuditSeverity.MEDIUM,
      action: 'Data retention policy created',
      details: { policyId, policyName: policy.name, dataType: policy.dataType },
      success: true
    });

    return policyId;
  }

  /**
   * Update retention policy
   */
  async updatePolicy(policyId: string, updates: Partial<RetentionPolicy>): Promise<void> {
    const policy = this.policies.get(policyId);
    if (!policy) {
      throw new Error(`Policy ${policyId} not found`);
    }

    const updatedPolicy = {
      ...policy,
      ...updates,
      updatedAt: Date.now()
    };

    this.policies.set(policyId, updatedPolicy);
    await this.savePolicies();

    // Log policy update
    await auditLogger.logEvent({
      type: AuditEventType.CONFIGURATION_CHANGED,
      severity: AuditSeverity.MEDIUM,
      action: 'Data retention policy updated',
      details: { policyId, updates },
      success: true
    });
  }

  /**
   * Delete retention policy
   */
  async deletePolicy(policyId: string): Promise<void> {
    const policy = this.policies.get(policyId);
    if (!policy) {
      throw new Error(`Policy ${policyId} not found`);
    }

    this.policies.delete(policyId);
    await this.savePolicies();

    // Log policy deletion
    await auditLogger.logEvent({
      type: AuditEventType.CONFIGURATION_CHANGED,
      severity: AuditSeverity.HIGH,
      action: 'Data retention policy deleted',
      details: { policyId, policyName: policy.name },
      success: true
    });
  }

  /**
   * Register data for retention tracking
   */
  async registerData(
    dataId: string,
    dataType: string,
    createdAt: number = Date.now(),
    metadata?: Record<string, any>
  ): Promise<void> {
    // Find applicable policy
    const policy = this.findApplicablePolicy(dataType, metadata);
    if (!policy) {
      console.warn(`No retention policy found for data type: ${dataType}`);
      return;
    }

    const scheduledDeletionAt = createdAt + (policy.retentionPeriod * 24 * 60 * 60 * 1000);

    const record: DataRetentionRecord = {
      id: crypto.randomUUID(),
      dataId,
      dataType,
      policyId: policy.id,
      createdAt,
      scheduledDeletionAt,
      status: RetentionStatus.ACTIVE,
      metadata
    };

    this.records.set(dataId, record);
    await this.saveRecords();

    // Log data registration
    await auditLogger.logEvent({
      type: AuditEventType.CONFIGURATION_CHANGED,
      severity: AuditSeverity.LOW,
      action: 'Data registered for retention tracking',
      details: { dataId, dataType, policyId: policy.id, scheduledDeletionAt },
      success: true
    });
  }

  /**
   * Update data retention record
   */
  async updateRecord(dataId: string, updates: Partial<DataRetentionRecord>): Promise<void> {
    const record = this.records.get(dataId);
    if (!record) {
      throw new Error(`Retention record for ${dataId} not found`);
    }

    const updatedRecord = { ...record, ...updates };
    this.records.set(dataId, updatedRecord);
    await this.saveRecords();
  }

  /**
   * Mark data as exempt from deletion
   */
  async exemptFromDeletion(dataId: string, reason: string): Promise<void> {
    await this.updateRecord(dataId, {
      status: RetentionStatus.EXEMPT,
      metadata: { ...this.records.get(dataId)?.metadata, exemptionReason: reason }
    });

    // Log exemption
    await auditLogger.logEvent({
      type: AuditEventType.CONFIGURATION_CHANGED,
      severity: AuditSeverity.MEDIUM,
      action: 'Data exempted from deletion',
      details: { dataId, reason },
      success: true
    });
  }

  /**
   * Get data due for deletion
   */
  getDataDueForDeletion(): DataRetentionRecord[] {
    const now = Date.now();
    return Array.from(this.records.values()).filter(record => 
      record.status === RetentionStatus.ACTIVE &&
      record.scheduledDeletionAt <= now
    );
  }

  /**
   * Get data scheduled for deletion within timeframe
   */
  getDataScheduledForDeletion(days: number = 30): DataRetentionRecord[] {
    const cutoff = Date.now() + (days * 24 * 60 * 60 * 1000);
    return Array.from(this.records.values()).filter(record => 
      record.status === RetentionStatus.ACTIVE &&
      record.scheduledDeletionAt <= cutoff
    );
  }

  /**
   * Process data deletion
   */
  async processDataDeletion(dataId: string, deletedBy?: string): Promise<void> {
    const record = this.records.get(dataId);
    if (!record) {
      throw new Error(`Retention record for ${dataId} not found`);
    }

    const policy = this.policies.get(record.policyId);
    if (!policy) {
      throw new Error(`Policy ${record.policyId} not found`);
    }

    try {
      // Execute deletion based on policy method
      await this.executeDeletion(record, policy);

      // Update record status
      await this.updateRecord(dataId, {
        status: this.getStatusForDeletionMethod(policy.deletionMethod),
        deletedAt: Date.now(),
        deletedBy
      });

      // Log successful deletion
      await auditLogger.logEvent({
        type: AuditEventType.USER_DELETED, // Generic deletion event
        severity: AuditSeverity.HIGH,
        action: `Data ${policy.deletionMethod} completed`,
        details: {
          dataId,
          dataType: record.dataType,
          deletionMethod: policy.deletionMethod,
          policyId: policy.id
        },
        success: true
      });

    } catch (error) {
      // Log failed deletion
      await auditLogger.logEvent({
        type: AuditEventType.USER_DELETED,
        severity: AuditSeverity.CRITICAL,
        action: `Data ${policy.deletionMethod} failed`,
        details: {
          dataId,
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });

      throw error;
    }
  }

  /**
   * Generate retention report
   */
  generateRetentionReport(): RetentionReport {
    const records = Array.from(this.records.values());
    const policies = Array.from(this.policies.values());

    const report: RetentionReport = {
      generatedAt: Date.now(),
      totalRecords: records.length,
      activePolicies: policies.filter(p => p.isActive).length,
      recordsByStatus: this.groupRecordsByStatus(records),
      recordsByDataType: this.groupRecordsByDataType(records),
      upcomingDeletions: this.getDataScheduledForDeletion(30).length,
      overdueDeletions: this.getDataDueForDeletion().length,
      policies: policies.map(p => ({
        id: p.id,
        name: p.name,
        dataType: p.dataType,
        retentionPeriod: p.retentionPeriod,
        deletionMethod: p.deletionMethod,
        isActive: p.isActive,
        recordCount: records.filter(r => r.policyId === p.id).length
      }))
    };

    return report;
  }

  /**
   * Export retention data
   */
  exportRetentionData(format: 'json' | 'csv' = 'json'): string {
    const records = Array.from(this.records.values());
    
    if (format === 'csv') {
      const headers = [
        'Data ID', 'Data Type', 'Policy ID', 'Created At', 'Scheduled Deletion',
        'Status', 'Last Checked', 'Deleted At', 'Deleted By'
      ];

      const rows = records.map(record => [
        record.dataId,
        record.dataType,
        record.policyId,
        new Date(record.createdAt).toISOString(),
        new Date(record.scheduledDeletionAt).toISOString(),
        record.status,
        record.lastChecked ? new Date(record.lastChecked).toISOString() : '',
        record.deletedAt ? new Date(record.deletedAt).toISOString() : '',
        record.deletedBy || ''
      ]);

      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }

    return JSON.stringify({
      policies: Array.from(this.policies.values()),
      records: records
    }, null, 2);
  }

  /**
   * Find applicable policy for data type
   */
  private findApplicablePolicy(dataType: string, metadata?: Record<string, any>): RetentionPolicy | null {
    const policies = Array.from(this.policies.values())
      .filter(p => p.isActive && p.dataType === dataType);

    // If no conditions, return first matching policy
    const simplePolicy = policies.find(p => !p.conditions || p.conditions.length === 0);
    if (simplePolicy && policies.length === 1) {
      return simplePolicy;
    }

    // Check policies with conditions
    for (const policy of policies) {
      if (policy.conditions && this.evaluateConditions(policy.conditions, metadata)) {
        return policy;
      }
    }

    return simplePolicy || null;
  }

  /**
   * Evaluate retention conditions
   */
  private evaluateConditions(conditions: RetentionCondition[], metadata?: Record<string, any>): boolean {
    if (!metadata) return false;

    return conditions.every(condition => {
      const value = metadata[condition.field];
      
      switch (condition.operator) {
        case 'equals':
          return value === condition.value;
        case 'not_equals':
          return value !== condition.value;
        case 'greater_than':
          return value > condition.value;
        case 'less_than':
          return value < condition.value;
        case 'contains':
          return String(value).includes(String(condition.value));
        default:
          return false;
      }
    });
  }

  /**
   * Execute deletion based on method
   */
  private async executeDeletion(record: DataRetentionRecord, policy: RetentionPolicy): Promise<void> {
    switch (policy.deletionMethod) {
      case DeletionMethod.SOFT_DELETE:
        // Mark as deleted but keep data
        console.log(`Soft deleting data: ${record.dataId}`);
        break;
        
      case DeletionMethod.HARD_DELETE:
        // Permanently remove data
        console.log(`Hard deleting data: ${record.dataId}`);
        // In real implementation, this would call the actual deletion API
        break;
        
      case DeletionMethod.ARCHIVE:
        // Move to archive storage
        console.log(`Archiving data: ${record.dataId}`);
        // In real implementation, this would move data to archive storage
        break;
        
      case DeletionMethod.ANONYMIZE:
        // Remove PII but keep anonymized data
        console.log(`Anonymizing data: ${record.dataId}`);
        // In real implementation, this would anonymize the data
        break;
        
      default:
        throw new Error(`Unknown deletion method: ${policy.deletionMethod}`);
    }
  }

  /**
   * Get status for deletion method
   */
  private getStatusForDeletionMethod(method: DeletionMethod): RetentionStatus {
    switch (method) {
      case DeletionMethod.SOFT_DELETE:
      case DeletionMethod.HARD_DELETE:
        return RetentionStatus.DELETED;
      case DeletionMethod.ARCHIVE:
        return RetentionStatus.ARCHIVED;
      case DeletionMethod.ANONYMIZE:
        return RetentionStatus.ANONYMIZED;
      default:
        return RetentionStatus.DELETED;
    }
  }

  /**
   * Group records by status
   */
  private groupRecordsByStatus(records: DataRetentionRecord[]): Record<string, number> {
    return records.reduce((acc, record) => {
      acc[record.status] = (acc[record.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Group records by data type
   */
  private groupRecordsByDataType(records: DataRetentionRecord[]): Record<string, number> {
    return records.reduce((acc, record) => {
      acc[record.dataType] = (acc[record.dataType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Start retention scheduler
   */
  private startRetentionScheduler(): void {
    // Check for data due for deletion every hour
    setInterval(async () => {
      const dueDeletions = this.getDataDueForDeletion();
      
      for (const record of dueDeletions) {
        try {
          await this.processDataDeletion(record.dataId, 'system');
        } catch (error) {
          console.error(`Failed to process scheduled deletion for ${record.dataId}:`, error);
        }
      }
      
      if (dueDeletions.length > 0) {
        console.log(`Processed ${dueDeletions.length} scheduled deletions`);
      }
    }, 60 * 60 * 1000); // Every hour
  }

  /**
   * Load policies from storage
   */
  private loadPolicies(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const policies = JSON.parse(stored);
        policies.forEach((policy: RetentionPolicy) => {
          this.policies.set(policy.id, policy);
        });
      }
    } catch (error) {
      console.error('Failed to load retention policies:', error);
    }
  }

  /**
   * Save policies to storage
   */
  private async savePolicies(): Promise<void> {
    try {
      const policies = Array.from(this.policies.values());
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(policies));
    } catch (error) {
      console.error('Failed to save retention policies:', error);
    }
  }

  /**
   * Load records from storage
   */
  private loadRecords(): void {
    try {
      const stored = localStorage.getItem(this.RECORDS_KEY);
      if (stored) {
        const records = JSON.parse(stored);
        records.forEach((record: DataRetentionRecord) => {
          this.records.set(record.dataId, record);
        });
      }
    } catch (error) {
      console.error('Failed to load retention records:', error);
    }
  }

  /**
   * Save records to storage
   */
  private async saveRecords(): Promise<void> {
    try {
      const records = Array.from(this.records.values());
      localStorage.setItem(this.RECORDS_KEY, JSON.stringify(records));
    } catch (error) {
      console.error('Failed to save retention records:', error);
    }
  }
}

// Retention report interface
export interface RetentionReport {
  generatedAt: number;
  totalRecords: number;
  activePolicies: number;
  recordsByStatus: Record<string, number>;
  recordsByDataType: Record<string, number>;
  upcomingDeletions: number;
  overdueDeletions: number;
  policies: Array<{
    id: string;
    name: string;
    dataType: string;
    retentionPeriod: number;
    deletionMethod: DeletionMethod;
    isActive: boolean;
    recordCount: number;
  }>;
}

// Default retention policies
export const DEFAULT_RETENTION_POLICIES: Omit<RetentionPolicy, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: 'User Account Data',
    description: 'Retention policy for user account information',
    dataType: 'user',
    retentionPeriod: 2555, // 7 years
    deletionMethod: DeletionMethod.ANONYMIZE,
    isActive: true
  },
  {
    name: 'Financial Records',
    description: 'Retention policy for financial transactions and records',
    dataType: 'financial',
    retentionPeriod: 2555, // 7 years
    deletionMethod: DeletionMethod.ARCHIVE,
    isActive: true
  },
  {
    name: 'Audit Logs',
    description: 'Retention policy for audit and security logs',
    dataType: 'audit',
    retentionPeriod: 1095, // 3 years
    deletionMethod: DeletionMethod.ARCHIVE,
    isActive: true
  },
  {
    name: 'Communication Records',
    description: 'Retention policy for SMS, email, and other communications',
    dataType: 'communication',
    retentionPeriod: 365, // 1 year
    deletionMethod: DeletionMethod.SOFT_DELETE,
    isActive: true
  },
  {
    name: 'Temporary Files',
    description: 'Retention policy for temporary files and cache',
    dataType: 'temporary',
    retentionPeriod: 30, // 30 days
    deletionMethod: DeletionMethod.HARD_DELETE,
    isActive: true
  }
];

// Convenience function to get retention manager instance
export const dataRetentionManager = DataRetentionManager.getInstance();