import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { validatePropertyData, sanitizePropertyData } from "./lib/validation";

// Get all properties with optional filters
export const getProperties = query({
  args: {
    type: v.optional(v.union(v.literal("residential"), v.literal("commercial"), v.literal("mixed"))),
    ownerId: v.optional(v.id("users")),
    managerId: v.optional(v.id("users")),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let properties;

    if (args.type) {
      properties = await ctx.db
        .query("properties")
        .withIndex("by_type", (q) => q.eq("type", args.type!))
        .collect();
    } else if (args.ownerId) {
      properties = await ctx.db
        .query("properties")
        .withIndex("by_owner", (q) => q.eq("ownerId", args.ownerId!))
        .collect();
    } else if (args.managerId) {
      properties = await ctx.db
        .query("properties")
        .withIndex("by_manager", (q) => q.eq("managerId", args.managerId!))
        .collect();
    } else {
      properties = await ctx.db.query("properties").collect();
    }

    if (args.isActive !== undefined) {
      return properties.filter(p => p.isActive === args.isActive);
    }

    return properties;
  },
});

// Get property by ID
export const getPropertyById = query({
  args: { id: v.id("properties") },
  handler: async (ctx, args) => {
    const property = await ctx.db.get(args.id);
    if (!property) {
      throw new Error("Property not found");
    }
    return property;
  },
});

// Alias for getPropertyById for consistency
export const getById = getPropertyById;

// Create a new property
export const createProperty = mutation({
  args: {
    name: v.string(),
    type: v.union(v.literal("residential"), v.literal("commercial"), v.literal("mixed")),
    address: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      country: v.string(),
      postalCode: v.string(),
      coordinates: v.optional(v.object({
        lat: v.number(),
        lng: v.number(),
      })),
    }),
    ownerId: v.id("users"),
    managerId: v.optional(v.id("users")),
    branding: v.optional(v.object({
      logo: v.optional(v.string()),
      primaryColor: v.string(),
      secondaryColor: v.string(),
      customDomain: v.optional(v.string()),
    })),
    settings: v.optional(v.object({
      currency: v.string(),
      timezone: v.string(),
      language: v.string(),
      autoRentReminders: v.boolean(),
      maintenanceSLA: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    // Validate and sanitize input
    const sanitizedData = sanitizePropertyData(args);
    validatePropertyData(sanitizedData);

    // Check if owner exists
    const owner = await ctx.db.get(args.ownerId);
    if (!owner) {
      throw new Error("Owner not found");
    }

    // Check if manager exists (if provided)
    if (args.managerId) {
      const manager = await ctx.db.get(args.managerId);
      if (!manager) {
        throw new Error("Manager not found");
      }
    }

    const now = Date.now();
    
    const propertyId = await ctx.db.insert("properties", {
      name: sanitizedData.name,
      type: sanitizedData.type,
      address: sanitizedData.address,
      ownerId: sanitizedData.ownerId,
      managerId: sanitizedData.managerId,
      branding: sanitizedData.branding || {
        primaryColor: "#3b82f6",
        secondaryColor: "#1e40af",
      },
      settings: sanitizedData.settings || {
        currency: "KES",
        timezone: "Africa/Nairobi",
        language: "en",
        autoRentReminders: true,
        maintenanceSLA: 24,
      },
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    return propertyId;
  },
});

// Update property
export const updateProperty = mutation({
  args: {
    id: v.id("properties"),
    name: v.optional(v.string()),
    type: v.optional(v.union(v.literal("residential"), v.literal("commercial"), v.literal("mixed"))),
    address: v.optional(v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      country: v.string(),
      postalCode: v.string(),
      coordinates: v.optional(v.object({
        lat: v.number(),
        lng: v.number(),
      })),
    })),
    managerId: v.optional(v.id("users")),
    branding: v.optional(v.object({
      logo: v.optional(v.string()),
      primaryColor: v.string(),
      secondaryColor: v.string(),
      customDomain: v.optional(v.string()),
    })),
    settings: v.optional(v.object({
      currency: v.string(),
      timezone: v.string(),
      language: v.string(),
      autoRentReminders: v.boolean(),
      maintenanceSLA: v.number(),
    })),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    
    // Check if property exists
    const existingProperty = await ctx.db.get(id);
    if (!existingProperty) {
      throw new Error("Property not found");
    }

    // Validate updates if provided
    if (updates.name || updates.type || updates.address) {
      const dataToValidate = {
        name: updates.name || existingProperty.name,
        type: updates.type || existingProperty.type,
        address: updates.address || existingProperty.address,
      };
      validatePropertyData(dataToValidate);
    }

    // Check if manager exists (if being updated)
    if (updates.managerId) {
      const manager = await ctx.db.get(updates.managerId);
      if (!manager) {
        throw new Error("Manager not found");
      }
    }

    await ctx.db.patch(id, {
      ...updates,
      updatedAt: Date.now(),
    });

    return id;
  },
});

// Delete property (soft delete by setting isActive to false)
export const deleteProperty = mutation({
  args: { id: v.id("properties") },
  handler: async (ctx, args) => {
    const property = await ctx.db.get(args.id);
    if (!property) {
      throw new Error("Property not found");
    }

    // Check if property has active leases
    const activeLeases = await ctx.db
      .query("leases")
      .withIndex("by_property", (q) => q.eq("propertyId", args.id))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    if (activeLeases.length > 0) {
      throw new Error("Cannot delete property with active leases");
    }

    await ctx.db.patch(args.id, {
      isActive: false,
      updatedAt: Date.now(),
    });

    return args.id;
  },
});

// Get properties by owner
export const getPropertiesByOwner = query({
  args: { ownerId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("properties")
      .withIndex("by_owner", (q) => q.eq("ownerId", args.ownerId))
      .collect();
  },
});

// Get properties by manager
export const getPropertiesByManager = query({
  args: { managerId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("properties")
      .withIndex("by_manager", (q) => q.eq("managerId", args.managerId))
      .collect();
  },
});

// Get property analytics
export const getPropertyAnalytics = query({
  args: { propertyId: v.id("properties") },
  handler: async (ctx, args) => {
    const property = await ctx.db.get(args.propertyId);
    if (!property) {
      throw new Error("Property not found");
    }

    // Get all units for this property
    const units = await ctx.db
      .query("units")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .collect();

    // Get active leases
    const activeLeases = await ctx.db
      .query("leases")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    // Calculate occupancy rate
    const totalUnits = units.length;
    const occupiedUnits = units.filter(unit => unit.status === "occupied").length;
    const occupancyRate = totalUnits > 0 ? (occupiedUnits / totalUnits) * 100 : 0;

    // Calculate total monthly revenue
    const totalMonthlyRevenue = activeLeases.reduce((sum, lease) => sum + lease.monthlyRent, 0);

    // Get maintenance tickets
    const openTickets = await ctx.db
      .query("maintenanceTickets")
      .withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
      .filter((q) => q.neq(q.field("status"), "closed"))
      .collect();

    return {
      property,
      totalUnits,
      occupiedUnits,
      vacantUnits: totalUnits - occupiedUnits,
      occupancyRate,
      totalMonthlyRevenue,
      activeLeases: activeLeases.length,
      openMaintenanceTickets: openTickets.length,
    };
  },
});