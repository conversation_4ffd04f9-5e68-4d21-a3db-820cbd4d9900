// Test configuration and utilities for EstatePulse

export const TEST_CONFIG = {
  // API endpoints
  API_BASE_URL: process.env.VITE_API_URL || 'http://localhost:5173',
  
  // Test timeouts
  TIMEOUTS: {
    SHORT: 5000,
    MEDIUM: 10000,
    LONG: 30000,
    PAYMENT: 60000, // Payment operations can take longer
  },
  
  // Test data
  TEST_USERS: {
    OWNER: {
      email: '<EMAIL>',
      password: 'testpass123',
      name: 'Test Owner',
      role: 'owner' as const,
    },
    MANAGER: {
      email: '<EMAIL>',
      password: 'testpass123',
      name: 'Test Manager',
      role: 'manager' as const,
    },
    TENANT: {
      email: '<EMAIL>',
      password: 'testpass123',
      name: 'Test Tenant',
      role: 'tenant' as const,
    },
    VENDOR: {
      email: '<EMAIL>',
      password: 'testpass123',
      name: 'Test Vendor',
      role: 'vendor' as const,
    },
  },
  
  // Test properties
  TEST_PROPERTIES: {
    RESIDENTIAL: {
      name: 'Test Residential Property',
      type: 'residential' as const,
      address: {
        street: '123 Test Street',
        city: 'Nairobi',
        state: 'Nairobi County',
        postalCode: '00100',
        country: 'Kenya',
      },
    },
    COMMERCIAL: {
      name: 'Test Commercial Property',
      type: 'commercial' as const,
      address: {
        street: '456 Business Ave',
        city: 'Nairobi',
        state: 'Nairobi County',
        postalCode: '00200',
        country: 'Kenya',
      },
    },
  },
  
  // Test units
  TEST_UNITS: {
    APARTMENT: {
      unitNumber: 'A101',
      type: 'apartment' as const,
      size: 1200,
      rent: 50000,
      status: 'vacant' as const,
      amenities: ['parking', 'balcony'],
    },
    OFFICE: {
      unitNumber: 'B201',
      type: 'office' as const,
      size: 800,
      rent: 80000,
      status: 'vacant' as const,
      amenities: ['parking', 'conference_room'],
    },
  },
  
  // Test payment data
  TEST_PAYMENTS: {
    MPESA: {
      method: 'mpesa' as const,
      phoneNumber: '254712345678',
      amount: 50000,
    },
    STRIPE: {
      method: 'stripe' as const,
      cardNumber: '****************',
      expiryMonth: '12',
      expiryYear: '25',
      cvc: '123',
      amount: 50000,
    },
  },
  
  // Performance thresholds
  PERFORMANCE: {
    PAGE_LOAD_TIME: 3000, // 3 seconds
    API_RESPONSE_TIME: 1000, // 1 second
    SEARCH_RESPONSE_TIME: 500, // 500ms
    PAYMENT_PROCESSING_TIME: 10000, // 10 seconds
  },
  
  // Browser configurations
  BROWSERS: {
    DESKTOP: ['chromium', 'firefox', 'webkit'],
    MOBILE: ['Mobile Chrome', 'Mobile Safari'],
    TABLET: ['iPad Pro'],
  },
};

// Mock data generators
export class TestDataGenerator {
  static generateUser(role: 'owner' | 'manager' | 'tenant' | 'vendor' = 'tenant') {
    const timestamp = Date.now();
    return {
      email: `test-${role}-${timestamp}@example.com`,
      name: `Test ${role.charAt(0).toUpperCase() + role.slice(1)} ${timestamp}`,
      role,
      password: 'testpass123',
      phone: `+25471${Math.floor(Math.random() * 10000000)}`,
    };
  }
  
  static generateProperty(type: 'residential' | 'commercial' | 'mixed' = 'residential') {
    const timestamp = Date.now();
    return {
      name: `Test ${type} Property ${timestamp}`,
      type,
      address: {
        street: `${Math.floor(Math.random() * 999)} Test Street`,
        city: 'Nairobi',
        state: 'Nairobi County',
        postalCode: `00${Math.floor(Math.random() * 900) + 100}`,
        country: 'Kenya',
      },
    };
  }
  
  static generateUnit(propertyId: string) {
    const unitTypes = ['apartment', 'office', 'retail', 'parking'];
    const statuses = ['vacant', 'occupied', 'maintenance'];
    const timestamp = Date.now();
    
    return {
      propertyId,
      unitNumber: `T${Math.floor(Math.random() * 900) + 100}`,
      type: unitTypes[Math.floor(Math.random() * unitTypes.length)],
      size: Math.floor(Math.random() * 1000) + 500,
      rent: Math.floor(Math.random() * 50000) + 30000,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      amenities: ['parking', 'balcony'].filter(() => Math.random() > 0.5),
    };
  }
  
  static generateLease(propertyId: string, unitId: string, tenantId: string) {
    const startDate = Date.now();
    const endDate = startDate + (365 * 24 * 60 * 60 * 1000); // 1 year
    
    return {
      propertyId,
      unitId,
      tenantId,
      startDate,
      endDate,
      monthlyRent: Math.floor(Math.random() * 50000) + 30000,
      deposit: Math.floor(Math.random() * 100000) + 50000,
    };
  }
  
  static generateMaintenanceTicket(propertyId: string, unitId?: string) {
    const priorities = ['low', 'medium', 'high', 'emergency'];
    const categories = ['plumbing', 'electrical', 'hvac', 'general'];
    const timestamp = Date.now();
    
    return {
      propertyId,
      unitId,
      title: `Test Maintenance Issue ${timestamp}`,
      description: `This is a test maintenance ticket created at ${new Date().toISOString()}`,
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      category: categories[Math.floor(Math.random() * categories.length)],
    };
  }
  
  static generatePayment(invoiceId: string, method: 'mpesa' | 'stripe' = 'mpesa') {
    const basePayment = {
      invoiceId,
      amount: Math.floor(Math.random() * 100000) + 10000,
      method,
    };
    
    if (method === 'mpesa') {
      return {
        ...basePayment,
        phoneNumber: `25471${Math.floor(Math.random() * 10000000)}`,
      };
    } else {
      return {
        ...basePayment,
        cardToken: `tok_${Math.random().toString(36).substr(2, 9)}`,
      };
    }
  }
}

// Test utilities
export class TestUtils {
  static async waitForCondition(
    condition: () => Promise<boolean> | boolean,
    timeout: number = TEST_CONFIG.TIMEOUTS.MEDIUM,
    interval: number = 100
  ): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return;
      }
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    throw new Error(`Condition not met within ${timeout}ms`);
  }
  
  static generateRandomString(length: number = 8): string {
    return Math.random().toString(36).substr(2, length);
  }
  
  static generateRandomEmail(): string {
    return `test-${this.generateRandomString()}@example.com`;
  }
  
  static generateRandomPhoneNumber(): string {
    return `+25471${Math.floor(Math.random() * 10000000)}`;
  }
  
  static formatCurrency(amount: number): string {
    return `KES ${amount.toLocaleString()}`;
  }
  
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  
  static isValidPhoneNumber(phone: string): boolean {
    const phoneRegex = /^(\+254|0)?[17]\d{8}$/;
    return phoneRegex.test(phone);
  }
}

// Mock API responses
export class MockResponses {
  static successResponse(data: any) {
    return {
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(data),
    };
  }
  
  static errorResponse(message: string, status: number = 400) {
    return {
      status,
      contentType: 'application/json',
      body: JSON.stringify({ error: message }),
    };
  }
  
  static authResponse(user: any) {
    return this.successResponse({
      sessionToken: `mock-token-${Date.now()}`,
      user,
      expiresAt: Date.now() + 86400000, // 24 hours
    });
  }
  
  static paginatedResponse(data: any[], page: number = 1, limit: number = 10) {
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = data.slice(startIndex, endIndex);
    
    return this.successResponse({
      data: paginatedData,
      pagination: {
        page,
        limit,
        total: data.length,
        totalPages: Math.ceil(data.length / limit),
      },
    });
  }
}

// Test assertions
export class TestAssertions {
  static assertValidProperty(property: any) {
    expect(property).toHaveProperty('_id');
    expect(property).toHaveProperty('name');
    expect(property).toHaveProperty('type');
    expect(property).toHaveProperty('address');
    expect(property.address).toHaveProperty('street');
    expect(property.address).toHaveProperty('city');
  }
  
  static assertValidUnit(unit: any) {
    expect(unit).toHaveProperty('_id');
    expect(unit).toHaveProperty('unitNumber');
    expect(unit).toHaveProperty('type');
    expect(unit).toHaveProperty('size');
    expect(unit).toHaveProperty('rent');
    expect(unit).toHaveProperty('status');
  }
  
  static assertValidLease(lease: any) {
    expect(lease).toHaveProperty('_id');
    expect(lease).toHaveProperty('propertyId');
    expect(lease).toHaveProperty('unitId');
    expect(lease).toHaveProperty('tenantId');
    expect(lease).toHaveProperty('startDate');
    expect(lease).toHaveProperty('endDate');
    expect(lease).toHaveProperty('monthlyRent');
  }
  
  static assertValidPayment(payment: any) {
    expect(payment).toHaveProperty('_id');
    expect(payment).toHaveProperty('amount');
    expect(payment).toHaveProperty('method');
    expect(payment).toHaveProperty('status');
    expect(payment).toHaveProperty('transactionId');
  }
  
  static assertValidMaintenanceTicket(ticket: any) {
    expect(ticket).toHaveProperty('_id');
    expect(ticket).toHaveProperty('title');
    expect(ticket).toHaveProperty('description');
    expect(ticket).toHaveProperty('priority');
    expect(ticket).toHaveProperty('status');
    expect(ticket).toHaveProperty('category');
  }
}