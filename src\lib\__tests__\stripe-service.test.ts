import { describe, it, expect, vi, beforeEach } from "vitest";
import { StripeService } from "../stripe-service";
import { ConvexReactClient } from "convex/react";

// Mock ConvexReactClient
const mockConvex = {
  mutation: vi.fn(),
  action: vi.fn(),
  query: vi.fn(),
} as unknown as ConvexReactClient;

describe("StripeService", () => {
  let stripeService: StripeService;

  beforeEach(() => {
    vi.clearAllMocks();
    stripeService = new StripeService(mockConvex);
  });

  describe("initiatePayment", () => {
    it("should successfully initiate payment with valid data", async () => {
      const mockPaymentResult = { paymentId: "payment123", status: "initiated" };
      const mockStripeResult = {
        success: true,
        paymentIntentId: "pi_123456789",
        clientSecret: "pi_123456789_secret_abc",
        status: "requires_payment_method",
        message: "Payment intent created successfully",
      };

      (mockConvex.mutation as any).mockResolvedValue(mockPaymentResult);
      (mockConvex.action as any).mockResolvedValue(mockStripeResult);

      const result = await stripeService.initiatePayment({
        invoiceId: "invoice123" as any,
        amount: 1000,
        currency: "KES",
        customerEmail: "<EMAIL>",
        description: "Test payment",
      });

      expect(result.success).toBe(true);
      expect(result.paymentId).toBe("payment123");
      expect(result.paymentIntentId).toBe("pi_123456789");
      expect(result.clientSecret).toBe("pi_123456789_secret_abc");
      expect(mockConvex.mutation).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          amount: 1000,
          currency: "KES",
        })
      );
    });

    it("should reject invalid email addresses", async () => {
      const result = await stripeService.initiatePayment({
        invoiceId: "invoice123" as any,
        amount: 1000,
        currency: "KES",
        customerEmail: "invalid-email", // Invalid email
        description: "Test payment",
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe("Please provide a valid email address");
    });

    it("should reject invalid amounts", async () => {
      const result = await stripeService.initiatePayment({
        invoiceId: "invoice123" as any,
        amount: 0, // Invalid amount
        currency: "KES",
        customerEmail: "<EMAIL>",
        description: "Test payment",
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe("Amount must be greater than zero");
    });

    it("should handle payment initiation failure", async () => {
      (mockConvex.mutation as any).mockRejectedValue(new Error("Database error"));

      const result = await stripeService.initiatePayment({
        invoiceId: "invoice123" as any,
        amount: 1000,
        currency: "KES",
        customerEmail: "<EMAIL>",
        description: "Test payment",
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe("Database error");
    });
  });

  describe("createCustomer", () => {
    it("should create a Stripe customer successfully", async () => {
      const mockCustomerResult = {
        success: true,
        customerId: "cus_123456789",
        customer: {
          id: "cus_123456789",
          email: "<EMAIL>",
          name: "John Doe",
          phone: "+254712345678",
        },
      };

      (mockConvex.action as any).mockResolvedValue(mockCustomerResult);

      const result = await stripeService.createCustomer(
        "<EMAIL>",
        "John Doe",
        "+254712345678"
      );

      expect(result).toEqual({
        id: "cus_123456789",
        email: "<EMAIL>",
        name: "John Doe",
        phone: "+254712345678",
      });
    });

    it("should handle customer creation failure", async () => {
      (mockConvex.action as any).mockRejectedValue(new Error("Stripe error"));

      const result = await stripeService.createCustomer("<EMAIL>", "John Doe");

      expect(result).toBeNull();
    });
  });

  describe("refundPayment", () => {
    it("should process refund successfully", async () => {
      const mockRefundResult = {
        success: true,
        refundId: "re_123456789",
        amount: 1000,
        status: "succeeded",
      };

      (mockConvex.action as any).mockResolvedValue(mockRefundResult);

      const result = await stripeService.refundPayment("pi_123456789", 1000, "requested_by_customer");

      expect(result.success).toBe(true);
      expect(result.refundId).toBe("re_123456789");
      expect(result.amount).toBe(1000);
      expect(result.status).toBe("succeeded");
    });

    it("should handle refund failure", async () => {
      (mockConvex.action as any).mockRejectedValue(new Error("Refund failed"));

      const result = await stripeService.refundPayment("pi_123456789");

      expect(result.success).toBe(false);
      expect(result.error).toBe("Refund failed");
    });
  });

  describe("static methods", () => {
    it("should format amount correctly", () => {
      expect(StripeService.formatAmount(1000, "KES")).toMatch(/KES\s*1,000\.00/);
      expect(StripeService.formatAmount(1500.50, "USD")).toMatch(/\$1,500\.50/);
    });

    it("should convert to cents correctly", () => {
      expect(StripeService.toCents(10.50)).toBe(1050);
      expect(StripeService.toCents(100)).toBe(10000);
      expect(StripeService.toCents(0.99)).toBe(99);
    });

    it("should convert from cents correctly", () => {
      expect(StripeService.fromCents(1050)).toBe(10.50);
      expect(StripeService.fromCents(10000)).toBe(100);
      expect(StripeService.fromCents(99)).toBe(0.99);
    });

    it("should return supported currencies", () => {
      const currencies = StripeService.getSupportedCurrencies();
      expect(currencies).toHaveLength(4);
      expect(currencies[0]).toEqual({ code: "KES", name: "Kenyan Shilling", symbol: "KSh" });
      expect(currencies[1]).toEqual({ code: "USD", name: "US Dollar", symbol: "$" });
    });

    it("should provide correct status descriptions", () => {
      expect(StripeService.getStatusDescription("succeeded")).toBe("Payment succeeded");
      expect(StripeService.getStatusDescription("payment_failed")).toBe("Payment failed");
      expect(StripeService.getStatusDescription("processing")).toBe("Processing payment");
      expect(StripeService.getStatusDescription("unknown_status")).toBe("Unknown status: unknown_status");
    });
  });

  describe("getPaymentDetails", () => {
    it("should return payment details", async () => {
      const mockPayment = {
        _id: "payment123",
        amount: 1000,
        status: "completed",
        method: "stripe",
      };

      (mockConvex.query as any).mockResolvedValue(mockPayment);

      const result = await stripeService.getPaymentDetails("payment123" as any);

      expect(result).toEqual(mockPayment);
      expect(mockConvex.query).toHaveBeenCalledWith(
        expect.any(Object),
        { paymentId: "payment123" }
      );
    });

    it("should handle errors gracefully", async () => {
      (mockConvex.query as any).mockRejectedValue(new Error("Network error"));

      const result = await stripeService.getPaymentDetails("payment123" as any);

      expect(result).toBeNull();
    });
  });

  describe("getInvoicePayments", () => {
    it("should return payments for an invoice", async () => {
      const mockPayments = [
        { _id: "payment1", amount: 500, status: "completed", method: "stripe" },
        { _id: "payment2", amount: 500, status: "pending", method: "stripe" },
      ];

      (mockConvex.query as any).mockResolvedValue(mockPayments);

      const result = await stripeService.getInvoicePayments("invoice123" as any);

      expect(result).toEqual(mockPayments);
      expect(mockConvex.query).toHaveBeenCalledWith(
        expect.any(Object),
        { invoiceId: "invoice123" }
      );
    });

    it("should return empty array on error", async () => {
      (mockConvex.query as any).mockRejectedValue(new Error("Network error"));

      const result = await stripeService.getInvoicePayments("invoice123" as any);

      expect(result).toEqual([]);
    });
  });
});