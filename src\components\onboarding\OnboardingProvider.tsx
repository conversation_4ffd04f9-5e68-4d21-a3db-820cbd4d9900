/**
 * Onboarding provider for managing guided tours and help flows
 */
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAccessibility } from '../../contexts/AccessibilityContext';

export interface OnboardingStep {
  id: string;
  title: string;
  content: string;
  target?: string; // CSS selector for the element to highlight
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center';
  action?: {
    type: 'click' | 'input' | 'navigate';
    element?: string;
    value?: string;
    url?: string;
  };
  skippable?: boolean;
  required?: boolean;
}

export interface OnboardingFlow {
  id: string;
  name: string;
  description: string;
  role?: string; // Target user role
  steps: OnboardingStep[];
  autoStart?: boolean;
  completionKey: string; // localStorage key to track completion
}

interface OnboardingContextType {
  currentFlow: OnboardingFlow | null;
  currentStep: number;
  isActive: boolean;
  startFlow: (flowId: string) => void;
  nextStep: () => void;
  previousStep: () => void;
  skipStep: () => void;
  completeFlow: () => void;
  dismissFlow: () => void;
  registerFlow: (flow: OnboardingFlow) => void;
  getAvailableFlows: () => OnboardingFlow[];
  isFlowCompleted: (flowId: string) => boolean;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}

interface OnboardingProviderProps {
  children: React.ReactNode;
}

export function OnboardingProvider({ children }: OnboardingProviderProps) {
  const { announceToScreenReader } = useAccessibility();
  const [flows, setFlows] = useState<Map<string, OnboardingFlow>>(new Map());
  const [currentFlow, setCurrentFlow] = useState<OnboardingFlow | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [isActive, setIsActive] = useState(false);

  const registerFlow = (flow: OnboardingFlow) => {
    setFlows(prev => new Map(prev.set(flow.id, flow)));
  };

  const getAvailableFlows = () => {
    return Array.from(flows.values());
  };

  const isFlowCompleted = (flowId: string) => {
    const flow = flows.get(flowId);
    if (!flow) return false;
    return localStorage.getItem(flow.completionKey) === 'true';
  };

  const startFlow = (flowId: string) => {
    const flow = flows.get(flowId);
    if (!flow) {
      console.warn(`Onboarding flow "${flowId}" not found`);
      return;
    }

    if (isFlowCompleted(flowId)) {
      console.log(`Onboarding flow "${flowId}" already completed`);
      return;
    }

    setCurrentFlow(flow);
    setCurrentStep(0);
    setIsActive(true);
    
    announceToScreenReader(`Starting ${flow.name} onboarding. ${flow.steps.length} steps total.`, 'polite');
  };

  const nextStep = () => {
    if (!currentFlow) return;

    if (currentStep < currentFlow.steps.length - 1) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      
      const step = currentFlow.steps[newStep];
      announceToScreenReader(`Step ${newStep + 1} of ${currentFlow.steps.length}: ${step.title}`, 'polite');
    } else {
      completeFlow();
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      
      const step = currentFlow.steps[newStep];
      announceToScreenReader(`Step ${newStep + 1} of ${currentFlow.steps.length}: ${step.title}`, 'polite');
    }
  };

  const skipStep = () => {
    if (!currentFlow) return;
    
    const step = currentFlow.steps[currentStep];
    if (step.skippable !== false) {
      nextStep();
    }
  };

  const completeFlow = () => {
    if (!currentFlow) return;

    localStorage.setItem(currentFlow.completionKey, 'true');
    announceToScreenReader(`${currentFlow.name} onboarding completed!`, 'polite');
    
    setCurrentFlow(null);
    setCurrentStep(0);
    setIsActive(false);
  };

  const dismissFlow = () => {
    if (!currentFlow) return;

    announceToScreenReader(`${currentFlow.name} onboarding dismissed`, 'polite');
    
    setCurrentFlow(null);
    setCurrentStep(0);
    setIsActive(false);
  };

  // Auto-start flows based on user role and completion status
  useEffect(() => {
    const autoStartFlows = Array.from(flows.values()).filter(
      flow => flow.autoStart && !isFlowCompleted(flow.id)
    );

    if (autoStartFlows.length > 0 && !isActive) {
      // Start the first available auto-start flow
      const flowToStart = autoStartFlows[0];
      setTimeout(() => startFlow(flowToStart.id), 1000); // Delay to allow UI to settle
    }
  }, [flows, isActive]);

  const value: OnboardingContextType = {
    currentFlow,
    currentStep,
    isActive,
    startFlow,
    nextStep,
    previousStep,
    skipStep,
    completeFlow,
    dismissFlow,
    registerFlow,
    getAvailableFlows,
    isFlowCompleted,
  };

  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  );
}