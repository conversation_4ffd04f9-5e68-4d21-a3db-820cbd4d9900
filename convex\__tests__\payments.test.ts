import { describe, it, expect, beforeEach } from "vitest";
import { convexTest } from "convex-test";
import { api } from "../_generated/api";
import schema from "../schema";

describe("Payments", () => {
  let t: ReturnType<typeof convexTest>;

  beforeEach(() => {
    t = convexTest(schema);
  });

  describe("initiateMPESAPayment", () => {
    it("should create a pending payment record", async () => {
      // Create test data
      const userId = await t.mutation(api.users.create, {
        email: "<EMAIL>",
        name: "Test Tenant",
        role: "tenant",
        propertyAccess: [],
        kycStatus: "verified",
        phone: "254712345678",
        isActive: true,
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: "Test Property",
        type: "residential",
        address: {
          street: "123 Test St",
          city: "Nairobi",
          state: "Nairobi",
          country: "Kenya",
          postalCode: "00100",
        },
        ownerId: userId,
        branding: {
          primaryColor: "#000000",
          secondaryColor: "#ffffff",
        },
        settings: {
          currency: "KES",
          timezone: "Africa/Nairobi",
          language: "en",
          autoRentReminders: true,
          maintenanceSLA: 24,
        },
        isActive: true,
      });

      const unitId = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: "101",
        type: "apartment",
        size: 1000,
        rent: 50000,
        deposit: 100000,
        status: "occupied",
        amenities: ["parking", "gym"],
      });

      const leaseId = await t.mutation(api.leases.create, {
        propertyId,
        unitId,
        tenantId: userId,
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000, // 1 year
        monthlyRent: 50000,
        deposit: 100000,
        terms: {
          noticePeriod: 30,
          lateFeePercentage: 5,
          gracePeriod: 5,
          renewalOption: true,
        },
        eSignatureStatus: "signed",
      });

      const invoiceId = await t.mutation(api.invoices.create, {
        leaseId,
        amount: 50000,
        dueDate: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days
        type: "rent",
        items: [
          {
            description: "Monthly Rent - January 2024",
            amount: 50000,
            quantity: 1,
          },
        ],
      });

      // Test payment initiation
      const result = await t.mutation(api.payments.initiateMPESAPayment, {
        invoiceId,
        phoneNumber: "254712345678",
        amount: 50000,
      });

      expect(result.status).toBe("initiated");
      expect(result.paymentId).toBeDefined();

      // Verify payment record was created
      const payment = await t.query(api.payments.getPayment, {
        paymentId: result.paymentId,
      });

      expect(payment).toBeDefined();
      expect(payment!.amount).toBe(50000);
      expect(payment!.method).toBe("mpesa");
      expect(payment!.status).toBe("pending");
      expect(payment!.metadata.phoneNumber).toBe("254712345678");
    });

    it("should reject payment for already paid invoice", async () => {
      // Create test data (similar to above)
      const userId = await t.mutation(api.users.create, {
        email: "<EMAIL>",
        name: "Test Tenant",
        role: "tenant",
        propertyAccess: [],
        kycStatus: "verified",
        phone: "254712345678",
        isActive: true,
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: "Test Property",
        type: "residential",
        address: {
          street: "123 Test St",
          city: "Nairobi",
          state: "Nairobi",
          country: "Kenya",
          postalCode: "00100",
        },
        ownerId: userId,
        branding: {
          primaryColor: "#000000",
          secondaryColor: "#ffffff",
        },
        settings: {
          currency: "KES",
          timezone: "Africa/Nairobi",
          language: "en",
          autoRentReminders: true,
          maintenanceSLA: 24,
        },
        isActive: true,
      });

      const unitId = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: "101",
        type: "apartment",
        size: 1000,
        rent: 50000,
        deposit: 100000,
        status: "occupied",
        amenities: ["parking", "gym"],
      });

      const leaseId = await t.mutation(api.leases.create, {
        propertyId,
        unitId,
        tenantId: userId,
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: 50000,
        deposit: 100000,
        terms: {
          noticePeriod: 30,
          lateFeePercentage: 5,
          gracePeriod: 5,
          renewalOption: true,
        },
        eSignatureStatus: "signed",
      });

      const invoiceId = await t.mutation(api.invoices.create, {
        leaseId,
        amount: 50000,
        dueDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
        type: "rent",
        items: [
          {
            description: "Monthly Rent - January 2024",
            amount: 50000,
            quantity: 1,
          },
        ],
      });

      // Mark invoice as paid
      await t.mutation(api.invoices.updateInvoiceStatus, {
        invoiceId,
        status: "paid",
        paymentMethod: "mpesa",
      });

      // Try to initiate payment for paid invoice
      await expect(
        t.mutation(api.payments.initiateMPESAPayment, {
          invoiceId,
          phoneNumber: "254712345678",
          amount: 50000,
        })
      ).rejects.toThrow("Invoice is already paid");
    });

    it("should reject payment with mismatched amount", async () => {
      // Create test data (similar to above, abbreviated)
      const userId = await t.mutation(api.users.create, {
        email: "<EMAIL>",
        name: "Test Tenant",
        role: "tenant",
        propertyAccess: [],
        kycStatus: "verified",
        phone: "254712345678",
        isActive: true,
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: "Test Property",
        type: "residential",
        address: {
          street: "123 Test St",
          city: "Nairobi",
          state: "Nairobi",
          country: "Kenya",
          postalCode: "00100",
        },
        ownerId: userId,
        branding: {
          primaryColor: "#000000",
          secondaryColor: "#ffffff",
        },
        settings: {
          currency: "KES",
          timezone: "Africa/Nairobi",
          language: "en",
          autoRentReminders: true,
          maintenanceSLA: 24,
        },
        isActive: true,
      });

      const unitId = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: "101",
        type: "apartment",
        size: 1000,
        rent: 50000,
        deposit: 100000,
        status: "occupied",
        amenities: ["parking", "gym"],
      });

      const leaseId = await t.mutation(api.leases.create, {
        propertyId,
        unitId,
        tenantId: userId,
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: 50000,
        deposit: 100000,
        terms: {
          noticePeriod: 30,
          lateFeePercentage: 5,
          gracePeriod: 5,
          renewalOption: true,
        },
        eSignatureStatus: "signed",
      });

      const invoiceId = await t.mutation(api.invoices.create, {
        leaseId,
        amount: 50000,
        dueDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
        type: "rent",
        items: [
          {
            description: "Monthly Rent - January 2024",
            amount: 50000,
            quantity: 1,
          },
        ],
      });

      // Try to pay with wrong amount
      await expect(
        t.mutation(api.payments.initiateMPESAPayment, {
          invoiceId,
          phoneNumber: "254712345678",
          amount: 40000, // Wrong amount
        })
      ).rejects.toThrow("Payment amount does not match invoice amount");
    });
  });

  describe("handleMPESACallback", () => {
    it("should process successful payment callback", async () => {
      // Create test data and payment
      const userId = await t.mutation(api.users.create, {
        email: "<EMAIL>",
        name: "Test Tenant",
        role: "tenant",
        propertyAccess: [],
        kycStatus: "verified",
        phone: "254712345678",
        isActive: true,
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: "Test Property",
        type: "residential",
        address: {
          street: "123 Test St",
          city: "Nairobi",
          state: "Nairobi",
          country: "Kenya",
          postalCode: "00100",
        },
        ownerId: userId,
        branding: {
          primaryColor: "#000000",
          secondaryColor: "#ffffff",
        },
        settings: {
          currency: "KES",
          timezone: "Africa/Nairobi",
          language: "en",
          autoRentReminders: true,
          maintenanceSLA: 24,
        },
        isActive: true,
      });

      const unitId = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: "101",
        type: "apartment",
        size: 1000,
        rent: 50000,
        deposit: 100000,
        status: "occupied",
        amenities: ["parking", "gym"],
      });

      const leaseId = await t.mutation(api.leases.create, {
        propertyId,
        unitId,
        tenantId: userId,
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: 50000,
        deposit: 100000,
        terms: {
          noticePeriod: 30,
          lateFeePercentage: 5,
          gracePeriod: 5,
          renewalOption: true,
        },
        eSignatureStatus: "signed",
      });

      const invoiceId = await t.mutation(api.invoices.create, {
        leaseId,
        amount: 50000,
        dueDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
        type: "rent",
        items: [
          {
            description: "Monthly Rent - January 2024",
            amount: 50000,
            quantity: 1,
          },
        ],
      });

      const paymentResult = await t.mutation(api.payments.initiateMPESAPayment, {
        invoiceId,
        phoneNumber: "254712345678",
        amount: 50000,
      });

      // Update payment with checkout request ID
      const checkoutRequestId = "ws_CO_123456789";
      await t.mutation(api.payments.updatePaymentTransaction, {
        paymentId: paymentResult.paymentId,
        transactionId: checkoutRequestId,
        metadata: {
          phoneNumber: "254712345678",
          checkoutRequestId,
        },
      });

      // Process successful callback
      const callbackResult = await t.mutation(api.payments.handleMPESACallback, {
        checkoutRequestId,
        resultCode: 0,
        resultDesc: "The service request is processed successfully.",
        mpesaReceiptNumber: "NLJ7RT61SV",
        transactionDate: "20240115143022",
        phoneNumber: "254712345678",
        amount: 50000,
      });

      expect(callbackResult.success).toBe(true);
      expect(callbackResult.status).toBe("completed");

      // Verify payment was updated
      const payment = await t.query(api.payments.getPayment, {
        paymentId: paymentResult.paymentId,
      });

      expect(payment!.status).toBe("completed");
      expect(payment!.metadata.mpesaReceiptNumber).toBe("NLJ7RT61SV");

      // Verify invoice was marked as paid
      const invoice = await t.query(api.invoices.getInvoice, { invoiceId });
      expect(invoice!.status).toBe("paid");
      expect(invoice!.paymentMethod).toBe("mpesa");
    });

    it("should process failed payment callback", async () => {
      // Create test data (abbreviated)
      const userId = await t.mutation(api.users.create, {
        email: "<EMAIL>",
        name: "Test Tenant",
        role: "tenant",
        propertyAccess: [],
        kycStatus: "verified",
        phone: "254712345678",
        isActive: true,
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: "Test Property",
        type: "residential",
        address: {
          street: "123 Test St",
          city: "Nairobi",
          state: "Nairobi",
          country: "Kenya",
          postalCode: "00100",
        },
        ownerId: userId,
        branding: {
          primaryColor: "#000000",
          secondaryColor: "#ffffff",
        },
        settings: {
          currency: "KES",
          timezone: "Africa/Nairobi",
          language: "en",
          autoRentReminders: true,
          maintenanceSLA: 24,
        },
        isActive: true,
      });

      const unitId = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: "101",
        type: "apartment",
        size: 1000,
        rent: 50000,
        deposit: 100000,
        status: "occupied",
        amenities: ["parking", "gym"],
      });

      const leaseId = await t.mutation(api.leases.create, {
        propertyId,
        unitId,
        tenantId: userId,
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: 50000,
        deposit: 100000,
        terms: {
          noticePeriod: 30,
          lateFeePercentage: 5,
          gracePeriod: 5,
          renewalOption: true,
        },
        eSignatureStatus: "signed",
      });

      const invoiceId = await t.mutation(api.invoices.create, {
        leaseId,
        amount: 50000,
        dueDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
        type: "rent",
        items: [
          {
            description: "Monthly Rent - January 2024",
            amount: 50000,
            quantity: 1,
          },
        ],
      });

      const paymentResult = await t.mutation(api.payments.initiateMPESAPayment, {
        invoiceId,
        phoneNumber: "254712345678",
        amount: 50000,
      });

      const checkoutRequestId = "ws_CO_123456789";
      await t.mutation(api.payments.updatePaymentTransaction, {
        paymentId: paymentResult.paymentId,
        transactionId: checkoutRequestId,
        metadata: {
          phoneNumber: "254712345678",
          checkoutRequestId,
        },
      });

      // Process failed callback
      const callbackResult = await t.mutation(api.payments.handleMPESACallback, {
        checkoutRequestId,
        resultCode: 1032,
        resultDesc: "Request cancelled by user",
      });

      expect(callbackResult.success).toBe(true);
      expect(callbackResult.status).toBe("failed");

      // Verify payment was marked as failed
      const payment = await t.query(api.payments.getPayment, {
        paymentId: paymentResult.paymentId,
      });

      expect(payment!.status).toBe("failed");

      // Verify invoice is still pending
      const invoice = await t.query(api.invoices.getInvoice, { invoiceId });
      expect(invoice!.status).toBe("pending");
    });
  });

  describe("getPaymentsByInvoice", () => {
    it("should return all payments for an invoice", async () => {
      // Create test data (abbreviated)
      const userId = await t.mutation(api.users.create, {
        email: "<EMAIL>",
        name: "Test Tenant",
        role: "tenant",
        propertyAccess: [],
        kycStatus: "verified",
        phone: "254712345678",
        isActive: true,
      });

      const propertyId = await t.mutation(api.properties.create, {
        name: "Test Property",
        type: "residential",
        address: {
          street: "123 Test St",
          city: "Nairobi",
          state: "Nairobi",
          country: "Kenya",
          postalCode: "00100",
        },
        ownerId: userId,
        branding: {
          primaryColor: "#000000",
          secondaryColor: "#ffffff",
        },
        settings: {
          currency: "KES",
          timezone: "Africa/Nairobi",
          language: "en",
          autoRentReminders: true,
          maintenanceSLA: 24,
        },
        isActive: true,
      });

      const unitId = await t.mutation(api.units.create, {
        propertyId,
        unitNumber: "101",
        type: "apartment",
        size: 1000,
        rent: 50000,
        deposit: 100000,
        status: "occupied",
        amenities: ["parking", "gym"],
      });

      const leaseId = await t.mutation(api.leases.create, {
        propertyId,
        unitId,
        tenantId: userId,
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: 50000,
        deposit: 100000,
        terms: {
          noticePeriod: 30,
          lateFeePercentage: 5,
          gracePeriod: 5,
          renewalOption: true,
        },
        eSignatureStatus: "signed",
      });

      const invoiceId = await t.mutation(api.invoices.create, {
        leaseId,
        amount: 50000,
        dueDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
        type: "rent",
        items: [
          {
            description: "Monthly Rent - January 2024",
            amount: 50000,
            quantity: 1,
          },
        ],
      });

      // Create multiple payments
      const payment1 = await t.mutation(api.payments.initiateMPESAPayment, {
        invoiceId,
        phoneNumber: "254712345678",
        amount: 25000,
      });

      const payment2 = await t.mutation(api.payments.initiateMPESAPayment, {
        invoiceId,
        phoneNumber: "254712345678",
        amount: 25000,
      });

      // Get payments for invoice
      const payments = await t.query(api.payments.getPaymentsByInvoice, { invoiceId });

      expect(payments).toHaveLength(2);
      expect(payments.map(p => p._id)).toContain(payment1.paymentId);
      expect(payments.map(p => p._id)).toContain(payment2.paymentId);
    });
  });
});