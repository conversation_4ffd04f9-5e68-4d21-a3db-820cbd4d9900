import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { ConvexReactClient } from "convex/react";

export interface MPESAPaymentRequest {
  invoiceId: Id<"invoices">;
  phoneNumber: string;
  amount: number;
  reference: string;
}

export interface MPESAPaymentResult {
  success: boolean;
  paymentId?: Id<"payments">;
  checkoutRequestId?: string;
  merchantRequestId?: string;
  message: string;
  error?: string;
}

export interface PaymentStatus {
  status: "pending" | "completed" | "failed" | "refunded";
  transactionId?: string;
  mpesaReceiptNumber?: string;
  message?: string;
}

export class MPESAService {
  constructor(private convex: ConvexReactClient) {}

  /**
   * Initiate M-PESA STK Push payment
   */
  async initiatePayment(request: MPESAPaymentRequest): Promise<MPESAPaymentResult> {
    try {
      // Validate phone number format
      const phoneNumber = this.formatPhoneNumber(request.phoneNumber);
      if (!this.isValidPhoneNumber(phoneNumber)) {
        return {
          success: false,
          message: "Invalid phone number format",
          error: "Phone number must be a valid Kenyan mobile number",
        };
      }

      // Create pending payment record
      const paymentResult = await this.convex.mutation(api.payments.initiateMPESAPayment, {
        invoiceId: request.invoiceId,
        phoneNumber: phoneNumber,
        amount: request.amount,
      });

      if (!paymentResult.paymentId) {
        return {
          success: false,
          message: "Failed to create payment record",
          error: "Unable to initialize payment",
        };
      }

      // Process STK Push
      const stkResult = await this.convex.action(api.payments.processMPESASTKPush, {
        paymentId: paymentResult.paymentId,
        phoneNumber: phoneNumber,
        amount: request.amount,
        reference: request.reference,
      });

      return {
        success: stkResult.success,
        paymentId: paymentResult.paymentId,
        checkoutRequestId: stkResult.checkoutRequestId,
        merchantRequestId: stkResult.merchantRequestId,
        message: stkResult.message,
      };
    } catch (error) {
      console.error("M-PESA payment initiation failed:", error);
      return {
        success: false,
        message: "Payment initiation failed",
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Check payment status
   */
  async checkPaymentStatus(checkoutRequestId: string): Promise<PaymentStatus> {
    try {
      const result = await this.convex.action(api.payments.queryMPESAPaymentStatus, {
        checkoutRequestId,
      });

      // Map M-PESA result codes to our status
      let status: PaymentStatus["status"] = "pending";
      let message = result.resultDesc;

      if (result.resultCode === 0) {
        status = "completed";
        message = "Payment completed successfully";
      } else if (result.resultCode === 1032) {
        status = "failed";
        message = "Payment cancelled by user";
      } else if (result.resultCode === 1037) {
        // 1037 is "DS timeout" which means still processing
        status = "pending";
        message = result.resultDesc;
      } else if (result.resultCode === 2001) {
        status = "failed";
        message = "Invalid phone number";
      } else {
        status = "failed";
      }

      return {
        status,
        message,
      };
    } catch (error) {
      console.error("Payment status check failed:", error);
      return {
        status: "failed",
        message: "Failed to check payment status",
      };
    }
  }

  /**
   * Get payment details by payment ID
   */
  async getPaymentDetails(paymentId: Id<"payments">) {
    try {
      return await this.convex.query(api.payments.getPayment, { paymentId });
    } catch (error) {
      console.error("Failed to get payment details:", error);
      return null;
    }
  }

  /**
   * Get all payments for an invoice
   */
  async getInvoicePayments(invoiceId: Id<"invoices">) {
    try {
      return await this.convex.query(api.payments.getPaymentsByInvoice, { invoiceId });
    } catch (error) {
      console.error("Failed to get invoice payments:", error);
      return [];
    }
  }

  /**
   * Format phone number to M-PESA format (254XXXXXXXXX)
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove any non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, "");

    // Handle different formats
    if (cleaned.startsWith("254")) {
      return cleaned;
    } else if (cleaned.startsWith("0")) {
      return "254" + cleaned.slice(1);
    } else if (cleaned.length === 9) {
      return "254" + cleaned;
    }

    return cleaned;
  }

  /**
   * Validate Kenyan phone number format
   */
  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Should be 254XXXXXXXXX format and 12 digits total
    const phoneRegex = /^254[17]\d{8}$/;
    return phoneRegex.test(phoneNumber);
  }

  /**
   * Format amount for display
   */
  static formatAmount(amount: number): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "KES",
      minimumFractionDigits: 2,
    }).format(amount);
  }

  /**
   * Get M-PESA result code descriptions
   */
  static getResultCodeDescription(resultCode: number): string {
    const descriptions: Record<number, string> = {
      0: "Success",
      1: "Insufficient Funds",
      2: "Less Than Minimum Transaction Value",
      3: "More Than Maximum Transaction Value",
      4: "Would Exceed Daily Transfer Limit",
      5: "Would Exceed Minimum Balance",
      6: "Unresolved Primary Party",
      7: "Unresolved Receiver Party",
      8: "Would Exceed Maximum Balance",
      11: "Debit Account Invalid",
      12: "Credit Account Invalid",
      13: "Unresolved Debit Account",
      14: "Unresolved Credit Account",
      15: "Duplicate Detected",
      17: "Internal Failure",
      20: "Unresolved Initiator",
      26: "Traffic Blocking Condition In Place",
      1001: "Invalid Phone Number",
      1019: "Dialing Code Not Supported",
      1032: "Cancelled by User",
      1037: "DS Timeout User cannot be reached",
      2001: "Invalid Phone Number",
      9999: "Request cancelled by user",
    };

    return descriptions[resultCode] || `Unknown error (Code: ${resultCode})`;
  }
}

// Singleton instance
let mpesaServiceInstance: MPESAService | null = null;

export const getMPESAService = (convex: ConvexReactClient): MPESAService => {
  if (!mpesaServiceInstance) {
    mpesaServiceInstance = new MPESAService(convex);
  }
  return mpesaServiceInstance;
};