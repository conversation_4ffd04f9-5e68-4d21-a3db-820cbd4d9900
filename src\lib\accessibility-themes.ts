/**
 * Accessibility-focused theme system with high contrast and color-blind friendly options
 */

export interface AccessibilityTheme {
  name: string;
  displayName: string;
  description: string;
  colors: {
    // Base colors
    background: string;
    foreground: string;
    
    // UI colors
    primary: string;
    primaryForeground: string;
    secondary: string;
    secondaryForeground: string;
    
    // Status colors
    success: string;
    successForeground: string;
    warning: string;
    warningForeground: string;
    error: string;
    errorForeground: string;
    info: string;
    infoForeground: string;
    
    // Interactive colors
    link: string;
    linkHover: string;
    linkVisited: string;
    
    // Border and surface colors
    border: string;
    input: string;
    ring: string;
    
    // Muted colors
    muted: string;
    mutedForeground: string;
    
    // Accent colors
    accent: string;
    accentForeground: string;
    
    // Destructive colors
    destructive: string;
    destructiveForeground: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
  };
}

// Default theme (WCAG AA compliant)
export const defaultTheme: AccessibilityTheme = {
  name: 'default',
  displayName: 'Default',
  description: 'Standard theme with WCAG AA compliance',
  colors: {
    background: '#ffffff',
    foreground: '#0f172a',
    
    primary: '#1e40af',
    primaryForeground: '#ffffff',
    secondary: '#f1f5f9',
    secondaryForeground: '#0f172a',
    
    success: '#16a34a',
    successForeground: '#ffffff',
    warning: '#d97706',
    warningForeground: '#ffffff',
    error: '#dc2626',
    errorForeground: '#ffffff',
    info: '#0ea5e9',
    infoForeground: '#ffffff',
    
    link: '#1e40af',
    linkHover: '#1e3a8a',
    linkVisited: '#7c3aed',
    
    border: '#e2e8f0',
    input: '#ffffff',
    ring: '#1e40af',
    
    muted: '#f8fafc',
    mutedForeground: '#64748b',
    
    accent: '#f1f5f9',
    accentForeground: '#0f172a',
    
    destructive: '#dc2626',
    destructiveForeground: '#ffffff',
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  },
  borderRadius: {
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
  },
};

// High contrast theme
export const highContrastTheme: AccessibilityTheme = {
  name: 'high-contrast',
  displayName: 'High Contrast',
  description: 'High contrast theme for better visibility',
  colors: {
    background: '#000000',
    foreground: '#ffffff',
    
    primary: '#ffffff',
    primaryForeground: '#000000',
    secondary: '#333333',
    secondaryForeground: '#ffffff',
    
    success: '#00ff00',
    successForeground: '#000000',
    warning: '#ffff00',
    warningForeground: '#000000',
    error: '#ff0000',
    errorForeground: '#ffffff',
    info: '#00ffff',
    infoForeground: '#000000',
    
    link: '#00ffff',
    linkHover: '#ffffff',
    linkVisited: '#ff00ff',
    
    border: '#ffffff',
    input: '#000000',
    ring: '#ffffff',
    
    muted: '#333333',
    mutedForeground: '#cccccc',
    
    accent: '#333333',
    accentForeground: '#ffffff',
    
    destructive: '#ff0000',
    destructiveForeground: '#ffffff',
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(255 255 255 / 0.2)',
    md: '0 4px 6px -1px rgb(255 255 255 / 0.3), 0 2px 4px -2px rgb(255 255 255 / 0.3)',
    lg: '0 10px 15px -3px rgb(255 255 255 / 0.3), 0 4px 6px -4px rgb(255 255 255 / 0.3)',
    xl: '0 20px 25px -5px rgb(255 255 255 / 0.3), 0 8px 10px -6px rgb(255 255 255 / 0.3)',
  },
  borderRadius: {
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
  },
};

// Deuteranopia (red-green color blind) friendly theme
export const deuteranopiaTheme: AccessibilityTheme = {
  name: 'deuteranopia',
  displayName: 'Deuteranopia Friendly',
  description: 'Color-blind friendly theme for red-green color blindness',
  colors: {
    background: '#ffffff',
    foreground: '#1a1a1a',
    
    primary: '#0066cc',
    primaryForeground: '#ffffff',
    secondary: '#f5f5f5',
    secondaryForeground: '#1a1a1a',
    
    success: '#0066cc', // Blue instead of green
    successForeground: '#ffffff',
    warning: '#ff9900',
    warningForeground: '#000000',
    error: '#cc0000',
    errorForeground: '#ffffff',
    info: '#6600cc',
    infoForeground: '#ffffff',
    
    link: '#0066cc',
    linkHover: '#004499',
    linkVisited: '#6600cc',
    
    border: '#cccccc',
    input: '#ffffff',
    ring: '#0066cc',
    
    muted: '#f9f9f9',
    mutedForeground: '#666666',
    
    accent: '#f5f5f5',
    accentForeground: '#1a1a1a',
    
    destructive: '#cc0000',
    destructiveForeground: '#ffffff',
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  },
  borderRadius: {
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
  },
};

// Protanopia (red-green color blind) friendly theme
export const protanopiaTheme: AccessibilityTheme = {
  name: 'protanopia',
  displayName: 'Protanopia Friendly',
  description: 'Color-blind friendly theme for red color blindness',
  colors: {
    background: '#ffffff',
    foreground: '#1a1a1a',
    
    primary: '#0066cc',
    primaryForeground: '#ffffff',
    secondary: '#f5f5f5',
    secondaryForeground: '#1a1a1a',
    
    success: '#0066cc', // Blue instead of green
    successForeground: '#ffffff',
    warning: '#ffaa00',
    warningForeground: '#000000',
    error: '#0066cc', // Blue instead of red
    errorForeground: '#ffffff',
    info: '#6600cc',
    infoForeground: '#ffffff',
    
    link: '#0066cc',
    linkHover: '#004499',
    linkVisited: '#6600cc',
    
    border: '#cccccc',
    input: '#ffffff',
    ring: '#0066cc',
    
    muted: '#f9f9f9',
    mutedForeground: '#666666',
    
    accent: '#f5f5f5',
    accentForeground: '#1a1a1a',
    
    destructive: '#0066cc',
    destructiveForeground: '#ffffff',
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  },
  borderRadius: {
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
  },
};

// Tritanopia (blue-yellow color blind) friendly theme
export const tritanopiaTheme: AccessibilityTheme = {
  name: 'tritanopia',
  displayName: 'Tritanopia Friendly',
  description: 'Color-blind friendly theme for blue-yellow color blindness',
  colors: {
    background: '#ffffff',
    foreground: '#1a1a1a',
    
    primary: '#cc0066',
    primaryForeground: '#ffffff',
    secondary: '#f5f5f5',
    secondaryForeground: '#1a1a1a',
    
    success: '#009900',
    successForeground: '#ffffff',
    warning: '#cc0066', // Pink instead of yellow
    warningForeground: '#ffffff',
    error: '#cc0000',
    errorForeground: '#ffffff',
    info: '#009900',
    infoForeground: '#ffffff',
    
    link: '#cc0066',
    linkHover: '#990044',
    linkVisited: '#660033',
    
    border: '#cccccc',
    input: '#ffffff',
    ring: '#cc0066',
    
    muted: '#f9f9f9',
    mutedForeground: '#666666',
    
    accent: '#f5f5f5',
    accentForeground: '#1a1a1a',
    
    destructive: '#cc0000',
    destructiveForeground: '#ffffff',
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  },
  borderRadius: {
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
  },
};

export const accessibilityThemes = {
  default: defaultTheme,
  'high-contrast': highContrastTheme,
  deuteranopia: deuteranopiaTheme,
  protanopia: protanopiaTheme,
  tritanopia: tritanopiaTheme,
};

export type AccessibilityThemeName = keyof typeof accessibilityThemes;

// Theme application utilities
export function applyTheme(theme: AccessibilityTheme): void {
  const root = document.documentElement;
  
  // Apply CSS custom properties
  Object.entries(theme.colors).forEach(([key, value]) => {
    root.style.setProperty(`--color-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`, value);
  });
  
  Object.entries(theme.shadows).forEach(([key, value]) => {
    root.style.setProperty(`--shadow-${key}`, value);
  });
  
  Object.entries(theme.borderRadius).forEach(([key, value]) => {
    root.style.setProperty(`--radius-${key}`, value);
  });
}

export function getThemeByName(name: AccessibilityThemeName): AccessibilityTheme {
  return accessibilityThemes[name];
}

export function getAllThemes(): AccessibilityTheme[] {
  return Object.values(accessibilityThemes);
}