import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Separator } from '../ui/separator';
import { CalendarIcon, DollarSign, RefreshCw, AlertTriangle } from 'lucide-react';

interface LeaseRenewalProps {
  leaseId: Id<"leases">;
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface RenewalFormData {
  newEndDate: string;
  newMonthlyRent: string;
  noticePeriod: string;
  lateFeePercentage: string;
  gracePeriod: string;
  renewalOption: boolean;
}

export function LeaseRenewal({ leaseId, onSuccess, onCancel }: LeaseRenewalProps) {
  const leaseData = useQuery(api.leases.getLeaseById, { id: leaseId });
  const renewLease = useMutation(api.leases.renewLease);

  const [formData, setFormData] = useState<RenewalFormData>({
    newEndDate: '',
    newMonthlyRent: '',
    noticePeriod: '',
    lateFeePercentage: '',
    gracePeriod: '',
    renewalOption: true,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when lease data is loaded
  React.useEffect(() => {
    if (leaseData?.lease) {
      const { lease } = leaseData;
      // Calculate default new end date (1 year from current end date)
      const defaultNewEndDate = new Date(lease.endDate + (365 * 24 * 60 * 60 * 1000));
      
      setFormData({
        newEndDate: defaultNewEndDate.toISOString().split('T')[0],
        newMonthlyRent: lease.monthlyRent.toString(),
        noticePeriod: lease.terms.noticePeriod.toString(),
        lateFeePercentage: lease.terms.lateFeePercentage.toString(),
        gracePeriod: lease.terms.gracePeriod.toString(),
        renewalOption: lease.terms.renewalOption,
      });
    }
  }, [leaseData]);

  if (!leaseData) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { lease, property, unit, tenant } = leaseData;

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.newEndDate) {
      newErrors.newEndDate = 'New end date is required';
    } else {
      const newEndDate = new Date(formData.newEndDate);
      const currentEndDate = new Date(lease.endDate);
      if (newEndDate <= currentEndDate) {
        newErrors.newEndDate = 'New end date must be after current end date';
      }
    }

    if (!formData.newMonthlyRent) {
      newErrors.newMonthlyRent = 'Monthly rent is required';
    } else if (isNaN(Number(formData.newMonthlyRent)) || Number(formData.newMonthlyRent) <= 0) {
      newErrors.newMonthlyRent = 'Monthly rent must be a positive number';
    }

    if (formData.noticePeriod && (isNaN(Number(formData.noticePeriod)) || Number(formData.noticePeriod) < 0)) {
      newErrors.noticePeriod = 'Notice period must be a non-negative number';
    }

    if (formData.lateFeePercentage && (isNaN(Number(formData.lateFeePercentage)) || Number(formData.lateFeePercentage) < 0 || Number(formData.lateFeePercentage) > 100)) {
      newErrors.lateFeePercentage = 'Late fee percentage must be between 0 and 100';
    }

    if (formData.gracePeriod && (isNaN(Number(formData.gracePeriod)) || Number(formData.gracePeriod) < 0)) {
      newErrors.gracePeriod = 'Grace period must be a non-negative number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await renewLease({
        id: leaseId,
        newEndDate: new Date(formData.newEndDate).getTime(),
        newMonthlyRent: Number(formData.newMonthlyRent),
        newTerms: {
          noticePeriod: Number(formData.noticePeriod),
          lateFeePercentage: Number(formData.lateFeePercentage),
          gracePeriod: Number(formData.gracePeriod),
          renewalOption: formData.renewalOption,
        },
      });

      onSuccess?.();
    } catch (error) {
      console.error('Error renewing lease:', error);
      setErrors({ submit: error instanceof Error ? error.message : 'Failed to renew lease' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof RenewalFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const calculateRentIncrease = () => {
    const currentRent = lease.monthlyRent;
    const newRent = Number(formData.newMonthlyRent) || 0;
    const increase = newRent - currentRent;
    const percentage = currentRent > 0 ? (increase / currentRent) * 100 : 0;
    return { increase, percentage };
  };

  const rentChange = calculateRentIncrease();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-2xl font-bold">Renew Lease</h2>
          <p className="text-gray-600">Lease #{lease._id.slice(-8)}</p>
        </div>
      </div>

      {/* Current Lease Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Current Lease Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-600">Current End Date</p>
              <p className="font-semibold">{formatDate(lease.endDate)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Current Monthly Rent</p>
              <p className="font-semibold">{formatCurrency(lease.monthlyRent)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Property & Unit</p>
              <p className="font-semibold">
                {property?.name} - {unit?.unitNumber}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Renewal Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            Renewal Terms
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* New End Date and Rent */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="newEndDate">New End Date *</Label>
                <div className="relative">
                  <Input
                    id="newEndDate"
                    type="date"
                    value={formData.newEndDate}
                    onChange={(e) => handleInputChange('newEndDate', e.target.value)}
                    className="pl-10"
                  />
                  <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                </div>
                {errors.newEndDate && <p className="text-sm text-red-500">{errors.newEndDate}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="newMonthlyRent">New Monthly Rent *</Label>
                <div className="relative">
                  <Input
                    id="newMonthlyRent"
                    type="number"
                    step="0.01"
                    value={formData.newMonthlyRent}
                    onChange={(e) => handleInputChange('newMonthlyRent', e.target.value)}
                    className="pl-10"
                    placeholder="0.00"
                  />
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                </div>
                {errors.newMonthlyRent && <p className="text-sm text-red-500">{errors.newMonthlyRent}</p>}
                
                {/* Rent Change Indicator */}
                {formData.newMonthlyRent && (
                  <div className={`text-sm ${rentChange.increase > 0 ? 'text-red-600' : rentChange.increase < 0 ? 'text-green-600' : 'text-gray-600'}`}>
                    {rentChange.increase > 0 ? '+' : ''}{formatCurrency(rentChange.increase)} 
                    ({rentChange.percentage > 0 ? '+' : ''}{rentChange.percentage.toFixed(1)}%)
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* Updated Terms */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Updated Lease Terms</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="noticePeriod">Notice Period (days)</Label>
                  <Input
                    id="noticePeriod"
                    type="number"
                    value={formData.noticePeriod}
                    onChange={(e) => handleInputChange('noticePeriod', e.target.value)}
                    placeholder="30"
                  />
                  {errors.noticePeriod && <p className="text-sm text-red-500">{errors.noticePeriod}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lateFeePercentage">Late Fee (%)</Label>
                  <Input
                    id="lateFeePercentage"
                    type="number"
                    step="0.1"
                    value={formData.lateFeePercentage}
                    onChange={(e) => handleInputChange('lateFeePercentage', e.target.value)}
                    placeholder="5"
                  />
                  {errors.lateFeePercentage && <p className="text-sm text-red-500">{errors.lateFeePercentage}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gracePeriod">Grace Period (days)</Label>
                  <Input
                    id="gracePeriod"
                    type="number"
                    value={formData.gracePeriod}
                    onChange={(e) => handleInputChange('gracePeriod', e.target.value)}
                    placeholder="5"
                  />
                  {errors.gracePeriod && <p className="text-sm text-red-500">{errors.gracePeriod}</p>}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  id="renewalOption"
                  type="checkbox"
                  checked={formData.renewalOption}
                  onChange={(e) => handleInputChange('renewalOption', e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="renewalOption">Allow future lease renewals</Label>
              </div>
            </div>

            {/* Renewal Summary */}
            {formData.newEndDate && formData.newMonthlyRent && (
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="pt-6">
                  <h4 className="font-semibold mb-3">Renewal Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p><strong>Extension Period:</strong> {Math.ceil((new Date(formData.newEndDate).getTime() - lease.endDate) / (1000 * 60 * 60 * 24))} days</p>
                      <p><strong>New Lease Duration:</strong> {Math.ceil((new Date(formData.newEndDate).getTime() - lease.startDate) / (1000 * 60 * 60 * 24))} days total</p>
                    </div>
                    <div>
                      <p><strong>Monthly Rent Change:</strong> {formatCurrency(rentChange.increase)} ({rentChange.percentage.toFixed(1)}%)</p>
                      <p><strong>Additional Revenue:</strong> {formatCurrency(rentChange.increase * Math.ceil((new Date(formData.newEndDate).getTime() - lease.endDate) / (1000 * 60 * 60 * 24 * 30)))}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Warning for significant rent increases */}
            {rentChange.percentage > 10 && (
              <div className="flex items-center gap-2 p-3 bg-yellow-100 border border-yellow-200 rounded-md">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <p className="text-sm text-yellow-800">
                  <strong>High rent increase detected:</strong> Consider tenant retention and market rates before proceeding.
                </p>
              </div>
            )}

            {/* Error Display */}
            {errors.submit && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{errors.submit}</p>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-3">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Renewing...' : 'Renew Lease'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}