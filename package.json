{"name": "estate-pulse", "version": "1.0.0", "description": "Cross-platform property management desktop application", "main": "dist/main/main.js", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:electron\"", "dev:renderer": "vite --config vite.renderer.config.ts", "dev:electron": "npm run build:main && npm run build:preload && electron dist/main/main.cjs", "build": "npm run build:convex && npm run build:electron", "build:production": "NODE_ENV=production npm run build:convex && npm run build:electron", "build:convex": "convex deploy", "build:electron": "npm run build:main && npm run build:preload && npm run build:renderer", "build:renderer": "vite build --config vite.renderer.config.ts", "build:main": "vite build --config vite.main.config.ts", "build:preload": "vite build --config vite.preload.config.ts", "pack": "npm run build:electron && electron-builder --dir", "dist": "npm run build:electron && electron-builder", "dist:win": "npm run build:electron && electron-builder --win", "dist:mac": "npm run build:electron && electron-builder --mac", "dist:linux": "npm run build:electron && electron-builder --linux", "publish": "npm run build:electron && electron-builder --publish=always", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "test": "vitest", "test:run": "vitest run", "test:unit": "vitest run --reporter=verbose", "test:integration": "vitest run tests/integration", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:performance": "k6 run tests/performance/load-test.js", "test:stress": "k6 run tests/performance/stress-test.js", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "playwright:install": "playwright install"}, "keywords": ["property-management", "electron", "react", "convex", "real-estate"], "author": "EstatePulse Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.55.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/dompurify": "^3.0.5", "@types/jest": "^30.0.0", "@types/node": "^20.10.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "convex-test": "^0.0.38", "electron": "^28.3.3", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "idb": "^8.0.3", "jsdom": "^26.1.0", "k6": "^0.0.0", "playwright": "^1.55.0", "postcss": "^8.4.32", "react-router-dom": "^7.8.2", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-electron": "^0.28.0", "vite-plugin-electron-renderer": "^0.14.5", "vitest": "^3.2.4"}, "dependencies": {"@convex-dev/auth": "^0.0.88", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.2.8", "@sentry/electron": "^7.0.0", "@sentry/react": "^10.10.0", "@sentry/tracing": "^7.120.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.8.1", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "electron-log": "^5.4.3", "electron-updater": "^6.6.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.303.0", "posthog-js": "^1.261.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.62.0", "recharts": "^3.1.2", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^4.1.5", "zustand": "^4.4.7"}}