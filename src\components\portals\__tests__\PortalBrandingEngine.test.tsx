// import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ConvexProvider } from 'convex/react';
import { ConvexReactClient } from 'convex/react';
import { PortalBrandingEngine } from '../PortalBrandingEngine';

// Mock Convex client
const mockConvexClient = new ConvexReactClient('https://test.convex.cloud');

// Mock the useQuery and useMutation hooks
const mockUseQuery = vi.fn();
const mockUseMutation = vi.fn();

vi.mock('convex/react', async () => {
  const actual = await vi.importActual('convex/react');
  return {
    ...actual,
    useQuery: mockUseQuery,
    useMutation: mockUseMutation,
  };
});

// Mock the toast hook
vi.mock('../../ui/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

const mockProperty = {
  _id: 'property123' as any,
  name: 'Test Property',
  type: 'residential' as const,
  address: {
    street: '123 Test St',
    city: 'Test City',
    state: 'Test State',
    country: 'Test Country',
    postalCode: '12345',
  },
  ownerId: 'owner123' as any,
  branding: {
    primaryColor: '#3b82f6',
    secondaryColor: '#64748b',
    customDomain: undefined,
  },
  settings: {
    currency: 'USD',
    timezone: 'UTC',
    language: 'en',
    autoRentReminders: true,
    maintenanceSLA: 24,
  },
  isActive: true,
  createdAt: Date.now(),
  updatedAt: Date.now(),
};

const mockPortal = {
  _id: 'portal123' as any,
  propertyId: 'property123' as any,
  name: 'Test Portal',
  subdomain: 'test-property',
  customDomain: undefined,
  branding: {
    logo: undefined,
    favicon: undefined,
    primaryColor: '#3b82f6',
    secondaryColor: '#64748b',
    accentColor: '#10b981',
    backgroundColor: '#ffffff',
    textColor: '#1f2937',
    fontFamily: 'Inter, sans-serif',
    customCSS: undefined,
  },
  theme: {
    layout: 'modern' as const,
    headerStyle: 'fixed' as const,
    sidebarStyle: 'expanded' as const,
    cardStyle: 'elevated' as const,
    borderRadius: 'medium' as const,
  },
  features: {
    paymentPortal: true,
    maintenanceRequests: true,
    documentAccess: true,
    leaseInformation: true,
    communicationCenter: true,
    announcementsBoard: true,
  },
  customization: {
    welcomeMessage: undefined,
    footerText: undefined,
    contactInfo: undefined,
    socialLinks: undefined,
    customPages: [],
  },
  seoSettings: {
    title: 'Test Portal',
    description: 'Test portal description',
    keywords: ['test'],
    ogImage: undefined,
  },
  analytics: {
    googleAnalyticsId: undefined,
    facebookPixelId: undefined,
    customTrackingCode: undefined,
  },
  isActive: true,
  isPublished: false,
  createdAt: Date.now(),
  updatedAt: Date.now(),
};

describe('PortalBrandingEngine', () => {
  const mockCreatePortal = vi.fn();
  const mockUpdateBranding = vi.fn();
  const mockUpdateTheme = vi.fn();
  const mockUpdateCustomDomain = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseQuery.mockImplementation((query: any, _args?: any) => {
      if (query.toString().includes('getPortalByProperty')) {
        return mockPortal;
      }
      if (query.toString().includes('getById')) {
        return mockProperty;
      }
      if (query.toString().includes('generatePortalCSS')) {
        return ':root { --primary-color: #3b82f6; }';
      }
      if (query.toString().includes('checkSubdomainAvailability')) {
        return true;
      }
      return undefined;
    });

    mockUseMutation.mockImplementation((mutation: any) => {
      if (mutation.toString().includes('createPortal')) {
        return mockCreatePortal;
      }
      if (mutation.toString().includes('updatePortalBranding')) {
        return mockUpdateBranding;
      }
      if (mutation.toString().includes('updatePortalTheme')) {
        return mockUpdateTheme;
      }
      if (mutation.toString().includes('updateCustomDomain')) {
        return mockUpdateCustomDomain;
      }
      return vi.fn();
    });
  });

  const renderComponent = () => {
    return render(
      <ConvexProvider client={mockConvexClient}>
        <PortalBrandingEngine propertyId="property123" as any />
      </ConvexProvider>
    );
  };

  it('renders the branding engine interface', () => {
    renderComponent();
    
    expect(screen.getByText('Portal Branding & Customization')).toBeInTheDocument();
    expect(screen.getByText('Customize your tenant portal\'s appearance and branding')).toBeInTheDocument();
  });

  it('displays existing portal data when portal exists', () => {
    renderComponent();
    
    // Check if the portal name is displayed
    expect(screen.getByDisplayValue('Test Portal')).toBeInTheDocument();
    expect(screen.getByDisplayValue('test-property')).toBeInTheDocument();
  });

  it('allows color customization', async () => {
    renderComponent();
    
    // Click on the branding tab (should be active by default)
    const primaryColorInput = screen.getByDisplayValue('#3b82f6');
    expect(primaryColorInput).toBeInTheDocument();
    
    // Change the primary color
    fireEvent.change(primaryColorInput, { target: { value: '#ff0000' } });
    expect(primaryColorInput).toHaveValue('#ff0000');
  });

  it('allows font family selection', async () => {
    renderComponent();
    
    // Find the font family select
    const fontSelect = screen.getByRole('combobox');
    expect(fontSelect).toBeInTheDocument();
  });

  it('saves branding changes', async () => {
    renderComponent();
    
    // Change a color
    const primaryColorInput = screen.getByDisplayValue('#3b82f6');
    fireEvent.change(primaryColorInput, { target: { value: '#ff0000' } });
    
    // Click save button
    const saveButton = screen.getByText('Save Branding');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(mockUpdateBranding).toHaveBeenCalledWith({
        portalId: 'portal123',
        branding: expect.objectContaining({
          primaryColor: '#ff0000',
        }),
      });
    });
  });

  it('switches between preview and edit modes', () => {
    renderComponent();
    
    // Click preview button
    const previewButton = screen.getByText('Preview');
    fireEvent.click(previewButton);
    
    // Should show preview content
    expect(screen.getByText('Portal Preview')).toBeInTheDocument();
    
    // Click edit button
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    // Should show edit interface again
    expect(screen.getByText('Branding')).toBeInTheDocument();
  });

  it('validates subdomain availability', async () => {
    renderComponent();
    
    const subdomainInput = screen.getByDisplayValue('test-property');
    fireEvent.change(subdomainInput, { target: { value: 'new-subdomain' } });
    
    await waitFor(() => {
      expect(screen.getByText('✓ Available')).toBeInTheDocument();
    });
  });

  it('handles theme changes', async () => {
    renderComponent();
    
    // Switch to theme tab
    const themeTab = screen.getByText('Theme');
    fireEvent.click(themeTab);
    
    // Save theme button should be present
    expect(screen.getByText('Save Theme')).toBeInTheDocument();
  });

  it('handles domain configuration', async () => {
    renderComponent();
    
    // Switch to domain tab
    const domainTab = screen.getByText('Domain');
    fireEvent.click(domainTab);
    
    // Domain settings should be present
    expect(screen.getByText('Save Domain Settings')).toBeInTheDocument();
  });

  it('handles custom CSS', async () => {
    renderComponent();
    
    // Switch to advanced tab
    const advancedTab = screen.getByText('Advanced');
    fireEvent.click(advancedTab);
    
    // Custom CSS textarea should be present
    const cssTextarea = screen.getByPlaceholderText(/Add your custom CSS here/);
    expect(cssTextarea).toBeInTheDocument();
  });

  it('creates new portal when none exists', async () => {
    // Mock no existing portal
    mockUseQuery.mockImplementation((query: any) => {
      if (query.toString().includes('getPortalByProperty')) {
        return null; // No portal exists
      }
      if (query.toString().includes('getById')) {
        return mockProperty;
      }
      return undefined;
    });

    renderComponent();
    
    // Should show create portal interface
    const saveButton = screen.getByText('Save Branding');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(mockCreatePortal).toHaveBeenCalled();
    });
  });

  it('displays portal status badge', () => {
    renderComponent();
    
    // Should show draft status since isPublished is false
    expect(screen.getByText('Draft')).toBeInTheDocument();
  });

  it('generates CSS preview', () => {
    renderComponent();
    
    // Switch to preview mode
    const previewButton = screen.getByText('Preview');
    fireEvent.click(previewButton);
    
    // Should show generated CSS
    expect(screen.getByText('Generated CSS')).toBeInTheDocument();
  });
});