import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { Switch } from '../ui/switch';
import { useToast } from '../ui/use-toast';
import { 
  FileText, 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  MessageSquare, 
  Phone, 
  Mail,
  Eye,
  Save,
  X
} from 'lucide-react';

interface MessageTemplatesProps {
  propertyId?: Id<"properties">;
  userId: Id<"users">;
}

interface Template {
  _id: Id<"messageTemplates">;
  name: string;
  type: 'sms' | 'whatsapp' | 'email';
  category: 'payment_reminder' | 'maintenance_update' | 'lease_expiry' | 'welcome' | 'general' | 'emergency';
  subject?: string;
  content: string;
  variables: string[];
  isActive: boolean;
  propertyId?: Id<"properties">;
  createdBy: Id<"users">;
  createdAt: number;
  updatedAt: number;
}

export function MessageTemplates({ propertyId, userId }: MessageTemplatesProps) {
  const { toast } = useToast();
  const [isCreating, setIsCreating] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [previewTemplate, setPreviewTemplate] = useState<Template | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    type: 'sms' as 'sms' | 'whatsapp' | 'email',
    category: 'general' as Template['category'],
    subject: '',
    content: '',
    variables: [] as string[],
    isActive: true,
  });

  // Queries
  const templates = useQuery(api.communications.getMessageTemplates, {
    propertyId,
    type: undefined, // Get all types
  });

  // Mutations
  const createTemplate = useMutation(api.communications.createMessageTemplate);
  const updateTemplate = useMutation(api.communications.updateMessageTemplate);
  const deleteTemplate = useMutation(api.communications.deleteMessageTemplate);

  const handleCreateTemplate = async () => {
    if (!formData.name || !formData.content) {
      toast({
        title: "Missing Information",
        description: "Please provide template name and content",
        variant: "destructive",
      });
      return;
    }

    try {
      await createTemplate({
        name: formData.name,
        type: formData.type,
        category: formData.category,
        subject: formData.type === 'email' ? formData.subject : undefined,
        content: formData.content,
        variables: extractVariables(formData.content),
        isActive: formData.isActive,
        propertyId,
        createdBy: userId,
      });

      toast({
        title: "Template Created",
        description: "Message template created successfully",
      });

      resetForm();
      setIsCreating(false);
    } catch (error) {
      toast({
        title: "Creation Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleUpdateTemplate = async () => {
    if (!editingTemplate || !formData.name || !formData.content) {
      return;
    }

    try {
      await updateTemplate({
        templateId: editingTemplate._id,
        name: formData.name,
        type: formData.type,
        category: formData.category,
        subject: formData.type === 'email' ? formData.subject : undefined,
        content: formData.content,
        variables: extractVariables(formData.content),
        isActive: formData.isActive,
      });

      toast({
        title: "Template Updated",
        description: "Message template updated successfully",
      });

      resetForm();
      setEditingTemplate(null);
    } catch (error) {
      toast({
        title: "Update Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleDeleteTemplate = async (templateId: Id<"messageTemplates">) => {
    try {
      await deleteTemplate({ templateId });
      toast({
        title: "Template Deleted",
        description: "Message template deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Deletion Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleEditTemplate = (template: Template) => {
    setFormData({
      name: template.name,
      type: template.type,
      category: template.category,
      subject: template.subject || '',
      content: template.content,
      variables: template.variables,
      isActive: template.isActive,
    });
    setEditingTemplate(template);
    setIsCreating(true);
  };

  const handleDuplicateTemplate = (template: Template) => {
    setFormData({
      name: `${template.name} (Copy)`,
      type: template.type,
      category: template.category,
      subject: template.subject || '',
      content: template.content,
      variables: template.variables,
      isActive: template.isActive,
    });
    setIsCreating(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      type: 'sms',
      category: 'general',
      subject: '',
      content: '',
      variables: [],
      isActive: true,
    });
  };

  const extractVariables = (content: string): string[] => {
    const matches = content.match(/\{\{(\w+)\}\}/g);
    if (!matches) return [];
    return [...new Set(matches.map(match => match.slice(2, -2)))];
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'sms':
        return <Phone className="h-4 w-4" />;
      case 'whatsapp':
        return <MessageSquare className="h-4 w-4" />;
      case 'email':
        return <Mail className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'payment_reminder':
        return 'bg-red-100 text-red-800';
      case 'maintenance_update':
        return 'bg-blue-100 text-blue-800';
      case 'lease_expiry':
        return 'bg-yellow-100 text-yellow-800';
      case 'welcome':
        return 'bg-green-100 text-green-800';
      case 'emergency':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderPreview = (template: Template) => {
    let previewContent = template.content;
    
    // Replace common variables with sample data
    const sampleData = {
      tenant_name: 'John Doe',
      property_name: 'Sunset Apartments',
      unit_number: 'A-101',
      amount: '50,000',
      due_date: '2024-02-15',
      ticket_id: 'MT-001',
      vendor_name: 'ABC Repairs',
    };

    Object.entries(sampleData).forEach(([key, value]) => {
      previewContent = previewContent.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), value);
    });

    return previewContent;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <FileText className="h-6 w-6" />
          <h2 className="text-2xl font-bold">Message Templates</h2>
        </div>
        <Button onClick={() => setIsCreating(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Template
        </Button>
      </div>

      {/* Create/Edit Form */}
      {isCreating && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingTemplate ? 'Edit Template' : 'Create New Template'}
            </CardTitle>
            <CardDescription>
              Create reusable message templates with dynamic variables
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Template Name</label>
                <Input
                  placeholder="e.g., Payment Reminder"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Type</label>
                <Select 
                  value={formData.type} 
                  onValueChange={(value: 'sms' | 'whatsapp' | 'email') => 
                    setFormData(prev => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sms">SMS</SelectItem>
                    <SelectItem value="whatsapp">WhatsApp</SelectItem>
                    <SelectItem value="email">Email</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Category</label>
                <Select 
                  value={formData.category} 
                  onValueChange={(value: Template['category']) => 
                    setFormData(prev => ({ ...prev, category: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="payment_reminder">Payment Reminder</SelectItem>
                    <SelectItem value="maintenance_update">Maintenance Update</SelectItem>
                    <SelectItem value="lease_expiry">Lease Expiry</SelectItem>
                    <SelectItem value="welcome">Welcome</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="emergency">Emergency</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                />
                <span className="text-sm">Active</span>
              </div>
            </div>

            {formData.type === 'email' && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Subject</label>
                <Input
                  placeholder="Email subject line"
                  value={formData.subject}
                  onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
                />
              </div>
            )}

            <div className="space-y-2">
              <label className="text-sm font-medium">Content</label>
              <Textarea
                placeholder="Enter your message template here. Use {{variable_name}} for dynamic content."
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                rows={6}
              />
              <div className="text-xs text-gray-500">
                Available variables: {{tenant_name}}, {{property_name}}, {{unit_number}}, {{amount}}, {{due_date}}, {{ticket_id}}, {{vendor_name}}
              </div>
            </div>

            {extractVariables(formData.content).length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Detected Variables</label>
                <div className="flex flex-wrap gap-2">
                  {extractVariables(formData.content).map((variable) => (
                    <Badge key={variable} variant="outline">
                      {variable}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-2">
              <Button 
                variant="outline" 
                onClick={() => {
                  setIsCreating(false);
                  setEditingTemplate(null);
                  resetForm();
                }}
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button onClick={editingTemplate ? handleUpdateTemplate : handleCreateTemplate}>
                <Save className="h-4 w-4 mr-2" />
                {editingTemplate ? 'Update' : 'Create'} Template
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Templates List */}
      <div className="grid gap-4">
        {templates === undefined ? (
          <div className="text-center py-8">Loading templates...</div>
        ) : templates.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No templates created yet
          </div>
        ) : (
          templates.map((template) => (
            <Card key={template._id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(template.type)}
                      <span className="font-semibold">{template.name}</span>
                    </div>
                    <Badge className={getCategoryColor(template.category)}>
                      {template.category.replace('_', ' ')}
                    </Badge>
                    {!template.isActive && (
                      <Badge variant="secondary">Inactive</Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setPreviewTemplate(template)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDuplicateTemplate(template)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditTemplate(template)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteTemplate(template._id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {template.subject && (
                    <div className="text-sm">
                      <span className="font-medium">Subject:</span> {template.subject}
                    </div>
                  )}
                  <div className="text-sm text-gray-600 line-clamp-2">
                    {template.content}
                  </div>
                  {template.variables.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {template.variables.map((variable) => (
                        <Badge key={variable} variant="outline" className="text-xs">
                          {variable}
                        </Badge>
                      ))}
                    </div>
                  )}
                  <div className="text-xs text-gray-500">
                    Created {new Date(template.createdAt).toLocaleDateString()}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Preview Modal */}
      {previewTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Template Preview</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setPreviewTemplate(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                {getTypeIcon(previewTemplate.type)}
                <span className="font-semibold">{previewTemplate.name}</span>
                <Badge className={getCategoryColor(previewTemplate.category)}>
                  {previewTemplate.category.replace('_', ' ')}
                </Badge>
              </div>
              
              {previewTemplate.subject && (
                <div>
                  <div className="text-sm font-medium mb-1">Subject:</div>
                  <div className="p-3 bg-gray-50 rounded border">
                    {previewTemplate.subject}
                  </div>
                </div>
              )}
              
              <div>
                <div className="text-sm font-medium mb-1">Content Preview:</div>
                <div className="p-3 bg-gray-50 rounded border whitespace-pre-wrap">
                  {renderPreview(previewTemplate)}
                </div>
              </div>
              
              <div>
                <div className="text-sm font-medium mb-1">Raw Template:</div>
                <div className="p-3 bg-gray-100 rounded border text-sm font-mono whitespace-pre-wrap">
                  {previewTemplate.content}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}