import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { useToast } from '../ui/use-toast';
import { 
  Code, 
  Eye, 
  Save, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  Copy,
  Download
} from 'lucide-react';

interface CSSCustomizerProps {
  portalId: Id<"portals">;
}

const CSS_TEMPLATES = {
  modern: `/* Modern Theme Customizations */
.portal-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  backdrop-filter: blur(10px);
}

.portal-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}`,

  minimal: `/* Minimal Theme Customizations */
.portal-header {
  background: var(--background-color);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.portal-card {
  background: var(--background-color);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: none;
}

.btn-primary {
  background: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 4px;
}

.btn-secondary {
  background: transparent;
  border: 1px solid var(--secondary-color);
  color: var(--secondary-color);
}`,

  classic: `/* Classic Theme Customizations */
.portal-header {
  background: var(--primary-color);
  border-bottom: 3px solid var(--accent-color);
}

.portal-card {
  background: var(--background-color);
  border: 2px solid var(--secondary-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: var(--primary-color);
  border: 2px solid var(--primary-color);
  border-radius: 6px;
  font-weight: 600;
}

.btn-primary:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
}`
};

const CSS_VARIABLES_REFERENCE = `/* Available CSS Variables */
:root {
  --primary-color: /* Your primary brand color */
  --secondary-color: /* Your secondary color */
  --accent-color: /* Your accent color */
  --background-color: /* Main background color */
  --text-color: /* Primary text color */
  --font-family: /* Selected font family */
  --border-radius: /* Border radius setting */
}

/* Common Classes */
.portal-header { /* Main header */}
.portal-sidebar { /* Sidebar navigation */}
.portal-card { /* Content cards */}
.portal-footer { /* Footer */}
.btn-primary { /* Primary buttons */}
.btn-secondary { /* Secondary buttons */}
.btn-accent { /* Accent buttons */}`;

export const CSSCustomizer: React.FC<CSSCustomizerProps> = ({ portalId }) => {
  const { toast } = useToast();
  const [customCSS, setCustomCSS] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('editor');

  const portal = useQuery(api.portals.getPortalById, { portalId });
  const generatedCSS = useQuery(api.portals.generatePortalCSS, { portalId });
  const updateBranding = useMutation(api.portals.updatePortalBranding);

  useEffect(() => {
    if (portal?.branding.customCSS) {
      setCustomCSS(portal.branding.customCSS);
    }
  }, [portal]);

  const validateCSS = (css: string): string[] => {
    const errors: string[] = [];
    
    // Basic CSS validation
    const openBraces = (css.match(/{/g) || []).length;
    const closeBraces = (css.match(/}/g) || []).length;
    
    if (openBraces !== closeBraces) {
      errors.push('Mismatched braces: Check that all CSS rules are properly closed');
    }
    
    // Check for potentially dangerous CSS
    const dangerousPatterns = [
      /javascript:/i,
      /expression\(/i,
      /behavior:/i,
      /@import/i,
    ];
    
    dangerousPatterns.forEach(pattern => {
      if (pattern.test(css)) {
        errors.push('Potentially unsafe CSS detected. Please remove JavaScript or import statements.');
      }
    });
    
    return errors;
  };

  const handleValidateCSS = () => {
    setIsValidating(true);
    
    setTimeout(() => {
      const errors = validateCSS(customCSS);
      setValidationErrors(errors);
      setIsValidating(false);
      
      if (errors.length === 0) {
        toast({
          title: "CSS Valid",
          description: "Your CSS is valid and ready to use.",
        });
      } else {
        toast({
          title: "CSS Validation Failed",
          description: `Found ${errors.length} error(s) in your CSS.`,
          variant: "destructive",
        });
      }
    }, 1000);
  };

  const handleSaveCSS = async () => {
    const errors = validateCSS(customCSS);
    
    if (errors.length > 0) {
      setValidationErrors(errors);
      toast({
        title: "Cannot Save",
        description: "Please fix CSS validation errors before saving.",
        variant: "destructive",
      });
      return;
    }

    try {
      if (portal) {
        await updateBranding({
          portalId,
          branding: {
            ...portal.branding,
            customCSS,
          },
        });
        
        toast({
          title: "CSS Saved",
          description: "Your custom CSS has been successfully saved.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save custom CSS",
        variant: "destructive",
      });
    }
  };

  const handleLoadTemplate = (template: keyof typeof CSS_TEMPLATES) => {
    setCustomCSS(CSS_TEMPLATES[template]);
    setValidationErrors([]);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "CSS copied to clipboard",
    });
  };

  const downloadCSS = () => {
    const blob = new Blob([customCSS], { type: 'text/css' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `portal-${portalId}-custom.css`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">CSS Customizer</h3>
          <p className="text-sm text-muted-foreground">
            Add custom CSS to further customize your portal's appearance
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {validationErrors.length === 0 && customCSS && (
            <Badge variant="default" className="flex items-center">
              <CheckCircle className="w-3 h-3 mr-1" />
              Valid
            </Badge>
          )}
          {validationErrors.length > 0 && (
            <Badge variant="destructive" className="flex items-center">
              <AlertTriangle className="w-3 h-3 mr-1" />
              {validationErrors.length} Error(s)
            </Badge>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="editor">
            <Code className="w-4 h-4 mr-2" />
            Editor
          </TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="reference">Reference</TabsTrigger>
          <TabsTrigger value="preview">
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </TabsTrigger>
        </TabsList>

        <TabsContent value="editor" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Custom CSS Editor</CardTitle>
              <CardDescription>
                Write custom CSS to override default styles. Use CSS variables for consistency.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="customCSS">CSS Code</Label>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleValidateCSS}
                      disabled={isValidating}
                    >
                      {isValidating ? (
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <CheckCircle className="w-4 h-4 mr-2" />
                      )}
                      Validate
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(customCSS)}
                    >
                      <Copy className="w-4 h-4 mr-2" />
                      Copy
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={downloadCSS}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </div>
                <Textarea
                  id="customCSS"
                  value={customCSS}
                  onChange={(e) => setCustomCSS(e.target.value)}
                  placeholder="/* Add your custom CSS here */
.portal-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.portal-card {
  border-radius: var(--border-radius);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}"
                  rows={20}
                  className="font-mono text-sm"
                />
              </div>

              {validationErrors.length > 0 && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <p className="font-medium">CSS Validation Errors:</p>
                      <ul className="list-disc list-inside space-y-1">
                        {validationErrors.map((error, index) => (
                          <li key={index} className="text-sm">{error}</li>
                        ))}
                      </ul>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={handleValidateCSS}>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Validate CSS
                </Button>
                <Button onClick={handleSaveCSS} disabled={validationErrors.length > 0}>
                  <Save className="w-4 h-4 mr-2" />
                  Save CSS
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>CSS Templates</CardTitle>
              <CardDescription>
                Start with pre-built CSS templates for common styling patterns
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(CSS_TEMPLATES).map(([name, css]) => (
                <div key={name} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium capitalize">{name} Theme</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleLoadTemplate(name as keyof typeof CSS_TEMPLATES)}
                    >
                      Load Template
                    </Button>
                  </div>
                  <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-32">
                    {css}
                  </pre>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reference" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>CSS Variables Reference</CardTitle>
              <CardDescription>
                Available CSS variables and common classes for customization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded text-sm overflow-auto">
                {CSS_VARIABLES_REFERENCE}
              </pre>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Generated CSS</CardTitle>
              <CardDescription>
                CSS automatically generated from your branding settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              {generatedCSS ? (
                <pre className="bg-muted p-4 rounded text-sm overflow-auto max-h-96">
                  {generatedCSS}
                </pre>
              ) : (
                <p className="text-muted-foreground">Loading generated CSS...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>CSS Preview</CardTitle>
              <CardDescription>
                Preview how your custom CSS affects the portal appearance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-6 space-y-4">
                <style dangerouslySetInnerHTML={{ __html: customCSS }} />
                
                {/* Preview Components */}
                <div className="portal-header p-4 rounded-lg">
                  <h3 className="text-lg font-semibold text-white">Portal Header</h3>
                  <p className="text-white/80">This is how your header will look</p>
                </div>

                <div className="portal-card p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Portal Card</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    This is how content cards will appear in your portal.
                  </p>
                  <div className="flex space-x-2">
                    <button className="btn-primary px-4 py-2 rounded text-white">
                      Primary Button
                    </button>
                    <button className="btn-secondary px-4 py-2 rounded border">
                      Secondary Button
                    </button>
                  </div>
                </div>

                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    This is a preview of your custom styles. The actual portal may look 
                    slightly different due to additional base styles and responsive design.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};