import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

interface UnitDetailsProps {
  unitId: Id<"units">;
  onEdit?: () => void;
  onBack?: () => void;
}

export const UnitDetails: React.FC<UnitDetailsProps> = ({
  unitId,
  onEdit,
  onBack,
}) => {
  const unit = useQuery(api.units.getUnitById, { id: unitId });
  const updateUnitStatus = useMutation(api.units.updateUnitStatus);
  const deleteUnit = useMutation(api.units.deleteUnit);
  
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleStatusChange = async (newStatus: 'vacant' | 'occupied' | 'maintenance') => {
    try {
      await updateUnitStatus({ id: unitId, status: newStatus });
    } catch (error) {
      console.error('Error updating unit status:', error);
      alert('Failed to update unit status. Please try again.');
    }
  };

  const handleDelete = async () => {
    if (!showDeleteConfirm) {
      setShowDeleteConfirm(true);
      return;
    }

    setIsDeleting(true);
    try {
      await deleteUnit({ id: unitId });
      onBack?.();
    } catch (error) {
      console.error('Error deleting unit:', error);
      alert('Failed to delete unit. It may have active leases or maintenance tickets.');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  if (!unit) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'vacant':
        return 'bg-green-100 text-green-800';
      case 'occupied':
        return 'bg-blue-100 text-blue-800';
      case 'maintenance':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'apartment':
        return 'bg-purple-100 text-purple-800';
      case 'office':
        return 'bg-indigo-100 text-indigo-800';
      case 'retail':
        return 'bg-pink-100 text-pink-800';
      case 'parking':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button variant="outline" onClick={onBack}>
              ← Back
            </Button>
          )}
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Unit {unit.unitNumber}</h1>
            <p className="text-gray-600 capitalize">{unit.type} • {unit.size} sq ft</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span
            className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full capitalize ${getTypeColor(
              unit.type
            )}`}
          >
            {unit.type}
          </span>
          <Select
            value={unit.status}
            onValueChange={(value) => handleStatusChange(value as any)}
          >
            <SelectTrigger className={`${getStatusColor(unit.status)}`}>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="vacant">Vacant</SelectItem>
              <SelectItem value="occupied">Occupied</SelectItem>
              <SelectItem value="maintenance">Maintenance</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Unit Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold text-gray-900">Unit Number</h4>
              <p className="text-gray-600">{unit.unitNumber}</p>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900">Type</h4>
              <p className="text-gray-600 capitalize">{unit.type}</p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900">Size</h4>
              <p className="text-gray-600">{unit.size} square feet</p>
            </div>

            {unit.type === 'apartment' && (
              <>
                <div>
                  <h4 className="font-semibold text-gray-900">Bedrooms</h4>
                  <p className="text-gray-600">{unit.bedrooms || 'Not specified'}</p>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900">Bathrooms</h4>
                  <p className="text-gray-600">{unit.bathrooms || 'Not specified'}</p>
                </div>
              </>
            )}

            <div>
              <h4 className="font-semibold text-gray-900">Status</h4>
              <span
                className={`inline-flex px-2 py-1 text-sm font-semibold rounded-full capitalize ${getStatusColor(
                  unit.status
                )}`}
              >
                {unit.status}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Pricing Information */}
        <Card>
          <CardHeader>
            <CardTitle>Pricing</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold text-gray-900">Monthly Rent</h4>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(unit.rent)}</p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900">Security Deposit</h4>
              <p className="text-xl font-semibold text-gray-900">{formatCurrency(unit.deposit)}</p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900">Price per sq ft</h4>
              <p className="text-gray-600">
                {formatCurrency(Math.round(unit.rent / unit.size))} per sq ft
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Amenities */}
        {unit.amenities && unit.amenities.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Amenities</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {unit.amenities.map((amenity: any, index: number) => (
                  <span
                    key={index}
                    className="inline-flex px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full"
                  >
                    {amenity}
                  </span>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Description */}
        {unit.description && (
          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">{unit.description}</p>
            </CardContent>
          </Card>
        )}

        {/* Timestamps */}
        <Card>
          <CardHeader>
            <CardTitle>Timeline</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold text-gray-900">Created</h4>
              <p className="text-gray-600">
                {new Date(unit.createdAt).toLocaleDateString()} at{' '}
                {new Date(unit.createdAt).toLocaleTimeString()}
              </p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900">Last Updated</h4>
              <p className="text-gray-600">
                {new Date(unit.updatedAt).toLocaleDateString()} at{' '}
                {new Date(unit.updatedAt).toLocaleTimeString()}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {onEdit && (
              <Button onClick={onEdit} className="w-full">
                Edit Unit
              </Button>
            )}
            
            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirm(!showDeleteConfirm)}
              className="w-full text-red-600 border-red-300 hover:bg-red-50"
              disabled={isDeleting}
            >
              {showDeleteConfirm ? 'Cancel Delete' : 'Delete Unit'}
            </Button>

            {showDeleteConfirm && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-800 mb-3">
                  Are you sure you want to delete this unit? This action cannot be undone.
                  Units with active leases or open maintenance tickets cannot be deleted.
                </p>
                <Button
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="w-full bg-red-600 hover:bg-red-700"
                >
                  {isDeleting ? 'Deleting...' : 'Confirm Delete'}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};