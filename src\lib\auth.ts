import { api } from "../../convex/_generated/api";
import { ConvexReactClient } from "convex/react";

// Auth types
export interface User {
  _id: string;
  email: string;
  name: string;
  role: "owner" | "manager" | "vendor" | "tenant";
  propertyAccess: string[];
  kycStatus: "pending" | "verified" | "rejected";
  phone?: string;
  avatar?: string;
  isActive: boolean;
  lastLogin?: number;
  createdAt: number;
  updatedAt: number;
}

export interface AuthSession {
  sessionToken: string;
  user: User;
  expiresAt: number;
}

export interface Permission {
  resource: string;
  actions: ("create" | "read" | "update" | "delete")[];
}

// Auth service class
export class AuthService {
  private convex: ConvexReactClient;
  private sessionToken: string | null = null;
  private user: User | null = null;

  constructor(convex: ConvexReactClient) {
    this.convex = convex;
    this.loadSession();
  }

  // Load session from localStorage
  private loadSession() {
    try {
      const sessionData = localStorage.getItem('estate-pulse-session');
      if (sessionData) {
        const session: AuthSession = JSON.parse(sessionData);
        if (session.expiresAt > Date.now()) {
          this.sessionToken = session.sessionToken;
          this.user = session.user;
        } else {
          this.clearSession();
        }
      }
    } catch (error) {
      console.error('Error loading session:', error);
      this.clearSession();
    }
  }

  // Save session to localStorage
  private saveSession(session: AuthSession) {
    try {
      localStorage.setItem('estate-pulse-session', JSON.stringify(session));
      this.sessionToken = session.sessionToken;
      this.user = session.user;
    } catch (error) {
      console.error('Error saving session:', error);
    }
  }

  // Clear session
  private clearSession() {
    localStorage.removeItem('estate-pulse-session');
    this.sessionToken = null;
    this.user = null;
  }

  // Sign in user
  async signIn(email: string, password: string): Promise<AuthSession> {
    try {
      const result = await this.convex.mutation(api.auth.signIn, {
        email,
        password,
      });

      const session: AuthSession = {
        sessionToken: result.sessionToken,
        user: result.user,
        expiresAt: result.expiresAt,
      };

      this.saveSession(session);
      return session;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Sign in failed');
    }
  }

  // Sign up new user
  async signUp(
    email: string,
    password: string,
    name: string,
    role: "owner" | "manager" | "vendor" | "tenant",
    phone?: string
  ): Promise<AuthSession> {
    try {
      const result = await this.convex.mutation(api.auth.signUp, {
        email,
        password,
        name,
        role,
        phone,
      });

      if (!result.user) {
        throw new Error("Authentication failed - no user data");
      }

      const session: AuthSession = {
        sessionToken: result.sessionToken,
        user: result.user,
        expiresAt: result.expiresAt,
      };

      this.saveSession(session);
      return session;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Sign up failed');
    }
  }

  // Sign out user
  async signOut(): Promise<void> {
    try {
      if (this.sessionToken) {
        await this.convex.mutation(api.auth.signOut, {
          sessionToken: this.sessionToken,
        });
      }
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      this.clearSession();
    }
  }

  // Get current user
  getCurrentUser(): User | null {
    return this.user;
  }

  // Get session token
  getSessionToken(): string | null {
    return this.sessionToken;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.sessionToken !== null && this.user !== null;
  }

  // Verify session
  async verifySession(): Promise<boolean> {
    if (!this.sessionToken) {
      return false;
    }

    try {
      const result = await this.convex.query(api.auth.verifySession, {
        sessionToken: this.sessionToken,
      });

      if (result.isAuthenticated && result.user) {
        this.user = result.user as User;
        return true;
      } else {
        this.clearSession();
        return false;
      }
    } catch (error) {
      console.error('Error verifying session:', error);
      this.clearSession();
      return false;
    }
  }

  // Refresh token
  async refreshToken(): Promise<boolean> {
    if (!this.sessionToken) {
      return false;
    }

    try {
      const result = await this.convex.mutation(api.auth.refreshToken, {
        sessionToken: this.sessionToken,
      });

      if (result.success) {
        // Update session expiry
        const sessionData = localStorage.getItem('estate-pulse-session');
        if (sessionData) {
          const session: AuthSession = JSON.parse(sessionData);
          session.expiresAt = result.expiresAt;
          this.saveSession(session);
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error refreshing token:', error);
      this.clearSession();
      return false;
    }
  }

  // Change password
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    if (!this.sessionToken) {
      throw new Error('Not authenticated');
    }

    try {
      await this.convex.mutation(api.auth.changePassword, {
        sessionToken: this.sessionToken,
        currentPassword,
        newPassword,
      });
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Password change failed');
    }
  }
}

// RBAC utility functions
export class RBACService {
  private convex: ConvexReactClient;
  private authService: AuthService;

  constructor(convex: ConvexReactClient, authService: AuthService) {
    this.convex = convex;
    this.authService = authService;
  }

  // Get user permissions
  async getUserPermissions(userId?: string): Promise<Permission[]> {
    const sessionToken = this.authService.getSessionToken();
    if (!sessionToken) {
      throw new Error('Not authenticated');
    }

    try {
      return await this.convex.query(api.permissions.getUserPermissions, {
        sessionToken,
        userId: userId as any,
      });
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to get permissions');
    }
  }

  // Check if user has specific permission
  async hasPermission(
    resource: string,
    action: "create" | "read" | "update" | "delete",
    userId?: string
  ): Promise<boolean> {
    const sessionToken = this.authService.getSessionToken();
    if (!sessionToken) {
      return false;
    }

    try {
      return await this.convex.query(api.permissions.hasPermission, {
        sessionToken,
        userId: userId as any,
        resource,
        action,
      });
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  // Assign property access to user
  async assignPropertyAccess(userId: string, propertyId: string): Promise<void> {
    const sessionToken = this.authService.getSessionToken();
    if (!sessionToken) {
      throw new Error('Not authenticated');
    }

    try {
      await this.convex.mutation(api.permissions.assignPropertyAccess, {
        sessionToken,
        userId: userId as any,
        propertyId: propertyId as any,
      });
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to assign property access');
    }
  }

  // Remove property access from user
  async removePropertyAccess(userId: string, propertyId: string): Promise<void> {
    const sessionToken = this.authService.getSessionToken();
    if (!sessionToken) {
      throw new Error('Not authenticated');
    }

    try {
      await this.convex.mutation(api.permissions.removePropertyAccess, {
        sessionToken,
        userId: userId as any,
        propertyId: propertyId as any,
      });
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to remove property access');
    }
  }

  // Update user role
  async updateUserRole(userId: string, role: "owner" | "manager" | "vendor" | "tenant"): Promise<void> {
    const sessionToken = this.authService.getSessionToken();
    if (!sessionToken) {
      throw new Error('Not authenticated');
    }

    try {
      await this.convex.mutation(api.permissions.updateUserRole, {
        sessionToken,
        userId: userId as any,
        role,
      });
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to update user role');
    }
  }

  // Check if current user can perform action on resource
  canPerform(resource: string, action: "create" | "read" | "update" | "delete"): boolean {
    const user = this.authService.getCurrentUser();
    if (!user) {
      return false;
    }

    // Define role permissions (same as backend)
    const rolePermissions: Record<string, Permission[]> = {
      owner: [
        { resource: "properties", actions: ["create", "read", "update", "delete"] },
        { resource: "units", actions: ["create", "read", "update", "delete"] },
        { resource: "leases", actions: ["create", "read", "update", "delete"] },
        { resource: "tenants", actions: ["create", "read", "update", "delete"] },
        { resource: "vendors", actions: ["create", "read", "update", "delete"] },
        { resource: "managers", actions: ["create", "read", "update", "delete"] },
        { resource: "maintenance", actions: ["create", "read", "update", "delete"] },
        { resource: "payments", actions: ["create", "read", "update", "delete"] },
        { resource: "reports", actions: ["create", "read", "update", "delete"] },
        { resource: "settings", actions: ["create", "read", "update", "delete"] },
      ],
      manager: [
        { resource: "properties", actions: ["read", "update"] },
        { resource: "units", actions: ["create", "read", "update", "delete"] },
        { resource: "leases", actions: ["create", "read", "update", "delete"] },
        { resource: "tenants", actions: ["create", "read", "update"] },
        { resource: "vendors", actions: ["read", "update"] },
        { resource: "maintenance", actions: ["create", "read", "update", "delete"] },
        { resource: "payments", actions: ["read", "update"] },
        { resource: "reports", actions: ["read"] },
        { resource: "settings", actions: ["read"] },
      ],
      vendor: [
        { resource: "maintenance", actions: ["read", "update"] },
        { resource: "properties", actions: ["read"] },
        { resource: "units", actions: ["read"] },
      ],
      tenant: [
        { resource: "leases", actions: ["read"] },
        { resource: "payments", actions: ["create", "read"] },
        { resource: "maintenance", actions: ["create", "read"] },
        { resource: "units", actions: ["read"] },
      ],
    };

    const permissions = rolePermissions[user.role] || [];
    const resourcePermission = permissions.find(p => p.resource === resource);
    
    return resourcePermission ? resourcePermission.actions.includes(action) : false;
  }
}

// Permission checking hook for React components
export const usePermissions = (authService: AuthService, rbacService: RBACService) => {
  const user = authService.getCurrentUser();

  const canCreate = (resource: string) => rbacService.canPerform(resource, "create");
  const canRead = (resource: string) => rbacService.canPerform(resource, "read");
  const canUpdate = (resource: string) => rbacService.canPerform(resource, "update");
  const canDelete = (resource: string) => rbacService.canPerform(resource, "delete");

  const hasRole = (role: "owner" | "manager" | "vendor" | "tenant") => user?.role === role;
  const hasAnyRole = (roles: ("owner" | "manager" | "vendor" | "tenant")[]) => 
    user ? roles.includes(user.role) : false;

  return {
    user,
    canCreate,
    canRead,
    canUpdate,
    canDelete,
    hasRole,
    hasAnyRole,
  };
};