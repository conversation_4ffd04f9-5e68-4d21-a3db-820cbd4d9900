import SentryMonitoring, { PERFORMANCE_BUDGETS } from './sentry';
import PostHogAnalytics from './posthog';

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  context?: Record<string, any>;
}

export interface PerformanceBudget {
  metric: string;
  threshold: number;
  unit: string;
  alertOnExceed: boolean;
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private sentry: SentryMonitoring;
  private posthog: PostHogAnalytics;
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private budgets: Map<string, PerformanceBudget> = new Map();
  private observer?: PerformanceObserver;

  private constructor() {
    this.sentry = SentryMonitoring.getInstance();
    this.posthog = PostHogAnalytics.getInstance();
    this.setupDefaultBudgets();
    this.initializePerformanceObserver();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private setupDefaultBudgets(): void {
    // Page performance budgets
    this.setBudget('page-load', PERFORMANCE_BUDGETS.pageLoad, 'ms', true);
    this.setBudget('fcp', PERFORMANCE_BUDGETS.firstContentfulPaint, 'ms', true);
    this.setBudget('lcp', PERFORMANCE_BUDGETS.largestContentfulPaint, 'ms', true);
    
    // Database performance budgets
    this.setBudget('db-query', PERFORMANCE_BUDGETS.databaseQuery, 'ms', true);
    this.setBudget('complex-query', PERFORMANCE_BUDGETS.complexQuery, 'ms', true);
    
    // Memory budgets
    this.setBudget('memory-usage', PERFORMANCE_BUDGETS.memoryUsage, 'bytes', true);
  }

  private initializePerformanceObserver(): void {
    if (typeof PerformanceObserver === 'undefined') return;

    this.observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.handlePerformanceEntry(entry);
      }
    });

    // Observe different types of performance entries
    try {
      this.observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'measure'] });
    } catch (error) {
      console.warn('Performance observer not fully supported:', error);
    }
  }

  private handlePerformanceEntry(entry: PerformanceEntry): void {
    const metric: PerformanceMetric = {
      name: entry.name,
      value: entry.duration || (entry as any).value || 0,
      unit: 'ms',
      timestamp: Date.now(),
      context: {
        entryType: entry.entryType,
        startTime: entry.startTime,
      },
    };

    this.recordMetric(metric);

    // Check specific performance metrics
    switch (entry.entryType) {
      case 'navigation':
        this.handleNavigationTiming(entry as PerformanceNavigationTiming);
        break;
      case 'paint':
        this.handlePaintTiming(entry);
        break;
      case 'largest-contentful-paint':
        this.handleLCPTiming(entry);
        break;
    }
  }

  private handleNavigationTiming(entry: PerformanceNavigationTiming): void {
    const loadTime = entry.loadEventEnd - entry.navigationStart;
    this.recordMetric({
      name: 'page-load',
      value: loadTime,
      unit: 'ms',
      timestamp: Date.now(),
    });

    this.posthog.trackLoadTime(window.location.pathname, loadTime);
  }

  private handlePaintTiming(entry: PerformanceEntry): void {
    if (entry.name === 'first-contentful-paint') {
      this.recordMetric({
        name: 'fcp',
        value: entry.startTime,
        unit: 'ms',
        timestamp: Date.now(),
      });
    }
  }

  private handleLCPTiming(entry: PerformanceEntry): void {
    this.recordMetric({
      name: 'lcp',
      value: entry.startTime,
      unit: 'ms',
      timestamp: Date.now(),
    });
  }

  // Public API methods
  recordMetric(metric: PerformanceMetric): void {
    // Store metric locally
    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }
    this.metrics.get(metric.name)!.push(metric);

    // Keep only last 100 metrics per type
    const metrics = this.metrics.get(metric.name)!;
    if (metrics.length > 100) {
      metrics.shift();
    }

    // Send to monitoring services
    this.sentry.recordMetric(metric.name, metric.value, metric.unit);
    this.posthog.trackPerformanceMetric(metric.name, metric.value, metric.context);

    // Check against budgets
    this.checkBudget(metric.name, metric.value);
  }

  setBudget(metric: string, threshold: number, unit: string, alertOnExceed: boolean = true): void {
    this.budgets.set(metric, { metric, threshold, unit, alertOnExceed });
  }

  private checkBudget(metricName: string, value: number): void {
    const budget = this.budgets.get(metricName);
    if (!budget) return;

    if (value > budget.threshold) {
      if (budget.alertOnExceed) {
        this.sentry.checkPerformanceBudget(metricName, value, budget.threshold);
        this.posthog.track('performance_budget_exceeded', {
          metric: metricName,
          value,
          threshold: budget.threshold,
          unit: budget.unit,
        });
      }
    }
  }

  // Database query monitoring
  async monitorDatabaseQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    const startTime = performance.now();
    let success = true;
    let error: Error | null = null;

    try {
      const result = await queryFn();
      return result;
    } catch (err) {
      success = false;
      error = err as Error;
      throw err;
    } finally {
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name: 'db-query',
        value: duration,
        unit: 'ms',
        timestamp: Date.now(),
        context: {
          queryName,
          success,
          error: error?.message,
          ...context,
        },
      });

      this.sentry.recordDatabaseQuery(queryName, duration, success);
      this.posthog.trackDatabaseQuery(queryName, duration, success);
    }
  }

  // Component render monitoring
  monitorComponentRender(componentName: string, renderFn: () => void): void {
    const startTime = performance.now();
    
    try {
      renderFn();
    } finally {
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name: 'component-render',
        value: duration,
        unit: 'ms',
        timestamp: Date.now(),
        context: { componentName },
      });
    }
  }

  // Memory monitoring
  recordMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      
      this.recordMetric({
        name: 'memory-usage',
        value: memory.usedJSHeapSize,
        unit: 'bytes',
        timestamp: Date.now(),
        context: {
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
        },
      });

      this.sentry.recordMemoryUsage();
    }
  }

  // Bundle size monitoring
  recordBundleSize(bundleName: string, size: number): void {
    this.recordMetric({
      name: 'bundle-size',
      value: size,
      unit: 'bytes',
      timestamp: Date.now(),
      context: { bundleName },
    });
  }

  // API call monitoring
  async monitorAPICall<T>(
    endpoint: string,
    apiCall: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    const startTime = performance.now();
    let success = true;
    let error: Error | null = null;

    try {
      const result = await apiCall();
      return result;
    } catch (err) {
      success = false;
      error = err as Error;
      throw err;
    } finally {
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name: 'api-call',
        value: duration,
        unit: 'ms',
        timestamp: Date.now(),
        context: {
          endpoint,
          success,
          error: error?.message,
          ...context,
        },
      });

      this.posthog.track('api_call', {
        endpoint,
        duration_ms: duration,
        success,
        error: error?.message,
        ...context,
      });
    }
  }

  // Get performance metrics
  getMetrics(metricName?: string): PerformanceMetric[] {
    if (metricName) {
      return this.metrics.get(metricName) || [];
    }
    
    const allMetrics: PerformanceMetric[] = [];
    for (const metrics of this.metrics.values()) {
      allMetrics.push(...metrics);
    }
    return allMetrics.sort((a, b) => b.timestamp - a.timestamp);
  }

  // Get performance summary
  getPerformanceSummary(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const summary: Record<string, { avg: number; min: number; max: number; count: number }> = {};
    
    for (const [name, metrics] of this.metrics.entries()) {
      if (metrics.length === 0) continue;
      
      const values = metrics.map(m => m.value);
      summary[name] = {
        avg: values.reduce((sum, val) => sum + val, 0) / values.length,
        min: Math.min(...values),
        max: Math.max(...values),
        count: values.length,
      };
    }
    
    return summary;
  }

  // Cleanup
  destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
    this.metrics.clear();
    this.budgets.clear();
  }
}

export default PerformanceMonitor;