import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useAutoUpdater } from '../useAutoUpdater'

const mockElectronAPI = {
  getAppVersion: vi.fn(),
  getAutoLaunch: vi.fn(),
  checkForUpdates: vi.fn(),
  downloadUpdate: vi.fn(),
  installUpdate: vi.fn(),
  setAutoLaunch: vi.fn(),
  reportCrash: vi.fn(),
  onUpdateStatus: vi.fn(),
  onUpdateAction: vi.fn(),
  removeAllListeners: vi.fn()
}

beforeEach(() => {
  // @ts-ignore
  global.window.electronAPI = mockElectronAPI
  vi.clearAllMocks()
})

describe('useAutoUpdater', () => {
  it('initializes with default state', async () => {
    mockElectronAPI.getAppVersion.mockResolvedValue('1.0.0')
    mockElectronAPI.getAutoLaunch.mockResolvedValue(false)

    const { result } = renderHook(() => useAutoUpdater())

    expect(result.current.isChecking).toBe(false)
    expect(result.current.isDownloading).toBe(false)
    expect(result.current.isUpdateAvailable).toBe(false)
    expect(result.current.isUpdateDownloaded).toBe(false)
    expect(result.current.downloadProgress).toBe(0)
  })

  it('gets app version and auto-launch setting on mount', async () => {
    mockElectronAPI.getAppVersion.mockResolvedValue('1.2.3')
    mockElectronAPI.getAutoLaunch.mockResolvedValue(true)

    const { result } = renderHook(() => useAutoUpdater())

    await act(async () => {
      // Wait for useEffect to complete
      await new Promise(resolve => setTimeout(resolve, 0))
    })

    expect(mockElectronAPI.getAppVersion).toHaveBeenCalled()
    expect(mockElectronAPI.getAutoLaunch).toHaveBeenCalled()
  })

  it('checks for updates successfully', async () => {
    mockElectronAPI.getAppVersion.mockResolvedValue('1.0.0')
    mockElectronAPI.getAutoLaunch.mockResolvedValue(false)
    mockElectronAPI.checkForUpdates.mockResolvedValue({
      success: true,
      updateInfo: { version: '1.1.0' }
    })

    const { result } = renderHook(() => useAutoUpdater())

    await act(async () => {
      const updateInfo = await result.current.checkForUpdates()
      expect(updateInfo).toEqual({ version: '1.1.0' })
    })

    expect(mockElectronAPI.checkForUpdates).toHaveBeenCalled()
  })

  it('handles check for updates failure', async () => {
    mockElectronAPI.getAppVersion.mockResolvedValue('1.0.0')
    mockElectronAPI.getAutoLaunch.mockResolvedValue(false)
    mockElectronAPI.checkForUpdates.mockResolvedValue({
      success: false,
      error: 'Network error'
    })

    const { result } = renderHook(() => useAutoUpdater())

    await act(async () => {
      await expect(result.current.checkForUpdates()).rejects.toThrow('Network error')
    })
  })

  it('downloads update successfully', async () => {
    mockElectronAPI.getAppVersion.mockResolvedValue('1.0.0')
    mockElectronAPI.getAutoLaunch.mockResolvedValue(false)
    mockElectronAPI.downloadUpdate.mockResolvedValue({ success: true })

    const { result } = renderHook(() => useAutoUpdater())

    await act(async () => {
      await result.current.downloadUpdate()
    })

    expect(mockElectronAPI.downloadUpdate).toHaveBeenCalled()
  })

  it('handles download update failure', async () => {
    mockElectronAPI.getAppVersion.mockResolvedValue('1.0.0')
    mockElectronAPI.getAutoLaunch.mockResolvedValue(false)
    mockElectronAPI.downloadUpdate.mockResolvedValue({
      success: false,
      error: 'Download failed'
    })

    const { result } = renderHook(() => useAutoUpdater())

    await act(async () => {
      await expect(result.current.downloadUpdate()).rejects.toThrow('Download failed')
    })
  })

  it('installs update', async () => {
    mockElectronAPI.getAppVersion.mockResolvedValue('1.0.0')
    mockElectronAPI.getAutoLaunch.mockResolvedValue(false)
    mockElectronAPI.installUpdate.mockResolvedValue(undefined)

    const { result } = renderHook(() => useAutoUpdater())

    await act(async () => {
      await result.current.installUpdate()
    })

    expect(mockElectronAPI.installUpdate).toHaveBeenCalled()
  })

  it('sets auto-launch successfully', async () => {
    mockElectronAPI.getAppVersion.mockResolvedValue('1.0.0')
    mockElectronAPI.getAutoLaunch.mockResolvedValue(false)
    mockElectronAPI.setAutoLaunch.mockResolvedValue({ success: true })

    const { result } = renderHook(() => useAutoUpdater())

    await act(async () => {
      await result.current.setAutoLaunch(true)
    })

    expect(mockElectronAPI.setAutoLaunch).toHaveBeenCalledWith(true)
  })

  it('handles set auto-launch failure', async () => {
    mockElectronAPI.getAppVersion.mockResolvedValue('1.0.0')
    mockElectronAPI.getAutoLaunch.mockResolvedValue(false)
    mockElectronAPI.setAutoLaunch.mockResolvedValue({
      success: false,
      error: 'Permission denied'
    })

    const { result } = renderHook(() => useAutoUpdater())

    await act(async () => {
      await expect(result.current.setAutoLaunch(true)).rejects.toThrow('Permission denied')
    })
  })

  it('reports crash data', async () => {
    mockElectronAPI.getAppVersion.mockResolvedValue('1.0.0')
    mockElectronAPI.getAutoLaunch.mockResolvedValue(false)
    mockElectronAPI.reportCrash.mockResolvedValue({ success: true })

    const { result } = renderHook(() => useAutoUpdater())

    const crashData = { error: 'Test crash', timestamp: Date.now() }

    await act(async () => {
      await result.current.reportCrash(crashData)
    })

    expect(mockElectronAPI.reportCrash).toHaveBeenCalledWith(crashData)
  })

  it('handles update status events', async () => {
    mockElectronAPI.getAppVersion.mockResolvedValue('1.0.0')
    mockElectronAPI.getAutoLaunch.mockResolvedValue(false)

    let statusCallback: (status: any) => void = () => {}
    mockElectronAPI.onUpdateStatus.mockImplementation((callback) => {
      statusCallback = callback
    })

    const { result } = renderHook(() => useAutoUpdater())

    // Simulate update available status
    act(() => {
      statusCallback({
        type: 'available',
        version: '1.1.0',
        releaseNotes: 'Bug fixes'
      })
    })

    expect(result.current.isUpdateAvailable).toBe(true)
    expect(result.current.updateStatus?.version).toBe('1.1.0')

    // Simulate download progress
    act(() => {
      statusCallback({
        type: 'download-progress',
        percent: 50
      })
    })

    expect(result.current.isDownloading).toBe(true)
    expect(result.current.downloadProgress).toBe(50)

    // Simulate download complete
    act(() => {
      statusCallback({
        type: 'downloaded',
        version: '1.1.0'
      })
    })

    expect(result.current.isUpdateDownloaded).toBe(true)
    expect(result.current.isDownloading).toBe(false)
  })

  it('throws error when not in Electron environment', async () => {
    // @ts-ignore
    global.window.electronAPI = undefined

    const { result } = renderHook(() => useAutoUpdater())

    await act(async () => {
      await expect(result.current.checkForUpdates()).rejects.toThrow(
        'Auto-updater not available in web environment'
      )
    })
  })

  it('cleans up event listeners on unmount', () => {
    mockElectronAPI.getAppVersion.mockResolvedValue('1.0.0')
    mockElectronAPI.getAutoLaunch.mockResolvedValue(false)

    const { unmount } = renderHook(() => useAutoUpdater())

    unmount()

    expect(mockElectronAPI.removeAllListeners).toHaveBeenCalledWith('update-status')
    expect(mockElectronAPI.removeAllListeners).toHaveBeenCalledWith('update-action')
  })
})