import { useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'

export type MenuAction = 
  | 'new-property'
  | 'new-lease'
  | 'new-ticket'
  | 'import-documents'
  | 'export-report'
  | 'navigate-dashboard'
  | 'navigate-properties'
  | 'navigate-leases'
  | 'navigate-maintenance'
  | 'navigate-payments'
  | 'show-about'
  | 'show-help'
  | 'show-shortcuts'
  | 'quick-new'
  | 'global-search'

export interface MenuActionHandlers {
  onNewProperty?: () => void
  onNewLease?: () => void
  onNewTicket?: () => void
  onImportDocuments?: () => void
  onExportReport?: () => void
  onShowAbout?: () => void
  onShowHelp?: () => void
  onShowShortcuts?: () => void
  onQuickNew?: () => void
  onGlobalSearch?: () => void
}

export const useNativeMenuActions = (handlers: MenuActionHandlers = {}) => {
  const navigate = useNavigate()

  const handleMenuAction = useCallback((action: MenuAction) => {
    switch (action) {
      case 'new-property':
        handlers.onNewProperty?.() || navigate('/properties/new')
        break
      case 'new-lease':
        handlers.onNewLease?.() || navigate('/leases/new')
        break
      case 'new-ticket':
        handlers.onNewTicket?.() || navigate('/maintenance/new')
        break
      case 'import-documents':
        handlers.onImportDocuments?.()
        break
      case 'export-report':
        handlers.onExportReport?.()
        break
      case 'navigate-dashboard':
        navigate('/')
        break
      case 'navigate-properties':
        navigate('/properties')
        break
      case 'navigate-leases':
        navigate('/leases')
        break
      case 'navigate-maintenance':
        navigate('/maintenance')
        break
      case 'navigate-payments':
        navigate('/payments')
        break
      case 'show-about':
        handlers.onShowAbout?.()
        break
      case 'show-help':
        handlers.onShowHelp?.()
        break
      case 'show-shortcuts':
        handlers.onShowShortcuts?.()
        break
      case 'quick-new':
        handlers.onQuickNew?.()
        break
      case 'global-search':
        handlers.onGlobalSearch?.()
        break
      default:
        console.warn(`Unhandled menu action: ${action}`)
    }
  }, [navigate, handlers])

  useEffect(() => {
    if (!window.electronAPI) {
      return // Not in Electron environment
    }

    const cleanup = () => {
      window.electronAPI.removeAllListeners('menu-action')
    }

    window.electronAPI.onMenuAction(handleMenuAction)

    return cleanup
  }, [handleMenuAction])

  // Keyboard shortcut handlers for web environment
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const { ctrlKey, metaKey, shiftKey, key } = event
      const cmdOrCtrl = ctrlKey || metaKey

      if (cmdOrCtrl && !shiftKey) {
        switch (key) {
          case 'n':
            event.preventDefault()
            handleMenuAction('new-property')
            break
          case 'i':
            event.preventDefault()
            handleMenuAction('import-documents')
            break
          case 'e':
            event.preventDefault()
            handleMenuAction('export-report')
            break
          case 'k':
            event.preventDefault()
            handleMenuAction('global-search')
            break
          case '1':
            event.preventDefault()
            handleMenuAction('navigate-dashboard')
            break
          case '2':
            event.preventDefault()
            handleMenuAction('navigate-properties')
            break
          case '3':
            event.preventDefault()
            handleMenuAction('navigate-leases')
            break
          case '4':
            event.preventDefault()
            handleMenuAction('navigate-maintenance')
            break
          case '5':
            event.preventDefault()
            handleMenuAction('navigate-payments')
            break
          case '/':
            event.preventDefault()
            handleMenuAction('show-shortcuts')
            break
        }
      } else if (cmdOrCtrl && shiftKey) {
        switch (key) {
          case 'L':
            event.preventDefault()
            handleMenuAction('new-lease')
            break
          case 'M':
            event.preventDefault()
            handleMenuAction('new-ticket')
            break
          case 'N':
            event.preventDefault()
            handleMenuAction('quick-new')
            break
          case 'H':
            event.preventDefault()
            // Toggle window visibility (Electron only)
            if (window.electronAPI) {
              window.electronAPI.hideWindow()
            }
            break
        }
      } else if (key === 'F1') {
        event.preventDefault()
        handleMenuAction('show-help')
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleMenuAction])

  return {
    handleMenuAction
  }
}