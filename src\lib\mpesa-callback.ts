// M-PESA Callback Handler for Express.js or similar backend
// This would typically be implemented in a separate backend service
// but included here for reference

export interface MPESACallbackRequest {
  Body: {
    stkCallback: {
      MerchantRequestID: string;
      CheckoutRequestID: string;
      ResultCode: number;
      ResultDesc: string;
      CallbackMetadata?: {
        Item: Array<{
          Name: string;
          Value: string | number;
        }>;
      };
    };
  };
}

export interface MPESACallbackResponse {
  ResultCode: number;
  ResultDesc: string;
}

/**
 * Parse M-PESA callback metadata
 */
export const parseCallbackMetadata = (metadata?: MPESACallbackRequest["Body"]["stkCallback"]["CallbackMetadata"]) => {
  if (!metadata?.Item) {
    return {};
  }

  const parsed: Record<string, string | number> = {};
  
  metadata.Item.forEach((item) => {
    switch (item.Name) {
      case "Amount":
        parsed.amount = Number(item.Value);
        break;
      case "MpesaReceiptNumber":
        parsed.mpesaReceiptNumber = String(item.Value);
        break;
      case "TransactionDate":
        parsed.transactionDate = String(item.Value);
        break;
      case "PhoneNumber":
        parsed.phoneNumber = String(item.Value);
        break;
      default:
        parsed[item.Name] = item.Value;
    }
  });

  return parsed;
};

/**
 * Handle M-PESA STK Push callback
 * This would be implemented as an Express.js route handler
 */
export const handleMPESACallback = async (
  req: { body: MPESACallbackRequest },
  convexClient: any // ConvexHttpClient
): Promise<MPESACallbackResponse> => {
  try {
    const { stkCallback } = req.body.Body;
    const metadata = parseCallbackMetadata(stkCallback.CallbackMetadata);

    // Process the callback through Convex
    await convexClient.mutation("payments:handleMPESACallback", {
      checkoutRequestId: stkCallback.CheckoutRequestID,
      resultCode: stkCallback.ResultCode,
      resultDesc: stkCallback.ResultDesc,
      mpesaReceiptNumber: metadata.mpesaReceiptNumber as string,
      transactionDate: metadata.transactionDate as string,
      phoneNumber: metadata.phoneNumber as string,
      amount: metadata.amount as number,
    });

    return {
      ResultCode: 0,
      ResultDesc: "Callback processed successfully",
    };
  } catch (error) {
    console.error("M-PESA callback processing failed:", error);
    
    return {
      ResultCode: 1,
      ResultDesc: "Callback processing failed",
    };
  }
};

/**
 * Validate M-PESA callback request
 */
export const validateCallbackRequest = (req: { body: any }): req is { body: MPESACallbackRequest } => {
  const { body } = req;
  
  return (
    body &&
    body.Body &&
    body.Body.stkCallback &&
    typeof body.Body.stkCallback.MerchantRequestID === "string" &&
    typeof body.Body.stkCallback.CheckoutRequestID === "string" &&
    typeof body.Body.stkCallback.ResultCode === "number" &&
    typeof body.Body.stkCallback.ResultDesc === "string"
  );
};

/**
 * Example Express.js route implementation
 */
/*
import express from 'express';
import { ConvexHttpClient } from 'convex/browser';

const app = express();
app.use(express.json());

const convex = new ConvexHttpClient(process.env.CONVEX_URL!);

app.post('/api/mpesa/callback', async (req, res) => {
  try {
    // Validate request
    if (!validateCallbackRequest(req)) {
      return res.status(400).json({
        ResultCode: 1,
        ResultDesc: "Invalid callback request format"
      });
    }

    // Process callback
    const response = await handleMPESACallback(req, convex);
    
    res.json(response);
  } catch (error) {
    console.error('Callback handler error:', error);
    res.status(500).json({
      ResultCode: 1,
      ResultDesc: "Internal server error"
    });
  }
});

export default app;
*/