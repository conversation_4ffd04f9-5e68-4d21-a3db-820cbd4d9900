import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { FinancialDashboard } from '../FinancialDashboard';

// Mock Convex
vi.mock('convex/react', () => ({
  useQuery: vi.fn(() => ({
    // Mock dashboard data
    period: { startDate: Date.now() - 30 * 24 * 60 * 60 * 1000, endDate: Date.now() },
    summary: {
      totalRevenue: 100000,
      rentRevenue: 80000,
      otherRevenue: 20000,
      totalExpenses: 30000,
      netIncome: 70000,
      profitMargin: 70,
      collectionRate: 95,
      pendingRevenue: 5000,
      overdueRevenue: 2000,
    },
    monthlyBreakdown: [
      {
        month: 1,
        year: 2024,
        revenue: 50000,
        expenses: 15000,
        netIncome: 35000,
      },
    ],
    invoiceMetrics: {
      totalInvoices: 50,
      paidInvoices: 45,
      pendingInvoices: 3,
      overdueInvoices: 2,
    },
    paymentMetrics: {
      totalPayments: 45,
      successfulPayments: 43,
      failedPayments: 2,
      mpesaPayments: 30,
      stripePayments: 13,
    },
    // Mock KPI data
    kpis: {
      totalRevenue: 100000,
      netIncome: 70000,
      profitMargin: 70,
      collectionRate: 95,
      revenueGrowth: 10,
      profitGrowth: 15,
      pendingAmount: 5000,
      overdueAmount: 2000,
    },
    trends: {
      isRevenueTrendingUp: true,
      monthlyAverage: 50000,
    },
    comparison: {
      previousPeriod: {
        revenue: 90000,
        netIncome: 60000,
        profitMargin: 66.7,
      },
    },
    // Mock P&L data
    revenue: {
      rent: 80000,
      deposits: 10000,
      maintenance: 5000,
      other: 5000,
      total: 100000,
    },
    expenses: {
      byCategory: {
        plumbing: 5000,
        electrical: 3000,
        hvac: 2000,
        appliance: 1000,
        structural: 0,
        cleaning: 1000,
        security: 0,
        other: 3000,
      },
      total: 15000,
    },
    profitLoss: {
      grossProfit: 85000,
      grossMargin: 85,
      netIncome: 70000,
    },
    // Mock cash flow data
    historicalData: [
      {
        month: 1,
        year: 2024,
        monthName: 'January',
        cashIn: 50000,
        cashOut: 15000,
        netCashFlow: 35000,
        invoicesGenerated: 10,
        paymentsReceived: 8,
        maintenanceExpenses: 3,
      },
    ],
    forecast: [
      {
        month: 2,
        year: 2024,
        monthName: 'February',
        projectedCashIn: 52000,
        projectedCashOut: 16000,
        projectedNetFlow: 36000,
      },
    ],
  })),
}));

// Mock Recharts
vi.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => <div data-testid="chart">{children}</div>,
  AreaChart: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Area: () => <div />,
  BarChart: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Bar: () => <div />,
  LineChart: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Line: () => <div />,
  XAxis: () => <div />,
  YAxis: () => <div />,
  CartesianGrid: () => <div />,
  Tooltip: () => <div />,
  PieChart: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Pie: () => <div />,
  Cell: () => <div />,
}));

describe('FinancialDashboard', () => {
  it('renders financial dashboard with key metrics', () => {
    render(<FinancialDashboard />);
    
    // Check if main title is rendered
    expect(screen.getByText('Financial Dashboard')).toBeInTheDocument();
    
    // Check if KPI cards are rendered
    expect(screen.getByText('Total Revenue')).toBeInTheDocument();
    expect(screen.getByText('Net Income')).toBeInTheDocument();
    expect(screen.getByText('Profit Margin')).toBeInTheDocument();
    expect(screen.getByText('Collection Rate')).toBeInTheDocument();
    
    // Check if tabs are rendered
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('P&L Statement')).toBeInTheDocument();
    expect(screen.getByText('Cash Flow')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
  });

  it('displays formatted currency values', () => {
    render(<FinancialDashboard />);
    
    // Check if currency values are formatted (should contain Ksh)
    const currencyElements = screen.getAllByText(/Ksh/);
    expect(currencyElements.length).toBeGreaterThan(0);
  });

  it('shows export button', () => {
    render(<FinancialDashboard />);
    
    expect(screen.getByText('Export')).toBeInTheDocument();
  });

  it('renders charts', () => {
    render(<FinancialDashboard />);
    
    // Check if chart containers are rendered
    const charts = screen.getAllByTestId('chart');
    expect(charts.length).toBeGreaterThan(0);
  });
});