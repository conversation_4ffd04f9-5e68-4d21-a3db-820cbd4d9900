# Design Document

## Overview

EstatePulse is a cross-platform property management desktop application built with Electron, React, and Convex. The application follows a modern architecture with real-time synchronization, role-based access control, and comprehensive property management capabilities. The system is designed to handle large-scale residential and commercial developments across East Africa, with specific focus on the Nairobi market.

### Key Design Principles

- **Real-time First**: All data operations leverage Convex's real-time capabilities
- **Role-Based Security**: Comprehensive RBAC implementation at all layers
- **Scalable Architecture**: Monorepo structure with atomic design system
- **Cross-Platform Consistency**: Native desktop experience across Windows, macOS, and Linux
- **Offline Resilience**: Local caching and sync capabilities for unreliable connections

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Desktop Application (Electron)"
        subgraph "Renderer Process"
            UI[React UI Layer]
            State[State Management]
            Cache[Local Cache]
        end
        subgraph "Main Process"
            Native[Native APIs]
            Updates[Auto Updates]
            Security[Security Layer]
        end
    end
    
    subgraph "Backend Services"
        Convex[Convex Backend]
        Auth[Authentication]
        DB[(Real-time Database)]
        Files[File Storage]
    end
    
    subgraph "External Services"
        MPESA[M-PESA API]
        Stripe[Stripe API]
        SMS[SMS/WhatsApp APIs]
        Storage[AWS S3/Cloudflare R2]
    end
    
    UI --> State
    State --> Cache
    UI --> Convex
    Convex --> Auth
    Convex --> DB
    Convex --> Files
    Convex --> MPESA
    Convex --> Stripe
    Convex --> SMS
    Files --> Storage
```

### Frontend Architecture

#### Technology Stack
- **Framework**: React 18 with Vite for fast development and builds
- **Language**: TypeScript for type safety and developer experience
- **Styling**: Tailwind CSS with shadcn/ui component library
- **State Management**: 
  - Convex queries for server state
  - Zustand for local UI state
  - React Context for theme and user preferences
- **Architecture Pattern**: MVVM + CQRS (Command Query Responsibility Segregation)

#### Component Architecture
```mermaid
graph TD
    subgraph "Presentation Layer"
        Pages[Page Components]
        Layouts[Layout Components]
        UI[UI Components - shadcn/ui]
    end
    
    subgraph "Business Logic Layer"
        ViewModels[View Models]
        Commands[Command Handlers]
        Queries[Query Handlers]
    end
    
    subgraph "Data Layer"
        ConvexQueries[Convex Queries]
        ConvexMutations[Convex Mutations]
        LocalState[Local State - Zustand]
    end
    
    Pages --> ViewModels
    ViewModels --> Commands
    ViewModels --> Queries
    Commands --> ConvexMutations
    Queries --> ConvexQueries
    ViewModels --> LocalState
```

### Backend Architecture (Convex)

#### Database Schema Design
```typescript
// Core Entities
interface Property {
  _id: Id<"properties">;
  name: string;
  type: "residential" | "commercial" | "mixed";
  address: PropertyAddress;
  ownerId: Id<"users">;
  managerId?: Id<"users">;
  branding: PropertyBranding;
  settings: PropertySettings;
  createdAt: number;
  updatedAt: number;
}

interface Unit {
  _id: Id<"units">;
  propertyId: Id<"properties">;
  unitNumber: string;
  type: "apartment" | "office" | "retail" | "parking";
  size: number;
  rent: number;
  status: "vacant" | "occupied" | "maintenance";
  amenities: string[];
}

interface Lease {
  _id: Id<"leases">;
  propertyId: Id<"properties">;
  unitId: Id<"units">;
  tenantId: Id<"users">;
  startDate: number;
  endDate: number;
  monthlyRent: number;
  deposit: number;
  status: "active" | "expired" | "terminated";
  documents: Id<"documents">[];
  eSignatureStatus: "pending" | "signed" | "expired";
}

interface MaintenanceTicket {
  _id: Id<"tickets">;
  propertyId: Id<"properties">;
  unitId?: Id<"units">;
  tenantId: Id<"users">;
  vendorId?: Id<"users">;
  title: string;
  description: string;
  priority: "low" | "medium" | "high" | "emergency";
  status: "open" | "assigned" | "in_progress" | "completed" | "closed";
  slaDeadline: number;
  createdAt: number;
  updatedAt: number;
}
```

#### Authentication and Authorization
```typescript
// Role-based access control
interface User {
  _id: Id<"users">;
  email: string;
  name: string;
  role: "owner" | "manager" | "vendor" | "tenant";
  propertyAccess: Id<"properties">[];
  permissions: Permission[];
  kycStatus: "pending" | "verified" | "rejected";
  documents: Id<"documents">[];
}

interface Permission {
  resource: string;
  actions: ("create" | "read" | "update" | "delete")[];
}
```

## Components and Interfaces

### Core Modules

#### 1. Authentication Module
```typescript
interface AuthService {
  login(credentials: LoginCredentials): Promise<AuthResult>;
  logout(): Promise<void>;
  getCurrentUser(): Promise<User | null>;
  refreshToken(): Promise<string>;
  verifyPermission(resource: string, action: string): boolean;
}

interface RBACService {
  checkPermission(userId: Id<"users">, resource: string, action: string): boolean;
  getUserRoles(userId: Id<"users">): Promise<Role[]>;
  assignRole(userId: Id<"users">, role: Role): Promise<void>;
}
```

#### 2. Property Management Module
```typescript
interface PropertyService {
  createProperty(data: CreatePropertyData): Promise<Id<"properties">>;
  updateProperty(id: Id<"properties">, data: UpdatePropertyData): Promise<void>;
  getProperties(filters: PropertyFilters): Promise<Property[]>;
  getPropertyAnalytics(id: Id<"properties">): Promise<PropertyAnalytics>;
}

interface UnitService {
  createUnit(data: CreateUnitData): Promise<Id<"units">>;
  updateUnitStatus(id: Id<"units">, status: UnitStatus): Promise<void>;
  getUnits(propertyId: Id<"properties">): Promise<Unit[]>;
  getOccupancyRate(propertyId: Id<"properties">): Promise<number>;
}
```

#### 3. Lease Management Module
```typescript
interface LeaseService {
  createLease(data: CreateLeaseData): Promise<Id<"leases">>;
  generateLeaseDocument(leaseId: Id<"leases">): Promise<DocumentUrl>;
  sendForESignature(leaseId: Id<"leases">): Promise<void>;
  renewLease(leaseId: Id<"leases">, terms: RenewalTerms): Promise<Id<"leases">>;
  terminateLease(leaseId: Id<"leases">, reason: string): Promise<void>;
}

interface RentService {
  generateRentRoll(propertyId: Id<"properties">, month: number, year: number): Promise<RentRoll>;
  createInvoice(leaseId: Id<"leases">, amount: number): Promise<Id<"invoices">>;
  processPayment(invoiceId: Id<"invoices">, paymentData: PaymentData): Promise<PaymentResult>;
  sendPaymentReminder(invoiceId: Id<"invoices">): Promise<void>;
}
```

#### 4. Maintenance Module
```typescript
interface MaintenanceService {
  createTicket(data: CreateTicketData): Promise<Id<"tickets">>;
  assignVendor(ticketId: Id<"tickets">, vendorId: Id<"users">): Promise<void>;
  updateTicketStatus(ticketId: Id<"tickets">, status: TicketStatus): Promise<void>;
  escalateTicket(ticketId: Id<"tickets">): Promise<void>;
  getSLAMetrics(propertyId: Id<"properties">): Promise<SLAMetrics>;
}
```

#### 5. Payment Integration Module
```typescript
interface PaymentService {
  processMPESAPayment(data: MPESAPaymentData): Promise<PaymentResult>;
  processStripePayment(data: StripePaymentData): Promise<PaymentResult>;
  verifyPayment(transactionId: string): Promise<PaymentStatus>;
  refundPayment(paymentId: string, amount?: number): Promise<RefundResult>;
}

interface MPESAService {
  initiateSTKPush(phoneNumber: string, amount: number, reference: string): Promise<STKPushResult>;
  queryPaymentStatus(checkoutRequestId: string): Promise<PaymentStatus>;
  registerC2BUrls(): Promise<void>;
}
```

#### 6. Communication Module
```typescript
interface CommunicationService {
  sendSMS(phoneNumber: string, message: string): Promise<SMSResult>;
  sendWhatsApp(phoneNumber: string, message: string): Promise<WhatsAppResult>;
  sendBulkNotification(recipients: Recipient[], message: string): Promise<BulkResult>;
  scheduleNotification(data: ScheduledNotificationData): Promise<Id<"notifications">>;
}
```

### White-Label Portal System

#### Portal Configuration
```typescript
interface PortalBranding {
  domain: string;
  logo: string;
  primaryColor: string;
  secondaryColor: string;
  fontFamily: string;
  customCSS?: string;
}

interface PortalService {
  createPortal(propertyId: Id<"properties">, branding: PortalBranding): Promise<Portal>;
  updateBranding(portalId: Id<"portals">, branding: Partial<PortalBranding>): Promise<void>;
  generatePortalUrl(portalId: Id<"portals">): string;
  getPortalAnalytics(portalId: Id<"portals">): Promise<PortalAnalytics>;
}
```

## Data Models

### Core Data Relationships

```mermaid
erDiagram
    User ||--o{ Property : owns
    User ||--o{ Lease : "tenant of"
    Property ||--o{ Unit : contains
    Property ||--o{ MaintenanceTicket : "has tickets"
    Unit ||--o{ Lease : "leased as"
    Lease ||--o{ Invoice : generates
    Invoice ||--o{ Payment : "paid by"
    MaintenanceTicket ||--o{ User : "assigned to vendor"
    Property ||--|| Portal : "has portal"
    User ||--o{ Document : uploads
    
    User {
        string id PK
        string email
        string name
        enum role
        array propertyAccess
        enum kycStatus
    }
    
    Property {
        string id PK
        string name
        enum type
        object address
        string ownerId FK
        string managerId FK
        object branding
    }
    
    Unit {
        string id PK
        string propertyId FK
        string unitNumber
        enum type
        number size
        number rent
        enum status
    }
    
    Lease {
        string id PK
        string propertyId FK
        string unitId FK
        string tenantId FK
        number startDate
        number endDate
        number monthlyRent
        enum status
    }
```

### Financial Data Models

```typescript
interface Invoice {
  _id: Id<"invoices">;
  leaseId: Id<"leases">;
  amount: number;
  dueDate: number;
  status: "pending" | "paid" | "overdue" | "cancelled";
  items: InvoiceItem[];
  paymentMethod?: "mpesa" | "stripe" | "bank_transfer";
  paidAt?: number;
}

interface Payment {
  _id: Id<"payments">;
  invoiceId: Id<"invoices">;
  amount: number;
  method: "mpesa" | "stripe" | "bank_transfer";
  transactionId: string;
  status: "pending" | "completed" | "failed" | "refunded";
  processedAt: number;
  metadata: Record<string, any>;
}

interface FinancialReport {
  propertyId: Id<"properties">;
  period: { start: number; end: number };
  revenue: RevenueBreakdown;
  expenses: ExpenseBreakdown;
  occupancyRate: number;
  netIncome: number;
  profitMargin: number;
}
```

## Error Handling

### Error Classification and Handling Strategy

```typescript
// Error Types
enum ErrorType {
  VALIDATION = "validation",
  AUTHENTICATION = "authentication",
  AUTHORIZATION = "authorization",
  NETWORK = "network",
  PAYMENT = "payment",
  EXTERNAL_SERVICE = "external_service",
  SYSTEM = "system"
}

interface AppError {
  type: ErrorType;
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: number;
  userId?: Id<"users">;
  context?: string;
}

// Error Handling Service
interface ErrorHandlingService {
  handleError(error: AppError): Promise<void>;
  logError(error: AppError): Promise<void>;
  notifyUser(error: AppError): void;
  reportToMonitoring(error: AppError): Promise<void>;
}
```

### Retry and Resilience Patterns

```typescript
interface RetryConfig {
  maxAttempts: number;
  backoffStrategy: "linear" | "exponential";
  baseDelay: number;
  maxDelay: number;
}

interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeout: number;
  monitoringPeriod: number;
}

// For external service calls (M-PESA, SMS, etc.)
interface ResilientService {
  executeWithRetry<T>(operation: () => Promise<T>, config: RetryConfig): Promise<T>;
  executeWithCircuitBreaker<T>(operation: () => Promise<T>, config: CircuitBreakerConfig): Promise<T>;
}
```

## Testing Strategy

### Testing Pyramid

#### Unit Tests (Jest)
- Component testing with React Testing Library
- Service layer testing with mocked dependencies
- Utility function testing
- Validation logic testing

#### Integration Tests (Playwright)
- End-to-end user workflows
- Cross-platform compatibility testing
- Payment flow testing (with test environments)
- Real-time synchronization testing

#### Performance Tests (k6)
- Load testing for concurrent users
- Database query performance
- File upload/download performance
- Real-time update latency testing

### Test Data Management

```typescript
interface TestDataFactory {
  createUser(overrides?: Partial<User>): User;
  createProperty(overrides?: Partial<Property>): Property;
  createLease(overrides?: Partial<Lease>): Lease;
  createMaintenanceTicket(overrides?: Partial<MaintenanceTicket>): MaintenanceTicket;
}

interface TestEnvironment {
  setupDatabase(): Promise<void>;
  seedTestData(): Promise<void>;
  cleanupDatabase(): Promise<void>;
  mockExternalServices(): void;
}
```

### Accessibility Testing

- Automated accessibility testing with axe-core
- Screen reader compatibility testing
- Keyboard navigation testing
- Color contrast validation
- WCAG 2.1 AA compliance verification

## Performance Optimization

### Frontend Performance

#### Code Splitting and Lazy Loading
```typescript
// Route-based code splitting
const PropertyManagement = lazy(() => import('./pages/PropertyManagement'));
const LeaseManagement = lazy(() => import('./pages/LeaseManagement'));
const MaintenanceModule = lazy(() => import('./pages/MaintenanceModule'));

// Component-based lazy loading
const HeavyChart = lazy(() => import('./components/HeavyChart'));
```

#### State Management Optimization
```typescript
// Selective subscriptions to Convex queries
const usePropertyData = (propertyId: Id<"properties">) => {
  const property = useQuery(api.properties.getById, { id: propertyId });
  const units = useQuery(api.units.getByProperty, { propertyId });
  
  return useMemo(() => ({
    property,
    units,
    occupancyRate: units ? calculateOccupancyRate(units) : 0
  }), [property, units]);
};
```

### Backend Performance

#### Database Optimization
- Proper indexing strategy for Convex queries
- Query optimization for complex aggregations
- Pagination for large datasets
- Caching frequently accessed data

#### Real-time Optimization
- Selective real-time subscriptions
- Batched updates for bulk operations
- Connection pooling for external services
- Rate limiting for API endpoints

## Security Considerations

### Data Protection
- End-to-end encryption for sensitive documents
- PII data anonymization in logs
- Secure file upload with virus scanning
- Regular security audits and penetration testing

### Authentication Security
- Multi-factor authentication support
- Session management with secure tokens
- Password policy enforcement
- Account lockout mechanisms

### API Security
- Rate limiting on all endpoints
- Input validation and sanitization
- SQL injection prevention (though using Convex)
- CORS configuration for web portals

### Compliance
- GDPR compliance for data handling
- Local data protection regulations
- Financial data security standards
- Audit trail maintenance for all operations