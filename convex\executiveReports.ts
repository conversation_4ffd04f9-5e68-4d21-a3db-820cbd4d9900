import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Generate executive summary report
export const generateExecutiveSummary = query({
  args: {
    propertyIds: v.optional(v.array(v.id("properties"))),
    startDate: v.number(),
    endDate: v.number(),
    reportType: v.union(v.literal("monthly"), v.literal("quarterly"), v.literal("annual")),
  },
  handler: async (ctx, args) => {
    const { startDate, endDate, propertyIds, reportType } = args;

    // Get all properties if none specified
    let properties = await ctx.db.query("properties").collect();
    if (propertyIds && propertyIds.length > 0) {
      properties = properties.filter(p => propertyIds.includes(p._id));
    }

    // Get all relevant data
    const allInvoices = await ctx.db.query("invoices").collect();
    const allPayments = await ctx.db.query("payments").collect();
    const allTickets = await ctx.db.query("maintenanceTickets").collect();
    const allLeases = await ctx.db.query("leases").collect();
    const allUnits = await ctx.db.query("units").collect();

    // Filter data by date range and properties
    const filteredInvoices = allInvoices.filter(invoice => 
      invoice.createdAt >= startDate && 
      invoice.createdAt <= endDate &&
      (!propertyIds || propertyIds.length === 0 || propertyIds.includes(invoice.propertyId))
    );

    const filteredPayments = allPayments.filter(payment => 
      payment.createdAt >= startDate && payment.createdAt <= endDate
    );

    const filteredTickets = allTickets.filter(ticket => 
      ticket.createdAt >= startDate && 
      ticket.createdAt <= endDate &&
      (!propertyIds || propertyIds.length === 0 || propertyIds.includes(ticket.propertyId))
    );

    const activeLeases = allLeases.filter(lease => 
      lease.status === "active" &&
      (!propertyIds || propertyIds.length === 0 || propertyIds.includes(lease.propertyId))
    );

    // Calculate key metrics
    const totalRevenue = filteredInvoices
      .filter(invoice => invoice.status === "paid")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    const totalExpenses = filteredTickets
      .filter(ticket => ticket.actualCost && ticket.status === "completed")
      .reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0);

    const netIncome = totalRevenue - totalExpenses;
    const profitMargin = totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0;

    // Calculate occupancy metrics
    const totalUnits = allUnits.filter(unit => 
      !propertyIds || propertyIds.length === 0 || propertyIds.includes(unit.propertyId)
    ).length;

    const occupiedUnits = activeLeases.length;
    const occupancyRate = totalUnits > 0 ? (occupiedUnits / totalUnits) * 100 : 0;

    // Collection metrics
    const totalInvoiced = filteredInvoices.reduce((sum, invoice) => sum + invoice.amount, 0);
    const collectionRate = totalInvoiced > 0 ? (totalRevenue / totalInvoiced) * 100 : 0;

    const pendingAmount = filteredInvoices
      .filter(invoice => invoice.status === "pending")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    const overdueAmount = filteredInvoices
      .filter(invoice => invoice.status === "overdue")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    // Property performance breakdown
    const propertyPerformance = properties.map(property => {
      const propertyInvoices = filteredInvoices.filter(i => i.propertyId === property._id);
      const propertyTickets = filteredTickets.filter(t => t.propertyId === property._id);
      const propertyUnits = allUnits.filter(u => u.propertyId === property._id);
      const propertyLeases = activeLeases.filter(l => l.propertyId === property._id);

      const revenue = propertyInvoices
        .filter(invoice => invoice.status === "paid")
        .reduce((sum, invoice) => sum + invoice.amount, 0);

      const expenses = propertyTickets
        .filter(ticket => ticket.actualCost && ticket.status === "completed")
        .reduce((sum, ticket) => sum + (ticket.actualCost || 0), 0);

      const occupancy = propertyUnits.length > 0 ? 
        (propertyLeases.length / propertyUnits.length) * 100 : 0;

      return {
        propertyId: property._id,
        propertyName: property.name,
        revenue,
        expenses,
        netIncome: revenue - expenses,
        occupancyRate: occupancy,
        unitCount: propertyUnits.length,
        occupiedUnits: propertyLeases.length,
      };
    });

    // Maintenance metrics
    const totalTickets = filteredTickets.length;
    const completedTickets = filteredTickets.filter(t => t.status === "completed").length;
    const avgResolutionTime = completedTickets > 0 ? 
      filteredTickets
        .filter(t => t.status === "completed" && t.resolvedAt)
        .reduce((sum, t) => sum + ((t.resolvedAt! - t.createdAt) / (1000 * 60 * 60 * 24)), 0) / completedTickets
      : 0;

    // Payment method breakdown
    const mpesaPayments = filteredPayments.filter(p => p.method === "mpesa").length;
    const stripePayments = filteredPayments.filter(p => p.method === "stripe").length;
    const totalPayments = filteredPayments.length;

    // Trends (compare with previous period)
    const periodLength = endDate - startDate;
    const prevStartDate = startDate - periodLength;
    const prevEndDate = startDate;

    const prevInvoices = allInvoices.filter(invoice => 
      invoice.createdAt >= prevStartDate && 
      invoice.createdAt < prevEndDate &&
      (!propertyIds || propertyIds.length === 0 || propertyIds.includes(invoice.propertyId))
    );

    const prevRevenue = prevInvoices
      .filter(invoice => invoice.status === "paid")
      .reduce((sum, invoice) => sum + invoice.amount, 0);

    const revenueGrowth = prevRevenue > 0 ? ((totalRevenue - prevRevenue) / prevRevenue) * 100 : 0;

    return {
      reportMetadata: {
        generatedAt: Date.now(),
        period: { startDate, endDate },
        reportType,
        propertyCount: properties.length,
        totalProperties: properties.length,
      },
      executiveSummary: {
        totalRevenue,
        totalExpenses,
        netIncome,
        profitMargin,
        revenueGrowth,
        occupancyRate,
        collectionRate,
      },
      keyMetrics: {
        financial: {
          totalRevenue,
          totalExpenses,
          netIncome,
          profitMargin,
          pendingAmount,
          overdueAmount,
          collectionRate,
        },
        operational: {
          totalUnits,
          occupiedUnits,
          occupancyRate,
          totalTickets,
          completedTickets,
          avgResolutionTime,
        },
        payments: {
          totalPayments,
          mpesaPayments,
          stripePayments,
          successRate: totalPayments > 0 ? (filteredPayments.filter(p => p.status === "completed").length / totalPayments) * 100 : 0,
        },
      },
      propertyPerformance,
      trends: {
        revenueGrowth,
        periodComparison: {
          current: { revenue: totalRevenue, expenses: totalExpenses },
          previous: { revenue: prevRevenue, expenses: 0 }, // Simplified for now
        },
      },
    };
  },
});

// Get available report templates
export const getReportTemplates = query({
  args: {},
  handler: async (ctx) => {
    return [
      {
        id: "executive-summary",
        name: "Executive Summary",
        description: "High-level overview for board meetings and stakeholders",
        sections: ["financial-overview", "occupancy-metrics", "key-trends", "property-performance"],
      },
      {
        id: "financial-performance",
        name: "Financial Performance Report",
        description: "Detailed financial analysis and P&L breakdown",
        sections: ["revenue-analysis", "expense-breakdown", "cash-flow", "profitability"],
      },
      {
        id: "operational-metrics",
        name: "Operational Metrics Report",
        description: "Maintenance, occupancy, and operational KPIs",
        sections: ["occupancy-analysis", "maintenance-metrics", "tenant-satisfaction", "operational-efficiency"],
      },
      {
        id: "comparative-analysis",
        name: "Comparative Analysis Report",
        description: "Property-by-property and period-over-period comparisons",
        sections: ["property-comparison", "trend-analysis", "benchmarking", "performance-ranking"],
      },
    ];
  },
});

// Save custom report configuration
export const saveReportConfiguration = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    templateId: v.string(),
    sections: v.array(v.string()),
    filters: v.object({
      propertyIds: v.optional(v.array(v.id("properties"))),
      dateRange: v.object({
        type: v.union(v.literal("monthly"), v.literal("quarterly"), v.literal("annual"), v.literal("custom")),
        customStart: v.optional(v.number()),
        customEnd: v.optional(v.number()),
      }),
    }),
    schedule: v.optional(v.object({
      frequency: v.union(v.literal("weekly"), v.literal("monthly"), v.literal("quarterly")),
      dayOfWeek: v.optional(v.number()),
      dayOfMonth: v.optional(v.number()),
      recipients: v.array(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const reportConfig = await ctx.db.insert("reportConfigurations", {
      name: args.name,
      description: args.description,
      templateId: args.templateId,
      sections: args.sections,
      filters: args.filters,
      schedule: args.schedule,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      isActive: true,
    });

    return reportConfig;
  },
});

// Get saved report configurations
export const getReportConfigurations = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("reportConfigurations")
      .filter(q => q.eq(q.field("isActive"), true))
      .collect();
  },
});

// Generate report data for export
export const generateReportForExport = query({
  args: {
    configId: v.id("reportConfigurations"),
    format: v.union(v.literal("pdf"), v.literal("excel"), v.literal("csv")),
  },
  handler: async (ctx, args) => {
    const config = await ctx.db.get(args.configId);
    if (!config) {
      throw new Error("Report configuration not found");
    }

    // Calculate date range based on config
    const now = Date.now();
    let startDate: number, endDate: number;

    switch (config.filters.dateRange.type) {
      case "monthly":
        startDate = now - (30 * 24 * 60 * 60 * 1000);
        endDate = now;
        break;
      case "quarterly":
        startDate = now - (90 * 24 * 60 * 60 * 1000);
        endDate = now;
        break;
      case "annual":
        startDate = now - (365 * 24 * 60 * 60 * 1000);
        endDate = now;
        break;
      case "custom":
        startDate = config.filters.dateRange.customStart || (now - (30 * 24 * 60 * 60 * 1000));
        endDate = config.filters.dateRange.customEnd || now;
        break;
    }

    // Generate the report data
    const reportData = await generateExecutiveSummary(ctx, {
      propertyIds: config.filters.propertyIds,
      startDate,
      endDate,
      reportType: config.filters.dateRange.type === "custom" ? "monthly" : config.filters.dateRange.type,
    });

    return {
      config,
      data: reportData,
      exportFormat: args.format,
      generatedAt: now,
    };
  },
});

// Get generated reports
export const getGeneratedReports = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("generatedReports")
      .order("desc")
      .take(args.limit || 50);
  },
});

// Get specific generated report
export const getGeneratedReport = query({
  args: {
    reportId: v.id("generatedReports"),
  },
  handler: async (ctx, args) => {
    const report = await ctx.db.get(args.reportId);
    if (!report) {
      throw new Error("Report not found");
    }

    // Update download count
    await ctx.db.patch(args.reportId, {
      downloadCount: (report.downloadCount || 0) + 1,
      lastDownloaded: Date.now(),
    });

    return report;
  },
});

// Delete report configuration
export const deleteReportConfiguration = mutation({
  args: {
    configId: v.id("reportConfigurations"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.configId, {
      isActive: false,
      updatedAt: Date.now(),
    });
    return { success: true };
  },
});
