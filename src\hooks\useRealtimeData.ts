/**
 * React hooks for real-time data synchronization
 * Provides optimistic updates and conflict resolution
 */

import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useCallback, useEffect, useRef, useState } from "react";
import { useRealtimeSync, optimisticUpdatesManager } from "../lib/realtime-sync";
import { useRealtimeNotifications, dataChangeNotificationSystem } from "../lib/realtime-notifications";

// Generic real-time data hook
export function useRealtimeData<T>(
  queryName: keyof typeof api,
  queryArgs: any,
  options?: {
    enableOptimisticUpdates?: boolean;
    conflictResolution?: 'last-write-wins' | 'user-intervention' | 'merge';
  }
) {
  const { syncState, queueOperation } = useRealtimeSync();
  const [optimisticData, setOptimisticData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Get data from Convex
  const serverData = useQuery(queryName as any, queryArgs);

  // Combine server data with optimistic updates
  const data = optimisticData || serverData;

  useEffect(() => {
    if (serverData !== undefined) {
      setIsLoading(false);
      setOptimisticData(null); // Clear optimistic data when server data arrives
    }
  }, [serverData]);

  // Optimistic update function
  const updateOptimistically = useCallback((update: Partial<T>) => {
    if (!options?.enableOptimisticUpdates) return;

    const currentData = data as any;
    const updatedData = { ...currentData, ...update };
    
    setOptimisticData(updatedData);

    // Set rollback callback
    const rollback = () => setOptimisticData(null);
    
    return optimisticUpdatesManager.applyOptimisticUpdate(
      'generic',
      currentData?._id || 'unknown',
      update,
      rollback
    );
  }, [data, options?.enableOptimisticUpdates]);

  return {
    data,
    isLoading,
    error,
    isOnline: syncState.isOnline,
    updateOptimistically,
  };
}

// Real-time properties hook
export function useRealtimeProperties(propertyId?: Id<"properties">) {
  const properties = useQuery(
    api.properties.getAll,
    propertyId ? { propertyId } : {}
  );

  const updateProperty = useMutation(api.properties.update);
  const { queueOperation } = useRealtimeSync();
  const { addNotification } = useRealtimeNotifications();

  const updatePropertyOptimistically = useCallback(async (
    id: Id<"properties">,
    updates: any
  ) => {
    try {
      // Apply optimistic update
      const rollback = () => {
        // Rollback logic would go here
        console.log('Rolling back property update');
      };

      const updateId = optimisticUpdatesManager.applyOptimisticUpdate(
        'properties',
        id,
        updates,
        rollback
      );

      // Try to update on server
      await updateProperty({ id, ...updates });
      
      // Confirm optimistic update
      optimisticUpdatesManager.confirmUpdate(updateId);
      
      addNotification({
        type: 'data_change',
        title: 'Property Updated',
        message: 'Property has been successfully updated',
        priority: 'low'
      });

    } catch (error) {
      // Queue for later if offline
      queueOperation({
        type: 'update',
        entity: 'properties',
        entityId: id,
        data: updates
      });

      addNotification({
        type: 'system_alert',
        title: 'Update Queued',
        message: 'Property update will be synced when connection is restored',
        priority: 'medium'
      });
    }
  }, [updateProperty, queueOperation, addNotification]);

  return {
    properties,
    updatePropertyOptimistically,
  };
}

// Real-time units hook
export function useRealtimeUnits(propertyId: Id<"properties">) {
  const units = useQuery(api.units.getByProperty, { propertyId });
  const updateUnit = useMutation(api.units.update);
  const { queueOperation } = useRealtimeSync();

  const updateUnitOptimistically = useCallback(async (
    id: Id<"units">,
    updates: any
  ) => {
    try {
      await updateUnit({ id, ...updates });
      
      // Notify about data change
      dataChangeNotificationSystem.notifyChange({
        entityType: 'units',
        entityId: id,
        changeType: 'updated',
        data: updates,
        timestamp: Date.now()
      });

    } catch (error) {
      queueOperation({
        type: 'update',
        entity: 'units',
        entityId: id,
        data: updates
      });
    }
  }, [updateUnit, queueOperation]);

  return {
    units,
    updateUnitOptimistically,
  };
}

// Real-time leases hook
export function useRealtimeLeases(propertyId?: Id<"properties">) {
  const leases = useQuery(
    api.leases.getAll,
    propertyId ? { propertyId } : {}
  );

  const updateLease = useMutation(api.leases.update);
  const { queueOperation } = useRealtimeSync();

  const updateLeaseOptimistically = useCallback(async (
    id: Id<"leases">,
    updates: any
  ) => {
    try {
      await updateLease({ id, ...updates });
      
      dataChangeNotificationSystem.notifyChange({
        entityType: 'leases',
        entityId: id,
        changeType: 'updated',
        data: updates,
        timestamp: Date.now()
      });

    } catch (error) {
      queueOperation({
        type: 'update',
        entity: 'leases',
        entityId: id,
        data: updates
      });
    }
  }, [updateLease, queueOperation]);

  return {
    leases,
    updateLeaseOptimistically,
  };
}

// Real-time maintenance tickets hook
export function useRealtimeMaintenanceTickets(propertyId?: Id<"properties">) {
  const tickets = useQuery(
    api.maintenance.getTickets,
    propertyId ? { propertyId } : {}
  );

  const updateTicket = useMutation(api.maintenance.updateTicket);
  const { queueOperation } = useRealtimeSync();
  const { addNotification } = useRealtimeNotifications();

  const updateTicketOptimistically = useCallback(async (
    id: Id<"maintenanceTickets">,
    updates: any
  ) => {
    try {
      await updateTicket({ id, ...updates });
      
      // Add notification for status changes
      if (updates.status) {
        addNotification({
          type: 'maintenance_update',
          title: 'Ticket Updated',
          message: `Maintenance ticket status changed to ${updates.status}`,
          priority: updates.status === 'completed' ? 'low' : 'medium'
        });
      }

      dataChangeNotificationSystem.notifyChange({
        entityType: 'maintenanceTickets',
        entityId: id,
        changeType: 'updated',
        data: updates,
        timestamp: Date.now()
      });

    } catch (error) {
      queueOperation({
        type: 'update',
        entity: 'maintenanceTickets',
        entityId: id,
        data: updates
      });
    }
  }, [updateTicket, queueOperation, addNotification]);

  return {
    tickets,
    updateTicketOptimistically,
  };
}

// Real-time sync status hook
export function useRealtimeSyncStatus() {
  const { syncState, processPendingOperations } = useRealtimeSync();
  const [lastHeartbeat, setLastHeartbeat] = useState<number>(Date.now());
  const heartbeatInterval = useRef<NodeJS.Timeout>();

  // Heartbeat to check connection
  const heartbeat = useQuery(api.realtimeSync.heartbeat, {});

  useEffect(() => {
    if (heartbeat) {
      setLastHeartbeat(heartbeat.timestamp);
    }
  }, [heartbeat]);

  // Start heartbeat interval
  useEffect(() => {
    heartbeatInterval.current = setInterval(() => {
      // Heartbeat will be automatically triggered by Convex query
    }, 30000); // Every 30 seconds

    return () => {
      if (heartbeatInterval.current) {
        clearInterval(heartbeatInterval.current);
      }
    };
  }, []);

  // Auto-retry pending operations
  useEffect(() => {
    if (syncState.isOnline && syncState.pendingOperations.length > 0) {
      const retryTimeout = setTimeout(() => {
        processPendingOperations();
      }, 1000);

      return () => clearTimeout(retryTimeout);
    }
  }, [syncState.isOnline, syncState.pendingOperations.length, processPendingOperations]);

  return {
    isOnline: syncState.isOnline,
    lastSyncTime: syncState.lastSyncTime,
    pendingOperations: syncState.pendingOperations.length,
    lastHeartbeat,
    isConnected: Date.now() - lastHeartbeat < 60000, // Consider connected if heartbeat within 1 minute
  };
}

// Batch operations hook for efficient syncing
export function useBatchOperations() {
  const batchSync = useMutation(api.realtimeSync.batchSync);
  const [pendingBatch, setPendingBatch] = useState<any[]>([]);
  const batchTimeout = useRef<NodeJS.Timeout>();

  const addToBatch = useCallback((operation: any) => {
    setPendingBatch(prev => [...prev, operation]);

    // Clear existing timeout
    if (batchTimeout.current) {
      clearTimeout(batchTimeout.current);
    }

    // Set new timeout to execute batch
    batchTimeout.current = setTimeout(async () => {
      if (pendingBatch.length > 0) {
        try {
          await batchSync({ operations: pendingBatch });
          setPendingBatch([]);
        } catch (error) {
          console.error('Batch sync failed:', error);
        }
      }
    }, 500); // Batch operations for 500ms

  }, [batchSync, pendingBatch]);

  const executeBatch = useCallback(async () => {
    if (pendingBatch.length === 0) return;

    try {
      const result = await batchSync({ operations: pendingBatch });
      setPendingBatch([]);
      return result;
    } catch (error) {
      console.error('Batch sync failed:', error);
      throw error;
    }
  }, [batchSync, pendingBatch]);

  return {
    addToBatch,
    executeBatch,
    pendingCount: pendingBatch.length,
  };
}