import React, { useState } from 'react';
import { Id } from '../../../convex/_generated/dataModel';
import { UnitList } from './UnitList';
import { UnitForm } from './UnitForm';
import { UnitDetails } from './UnitDetails';

type ViewMode = 'list' | 'create' | 'edit' | 'details';

interface UnitManagementProps {
  propertyId: Id<"properties">;
}

export const UnitManagement: React.FC<UnitManagementProps> = ({ propertyId }) => {
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedUnitId, setSelectedUnitId] = useState<Id<"units"> | null>(null);

  const handleCreateNew = () => {
    setSelectedUnitId(null);
    setViewMode('create');
  };

  const handleEditUnit = (unitId: Id<"units">) => {
    setSelectedUnitId(unitId);
    setViewMode('edit');
  };

  const handleUnitSelect = (unitId: Id<"units">) => {
    setSelectedUnitId(unitId);
    setViewMode('details');
  };

  const handleFormSuccess = () => {
    setViewMode('list');
    setSelectedUnitId(null);
  };

  const handleFormCancel = () => {
    setViewMode('list');
    setSelectedUnitId(null);
  };

  const handleBackToList = () => {
    setViewMode('list');
    setSelectedUnitId(null);
  };

  const handleEditFromDetails = () => {
    if (selectedUnitId) {
      setViewMode('edit');
    }
  };

  return (
    <div className="space-y-6">
      {viewMode === 'list' && (
        <UnitList
          propertyId={propertyId}
          onUnitSelect={handleUnitSelect}
          onCreateNew={handleCreateNew}
          onEditUnit={handleEditUnit}
        />
      )}

      {(viewMode === 'create' || viewMode === 'edit') && (
        <UnitForm
          propertyId={propertyId}
          unitId={viewMode === 'edit' ? selectedUnitId || undefined : undefined}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      )}

      {viewMode === 'details' && selectedUnitId && (
        <UnitDetails
          unitId={selectedUnitId}
          onEdit={handleEditFromDetails}
          onBack={handleBackToList}
        />
      )}
    </div>
  );
};