/**
 * Real-time Notification System
 * Handles real-time notifications for data changes and system events
 */

import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useCallback, useEffect, useState } from "react";
import { subscriptionManager } from "./realtime-sync";

// Notification types
export interface RealtimeNotification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  timestamp: number;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actions?: NotificationAction[];
}

export interface NotificationAction {
  id: string;
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

export type NotificationType = 
  | 'data_change'
  | 'maintenance_update'
  | 'payment_received'
  | 'lease_signed'
  | 'ticket_assigned'
  | 'sla_warning'
  | 'system_alert'
  | 'user_action';

// Real-time notification hook
export function useRealtimeNotifications(userId?: Id<"users">) {
  const [notifications, setNotifications] = useState<RealtimeNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Subscribe to user notifications
  const userNotifications = useQuery(
    api.notifications.getUserNotifications,
    userId ? { userId } : "skip"
  );

  // Update local notifications when server data changes
  useEffect(() => {
    if (userNotifications) {
      const realtimeNotifications: RealtimeNotification[] = userNotifications.map(notification => ({
        id: notification._id,
        type: mapNotificationType(notification.type),
        title: notification.title,
        message: notification.message,
        timestamp: notification.createdAt,
        read: notification.isRead,
        priority: notification.priority,
        data: notification.metadata
      }));

      setNotifications(realtimeNotifications);
      setUnreadCount(realtimeNotifications.filter(n => !n.read).length);
    }
  }, [userNotifications]);

  // Add new notification
  const addNotification = useCallback((notification: Omit<RealtimeNotification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: RealtimeNotification = {
      ...notification,
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      read: false
    };

    setNotifications(prev => [newNotification, ...prev]);
    setUnreadCount(prev => prev + 1);

    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: newNotification.id
      });
    }

    return newNotification.id;
  }, []);

  // Mark notification as read
  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(() => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
    setUnreadCount(0);
  }, []);

  // Remove notification
  const removeNotification = useCallback((notificationId: string) => {
    setNotifications(prev => {
      const notification = prev.find(n => n.id === notificationId);
      const newNotifications = prev.filter(n => n.id !== notificationId);
      
      if (notification && !notification.read) {
        setUnreadCount(count => Math.max(0, count - 1));
      }
      
      return newNotifications;
    });
  }, []);

  // Clear all notifications
  const clearAll = useCallback(() => {
    setNotifications([]);
    setUnreadCount(0);
  }, []);

  return {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll
  };
}

// Data change notification system
export class DataChangeNotificationSystem {
  private changeListeners = new Map<string, Set<(change: DataChange) => void>>();

  subscribe(entityType: string, callback: (change: DataChange) => void): () => void {
    if (!this.changeListeners.has(entityType)) {
      this.changeListeners.set(entityType, new Set());
    }
    
    this.changeListeners.get(entityType)!.add(callback);

    return () => {
      const listeners = this.changeListeners.get(entityType);
      if (listeners) {
        listeners.delete(callback);
        if (listeners.size === 0) {
          this.changeListeners.delete(entityType);
        }
      }
    };
  }

  notifyChange(change: DataChange) {
    const listeners = this.changeListeners.get(change.entityType);
    if (listeners) {
      listeners.forEach(callback => callback(change));
    }

    // Also notify wildcard listeners
    const wildcardListeners = this.changeListeners.get('*');
    if (wildcardListeners) {
      wildcardListeners.forEach(callback => callback(change));
    }
  }
}

export interface DataChange {
  entityType: string;
  entityId: string;
  changeType: 'created' | 'updated' | 'deleted';
  data: any;
  previousData?: any;
  timestamp: number;
  userId?: Id<"users">;
}

// Global data change notification system
export const dataChangeNotificationSystem = new DataChangeNotificationSystem();

// Real-time data change hook
export function useDataChangeNotifications(entityType: string, onDataChange?: (change: DataChange) => void) {
  const [changes, setChanges] = useState<DataChange[]>([]);

  useEffect(() => {
    const unsubscribe = dataChangeNotificationSystem.subscribe(entityType, (change) => {
      setChanges(prev => [change, ...prev.slice(0, 99)]); // Keep last 100 changes
      onDataChange?.(change);
    });

    return unsubscribe;
  }, [entityType, onDataChange]);

  return changes;
}

// Notification permission manager
export class NotificationPermissionManager {
  static async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return 'denied';
    }

    if (Notification.permission === 'granted') {
      return 'granted';
    }

    if (Notification.permission === 'denied') {
      return 'denied';
    }

    const permission = await Notification.requestPermission();
    return permission;
  }

  static isSupported(): boolean {
    return 'Notification' in window;
  }

  static getPermission(): NotificationPermission {
    return Notification.permission;
  }
}

// Helper function to map notification types
function mapNotificationType(type: string): NotificationType {
  const typeMap: Record<string, NotificationType> = {
    'payment_reminder': 'payment_received',
    'maintenance_update': 'maintenance_update',
    'maintenance_assigned': 'ticket_assigned',
    'maintenance_escalated': 'sla_warning',
    'maintenance_completed': 'maintenance_update',
    'sla_warning': 'sla_warning',
    'lease_expiry': 'lease_signed',
    'system': 'system_alert'
  };

  return typeMap[type] || 'system_alert';
}

// Real-time connection status hook
export function useConnectionStatus() {
  const [isConnected, setIsConnected] = useState(navigator.onLine);
  const [lastConnected, setLastConnected] = useState<Date | null>(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  useEffect(() => {
    const handleOnline = () => {
      setIsConnected(true);
      setLastConnected(new Date());
      setReconnectAttempts(0);
    };

    const handleOffline = () => {
      setIsConnected(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initial connection check
    if (navigator.onLine) {
      setLastConnected(new Date());
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const attemptReconnect = useCallback(() => {
    setReconnectAttempts(prev => prev + 1);
    // Trigger a connection check
    window.dispatchEvent(new Event('online'));
  }, []);

  return {
    isConnected,
    lastConnected,
    reconnectAttempts,
    attemptReconnect
  };
}