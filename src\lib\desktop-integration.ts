/**
 * Desktop Integration Service
 * Provides a unified interface for native desktop features
 */

export interface DesktopCapabilities {
  fileSystem: boolean
  notifications: boolean
  printing: boolean
  windowManagement: boolean
  systemTray: boolean
  globalShortcuts: boolean
}

export class DesktopIntegrationService {
  private static instance: DesktopIntegrationService
  private capabilities: DesktopCapabilities

  private constructor() {
    this.capabilities = this.detectCapabilities()
  }

  public static getInstance(): DesktopIntegrationService {
    if (!DesktopIntegrationService.instance) {
      DesktopIntegrationService.instance = new DesktopIntegrationService()
    }
    return DesktopIntegrationService.instance
  }

  private detectCapabilities(): DesktopCapabilities {
    const isElectron = !!window.electronAPI
    const hasNotifications = 'Notification' in window || isElectron
    
    return {
      fileSystem: isElectron,
      notifications: hasNotifications,
      printing: isElectron || window.print !== undefined,
      windowManagement: isElectron,
      systemTray: isElectron,
      globalShortcuts: isElectron
    }
  }

  public getCapabilities(): DesktopCapabilities {
    return { ...this.capabilities }
  }

  public isDesktopMode(): boolean {
    return !!window.electronAPI
  }

  public async getSystemInfo() {
    if (!window.electronAPI) {
      return {
        platform: 'web',
        version: '1.0.0',
        isElectron: false
      }
    }

    try {
      const [platform, version] = await Promise.all([
        window.electronAPI.getPlatform(),
        window.electronAPI.getVersion()
      ])

      return {
        platform,
        version,
        isElectron: true
      }
    } catch (error) {
      console.error('Failed to get system info:', error)
      return {
        platform: 'unknown',
        version: 'unknown',
        isElectron: true
      }
    }
  }

  // File System Operations
  public async selectFiles(options: {
    multiple?: boolean
    accept?: string[]
    title?: string
  } = {}): Promise<File[]> {
    if (!this.capabilities.fileSystem) {
      // Fallback to web file input
      return new Promise((resolve) => {
        const input = document.createElement('input')
        input.type = 'file'
        input.multiple = options.multiple || false
        if (options.accept) {
          input.accept = options.accept.join(',')
        }
        
        input.onchange = (e) => {
          const files = Array.from((e.target as HTMLInputElement).files || [])
          resolve(files)
        }
        
        input.click()
      })
    }

    // Use native file dialog
    const result = await window.electronAPI!.showOpenDialog({
      title: options.title,
      properties: options.multiple ? ['openFile', 'multiSelections'] : ['openFile'],
      filters: options.accept ? [
        { name: 'Allowed Files', extensions: options.accept }
      ] : undefined
    })

    if (result.canceled) {
      return []
    }

    // Convert file paths to File objects
    const files: File[] = []
    for (const filePath of result.filePaths) {
      try {
        const fileResult = await window.electronAPI!.readFile(filePath)
        if (fileResult.success && fileResult.content) {
          const fileName = filePath.split(/[/\\]/).pop() || 'unknown'
          const blob = new Blob([fileResult.content], { type: 'text/plain' })
          const file = new File([blob], fileName)
          files.push(file)
        }
      } catch (error) {
        console.error(`Failed to read file ${filePath}:`, error)
      }
    }

    return files
  }

  public async saveFile(content: string, filename: string, options: {
    filters?: Array<{ name: string; extensions: string[] }>
  } = {}): Promise<boolean> {
    if (!this.capabilities.fileSystem) {
      // Fallback to download
      const blob = new Blob([content], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      return true
    }

    const result = await window.electronAPI!.showSaveDialog({
      defaultPath: filename,
      filters: options.filters || [
        { name: 'All Files', extensions: ['*'] }
      ]
    })

    if (result.canceled || !result.filePath) {
      return false
    }

    const writeResult = await window.electronAPI!.writeFile(result.filePath, content)
    return writeResult.success
  }

  // Notification Operations
  public async showNotification(options: {
    title: string
    body: string
    icon?: string
    tag?: string
    requireInteraction?: boolean
  }): Promise<boolean> {
    if (this.capabilities.notifications && window.electronAPI) {
      try {
        return await window.electronAPI.showNotification(options)
      } catch (error) {
        console.error('Native notification failed:', error)
      }
    }

    // Fallback to web notifications
    if ('Notification' in window) {
      if (Notification.permission === 'granted') {
        new Notification(options.title, {
          body: options.body,
          icon: options.icon,
          tag: options.tag,
          requireInteraction: options.requireInteraction
        })
        return true
      } else if (Notification.permission !== 'denied') {
        const permission = await Notification.requestPermission()
        if (permission === 'granted') {
          new Notification(options.title, {
            body: options.body,
            icon: options.icon,
            tag: options.tag,
            requireInteraction: options.requireInteraction
          })
          return true
        }
      }
    }

    return false
  }

  // Print Operations
  public async printContent(html: string, options: any = {}): Promise<boolean> {
    if (this.capabilities.printing && window.electronAPI) {
      try {
        const result = await window.electronAPI.printDocument(html, options)
        return result.success
      } catch (error) {
        console.error('Native print failed:', error)
      }
    }

    // Fallback to browser print
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(html)
      printWindow.document.close()
      printWindow.print()
      printWindow.close()
      return true
    }

    return false
  }

  public async exportToPDF(html: string, filename: string, options: any = {}): Promise<boolean> {
    if (!this.capabilities.printing || !window.electronAPI) {
      throw new Error('PDF export not available in web environment')
    }

    try {
      const result = await window.electronAPI.printToPDF(html, options)
      if (result.success && result.buffer) {
        const uint8Array = new Uint8Array(result.buffer)
        const blob = new Blob([uint8Array], { type: 'application/pdf' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = filename
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        return true
      }
    } catch (error) {
      console.error('PDF export failed:', error)
    }

    return false
  }

  // Window Management
  public async minimizeWindow(): Promise<void> {
    if (this.capabilities.windowManagement && window.electronAPI) {
      await window.electronAPI.minimizeWindow()
    }
  }

  public async maximizeWindow(): Promise<void> {
    if (this.capabilities.windowManagement && window.electronAPI) {
      await window.electronAPI.maximizeWindow()
    }
  }

  public async hideWindow(): Promise<void> {
    if (this.capabilities.windowManagement && window.electronAPI) {
      await window.electronAPI.hideWindow()
    }
  }

  public async showWindow(): Promise<void> {
    if (this.capabilities.windowManagement && window.electronAPI) {
      await window.electronAPI.showWindow()
    }
  }

  // Utility Methods
  public async requestNotificationPermission(): Promise<boolean> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }
    return false
  }

  public getNotificationPermission(): NotificationPermission | 'granted' {
    if (window.electronAPI) {
      return 'granted' // Electron doesn't require permission
    }
    
    if ('Notification' in window) {
      return Notification.permission
    }
    
    return 'denied'
  }
}

// Export singleton instance
export const desktopIntegration = DesktopIntegrationService.getInstance()

// Export hook for React components
export const useDesktopIntegration = () => {
  return desktopIntegration
}