import { describe, test, expect, vi } from 'vitest';
import { TwilioService } from '../../../lib/twilio-service';

// Mock Twilio
const mockTwilioClient = {
  messages: {
    create: vi.fn(),
    list: vi.fn(),
  },
  api: {
    accounts: vi.fn(() => ({
      fetch: vi.fn(),
      balance: {
        fetch: vi.fn(),
      },
    })),
  },
};

vi.mock('twilio', () => ({
  Twilio: vi.fn(() => mockTwilioClient),
}));

describe('SMS and WhatsApp Integration', () => {
  const mockConfig = {
    accountSid: 'test_account_sid',
    authToken: 'test_auth_token',
    phoneNumber: '+**********',
    whatsappNumber: '+**********',
  };

  test('should create TwilioService instance', () => {
    const service = new TwilioService(mockConfig);
    expect(service).toBeDefined();
  });

  test('should process message templates correctly', () => {
    const service = new TwilioService(mockConfig);
    
    const template = {
      content: 'Hello {{tenant_name}}, your rent of {{amount}} is due on {{due_date}}.',
      variables: ['tenant_name', 'amount', 'due_date']
    };
    
    const data = {
      tenant_name: 'John Doe',
      amount: '50,000',
      due_date: '2024-01-15'
    };
    
    const result = service.processTemplate(template, data);
    
    expect(result).toBe('Hello John Doe, your rent of 50,000 is due on 2024-01-15.');
  });

  test('should validate phone numbers correctly', () => {
    const service = new TwilioService(mockConfig);
    
    // Valid international numbers
    expect(service.validatePhoneNumber('+254712345678')).toBe(true);
    expect(service.validatePhoneNumber('+**********')).toBe(true);
    
    // Invalid numbers
    expect(service.validatePhoneNumber('0712345678')).toBe(false);
    expect(service.validatePhoneNumber('invalid')).toBe(false);
  });

  test('should send templated SMS successfully', async () => {
    const service = new TwilioService(mockConfig);
    
    const mockResponse = {
      sid: 'SM123456789',
      status: 'sent',
      to: '+254712345678',
      from: '+**********',
      body: 'Hello John Doe, your rent of 50,000 is due on 2024-01-15.',
      dateCreated: new Date(),
    };

    mockTwilioClient.messages.create.mockResolvedValue(mockResponse);

    const template = {
      content: 'Hello {{tenant_name}}, your rent of {{amount}} is due on {{due_date}}.',
      variables: ['tenant_name', 'amount', 'due_date']
    };
    
    const data = {
      tenant_name: 'John Doe',
      amount: '50,000',
      due_date: '2024-01-15'
    };

    const result = await service.sendTemplatedSMS('+254712345678', template, data);

    expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
      body: 'Hello John Doe, your rent of 50,000 is due on 2024-01-15.',
      from: '+**********',
      to: '+254712345678',
    });

    expect(result.sid).toBe('SM123456789');
    expect(result.status).toBe('sent');
  });

  test('should send templated WhatsApp message successfully', async () => {
    const service = new TwilioService(mockConfig);
    
    const mockResponse = {
      sid: 'WA123456789',
      status: 'sent',
      to: 'whatsapp:+254712345678',
      from: 'whatsapp:+**********',
      body: 'Your maintenance request #MT-001 has been assigned to ABC Repairs.',
      dateCreated: new Date(),
    };

    mockTwilioClient.messages.create.mockResolvedValue(mockResponse);

    const template = {
      content: 'Your maintenance request #{{ticket_id}} has been assigned to {{vendor_name}}.',
      variables: ['ticket_id', 'vendor_name']
    };
    
    const data = {
      ticket_id: 'MT-001',
      vendor_name: 'ABC Repairs'
    };

    const result = await service.sendTemplatedWhatsApp('+254712345678', template, data);

    expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
      body: 'Your maintenance request #MT-001 has been assigned to ABC Repairs.',
      from: 'whatsapp:+**********',
      to: 'whatsapp:+254712345678',
    });

    expect(result.sid).toBe('WA123456789');
    expect(result.status).toBe('sent');
  });

  test('should handle bulk messaging with rate limiting', async () => {
    const service = new TwilioService(mockConfig);
    
    const recipients = [
      { phoneNumber: '+254712345678', personalizationData: { tenant_name: 'John Doe' } },
      { phoneNumber: '+254723456789', personalizationData: { tenant_name: 'Jane Smith' } },
    ];

    const template = {
      content: 'Hello {{tenant_name}}, this is a test message.',
      variables: ['tenant_name']
    };

    const mockResponses = [
      {
        sid: 'SM123456789',
        status: 'sent',
        to: '+254712345678',
        from: '+**********',
        body: 'Hello John Doe, this is a test message.',
        dateCreated: new Date(),
      },
      {
        sid: 'SM987654321',
        status: 'sent',
        to: '+254723456789',
        from: '+**********',
        body: 'Hello Jane Smith, this is a test message.',
        dateCreated: new Date(),
      },
    ];

    mockTwilioClient.messages.create
      .mockResolvedValueOnce(mockResponses[0])
      .mockResolvedValueOnce(mockResponses[1]);

    const results = await service.sendBulkTemplatedMessages('sms', recipients, template);

    expect(results).toHaveLength(2);
    expect(results[0].result?.sid).toBe('SM123456789');
    expect(results[1].result?.sid).toBe('SM987654321');
  });

  test('should get delivery status for messages', async () => {
    const service = new TwilioService(mockConfig);
    
    const mockMessage = {
      sid: 'SM123456789',
      status: 'delivered',
      to: '+254712345678',
      from: '+**********',
      body: 'Test message',
      dateCreated: new Date(),
      dateUpdated: new Date(),
      dateSent: new Date(),
    };

    // Mock the messages function to return an object with fetch method
    mockTwilioClient.messages = vi.fn(() => ({
      fetch: vi.fn().mockResolvedValue(mockMessage),
    }));

    const result = await service.getDeliveryStatus('SM123456789');

    expect(result.sid).toBe('SM123456789');
    expect(result.status).toBe('delivered');
  });
});