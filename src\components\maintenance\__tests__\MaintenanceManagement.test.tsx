import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { MaintenanceManagement } from '../MaintenanceManagement';
import { ConvexProvider, ConvexReactClient } from 'convex/react';

// Mock Convex client
const mockConvexClient = new ConvexReactClient('https://test.convex.cloud');

// Mock data
const mockTickets = [
  {
    _id: 'ticket1' as any,
    propertyId: 'property1' as any,
    tenantId: 'user1' as any,
    title: 'Leaking faucet in kitchen',
    description: 'The kitchen faucet has been leaking for 2 days',
    category: 'plumbing',
    priority: 'medium',
    status: 'open',
    slaDeadline: Date.now() + 24 * 60 * 60 * 1000, // 24 hours from now
    images: [],
    notes: [],
    escalationHistory: [],
    createdAt: Date.now() - 2 * 60 * 60 * 1000, // 2 hours ago
    updatedAt: Date.now() - 2 * 60 * 60 * 1000,
  },
  {
    _id: 'ticket2' as any,
    propertyId: 'property1' as any,
    tenantId: 'user2' as any,
    vendorId: 'vendor1' as any,
    title: 'Broken air conditioning',
    description: 'AC unit not working in unit 205',
    category: 'hvac',
    priority: 'high',
    status: 'assigned',
    slaDeadline: Date.now() + 6 * 60 * 60 * 1000, // 6 hours from now
    images: [],
    notes: [],
    escalationHistory: [],
    createdAt: Date.now() - 4 * 60 * 60 * 1000, // 4 hours ago
    updatedAt: Date.now() - 1 * 60 * 60 * 1000, // 1 hour ago
  }
];

const mockSLAMetrics = {
  totalTickets: 2,
  completedTickets: 0,
  slaCompliance: 85.5,
  averageResolutionTime: 12.5,
  ticketsByPriority: {
    emergency: 0,
    high: 1,
    medium: 1,
    low: 0,
  },
  ticketsByStatus: {
    open: 1,
    assigned: 1,
    in_progress: 0,
    completed: 0,
    closed: 0,
    escalated: 0,
  },
};

// Mock Convex hooks
vi.mock('convex/react', async () => {
  const actual = await vi.importActual('convex/react');
  return {
    ...actual,
    useQuery: vi.fn((query, args) => {
      if (query.toString().includes('getTicketsByProperty')) {
        return mockTickets;
      }
      if (query.toString().includes('getSLAMetrics')) {
        return mockSLAMetrics;
      }
      return null;
    }),
    useMutation: vi.fn(() => vi.fn()),
  };
});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ConvexProvider client={mockConvexClient}>
    {children}
  </ConvexProvider>
);

describe('MaintenanceManagement', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders maintenance management interface', () => {
    render(
      <TestWrapper>
        <MaintenanceManagement propertyId="property1" as any />
      </TestWrapper>
    );

    expect(screen.getByText('Maintenance Management')).toBeInTheDocument();
    expect(screen.getByText('Track and manage maintenance requests and tickets')).toBeInTheDocument();
    expect(screen.getByText('Create Ticket')).toBeInTheDocument();
  });

  it('displays SLA metrics cards', () => {
    render(
      <TestWrapper>
        <MaintenanceManagement propertyId="property1" as any />
      </TestWrapper>
    );

    expect(screen.getByText('Total Tickets')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('SLA Compliance')).toBeInTheDocument();
    expect(screen.getByText('85.5%')).toBeInTheDocument();
    expect(screen.getByText('Avg Resolution Time')).toBeInTheDocument();
    expect(screen.getByText('12.5h')).toBeInTheDocument();
  });

  it('displays ticket list', () => {
    render(
      <TestWrapper>
        <MaintenanceManagement propertyId="property1" as any />
      </TestWrapper>
    );

    expect(screen.getByText('Leaking faucet in kitchen')).toBeInTheDocument();
    expect(screen.getByText('Broken air conditioning')).toBeInTheDocument();
    expect(screen.getByText('medium')).toBeInTheDocument();
    expect(screen.getByText('high')).toBeInTheDocument();
  });

  it('filters tickets by search term', async () => {
    render(
      <TestWrapper>
        <MaintenanceManagement propertyId="property1" as any />
      </TestWrapper>
    );

    const searchInput = screen.getByPlaceholderText('Search tickets...');
    fireEvent.change(searchInput, { target: { value: 'faucet' } });

    await waitFor(() => {
      expect(screen.getByText('Leaking faucet in kitchen')).toBeInTheDocument();
      expect(screen.queryByText('Broken air conditioning')).not.toBeInTheDocument();
    });
  });

  it('filters tickets by status', async () => {
    render(
      <TestWrapper>
        <MaintenanceManagement propertyId="property1" as any />
      </TestWrapper>
    );

    const statusFilter = screen.getByDisplayValue('All Statuses');
    fireEvent.click(statusFilter);
    
    const openOption = screen.getByText('Open');
    fireEvent.click(openOption);

    // This would trigger a re-query with status filter
    // In a real test, we'd mock the query response differently
  });

  it('shows create ticket form when create button is clicked', () => {
    render(
      <TestWrapper>
        <MaintenanceManagement propertyId="property1" as any />
      </TestWrapper>
    );

    const createButton = screen.getByText('Create Ticket');
    fireEvent.click(createButton);

    // This would show the TicketForm component
    // We'd need to test this integration separately
  });

  it('shows ticket details when view details is clicked', () => {
    render(
      <TestWrapper>
        <MaintenanceManagement propertyId="property1" as any />
      </TestWrapper>
    );

    const viewDetailsButtons = screen.getAllByText('View Details');
    fireEvent.click(viewDetailsButtons[0]);

    // This would show the TicketDetails component
    // We'd need to test this integration separately
  });

  it('displays priority and status badges with correct colors', () => {
    render(
      <TestWrapper>
        <MaintenanceManagement propertyId="property1" as any />
      </TestWrapper>
    );

    // Check that priority badges are displayed
    const mediumBadge = screen.getByText('medium');
    const highBadge = screen.getByText('high');
    
    expect(mediumBadge).toBeInTheDocument();
    expect(highBadge).toBeInTheDocument();

    // Check that status badges are displayed
    const openBadge = screen.getByText('open');
    const assignedBadge = screen.getByText('assigned');
    
    expect(openBadge).toBeInTheDocument();
    expect(assignedBadge).toBeInTheDocument();
  });

  it('displays SLA progress indicators', () => {
    render(
      <TestWrapper>
        <MaintenanceManagement propertyId="property1" as any />
      </TestWrapper>
    );

    // Check for SLA progress elements
    const slaProgressElements = screen.getAllByText(/SLA Progress/);
    expect(slaProgressElements.length).toBeGreaterThan(0);
  });

  it('handles empty ticket list', () => {
    // Mock empty tickets
    const { useQuery } = require('convex/react');
    useQuery.mockImplementation((query) => {
      if (query.toString().includes('getTicketsByProperty')) {
        return [];
      }
      if (query.toString().includes('getSLAMetrics')) {
        return { ...mockSLAMetrics, totalTickets: 0 };
      }
      return null;
    });

    render(
      <TestWrapper>
        <MaintenanceManagement propertyId="property1" as any />
      </TestWrapper>
    );

    expect(screen.getByText('No tickets found')).toBeInTheDocument();
    expect(screen.getByText('No maintenance tickets match your current filters.')).toBeInTheDocument();
  });
});