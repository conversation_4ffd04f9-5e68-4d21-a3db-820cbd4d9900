/**
 * Accessibility context for managing accessibility preferences and settings
 */
import React, { createContext, useContext, useEffect, useState } from 'react';
import { AccessibilityTheme, AccessibilityThemeName, getThemeByName, applyTheme, defaultTheme } from '../lib/accessibility-themes';

interface AccessibilitySettings {
  // Theme settings
  theme: AccessibilityThemeName;
  
  // Motion preferences
  reduceMotion: boolean;
  
  // Font settings
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  fontWeight: 'normal' | 'medium' | 'bold';
  
  // Focus settings
  highContrastFocus: boolean;
  focusIndicatorSize: 'small' | 'medium' | 'large';
  
  // Screen reader settings
  announceChanges: boolean;
  verboseDescriptions: boolean;
  
  // Keyboard navigation
  skipLinks: boolean;
  keyboardShortcuts: boolean;
}

interface AccessibilityContextType {
  settings: AccessibilitySettings;
  updateSettings: (updates: Partial<AccessibilitySettings>) => void;
  currentTheme: AccessibilityTheme;
  announceToScreenReader: (message: string, priority?: 'polite' | 'assertive') => void;
  resetToDefaults: () => void;
}

const defaultSettings: AccessibilitySettings = {
  theme: 'default',
  reduceMotion: false,
  fontSize: 'medium',
  fontWeight: 'normal',
  highContrastFocus: false,
  focusIndicatorSize: 'medium',
  announceChanges: true,
  verboseDescriptions: false,
  skipLinks: true,
  keyboardShortcuts: true,
};

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined);

export function useAccessibility() {
  const context = useContext(AccessibilityContext);
  if (context === undefined) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  return context;
}

interface AccessibilityProviderProps {
  children: React.ReactNode;
}

export function AccessibilityProvider({ children }: AccessibilityProviderProps) {
  const [settings, setSettings] = useState<AccessibilitySettings>(() => {
    // Load settings from localStorage
    const saved = localStorage.getItem('accessibility-settings');
    if (saved) {
      try {
        return { ...defaultSettings, ...JSON.parse(saved) };
      } catch {
        return defaultSettings;
      }
    }
    
    // Check system preferences
    const systemSettings: Partial<AccessibilitySettings> = {};
    
    // Check for reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      systemSettings.reduceMotion = true;
    }
    
    // Check for high contrast preference
    if (window.matchMedia('(prefers-contrast: high)').matches) {
      systemSettings.theme = 'high-contrast';
      systemSettings.highContrastFocus = true;
    }
    
    return { ...defaultSettings, ...systemSettings };
  });

  const currentTheme = getThemeByName(settings.theme);

  const updateSettings = (updates: Partial<AccessibilitySettings>) => {
    setSettings(prev => {
      const newSettings = { ...prev, ...updates };
      
      // Save to localStorage
      localStorage.setItem('accessibility-settings', JSON.stringify(newSettings));
      
      return newSettings;
    });
  };

  const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!settings.announceChanges) return;
    
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      if (document.body.contains(announcement)) {
        document.body.removeChild(announcement);
      }
    }, 1000);
  };

  const resetToDefaults = () => {
    setSettings(defaultSettings);
    localStorage.removeItem('accessibility-settings');
    announceToScreenReader('Accessibility settings reset to defaults');
  };

  // Apply theme when it changes
  useEffect(() => {
    applyTheme(currentTheme);
  }, [currentTheme]);

  // Apply font size settings
  useEffect(() => {
    const root = document.documentElement;
    
    const fontSizeMap = {
      small: '14px',
      medium: '16px',
      large: '18px',
      'extra-large': '20px',
    };
    
    const fontWeightMap = {
      normal: '400',
      medium: '500',
      bold: '600',
    };
    
    root.style.setProperty('--base-font-size', fontSizeMap[settings.fontSize]);
    root.style.setProperty('--base-font-weight', fontWeightMap[settings.fontWeight]);
  }, [settings.fontSize, settings.fontWeight]);

  // Apply focus indicator settings
  useEffect(() => {
    const root = document.documentElement;
    
    const focusSizeMap = {
      small: '2px',
      medium: '3px',
      large: '4px',
    };
    
    root.style.setProperty('--focus-ring-width', focusSizeMap[settings.focusIndicatorSize]);
    root.style.setProperty('--focus-ring-style', settings.highContrastFocus ? 'solid' : 'solid');
    root.style.setProperty('--focus-ring-offset', settings.highContrastFocus ? '2px' : '1px');
  }, [settings.focusIndicatorSize, settings.highContrastFocus]);

  // Apply reduced motion settings
  useEffect(() => {
    const root = document.documentElement;
    root.style.setProperty('--animation-duration', settings.reduceMotion ? '0ms' : '150ms');
    root.style.setProperty('--transition-duration', settings.reduceMotion ? '0ms' : '150ms');
  }, [settings.reduceMotion]);

  // Listen for system preference changes
  useEffect(() => {
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');

    const handleReducedMotionChange = (e: MediaQueryListEvent) => {
      if (e.matches && !settings.reduceMotion) {
        updateSettings({ reduceMotion: true });
        announceToScreenReader('Reduced motion enabled based on system preference');
      }
    };

    const handleHighContrastChange = (e: MediaQueryListEvent) => {
      if (e.matches && settings.theme === 'default') {
        updateSettings({ theme: 'high-contrast', highContrastFocus: true });
        announceToScreenReader('High contrast theme enabled based on system preference');
      }
    };

    reducedMotionQuery.addEventListener('change', handleReducedMotionChange);
    highContrastQuery.addEventListener('change', handleHighContrastChange);

    return () => {
      reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
      highContrastQuery.removeEventListener('change', handleHighContrastChange);
    };
  }, [settings.reduceMotion, settings.theme]);

  const value: AccessibilityContextType = {
    settings,
    updateSettings,
    currentTheme,
    announceToScreenReader,
    resetToDefaults,
  };

  return (
    <AccessibilityContext.Provider value={value}>
      {children}
    </AccessibilityContext.Provider>
  );
}