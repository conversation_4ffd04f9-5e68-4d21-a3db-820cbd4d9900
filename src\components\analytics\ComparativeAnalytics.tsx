import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Select } from '../ui/select';

interface ComparativeAnalyticsProps {
  selectedProperties?: Id<"properties">[];
}

export const ComparativeAnalytics: React.FC<ComparativeAnalyticsProps> = ({
  selectedProperties = [],
}) => {
  const [compareMetric, setCompareMetric] = useState<'occupancy' | 'revenue' | 'efficiency'>('occupancy');
  
  const allProperties = useQuery(api.properties.getProperties, {});
  
  // Get analytics for selected properties
  const propertyAnalytics = selectedProperties.map(propertyId => ({
    propertyId,
    property: useQuery(api.properties.getPropertyById, { id: propertyId }),
    analytics: useQuery(api.properties.getPropertyAnalytics, { propertyId }),
    unitAnalytics: useQuery(api.units.getUnitOccupancyAnalytics, { propertyId }),
  }));

  const formatCurrency = (amount: number, currency = 'KES') => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getMetricValue = (data: any, metric: string) => {
    if (!data.analytics) return 0;
    
    switch (metric) {
      case 'occupancy':
        return data.analytics.occupancyRate;
      case 'revenue':
        return data.analytics.totalMonthlyRevenue;
      case 'efficiency':
        const potential = data.analytics.totalUnits * (data.analytics.totalMonthlyRevenue / Math.max(data.analytics.activeLeases, 1));
        return potential > 0 ? (data.analytics.totalMonthlyRevenue / potential) * 100 : 0;
      default:
        return 0;
    }
  };

  const getMetricLabel = (metric: string) => {
    switch (metric) {
      case 'occupancy':
        return 'Occupancy Rate (%)';
      case 'revenue':
        return 'Monthly Revenue';
      case 'efficiency':
        return 'Revenue Efficiency (%)';
      default:
        return '';
    }
  };

  const formatMetricValue = (value: number, metric: string, currency = 'KES') => {
    switch (metric) {
      case 'occupancy':
      case 'efficiency':
        return `${value.toFixed(1)}%`;
      case 'revenue':
        return formatCurrency(value, currency);
      default:
        return value.toString();
    }
  };

  const getBestPerformer = () => {
    if (propertyAnalytics.length === 0) return null;
    
    return propertyAnalytics.reduce((best, current) => {
      const bestValue = getMetricValue(best, compareMetric);
      const currentValue = getMetricValue(current, compareMetric);
      return currentValue > bestValue ? current : best;
    });
  };

  const getWorstPerformer = () => {
    if (propertyAnalytics.length === 0) return null;
    
    return propertyAnalytics.reduce((worst, current) => {
      const worstValue = getMetricValue(worst, compareMetric);
      const currentValue = getMetricValue(current, compareMetric);
      return currentValue < worstValue ? current : worst;
    });
  };

  const calculateAverage = () => {
    if (propertyAnalytics.length === 0) return 0;
    
    const total = propertyAnalytics.reduce((sum, data) => {
      return sum + getMetricValue(data, compareMetric);
    }, 0);
    
    return total / propertyAnalytics.length;
  };

  if (!allProperties || propertyAnalytics.some(p => !p.property || !p.analytics)) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (selectedProperties.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <div className="text-gray-500">
            <h3 className="text-lg font-semibold mb-2">No Properties Selected</h3>
            <p className="mb-4">
              Select properties from the main dashboard to compare their performance.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const bestPerformer = getBestPerformer();
  const worstPerformer = getWorstPerformer();
  const average = calculateAverage();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Comparative Analytics</h2>
          <p className="text-gray-600">Compare performance across {selectedProperties.length} properties</p>
        </div>
        <div>
          <Select
            value={compareMetric}
            onChange={(e) => setCompareMetric(e.target.value as any)}
          >
            <option value="occupancy">Occupancy Rate</option>
            <option value="revenue">Monthly Revenue</option>
            <option value="efficiency">Revenue Efficiency</option>
          </Select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Best Performer</CardTitle>
          </CardHeader>
          <CardContent>
            {bestPerformer && (
              <>
                <div className="text-2xl font-bold text-green-600 mb-1">
                  {formatMetricValue(
                    getMetricValue(bestPerformer, compareMetric),
                    compareMetric,
                    bestPerformer.property?.settings?.currency
                  )}
                </div>
                <p className="text-sm text-gray-600">{bestPerformer.property?.name}</p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Portfolio Average</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {formatMetricValue(average, compareMetric)}
            </div>
            <p className="text-sm text-gray-600">Across all properties</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Needs Attention</CardTitle>
          </CardHeader>
          <CardContent>
            {worstPerformer && (
              <>
                <div className="text-2xl font-bold text-red-600 mb-1">
                  {formatMetricValue(
                    getMetricValue(worstPerformer, compareMetric),
                    compareMetric,
                    worstPerformer.property?.settings?.currency
                  )}
                </div>
                <p className="text-sm text-gray-600">{worstPerformer.property?.name}</p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Detailed Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Property Comparison - {getMetricLabel(compareMetric)}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {propertyAnalytics
              .sort((a, b) => getMetricValue(b, compareMetric) - getMetricValue(a, compareMetric))
              .map((data, index) => {
                const value = getMetricValue(data, compareMetric);
                const maxValue = Math.max(...propertyAnalytics.map(p => getMetricValue(p, compareMetric)));
                const percentage = maxValue > 0 ? (value / maxValue) * 100 : 0;
                
                return (
                  <div key={data.propertyId} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium ${
                          index === 0 ? 'bg-green-100 text-green-800' :
                          index === propertyAnalytics.length - 1 ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {index + 1}
                        </span>
                        <span className="font-medium">{data.property?.name}</span>
                      </div>
                      <span className="font-semibold">
                        {formatMetricValue(value, compareMetric, data.property?.settings?.currency)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          index === 0 ? 'bg-green-500' :
                          index === propertyAnalytics.length - 1 ? 'bg-red-500' :
                          'bg-blue-500'
                        }`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>
                        {data.analytics?.totalUnits} units • {data.analytics?.occupancyRate.toFixed(1)}% occupied
                      </span>
                      <span>
                        {((value / average - 1) * 100).toFixed(1)}% vs avg
                      </span>
                    </div>
                  </div>
                );
              })}
          </div>
        </CardContent>
      </Card>

      {/* Portfolio Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Portfolio Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {propertyAnalytics.reduce((sum, data) => sum + (data.analytics?.totalUnits || 0), 0)}
              </div>
              <p className="text-sm text-gray-600">Total Units</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {propertyAnalytics.length > 0 
                  ? (propertyAnalytics.reduce((sum, data) => sum + (data.analytics?.occupancyRate || 0), 0) / propertyAnalytics.length).toFixed(1)
                  : 0
                }%
              </div>
              <p className="text-sm text-gray-600">Avg Occupancy</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(
                  propertyAnalytics.reduce((sum, data) => sum + (data.analytics?.totalMonthlyRevenue || 0), 0)
                )}
              </div>
              <p className="text-sm text-gray-600">Total Revenue</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {propertyAnalytics.reduce((sum, data) => sum + (data.analytics?.activeLeases || 0), 0)}
              </div>
              <p className="text-sm text-gray-600">Active Leases</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};