import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Switch } from '../ui/switch';
import { Badge } from '../ui/badge';
import { useToast } from '../ui/use-toast';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { 
  Zap, 
  Plus, 
  Edit, 
  Trash2, 
  Play, 
  Pause,
  MessageSquare,
  Phone,
  Mail,
  Bell
} from 'lucide-react';

interface NotificationTriggersProps {
  userId: Id<"users">;
  propertyId?: Id<"properties">;
}

interface TriggerForm {
  name: string;
  type: 'payment_due' | 'payment_overdue' | 'lease_expiry' | 'maintenance_assigned' | 'maintenance_escalated' | 'maintenance_completed' | 'sla_warning' | 'welcome_tenant' | 'scheduled_reminder';
  conditions: {
    entityType: 'invoice' | 'lease' | 'maintenance' | 'user';
    triggerEvent: string;
    conditionExpression: string;
  };
  notificationConfig: {
    channels: ('sms' | 'whatsapp' | 'email' | 'in_app')[];
    templateId?: Id<"messageTemplates">;
    customMessage?: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    respectQuietHours: boolean;
  };
  timing: {
    delay?: number;
    schedule?: {
      frequency: 'once' | 'daily' | 'weekly' | 'monthly';
      interval?: number;
      endCondition?: string;
    };
  };
  isActive: boolean;
}

export function NotificationTriggers({ userId, propertyId }: NotificationTriggersProps) {
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingTrigger, setEditingTrigger] = useState<any>(null);
  const [form, setForm] = useState<TriggerForm>({
    name: '',
    type: 'payment_due',
    conditions: {
      entityType: 'invoice',
      triggerEvent: 'status_change',
      conditionExpression: '{"field": "status", "operator": "equals", "value": "overdue"}',
    },
    notificationConfig: {
      channels: ['sms'],
      priority: 'medium',
      respectQuietHours: true,
    },
    timing: {},
    isActive: true,
  });

  // Queries
  const triggers = useQuery(api.notificationTriggers.getNotificationTriggers, {
    propertyId,
  });

  const messageTemplates = useQuery(api.communications.getMessageTemplates, {
    propertyId,
  });

  // Mutations
  const createTrigger = useMutation(api.notificationTriggers.createNotificationTrigger);
  const updateTrigger = useMutation(api.notificationTriggers.updateNotificationTrigger);
  const deleteTrigger = useMutation(api.notificationTriggers.deleteNotificationTrigger);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!form.name.trim()) {
      toast({
        title: "Error",
        description: "Trigger name is required",
        variant: "destructive",
      });
      return;
    }

    try {
      if (editingTrigger) {
        await updateTrigger({
          triggerId: editingTrigger._id,
          ...form,
        });
        toast({
          title: "Trigger Updated",
          description: "Notification trigger has been updated successfully",
        });
      } else {
        await createTrigger({
          ...form,
          propertyId,
          createdBy: userId,
        });
        toast({
          title: "Trigger Created",
          description: "Notification trigger has been created successfully",
        });
      }

      setIsDialogOpen(false);
      setEditingTrigger(null);
      resetForm();
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleEdit = (trigger: any) => {
    setEditingTrigger(trigger);
    setForm({
      name: trigger.name,
      type: trigger.type,
      conditions: trigger.conditions,
      notificationConfig: trigger.notificationConfig,
      timing: trigger.timing,
      isActive: trigger.isActive,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (triggerId: Id<"notificationTriggers">) => {
    if (confirm('Are you sure you want to delete this trigger?')) {
      try {
        await deleteTrigger({ triggerId });
        toast({
          title: "Trigger Deleted",
          description: "Notification trigger has been deleted successfully",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
      }
    }
  };

  const handleToggleActive = async (trigger: any) => {
    try {
      await updateTrigger({
        triggerId: trigger._id,
        isActive: !trigger.isActive,
      });
      toast({
        title: trigger.isActive ? "Trigger Deactivated" : "Trigger Activated",
        description: `Notification trigger has been ${trigger.isActive ? 'deactivated' : 'activated'}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setForm({
      name: '',
      type: 'payment_due',
      conditions: {
        entityType: 'invoice',
        triggerEvent: 'status_change',
        conditionExpression: '{"field": "status", "operator": "equals", "value": "overdue"}',
      },
      notificationConfig: {
        channels: ['sms'],
        priority: 'medium',
        respectQuietHours: true,
      },
      timing: {},
      isActive: true,
    });
  };

  const getTriggerTypeColor = (type: string) => {
    switch (type) {
      case 'payment_due':
      case 'payment_overdue':
        return 'bg-red-100 text-red-800';
      case 'lease_expiry':
        return 'bg-blue-100 text-blue-800';
      case 'maintenance_assigned':
      case 'maintenance_completed':
        return 'bg-green-100 text-green-800';
      case 'maintenance_escalated':
      case 'sla_warning':
        return 'bg-orange-100 text-orange-800';
      case 'welcome_tenant':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getChannelIcon = (type: string) => {
    switch (type) {
      case 'sms':
        return <Phone className="h-3 w-3" />;
      case 'whatsapp':
        return <MessageSquare className="h-3 w-3" />;
      case 'email':
        return <Mail className="h-3 w-3" />;
      case 'in_app':
        return <Bell className="h-3 w-3" />;
      default:
        return <Bell className="h-3 w-3" />;
    }
  };

  const triggerTypeOptions = [
    { value: 'payment_due', label: 'Payment Due', entity: 'invoice' },
    { value: 'payment_overdue', label: 'Payment Overdue', entity: 'invoice' },
    { value: 'lease_expiry', label: 'Lease Expiry', entity: 'lease' },
    { value: 'maintenance_assigned', label: 'Maintenance Assigned', entity: 'maintenance' },
    { value: 'maintenance_escalated', label: 'Maintenance Escalated', entity: 'maintenance' },
    { value: 'maintenance_completed', label: 'Maintenance Completed', entity: 'maintenance' },
    { value: 'sla_warning', label: 'SLA Warning', entity: 'maintenance' },
    { value: 'welcome_tenant', label: 'Welcome Tenant', entity: 'user' },
    { value: 'scheduled_reminder', label: 'Scheduled Reminder', entity: 'user' },
  ];

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5" />
                <span>Notification Triggers</span>
              </CardTitle>
              <CardDescription>
                Automate notifications based on system events and conditions
              </CardDescription>
            </div>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => { resetForm(); setEditingTrigger(null); }}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Trigger
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>
                    {editingTrigger ? 'Edit Notification Trigger' : 'Create Notification Trigger'}
                  </DialogTitle>
                  <DialogDescription>
                    Configure automated notifications that will be sent when specific conditions are met
                  </DialogDescription>
                </DialogHeader>
                
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Basic Information */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Trigger Name</label>
                    <Input
                      value={form.name}
                      onChange={(e) => setForm({ ...form, name: e.target.value })}
                      placeholder="e.g., Payment Overdue Reminder"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Trigger Type</label>
                    <Select 
                      value={form.type} 
                      onValueChange={(value: any) => {
                        const option = triggerTypeOptions.find(opt => opt.value === value);
                        setForm({ 
                          ...form, 
                          type: value,
                          conditions: {
                            ...form.conditions,
                            entityType: option?.entity as any || 'invoice'
                          }
                        });
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {triggerTypeOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Notification Channels */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Notification Channels</label>
                    <div className="grid grid-cols-2 gap-2">
                      {(['sms', 'whatsapp', 'email', 'in_app'] as const).map((channel) => (
                        <div key={channel} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={channel}
                            checked={form.notificationConfig.channels.includes(channel)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setForm({
                                  ...form,
                                  notificationConfig: {
                                    ...form.notificationConfig,
                                    channels: [...form.notificationConfig.channels, channel]
                                  }
                                });
                              } else {
                                setForm({
                                  ...form,
                                  notificationConfig: {
                                    ...form.notificationConfig,
                                    channels: form.notificationConfig.channels.filter(c => c !== channel)
                                  }
                                });
                              }
                            }}
                          />
                          <label htmlFor={channel} className="text-sm capitalize flex items-center space-x-1">
                            {getChannelIcon(channel)}
                            <span>{channel === 'in_app' ? 'In-App' : channel}</span>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Message Template */}
                  {messageTemplates && messageTemplates.length > 0 && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Message Template (Optional)</label>
                      <Select 
                        value={form.notificationConfig.templateId || ''} 
                        onValueChange={(value) => setForm({
                          ...form,
                          notificationConfig: {
                            ...form.notificationConfig,
                            templateId: value ? value as Id<"messageTemplates"> : undefined
                          }
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a template..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">No template</SelectItem>
                          {messageTemplates.map((template) => (
                            <SelectItem key={template._id} value={template._id}>
                              {template.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Custom Message */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Custom Message (Optional)</label>
                    <Textarea
                      value={form.notificationConfig.customMessage || ''}
                      onChange={(e) => setForm({
                        ...form,
                        notificationConfig: {
                          ...form.notificationConfig,
                          customMessage: e.target.value || undefined
                        }
                      })}
                      placeholder="Enter custom message or leave empty to use template..."
                      rows={3}
                    />
                  </div>

                  {/* Priority */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Priority</label>
                    <Select 
                      value={form.notificationConfig.priority} 
                      onValueChange={(value: any) => setForm({
                        ...form,
                        notificationConfig: {
                          ...form.notificationConfig,
                          priority: value
                        }
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Timing */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Delay (minutes)</label>
                    <Input
                      type="number"
                      min="0"
                      value={form.timing.delay || ''}
                      onChange={(e) => setForm({
                        ...form,
                        timing: {
                          ...form.timing,
                          delay: e.target.value ? parseInt(e.target.value) : undefined
                        }
                      })}
                      placeholder="0"
                    />
                  </div>

                  {/* Options */}
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={form.notificationConfig.respectQuietHours}
                        onCheckedChange={(checked) => setForm({
                          ...form,
                          notificationConfig: {
                            ...form.notificationConfig,
                            respectQuietHours: checked
                          }
                        })}
                      />
                      <label className="text-sm font-medium">Respect Quiet Hours</label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={form.isActive}
                        onCheckedChange={(checked) => setForm({ ...form, isActive: checked })}
                      />
                      <label className="text-sm font-medium">Active</label>
                    </div>
                  </div>

                  {/* Submit Buttons */}
                  <div className="flex justify-end space-x-2 pt-4">
                    <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit">
                      {editingTrigger ? 'Update Trigger' : 'Create Trigger'}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        
        <CardContent>
          {triggers === undefined ? (
            <div className="text-center py-8">Loading triggers...</div>
          ) : triggers.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No notification triggers configured. Create your first trigger to automate notifications.
            </div>
          ) : (
            <div className="space-y-4">
              {triggers.map((trigger) => (
                <div key={trigger._id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-medium">{trigger.name}</h4>
                        <Badge className={getTriggerTypeColor(trigger.type)}>
                          {trigger.type.replace('_', ' ')}
                        </Badge>
                        <Badge variant={trigger.isActive ? 'default' : 'secondary'}>
                          {trigger.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                        <span>Entity: {trigger.conditions.entityType}</span>
                        <span>Event: {trigger.conditions.triggerEvent}</span>
                        {trigger.timing.delay && (
                          <span>Delay: {trigger.timing.delay}min</span>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">Channels:</span>
                        {trigger.notificationConfig.channels.map((channel) => (
                          <div key={channel} className="flex items-center space-x-1 text-xs bg-gray-100 px-2 py-1 rounded">
                            {getChannelIcon(channel)}
                            <span className="capitalize">{channel === 'in_app' ? 'In-App' : channel}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleToggleActive(trigger)}
                      >
                        {trigger.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(trigger)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDelete(trigger._id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}