import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AuthService, RBACService } from '../auth';

// Mock ConvexReactClient
const mockConvex = {
  mutation: vi.fn(),
  query: vi.fn(),
} as any;

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('AuthService', () => {
  let authService: AuthService;

  beforeEach(() => {
    vi.clearAllMocks();
    authService = new AuthService(mockConvex);
  });

  describe('signIn', () => {
    it('should sign in user successfully', async () => {
      const mockResponse = {
        sessionToken: 'test-token',
        user: {
          _id: 'user1',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'owner',
          propertyAccess: [],
          kycStatus: 'verified',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        },
        expiresAt: Date.now() + 86400000,
      };

      mockConvex.mutation.mockResolvedValue(mockResponse);

      const result = await authService.signIn('<EMAIL>', 'password');

      expect(mockConvex.mutation).toHaveBeenCalledWith(
        expect.any(Object),
        {
          email: '<EMAIL>',
          password: 'password',
        }
      );
      expect(result).toEqual(mockResponse);
      expect(mockLocalStorage.setItem).toHaveBeenCalled();
    });

    it('should throw error on failed sign in', async () => {
      mockConvex.mutation.mockRejectedValue(new Error('Invalid credentials'));

      await expect(authService.signIn('<EMAIL>', 'wrong-password'))
        .rejects.toThrow('Invalid credentials');
    });
  });

  describe('signUp', () => {
    it('should sign up user successfully', async () => {
      const mockResponse = {
        sessionToken: 'test-token',
        user: {
          _id: 'user1',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'tenant',
          propertyAccess: [],
          kycStatus: 'pending',
          isActive: true,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        },
        expiresAt: Date.now() + 86400000,
      };

      mockConvex.mutation.mockResolvedValue(mockResponse);

      const result = await authService.signUp(
        '<EMAIL>',
        'password',
        'Test User',
        'tenant',
        '+1234567890'
      );

      expect(mockConvex.mutation).toHaveBeenCalledWith(
        expect.any(Object),
        {
          email: '<EMAIL>',
          password: 'password',
          name: 'Test User',
          role: 'tenant',
          phone: '+1234567890',
        }
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('verifySession', () => {
    it('should verify valid session', async () => {
      const mockUser = {
        _id: 'user1',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'owner',
        propertyAccess: [],
        kycStatus: 'verified',
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        sessionToken: 'valid-token',
        user: mockUser,
        expiresAt: Date.now() + 86400000,
      }));

      mockConvex.query.mockResolvedValue({
        isAuthenticated: true,
        user: mockUser,
      });

      // Create new instance to trigger loadSession
      const newAuthService = new AuthService(mockConvex);
      const result = await newAuthService.verifySession();

      expect(result).toBe(true);
    });

    it('should handle invalid session', async () => {
      mockConvex.query.mockResolvedValue({
        isAuthenticated: false,
        user: null,
      });

      const result = await authService.verifySession();

      expect(result).toBe(false);
      expect(mockLocalStorage.removeItem).toHaveBeenCalled();
    });
  });
});

describe('RBACService', () => {
  let authService: AuthService;
  let rbacService: RBACService;

  beforeEach(() => {
    vi.clearAllMocks();
    authService = new AuthService(mockConvex);
    rbacService = new RBACService(mockConvex, authService);
  });

  describe('canPerform', () => {
    it('should allow owner to perform all actions', () => {
      const mockUser = {
        _id: 'user1',
        email: '<EMAIL>',
        name: 'Owner User',
        role: 'owner' as const,
        propertyAccess: [],
        kycStatus: 'verified' as const,
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      // Mock getCurrentUser to return owner
      vi.spyOn(authService, 'getCurrentUser').mockReturnValue(mockUser);

      expect(rbacService.canPerform('properties', 'create')).toBe(true);
      expect(rbacService.canPerform('properties', 'read')).toBe(true);
      expect(rbacService.canPerform('properties', 'update')).toBe(true);
      expect(rbacService.canPerform('properties', 'delete')).toBe(true);
    });

    it('should restrict manager permissions correctly', () => {
      const mockUser = {
        _id: 'user2',
        email: '<EMAIL>',
        name: 'Manager User',
        role: 'manager' as const,
        propertyAccess: [],
        kycStatus: 'verified' as const,
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      vi.spyOn(authService, 'getCurrentUser').mockReturnValue(mockUser);

      // Manager can read and update properties but not create or delete
      expect(rbacService.canPerform('properties', 'create')).toBe(false);
      expect(rbacService.canPerform('properties', 'read')).toBe(true);
      expect(rbacService.canPerform('properties', 'update')).toBe(true);
      expect(rbacService.canPerform('properties', 'delete')).toBe(false);

      // Manager can manage units fully
      expect(rbacService.canPerform('units', 'create')).toBe(true);
      expect(rbacService.canPerform('units', 'delete')).toBe(true);
    });

    it('should restrict vendor permissions correctly', () => {
      const mockUser = {
        _id: 'user3',
        email: '<EMAIL>',
        name: 'Vendor User',
        role: 'vendor' as const,
        propertyAccess: [],
        kycStatus: 'verified' as const,
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      vi.spyOn(authService, 'getCurrentUser').mockReturnValue(mockUser);

      // Vendor can only read properties and units
      expect(rbacService.canPerform('properties', 'create')).toBe(false);
      expect(rbacService.canPerform('properties', 'read')).toBe(true);
      expect(rbacService.canPerform('properties', 'update')).toBe(false);
      expect(rbacService.canPerform('properties', 'delete')).toBe(false);

      // Vendor can read and update maintenance tickets
      expect(rbacService.canPerform('maintenance', 'read')).toBe(true);
      expect(rbacService.canPerform('maintenance', 'update')).toBe(true);
      expect(rbacService.canPerform('maintenance', 'create')).toBe(false);
      expect(rbacService.canPerform('maintenance', 'delete')).toBe(false);
    });

    it('should restrict tenant permissions correctly', () => {
      const mockUser = {
        _id: 'user4',
        email: '<EMAIL>',
        name: 'Tenant User',
        role: 'tenant' as const,
        propertyAccess: [],
        kycStatus: 'verified' as const,
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      vi.spyOn(authService, 'getCurrentUser').mockReturnValue(mockUser);

      // Tenant can only read leases
      expect(rbacService.canPerform('leases', 'read')).toBe(true);
      expect(rbacService.canPerform('leases', 'create')).toBe(false);
      expect(rbacService.canPerform('leases', 'update')).toBe(false);
      expect(rbacService.canPerform('leases', 'delete')).toBe(false);

      // Tenant can create and read payments
      expect(rbacService.canPerform('payments', 'create')).toBe(true);
      expect(rbacService.canPerform('payments', 'read')).toBe(true);
      expect(rbacService.canPerform('payments', 'update')).toBe(false);
      expect(rbacService.canPerform('payments', 'delete')).toBe(false);

      // Tenant can create and read maintenance requests
      expect(rbacService.canPerform('maintenance', 'create')).toBe(true);
      expect(rbacService.canPerform('maintenance', 'read')).toBe(true);
      expect(rbacService.canPerform('maintenance', 'update')).toBe(false);
      expect(rbacService.canPerform('maintenance', 'delete')).toBe(false);
    });

    it('should return false for unauthenticated user', () => {
      vi.spyOn(authService, 'getCurrentUser').mockReturnValue(null);

      expect(rbacService.canPerform('properties', 'read')).toBe(false);
      expect(rbacService.canPerform('maintenance', 'create')).toBe(false);
    });

    it('should return false for unknown resource', () => {
      const mockUser = {
        _id: 'user1',
        email: '<EMAIL>',
        name: 'Owner User',
        role: 'owner' as const,
        propertyAccess: [],
        kycStatus: 'verified' as const,
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      vi.spyOn(authService, 'getCurrentUser').mockReturnValue(mockUser);

      expect(rbacService.canPerform('unknown-resource', 'read')).toBe(false);
    });
  });

  describe('hasPermission', () => {
    it('should call Convex query with correct parameters', async () => {
      mockConvex.query.mockResolvedValue(true);
      vi.spyOn(authService, 'getSessionToken').mockReturnValue('test-token');

      const result = await rbacService.hasPermission('properties', 'read', 'user1');

      expect(mockConvex.query).toHaveBeenCalledWith(
        expect.any(Object),
        {
          sessionToken: 'test-token',
          userId: 'user1',
          resource: 'properties',
          action: 'read',
        }
      );
      expect(result).toBe(true);
    });

    it('should return false when not authenticated', async () => {
      vi.spyOn(authService, 'getSessionToken').mockReturnValue(null);

      const result = await rbacService.hasPermission('properties', 'read');

      expect(result).toBe(false);
      expect(mockConvex.query).not.toHaveBeenCalled();
    });
  });

  describe('assignPropertyAccess', () => {
    it('should assign property access successfully', async () => {
      mockConvex.mutation.mockResolvedValue({ success: true });
      vi.spyOn(authService, 'getSessionToken').mockReturnValue('test-token');

      await rbacService.assignPropertyAccess('user1', 'property1');

      expect(mockConvex.mutation).toHaveBeenCalledWith(
        expect.any(Object),
        {
          sessionToken: 'test-token',
          userId: 'user1',
          propertyId: 'property1',
        }
      );
    });

    it('should throw error when not authenticated', async () => {
      vi.spyOn(authService, 'getSessionToken').mockReturnValue(null);

      await expect(rbacService.assignPropertyAccess('user1', 'property1'))
        .rejects.toThrow('Not authenticated');
    });
  });
});