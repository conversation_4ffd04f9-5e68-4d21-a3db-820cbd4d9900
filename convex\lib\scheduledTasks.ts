import { cronJobs } from "convex/server";
import { internal } from "../_generated/api";

// Scheduled tasks for maintenance system

const crons = cronJobs();

// Check for SLA breaches and auto-escalate tickets every 15 minutes
crons.interval(
  "maintenance-sla-check",
  { minutes: 15 },
  internal.lib.escalation.checkAndProcessSLABreaches
);

// Send daily maintenance summary reports at 8 AM
crons.cron(
  "daily-maintenance-summary",
  "0 8 * * *", // 8 AM daily
  internal.lib.reports.sendDailyMaintenanceSummary
);

// Check for overdue scheduled maintenance weekly on Mondays at 9 AM
crons.cron(
  "scheduled-maintenance-check",
  "0 9 * * 1", // 9 AM every Monday
  internal.lib.scheduledMaintenance.checkOverdueScheduledMaintenance
);

// Clean up old notifications monthly on the 1st at midnight
crons.cron(
  "cleanup-old-notifications",
  "0 0 1 * *", // Midnight on the 1st of each month
  internal.lib.cleanup.cleanupOldNotifications
);

export default crons;