import { useCallback } from 'react'

export interface FileDialogOptions {
  title?: string
  defaultPath?: string
  buttonLabel?: string
  filters?: Array<{ name: string; extensions: string[] }>
  properties?: Array<'openFile' | 'openDirectory' | 'multiSelections' | 'showHiddenFiles'>
}

export interface SaveDialogOptions {
  title?: string
  defaultPath?: string
  buttonLabel?: string
  filters?: Array<{ name: string; extensions: string[] }>
}

export const useNativeFileSystem = () => {
  const openFileDialog = useCallback(async (options: FileDialogOptions = {}) => {
    if (!window.electronAPI) {
      throw new Error('Native file system not available in web environment')
    }

    const defaultOptions = {
      properties: ['openFile' as const],
      filters: [
        { name: 'All Files', extensions: ['*'] }
      ]
    }

    const result = await window.electronAPI.showOpenDialog({
      ...defaultOptions,
      ...options
    })

    return result
  }, [])

  const openDirectoryDialog = useCallback(async (options: Omit<FileDialogOptions, 'properties'> = {}) => {
    if (!window.electronAPI) {
      throw new Error('Native file system not available in web environment')
    }

    const result = await window.electronAPI.showOpenDialog({
      ...options,
      properties: ['openDirectory']
    })

    return result
  }, [])

  const saveFileDialog = useCallback(async (options: SaveDialogOptions = {}) => {
    if (!window.electronAPI) {
      throw new Error('Native file system not available in web environment')
    }

    const defaultOptions = {
      filters: [
        { name: 'All Files', extensions: ['*'] }
      ]
    }

    const result = await window.electronAPI.showSaveDialog({
      ...defaultOptions,
      ...options
    })

    return result
  }, [])

  const readFile = useCallback(async (filePath: string) => {
    if (!window.electronAPI) {
      throw new Error('Native file system not available in web environment')
    }

    return await window.electronAPI.readFile(filePath)
  }, [])

  const writeFile = useCallback(async (filePath: string, content: string) => {
    if (!window.electronAPI) {
      throw new Error('Native file system not available in web environment')
    }

    return await window.electronAPI.writeFile(filePath, content)
  }, [])

  const importDocuments = useCallback(async () => {
    const result = await openFileDialog({
      title: 'Import Documents',
      properties: ['openFile', 'multiSelections'],
      filters: [
        { name: 'Documents', extensions: ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png'] },
        { name: 'PDF Files', extensions: ['pdf'] },
        { name: 'Word Documents', extensions: ['doc', 'docx'] },
        { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    })

    if (!result.canceled && result.filePaths.length > 0) {
      const files = []
      for (const filePath of result.filePaths) {
        const fileResult = await readFile(filePath)
        if (fileResult.success) {
          files.push({
            path: filePath,
            name: filePath.split(/[/\\]/).pop() || 'unknown',
            content: fileResult.content
          })
        }
      }
      return files
    }

    return []
  }, [openFileDialog, readFile])

  const exportReport = useCallback(async (content: string, filename: string = 'report.txt') => {
    const result = await saveFileDialog({
      title: 'Export Report',
      defaultPath: filename,
      filters: [
        { name: 'Text Files', extensions: ['txt'] },
        { name: 'CSV Files', extensions: ['csv'] },
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    })

    if (!result.canceled && result.filePath) {
      return await writeFile(result.filePath, content)
    }

    return { success: false, error: 'Export cancelled' }
  }, [saveFileDialog, writeFile])

  return {
    openFileDialog,
    openDirectoryDialog,
    saveFileDialog,
    readFile,
    writeFile,
    importDocuments,
    exportReport
  }
}