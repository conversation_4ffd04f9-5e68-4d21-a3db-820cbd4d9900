import { test, expect } from '@playwright/test';

test.describe('Payment Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication as tenant
    await page.route('**/api/auth/verify', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          isAuthenticated: true,
          user: {
            _id: 'tenant1',
            email: '<EMAIL>',
            name: '<PERSON>',
            role: 'tenant'
          }
        })
      });
    });

    // Mock invoices data
    await page.route('**/api/invoices', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'invoice1',
            amount: 50000,
            dueDate: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days from now
            status: 'pending',
            description: 'Monthly Rent - Unit A101',
            lease: {
              unit: { unitNumber: 'A101' },
              property: { name: 'Sunset Apartments' }
            }
          }
        ])
      });
    });

    await page.goto('/dashboard/payments');
  });

  test('should display pending invoices', async ({ page }) => {
    await expect(page.getByRole('heading', { name: /payments/i })).toBeVisible();
    await expect(page.getByText('Monthly Rent - Unit A101')).toBeVisible();
    await expect(page.getByText('KES 50,000')).toBeVisible();
    await expect(page.getByText('Pending')).toBeVisible();
  });

  test('should initiate M-PESA payment', async ({ page }) => {
    // Mock M-PESA STK push API
    await page.route('**/api/payments/mpesa', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          checkoutRequestId: 'ws_CO_123456789',
          message: 'STK push sent successfully'
        })
      });
    });

    await page.getByRole('button', { name: /pay now/i }).first().click();
    
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(page.getByRole('heading', { name: /payment options/i })).toBeVisible();
    
    await page.getByRole('button', { name: /m-pesa/i }).click();
    
    await page.getByLabel(/phone number/i).fill('254712345678');
    await page.getByRole('button', { name: /pay with m-pesa/i }).click();
    
    await expect(page.getByText(/stk push sent/i)).toBeVisible();
    await expect(page.getByText(/check your phone/i)).toBeVisible();
  });

  test('should handle M-PESA payment success', async ({ page }) => {
    // Mock successful M-PESA payment
    await page.route('**/api/payments/mpesa/callback', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          transactionId: 'MPESA123456',
          status: 'completed'
        })
      });
    });

    await page.getByRole('button', { name: /pay now/i }).first().click();
    await page.getByRole('button', { name: /m-pesa/i }).click();
    await page.getByLabel(/phone number/i).fill('254712345678');
    await page.getByRole('button', { name: /pay with m-pesa/i }).click();
    
    // Simulate payment completion
    await page.waitForTimeout(3000);
    
    await expect(page.getByText(/payment successful/i)).toBeVisible();
    await expect(page.getByText(/transaction id: mpesa123456/i)).toBeVisible();
  });

  test('should handle M-PESA payment failure', async ({ page }) => {
    // Mock failed M-PESA payment
    await page.route('**/api/payments/mpesa', async route => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Insufficient funds'
        })
      });
    });

    await page.getByRole('button', { name: /pay now/i }).first().click();
    await page.getByRole('button', { name: /m-pesa/i }).click();
    await page.getByLabel(/phone number/i).fill('254712345678');
    await page.getByRole('button', { name: /pay with m-pesa/i }).click();
    
    await expect(page.getByText(/payment failed/i)).toBeVisible();
    await expect(page.getByText(/insufficient funds/i)).toBeVisible();
  });

  test('should process Stripe payment', async ({ page }) => {
    // Mock Stripe payment API
    await page.route('**/api/payments/stripe', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          paymentIntentId: 'pi_123456789',
          status: 'succeeded'
        })
      });
    });

    await page.getByRole('button', { name: /pay now/i }).first().click();
    await page.getByRole('button', { name: /credit card/i }).click();
    
    // Fill in card details (using test card)
    await page.getByLabel(/card number/i).fill('****************');
    await page.getByLabel(/expiry/i).fill('12/25');
    await page.getByLabel(/cvc/i).fill('123');
    await page.getByLabel(/cardholder name/i).fill('John Tenant');
    
    await page.getByRole('button', { name: /pay with card/i }).click();
    
    await expect(page.getByText(/payment successful/i)).toBeVisible();
  });

  test('should show payment history', async ({ page }) => {
    // Mock payment history
    await page.route('**/api/payments/history', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'payment1',
            amount: 50000,
            method: 'mpesa',
            transactionId: 'MPESA123456',
            status: 'completed',
            processedAt: Date.now() - 24 * 60 * 60 * 1000, // 1 day ago
            description: 'Monthly Rent - Unit A101'
          }
        ])
      });
    });

    await page.getByRole('tab', { name: /payment history/i }).click();
    
    await expect(page.getByText('MPESA123456')).toBeVisible();
    await expect(page.getByText('Completed')).toBeVisible();
    await expect(page.getByText('M-PESA')).toBeVisible();
  });

  test('should download payment receipt', async ({ page }) => {
    await page.getByRole('tab', { name: /payment history/i }).click();
    
    // Mock receipt download
    const downloadPromise = page.waitForEvent('download');
    await page.getByRole('button', { name: /download receipt/i }).first().click();
    
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toContain('receipt');
  });

  test('should set up recurring payments', async ({ page }) => {
    await page.getByRole('tab', { name: /auto pay/i }).click();
    
    await expect(page.getByRole('heading', { name: /automatic payments/i })).toBeVisible();
    
    await page.getByRole('switch', { name: /enable auto pay/i }).click();
    await page.getByLabel(/payment method/i).selectOption('mpesa');
    await page.getByLabel(/phone number/i).fill('254712345678');
    
    await page.getByRole('button', { name: /save settings/i }).click();
    
    await expect(page.getByText(/auto pay enabled/i)).toBeVisible();
  });

  test('should handle payment reminders', async ({ page }) => {
    // Mock overdue invoice
    await page.route('**/api/invoices', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'invoice2',
            amount: 50000,
            dueDate: Date.now() - 7 * 24 * 60 * 60 * 1000, // 7 days overdue
            status: 'overdue',
            description: 'Monthly Rent - Unit A101',
            lease: {
              unit: { unitNumber: 'A101' },
              property: { name: 'Sunset Apartments' }
            }
          }
        ])
      });
    });

    await page.reload();
    
    await expect(page.getByText('Overdue')).toBeVisible();
    await expect(page.getByText(/7 days overdue/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /pay now/i })).toHaveClass(/urgent/);
  });

  test('should validate payment form inputs', async ({ page }) => {
    await page.getByRole('button', { name: /pay now/i }).first().click();
    await page.getByRole('button', { name: /m-pesa/i }).click();
    
    // Try to submit without phone number
    await page.getByRole('button', { name: /pay with m-pesa/i }).click();
    
    await expect(page.getByText(/phone number is required/i)).toBeVisible();
    
    // Try with invalid phone number
    await page.getByLabel(/phone number/i).fill('123');
    await page.getByRole('button', { name: /pay with m-pesa/i }).click();
    
    await expect(page.getByText(/invalid phone number/i)).toBeVisible();
  });
});