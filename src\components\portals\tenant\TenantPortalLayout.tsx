import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../../convex/_generated/api';
import { Id } from '../../../../convex/_generated/dataModel';
import { <PERSON><PERSON> } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { Separator } from '../../ui/separator';
import { TenantDashboard } from './TenantDashboard';
import { TenantPaymentPortal } from './TenantPaymentPortal';
import { TenantMaintenancePortal } from './TenantMaintenancePortal';
import { TenantDocumentPortal } from './TenantDocumentPortal';
import { 
  Home, 
  CreditCard, 
  Wrench, 
  FileText, 
  MessageSquare,
  User,
  LogOut,
  Menu,
  X,
  Bell
} from 'lucide-react';

interface TenantPortalLayoutProps {
  tenantId: Id<"users">;
  portalId: Id<"portals">;
}

type ActiveView = 'dashboard' | 'payments' | 'maintenance' | 'documents' | 'messages';

export const TenantPortalLayout: React.FC<TenantPortalLayoutProps> = ({ tenantId, portalId }) => {
  const [activeView, setActiveView] = useState<ActiveView>('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const portal = useQuery(api.portals.getPortalById, { portalId });
  const tenant = useQuery(api.users.getById, { id: tenantId });
  const activeLease = useQuery(api.leases.getActiveLease, { tenantId });
  const property = useQuery(api.properties.getById, 
    activeLease ? { id: activeLease.propertyId } : 'skip'
  );
  const pendingInvoices = useQuery(api.invoices.getPendingInvoices, { tenantId });
  const openTickets = useQuery(api.maintenance.getOpenTickets, { tenantId });

  if (!portal || !tenant) {
    return <div>Loading...</div>;
  }

  const navigationItems = [
    {
      id: 'dashboard' as ActiveView,
      label: 'Dashboard',
      icon: Home,
      enabled: true,
    },
    {
      id: 'payments' as ActiveView,
      label: 'Payments',
      icon: CreditCard,
      enabled: portal.features.paymentPortal,
      badge: pendingInvoices?.length || 0,
    },
    {
      id: 'maintenance' as ActiveView,
      label: 'Maintenance',
      icon: Wrench,
      enabled: portal.features.maintenanceRequests,
      badge: openTickets?.length || 0,
    },
    {
      id: 'documents' as ActiveView,
      label: 'Documents',
      icon: FileText,
      enabled: portal.features.documentAccess,
    },
    {
      id: 'messages' as ActiveView,
      label: 'Messages',
      icon: MessageSquare,
      enabled: portal.features.communicationCenter,
    },
  ];

  const enabledNavItems = navigationItems.filter(item => item.enabled);

  const renderContent = () => {
    switch (activeView) {
      case 'dashboard':
        return <TenantDashboard tenantId={tenantId} portalId={portalId} />;
      case 'payments':
        return portal.features.paymentPortal ? 
          <TenantPaymentPortal tenantId={tenantId} portalId={portalId} /> : 
          <div>Feature not available</div>;
      case 'maintenance':
        return portal.features.maintenanceRequests ? 
          <TenantMaintenancePortal tenantId={tenantId} portalId={portalId} /> : 
          <div>Feature not available</div>;
      case 'documents':
        return portal.features.documentAccess ? 
          <TenantDocumentPortal tenantId={tenantId} portalId={portalId} /> : 
          <div>Feature not available</div>;
      case 'messages':
        return portal.features.communicationCenter ? 
          <div>Messages feature coming soon</div> : 
          <div>Feature not available</div>;
      default:
        return <TenantDashboard tenantId={tenantId} portalId={portalId} />;
    }
  };

  const Sidebar: React.FC<{ mobile?: boolean }> = ({ mobile = false }) => (
    <div className={`${mobile ? 'fixed inset-0 z-50 bg-background' : 'w-64'} flex flex-col`}>
      {mobile && (
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            {portal.branding.logo && (
              <img 
                src={portal.branding.logo} 
                alt={portal.name} 
                className="h-8 w-8 object-contain"
              />
            )}
            <span className="font-semibold">{portal.name}</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
      )}

      <div className="flex-1 p-4 space-y-6">
        {/* User Info */}
        <div className="space-y-2">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-primary" />
            </div>
            <div>
              <p className="font-medium text-sm">{tenant.name}</p>
              <p className="text-xs text-muted-foreground">{tenant.email}</p>
            </div>
          </div>
          {property && activeLease && (
            <div className="text-xs text-muted-foreground">
              {property.name} - Unit {activeLease.unitId}
            </div>
          )}
        </div>

        <Separator />

        {/* Navigation */}
        <nav className="space-y-1">
          {enabledNavItems.map((item) => {
            const Icon = item.icon;
            return (
              <Button
                key={item.id}
                variant={activeView === item.id ? "secondary" : "ghost"}
                className="w-full justify-start"
                onClick={() => {
                  setActiveView(item.id);
                  if (mobile) setSidebarOpen(false);
                }}
              >
                <Icon className="w-4 h-4 mr-3" />
                {item.label}
                {item.badge && item.badge > 0 && (
                  <Badge variant="destructive" className="ml-auto text-xs">
                    {item.badge}
                  </Badge>
                )}
              </Button>
            );
          })}
        </nav>

        <Separator />

        {/* Quick Actions */}
        <div className="space-y-2">
          <p className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
            Quick Actions
          </p>
          {portal.features.paymentPortal && pendingInvoices && pendingInvoices.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start text-orange-600 border-orange-200"
              onClick={() => {
                setActiveView('payments');
                if (mobile) setSidebarOpen(false);
              }}
            >
              <CreditCard className="w-4 h-4 mr-2" />
              Pay Rent (${pendingInvoices[0].amount})
            </Button>
          )}
          {portal.features.maintenanceRequests && (
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => {
                setActiveView('maintenance');
                if (mobile) setSidebarOpen(false);
              }}
            >
              <Wrench className="w-4 h-4 mr-2" />
              Report Issue
            </Button>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t">
        {portal.customization.contactInfo && (
          <div className="text-xs text-muted-foreground mb-3">
            <p className="font-medium">Need Help?</p>
            <p>{portal.customization.contactInfo.phone}</p>
            <p>{portal.customization.contactInfo.email}</p>
          </div>
        )}
        <Button variant="ghost" size="sm" className="w-full justify-start text-muted-foreground">
          <LogOut className="w-4 h-4 mr-2" />
          Sign Out
        </Button>
      </div>
    </div>
  );

  return (
    <div 
      className="min-h-screen bg-background"
      style={{
        fontFamily: portal.branding.fontFamily,
        backgroundColor: portal.branding.backgroundColor,
        color: portal.branding.textColor,
      }}
    >
      {/* Apply portal CSS */}
      <style dangerouslySetInnerHTML={{
        __html: `
          :root {
            --primary-color: ${portal.branding.primaryColor};
            --secondary-color: ${portal.branding.secondaryColor};
            --accent-color: ${portal.branding.accentColor};
            --background-color: ${portal.branding.backgroundColor};
            --text-color: ${portal.branding.textColor};
            --font-family: ${portal.branding.fontFamily};
          }
          ${portal.branding.customCSS || ''}
        `
      }} />

      <div className="flex">
        {/* Desktop Sidebar */}
        <div className="hidden lg:block border-r">
          <Sidebar />
        </div>

        {/* Mobile Sidebar Overlay */}
        {sidebarOpen && (
          <div className="lg:hidden">
            <div 
              className="fixed inset-0 bg-black/50 z-40"
              onClick={() => setSidebarOpen(false)}
            />
            <Sidebar mobile />
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1">
          {/* Mobile Header */}
          <div className="lg:hidden border-b p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSidebarOpen(true)}
                >
                  <Menu className="w-5 h-5" />
                </Button>
                {portal.branding.logo && (
                  <img 
                    src={portal.branding.logo} 
                    alt={portal.name} 
                    className="h-8 w-8 object-contain"
                  />
                )}
                <span className="font-semibold">{portal.name}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm">
                  <Bell className="w-5 h-5" />
                </Button>
                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-primary" />
                </div>
              </div>
            </div>
          </div>

          {/* Desktop Header */}
          <div className="hidden lg:block border-b p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {portal.branding.logo && (
                  <img 
                    src={portal.branding.logo} 
                    alt={portal.name} 
                    className="h-10 w-10 object-contain"
                  />
                )}
                <div>
                  <h1 className="text-xl font-semibold">{portal.name}</h1>
                  <p className="text-sm text-muted-foreground">Tenant Portal</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="sm">
                  <Bell className="w-5 h-5" />
                </Button>
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-primary" />
                  </div>
                  <span className="text-sm font-medium">{tenant.name}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {renderContent()}
          </div>

          {/* Footer */}
          <div className="border-t p-6 mt-12">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div>
                <p>&copy; 2024 {portal.name}. All rights reserved.</p>
                {portal.customization.footerText && (
                  <p className="mt-1">{portal.customization.footerText}</p>
                )}
              </div>
              <div className="flex items-center space-x-4">
                {portal.customization.socialLinks?.facebook && (
                  <a href={portal.customization.socialLinks.facebook} target="_blank" rel="noopener noreferrer">
                    Facebook
                  </a>
                )}
                {portal.customization.socialLinks?.twitter && (
                  <a href={portal.customization.socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                    Twitter
                  </a>
                )}
                <span>Powered by EstatePulse</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};