import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Badge } from '../ui/badge';
import { Switch } from '../ui/switch';
import { useToast } from '../ui/use-toast';
import { 
  Bell, 
  Settings, 
  Trash2, 
  Search
} from 'lucide-react';
import { NotificationHistory } from './NotificationHistory';
import { NotificationScheduler } from './NotificationScheduler';
import { NotificationTriggers } from './NotificationTriggers';

interface NotificationManagementProps {
  propertyId?: Id<"properties">;
  userId: Id<"users">;
}

export function NotificationManagement({ propertyId, userId }: NotificationManagementProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('preferences');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Queries
  const notifications = useQuery(api.communications.getNotifications, {
    userId,
    propertyId,
    limit: 50,
  });

  const scheduledNotifications = useQuery(api.communications.getScheduledNotifications, {
    userId,
    propertyId,
  });

  const notificationPreferences = useQuery(api.communications.getNotificationPreferences, {
    userId,
    propertyId,
  });

  // Mutations
  const markAsRead = useMutation(api.communications.markNotificationAsRead);
  const markAllAsRead = useMutation(api.communications.markAllNotificationsAsRead);
  const deleteNotification = useMutation(api.communications.deleteNotification);
  const cancelScheduledNotification = useMutation(api.communications.cancelScheduledNotification);

  const handleMarkAsRead = async (notificationId: Id<"notifications">) => {
    try {
      await markAsRead({ notificationId });
      toast({
        title: "Notification marked as read",
        description: "The notification has been marked as read",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead({ userId, propertyId });
      toast({
        title: "All notifications marked as read",
        description: "All notifications have been marked as read",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleDeleteNotification = async (notificationId: Id<"notifications">) => {
    try {
      await deleteNotification({ notificationId });
      toast({
        title: "Notification deleted",
        description: "The notification has been deleted",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleCancelScheduled = async (scheduledId: Id<"scheduledNotifications">) => {
    try {
      await cancelScheduledNotification({ scheduledId });
      toast({
        title: "Scheduled notification cancelled",
        description: "The scheduled notification has been cancelled",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'payment_reminder':
        return '💰';
      case 'maintenance_update':
      case 'maintenance_assigned':
      case 'maintenance_completed':
        return '🔧';
      case 'lease_expiry':
        return '📄';
      case 'sla_warning':
        return '⚠️';
      case 'maintenance_escalated':
        return '🚨';
      default:
        return '📢';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const filteredNotifications = notifications?.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'read' && notification.isRead) ||
                         (statusFilter === 'unread' && !notification.isRead);
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Bell className="h-6 w-6" />
          <h2 className="text-2xl font-bold">Notification Management</h2>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleMarkAllAsRead}>
            Mark All Read
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="triggers">Triggers</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="preferences" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Notification Preferences</span>
              </CardTitle>
              <CardDescription>
                Configure how and when you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              {notificationPreferences && (
                <div className="space-y-6">
                  {/* SMS Preferences */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="text-lg font-medium">SMS Notifications</h4>
                      <Switch
                        checked={notificationPreferences.preferences.sms.enabled}
                        onCheckedChange={() => {/* Handle change */}}
                      />
                    </div>
                    {notificationPreferences.preferences.sms.enabled && (
                      <div className="grid grid-cols-2 gap-4 pl-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Payment Reminders</span>
                          <Switch checked={notificationPreferences.preferences.sms.paymentReminders} />
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Maintenance Updates</span>
                          <Switch checked={notificationPreferences.preferences.sms.maintenanceUpdates} />
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Lease Notifications</span>
                          <Switch checked={notificationPreferences.preferences.sms.leaseNotifications} />
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Emergency Alerts</span>
                          <Switch checked={notificationPreferences.preferences.sms.emergencyAlerts} />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Quiet Hours */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="text-lg font-medium">Quiet Hours</h4>
                      <Switch
                        checked={notificationPreferences.quietHours.enabled}
                        onCheckedChange={() => {/* Handle change */}}
                      />
                    </div>
                    {notificationPreferences.quietHours.enabled && (
                      <div className="grid grid-cols-2 gap-4 pl-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Start Time</label>
                          <Input
                            type="time"
                            value={notificationPreferences.quietHours.startTime}
                            readOnly
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">End Time</label>
                          <Input
                            type="time"
                            value={notificationPreferences.quietHours.endTime}
                            readOnly
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Notifications</CardTitle>
              <CardDescription>
                View and manage your current notifications
              </CardDescription>
              <div className="flex items-center space-x-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search notifications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="unread">Unread</SelectItem>
                    <SelectItem value="read">Read</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              {filteredNotifications === undefined ? (
                <div className="text-center py-4">Loading notifications...</div>
              ) : filteredNotifications.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No notifications found
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredNotifications.map((notification) => (
                    <div
                      key={notification._id}
                      className={`border rounded-lg p-4 ${
                        !notification.isRead ? 'bg-blue-50 border-blue-200' : 'bg-white'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3 flex-1">
                          <div className="text-2xl">
                            {getNotificationIcon(notification.type)}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h4 className="font-medium">{notification.title}</h4>
                              <Badge className={getPriorityColor(notification.priority)}>
                                {notification.priority}
                              </Badge>
                              {!notification.isRead && (
                                <Badge variant="secondary">New</Badge>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 mb-2">
                              {notification.message}
                            </p>
                            <div className="text-xs text-gray-500">
                              {new Date(notification.createdAt).toLocaleString()}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {!notification.isRead && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleMarkAsRead(notification._id)}
                            >
                              Mark Read
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDeleteNotification(notification._id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-4">
          <NotificationScheduler 
            userId={userId} 
            propertyId={propertyId}
            onScheduled={() => {
              toast({
                title: "Notification Scheduled",
                description: "Your notification has been scheduled successfully",
              });
            }}
          />
          
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Notifications</CardTitle>
              <CardDescription>
                View and manage your scheduled notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              {scheduledNotifications === undefined ? (
                <div className="text-center py-4">Loading scheduled notifications...</div>
              ) : scheduledNotifications.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No scheduled notifications
                </div>
              ) : (
                <div className="space-y-3">
                  {scheduledNotifications.map((scheduled) => (
                    <div key={scheduled._id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge variant="outline" className="uppercase">
                              {scheduled.type}
                            </Badge>
                            <Badge 
                              className={
                                scheduled.status === 'scheduled' 
                                  ? 'bg-blue-100 text-blue-800'
                                  : scheduled.status === 'sent'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }
                            >
                              {scheduled.status}
                            </Badge>
                          </div>
                          <h4 className="font-medium mb-1">{scheduled.subject || 'No Subject'}</h4>
                          <p className="text-sm text-gray-600 mb-2">
                            {scheduled.content.substring(0, 100)}...
                          </p>
                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <span>
                              Scheduled: {new Date(scheduled.scheduledFor).toLocaleString()}
                            </span>
                            {scheduled.recurringPattern && (
                              <span>
                                Recurring: {scheduled.recurringPattern.frequency}
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {scheduled.status === 'scheduled' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleCancelScheduled(scheduled._id)}
                            >
                              Cancel
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="triggers" className="space-y-4">
          <NotificationTriggers 
            userId={userId} 
            propertyId={propertyId}
          />
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <NotificationHistory 
            userId={userId} 
            propertyId={propertyId}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}