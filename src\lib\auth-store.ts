import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User } from './auth';

interface AuthState {
  user: User | null;
  sessionToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // Actions
  setUser: (user: User | null) => void;
  setSessionToken: (token: string | null) => void;
  setLoading: (loading: boolean) => void;
  clearAuth: () => void;
  
  // UI State
  showLoginModal: boolean;
  setShowLoginModal: (show: boolean) => void;
  
  // Preferences
  theme: 'light' | 'dark' | 'system';
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  
  // Notifications
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: number;
  autoClose?: boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      sessionToken: null,
      isAuthenticated: false,
      isLoading: false,
      showLoginModal: false,
      theme: 'system',
      notifications: [],

      // Auth actions
      setUser: (user) => set({ 
        user, 
        isAuthenticated: user !== null 
      }),
      
      setSessionToken: (sessionToken) => set({ sessionToken }),
      
      setLoading: (isLoading) => set({ isLoading }),
      
      clearAuth: () => set({ 
        user: null, 
        sessionToken: null, 
        isAuthenticated: false 
      }),

      // UI actions
      setShowLoginModal: (showLoginModal) => set({ showLoginModal }),
      
      setTheme: (theme) => set({ theme }),

      // Notification actions
      addNotification: (notification) => {
        const id = Math.random().toString(36).substring(2);
        const newNotification: Notification = {
          ...notification,
          id,
          timestamp: Date.now(),
          autoClose: notification.autoClose ?? true,
        };
        
        set((state) => ({
          notifications: [...state.notifications, newNotification]
        }));

        // Auto-remove notification after 5 seconds if autoClose is true
        if (newNotification.autoClose) {
          setTimeout(() => {
            get().removeNotification(id);
          }, 5000);
        }
      },
      
      removeNotification: (id) => set((state) => ({
        notifications: state.notifications.filter(n => n.id !== id)
      })),
      
      clearNotifications: () => set({ notifications: [] }),
    }),
    {
      name: 'estate-pulse-auth',
      partialize: (state) => ({
        user: state.user,
        sessionToken: state.sessionToken,
        isAuthenticated: state.isAuthenticated,
        theme: state.theme,
      }),
    }
  )
);

// Selectors for better performance
export const useUser = () => useAuthStore((state) => state.user);
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated);
export const useSessionToken = () => useAuthStore((state) => state.sessionToken);
export const useTheme = () => useAuthStore((state) => state.theme);
export const useNotifications = () => useAuthStore((state) => state.notifications);

// Action selectors
export const useAuthActions = () => useAuthStore((state) => ({
  setUser: state.setUser,
  setSessionToken: state.setSessionToken,
  setLoading: state.setLoading,
  clearAuth: state.clearAuth,
}));

export const useUIActions = () => useAuthStore((state) => ({
  setShowLoginModal: state.setShowLoginModal,
  setTheme: state.setTheme,
}));

export const useNotificationActions = () => useAuthStore((state) => ({
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
  clearNotifications: state.clearNotifications,
}));