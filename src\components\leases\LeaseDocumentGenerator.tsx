import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { FileText, Download, Send, Clock, CheckCircle } from 'lucide-react';
import { ESignatureManager } from './ESignatureManager';

interface LeaseDocumentGeneratorProps {
  leaseId: Id<"leases">;
  onDocumentGenerated?: (documentUrl: string) => void;
}

export function LeaseDocumentGenerator({ leaseId, onDocumentGenerated }: LeaseDocumentGeneratorProps) {
  const leaseData = useQuery(api.leases.getLeaseById, { id: leaseId });
  const updateESignatureStatus = useMutation(api.leases.updateESignatureStatus);

  const [isGenerating, setIsGenerating] = useState(false);
  const [documentUrl, setDocumentUrl] = useState<string | null>(null);

  if (!leaseData) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { lease, property, unit, tenant } = leaseData;

  const generateDocument = async () => {
    setIsGenerating(true);
    try {
      // Simulate document generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, this would call a document generation service
      const mockDocumentUrl = `https://example.com/lease-documents/${leaseId}.pdf`;
      setDocumentUrl(mockDocumentUrl);
      
      // Update the lease with the document URL
      await updateESignatureStatus({
        id: leaseId,
        status: 'pending',
        documentUrl: mockDocumentUrl,
      });

      onDocumentGenerated?.(mockDocumentUrl);
    } catch (error) {
      console.error('Error generating document:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const sendForSignature = async () => {
    if (!documentUrl) return;
    
    try {
      // In a real implementation, this would integrate with an e-signature service
      // like DocuSign, HelloSign, or Adobe Sign
      console.log('Sending document for signature:', documentUrl);
      
      // For now, we'll just update the status
      await updateESignatureStatus({
        id: leaseId,
        status: 'pending',
        documentUrl,
      });
    } catch (error) {
      console.error('Error sending for signature:', error);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusIcon = () => {
    switch (lease.eSignatureStatus) {
      case 'signed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'signed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Document Status */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon()}
              Lease Document
            </CardTitle>
            <Badge className={getStatusColor(lease.eSignatureStatus)}>
              {lease.eSignatureStatus === 'signed' ? 'Signed' : 
               lease.eSignatureStatus === 'pending' ? 'Pending Signature' : 'Not Generated'}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Lease Summary */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <h4 className="font-semibold mb-2">Lease Details</h4>
                <div className="space-y-1 text-sm">
                  <p><strong>Property:</strong> {property?.name}</p>
                  <p><strong>Unit:</strong> {unit?.unitNumber}</p>
                  <p><strong>Tenant:</strong> {tenant?.name}</p>
                  <p><strong>Period:</strong> {formatDate(lease.startDate)} - {formatDate(lease.endDate)}</p>
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Financial Terms</h4>
                <div className="space-y-1 text-sm">
                  <p><strong>Monthly Rent:</strong> {formatCurrency(lease.monthlyRent)}</p>
                  <p><strong>Security Deposit:</strong> {formatCurrency(lease.deposit)}</p>
                  <p><strong>Late Fee:</strong> {lease.terms.lateFeePercentage}%</p>
                  <p><strong>Notice Period:</strong> {lease.terms.noticePeriod} days</p>
                </div>
              </div>
            </div>

            {/* Document Actions */}
            <div className="flex gap-3">
              {!documentUrl && !lease.documentUrl && (
                <Button 
                  onClick={generateDocument} 
                  disabled={isGenerating}
                  className="flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  {isGenerating ? 'Generating...' : 'Generate Document'}
                </Button>
              )}

              {(documentUrl || lease.documentUrl) && (
                <>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    Download PDF
                  </Button>
                  
                  {lease.eSignatureStatus !== 'signed' && (
                    <Button 
                      onClick={sendForSignature}
                      className="flex items-center gap-2"
                    >
                      <Send className="h-4 w-4" />
                      Send for Signature
                    </Button>
                  )}
                </>
              )}
            </div>

            {/* Document Preview Info */}
            {(documentUrl || lease.documentUrl) && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  <strong>Document Generated:</strong> The lease agreement has been created and is ready for signature.
                  {lease.eSignatureStatus === 'pending' && ' The document has been sent to the tenant for electronic signature.'}
                  {lease.eSignatureStatus === 'signed' && ' The document has been signed by all parties.'}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* E-Signature Management */}
      {(documentUrl || lease.documentUrl) && (
        <ESignatureManager
          leaseId={leaseId}
          documentUrl={documentUrl || lease.documentUrl}
          onSignatureCompleted={onDocumentGenerated}
        />
      )}

      {/* Document Template Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Document Template Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div className="border-l-4 border-blue-500 pl-4">
              <h4 className="font-semibold">RESIDENTIAL LEASE AGREEMENT</h4>
              <p className="text-gray-600 mt-1">
                This lease agreement template includes all standard clauses and terms
                as specified in the lease configuration.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h5 className="font-medium">Included Sections:</h5>
                <ul className="list-disc list-inside text-gray-600 mt-1 space-y-1">
                  <li>Property and tenant information</li>
                  <li>Lease term and rent details</li>
                  <li>Security deposit terms</li>
                  <li>Late payment policies</li>
                  <li>Maintenance responsibilities</li>
                  <li>Termination conditions</li>
                </ul>
              </div>
              <div>
                <h5 className="font-medium">Legal Compliance:</h5>
                <ul className="list-disc list-inside text-gray-600 mt-1 space-y-1">
                  <li>Local housing regulations</li>
                  <li>Tenant rights and protections</li>
                  <li>Fair housing compliance</li>
                  <li>State-specific requirements</li>
                  <li>Electronic signature validity</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}