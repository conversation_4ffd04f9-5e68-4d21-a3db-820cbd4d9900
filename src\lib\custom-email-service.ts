// Custom Email Service to replace third-party email services
export interface CustomEmailConfig {
  smtpHost: string;
  smtpPort: number;
  username: string;
  password: string;
  fromEmail: string;
  fromName?: string;
  secure?: boolean; // Use TLS
}

export interface EmailMessage {
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  text?: string;
  html?: string;
  attachments?: EmailAttachment[];
  replyTo?: string;
  priority?: 'high' | 'normal' | 'low';
}

export interface EmailAttachment {
  filename: string;
  content: string | Buffer;
  contentType?: string;
  encoding?: string;
  cid?: string; // Content-ID for inline attachments
}

export interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
  rejectedRecipients?: string[];
}

export interface EmailTemplate {
  name: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  variables: string[];
}

export interface BulkEmailRequest {
  recipients: string[];
  template?: EmailTemplate;
  personalizedData?: Record<string, Record<string, any>>;
  message?: EmailMessage;
}

export interface BulkEmailResult {
  success: boolean;
  totalEmails: number;
  successfulEmails: number;
  failedEmails: number;
  results: EmailResult[];
  error?: string;
}

export interface EmailDeliveryStatus {
  messageId: string;
  status: 'pending' | 'sent' | 'delivered' | 'bounced' | 'failed';
  timestamp?: Date;
  bounceReason?: string;
  error?: string;
}

export class CustomEmailService {
  private config: CustomEmailConfig;

  constructor(config: CustomEmailConfig) {
    this.config = config;
  }

  /**
   * Send a single email
   */
  async sendEmail(message: EmailMessage): Promise<EmailResult> {
    try {
      // Validate email addresses
      const toEmails = Array.isArray(message.to) ? message.to : [message.to];
      const invalidEmails = toEmails.filter(email => !this.isValidEmail(email));
      
      if (invalidEmails.length > 0) {
        return {
          success: false,
          error: `Invalid email addresses: ${invalidEmails.join(', ')}`,
        };
      }

      // TODO: Replace with actual SMTP implementation
      // This is a placeholder implementation using fetch to a custom email API
      const emailData = {
        from: {
          email: this.config.fromEmail,
          name: this.config.fromName || 'EstatePulse'
        },
        to: toEmails.map(email => ({ email })),
        cc: message.cc ? (Array.isArray(message.cc) ? message.cc : [message.cc]).map(email => ({ email })) : undefined,
        bcc: message.bcc ? (Array.isArray(message.bcc) ? message.bcc : [message.bcc]).map(email => ({ email })) : undefined,
        subject: message.subject,
        text: message.text,
        html: message.html,
        replyTo: message.replyTo ? { email: message.replyTo } : undefined,
        priority: message.priority || 'normal',
        attachments: message.attachments?.map(att => ({
          filename: att.filename,
          content: typeof att.content === 'string' ? att.content : att.content.toString('base64'),
          contentType: att.contentType,
          encoding: att.encoding || 'base64',
          cid: att.cid,
        })),
      };

      // In a real implementation, you would use nodemailer or similar SMTP library
      // For now, we'll simulate an API call
      const response = await fetch(`${this.getApiUrl()}/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getApiKey()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        };
      }

      const data = await response.json();
      
      return {
        success: true,
        messageId: data.messageId || data.id,
        rejectedRecipients: data.rejectedRecipients,
      };
    } catch (error) {
      console.error('Email sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Send templated email
   */
  async sendTemplatedEmail(
    to: string | string[],
    template: EmailTemplate,
    data: Record<string, any>
  ): Promise<EmailResult> {
    try {
      // Process template with personalization data
      let processedSubject = template.subject;
      let processedHtml = template.htmlContent;
      let processedText = template.textContent || '';

      Object.entries(data).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        processedSubject = processedSubject.replace(regex, String(value));
        processedHtml = processedHtml.replace(regex, String(value));
        processedText = processedText.replace(regex, String(value));
      });

      return await this.sendEmail({
        to,
        subject: processedSubject,
        html: processedHtml,
        text: processedText || undefined,
      });
    } catch (error) {
      console.error('Templated email sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Template processing failed',
      };
    }
  }

  /**
   * Send bulk emails
   */
  async sendBulkEmails(request: BulkEmailRequest): Promise<BulkEmailResult> {
    try {
      const results: EmailResult[] = [];
      let successfulEmails = 0;
      let failedEmails = 0;

      for (const recipient of request.recipients) {
        let emailToSend: EmailMessage;

        if (request.template && request.personalizedData) {
          const personalData = request.personalizedData[recipient] || {};
          const result = await this.sendTemplatedEmail(recipient, request.template, personalData);
          results.push(result);
          
          if (result.success) {
            successfulEmails++;
          } else {
            failedEmails++;
          }
          continue;
        }

        if (request.message) {
          emailToSend = {
            ...request.message,
            to: recipient,
          };
        } else {
          results.push({
            success: false,
            error: 'No message or template provided',
          });
          failedEmails++;
          continue;
        }

        const result = await this.sendEmail(emailToSend);
        results.push(result);

        if (result.success) {
          successfulEmails++;
        } else {
          failedEmails++;
        }

        // Add small delay to avoid overwhelming the SMTP server
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      return {
        success: successfulEmails > 0,
        totalEmails: request.recipients.length,
        successfulEmails,
        failedEmails,
        results,
      };
    } catch (error) {
      console.error('Bulk email sending failed:', error);
      return {
        success: false,
        totalEmails: request.recipients.length,
        successfulEmails: 0,
        failedEmails: request.recipients.length,
        results: [],
        error: error instanceof Error ? error.message : 'Bulk email failed',
      };
    }
  }

  /**
   * Get email delivery status
   */
  async getDeliveryStatus(messageId: string): Promise<EmailDeliveryStatus | null> {
    try {
      // TODO: Replace with actual email tracking API
      const response = await fetch(`${this.getApiUrl()}/status/${messageId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.getApiKey()}`,
        },
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      
      return {
        messageId,
        status: data.status || 'pending',
        timestamp: data.timestamp ? new Date(data.timestamp) : undefined,
        bounceReason: data.bounceReason,
        error: data.error,
      };
    } catch (error) {
      console.error('Failed to get email delivery status:', error);
      return null;
    }
  }

  /**
   * Validate email address format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Get API URL for email service
   */
  private getApiUrl(): string {
    // In a real implementation, this would be your custom email API endpoint
    return process.env.CUSTOM_EMAIL_API_URL || 'https://api.youremailservice.com';
  }

  /**
   * Get API key for email service
   */
  private getApiKey(): string {
    return process.env.CUSTOM_EMAIL_API_KEY || '';
  }
}

// Factory function to create CustomEmailService instance
export function createCustomEmailService(): CustomEmailService {
  const config: CustomEmailConfig = {
    smtpHost: process.env.CUSTOM_EMAIL_SMTP_HOST || '',
    smtpPort: parseInt(process.env.CUSTOM_EMAIL_SMTP_PORT || '587'),
    username: process.env.CUSTOM_EMAIL_USERNAME || '',
    password: process.env.CUSTOM_EMAIL_PASSWORD || '',
    fromEmail: process.env.CUSTOM_EMAIL_FROM_EMAIL || '<EMAIL>',
    fromName: process.env.CUSTOM_EMAIL_FROM_NAME || 'EstatePulse',
    secure: process.env.CUSTOM_EMAIL_SECURE === 'true',
  };

  if (!config.smtpHost || !config.username || !config.password) {
    throw new Error('Custom email configuration missing. Please set CUSTOM_EMAIL_SMTP_HOST, CUSTOM_EMAIL_USERNAME, and CUSTOM_EMAIL_PASSWORD environment variables.');
  }

  return new CustomEmailService(config);
}
