import { v } from "convex/values";
import { mutation, query, action, internalMutation } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { internal } from "./_generated/api";

// Communication types
export const communicationTypes = v.union(
  v.literal("sms"),
  v.literal("whatsapp"),
  v.literal("email")
);

export const messageStatus = v.union(
  v.literal("pending"),
  v.literal("sent"),
  v.literal("delivered"),
  v.literal("failed"),
  v.literal("read")
);

// Send SMS message
export const sendSMS = action({
  args: {
    phoneNumber: v.string(),
    message: v.string(),
    propertyId: v.optional(v.id("properties")),
    userId: v.optional(v.id("users")),
    templateId: v.optional(v.string()),
    personalizedData: v.optional(v.record(v.string(), v.any())),
  },
  handler: async (ctx, args) => {
    // Create message record first
    const messageId = await ctx.runMutation(internal.communications.createMessage, {
      type: "sms",
      recipientPhone: args.phoneNumber,
      content: args.message,
      status: "pending",
      propertyId: args.propertyId,
      recipientId: args.userId!,
      templateId: args.templateId,
    });

    try {
      // Send via custom SMS service
      const result = await ctx.runAction(internal.customCommunicationService.sendCustomSMS, {
        phoneNumber: args.phoneNumber,
        message: args.message,
        personalizedData: args.personalizedData,
      });

      if (result.success) {
        await ctx.runMutation(internal.communications.updateMessageStatus, {
          messageId,
          status: "sent",
          externalId: result.messageId,
          sentAt: Date.now(),
        });

        return {
          success: true,
          messageId,
          externalId: result.messageId,
          status: result.status,
        };
      } else {
        await ctx.runMutation(internal.communications.updateMessageStatus, {
          messageId,
          status: "failed",
          errorMessage: result.error,
        });

        throw new Error(`Failed to send SMS: ${result.error}`);
      }
    } catch (error) {
      await ctx.runMutation(internal.communications.updateMessageStatus, {
        messageId,
        status: "failed",
        errorMessage: error instanceof Error ? error.message : String(error),
      });

      throw new Error(`Failed to send SMS: ${error instanceof Error ? error.message : String(error)}`);
    }
  },
});

// Send WhatsApp message
export const sendWhatsApp = action({
  args: {
    phoneNumber: v.string(),
    message: v.string(),
    propertyId: v.optional(v.id("properties")),
    userId: v.optional(v.id("users")),
    templateId: v.optional(v.string()),
    personalizedData: v.optional(v.record(v.string(), v.any())),
  },
  handler: async (ctx, args) => {
    const messageId = await ctx.runMutation(internal.communications.createMessage, {
      type: "whatsapp",
      recipientPhone: args.phoneNumber,
      content: args.message,
      status: "pending",
      propertyId: args.propertyId,
      recipientId: args.userId!,
      templateId: args.templateId,
    });

    try {
      // Send via custom WhatsApp service
      const result = await ctx.runAction(internal.customCommunicationService.sendCustomWhatsApp, {
        phoneNumber: args.phoneNumber,
        message: args.message,
        personalizedData: args.personalizedData,
      });

      if (result.success) {
        await ctx.runMutation(internal.communications.updateMessageStatus, {
          messageId,
          status: "sent",
          externalId: result.messageId,
          sentAt: Date.now(),
        });

        return {
          success: true,
          messageId,
          externalId: result.messageId,
          status: result.status,
        };
      } else {
        await ctx.runMutation(internal.communications.updateMessageStatus, {
          messageId,
          status: "failed",
          errorMessage: result.error,
        });

        throw new Error(`Failed to send WhatsApp message: ${result.error}`);
      }
    } catch (error) {
      await ctx.runMutation(internal.communications.updateMessageStatus, {
        messageId,
        status: "failed",
        errorMessage: error instanceof Error ? error.message : String(error),
      });

      throw new Error(`Failed to send WhatsApp message: ${error instanceof Error ? error.message : String(error)}`);
    }
  },
});

// Send Email message
export const sendEmail = action({
  args: {
    to: v.string(),
    subject: v.string(),
    html: v.optional(v.string()),
    text: v.optional(v.string()),
    propertyId: v.optional(v.id("properties")),
    userId: v.optional(v.id("users")),
    templateId: v.optional(v.string()),
    personalizedData: v.optional(v.record(v.string(), v.any())),
  },
  handler: async (ctx, args) => {
    const messageId = await ctx.runMutation(internal.communications.createMessage, {
      type: "email",
      recipientEmail: args.to,
      content: args.html || args.text || args.subject,
      status: "pending",
      propertyId: args.propertyId,
      recipientId: args.userId!,
      templateId: args.templateId,
    });

    try {
      // Send via custom email service
      const result = await ctx.runAction(internal.customCommunicationService.sendCustomEmail, {
        to: args.to,
        subject: args.subject,
        html: args.html,
        text: args.text,
        personalizedData: args.personalizedData,
      });

      if (result.success) {
        await ctx.runMutation(internal.communications.updateMessageStatus, {
          messageId,
          status: "sent",
          externalId: result.messageId,
          sentAt: Date.now(),
        });

        return {
          success: true,
          messageId,
          externalId: result.messageId,
          status: result.status,
        };
      } else {
        await ctx.runMutation(internal.communications.updateMessageStatus, {
          messageId,
          status: "failed",
          errorMessage: result.error,
        });

        throw new Error(`Failed to send email: ${result.error}`);
      }
    } catch (error) {
      await ctx.runMutation(internal.communications.updateMessageStatus, {
        messageId,
        status: "failed",
        errorMessage: error instanceof Error ? error.message : String(error),
      });

      throw new Error(`Failed to send email: ${error instanceof Error ? error.message : String(error)}`);
    }
  },
});

// Send bulk messages with enhanced error handling and rate limiting
export const sendBulkMessages = action({
  args: {
    recipients: v.array(v.object({
      phoneNumber: v.string(),
      userId: v.optional(v.id("users")),
      personalizedData: v.optional(v.record(v.string(), v.any())),
    })),
    message: v.string(),
    type: communicationTypes,
    propertyId: v.optional(v.id("properties")),
    templateId: v.optional(v.string()),
    options: v.optional(v.object({
      batchSize: v.optional(v.number()),
      delayBetweenBatches: v.optional(v.number()),
    })),
  },
  handler: async (ctx, args) => {
    // Create bulk message record for tracking
    const bulkMessageId = await ctx.runMutation(internal.communications.createBulkMessage, {
      type: args.type,
      message: args.message,
      propertyId: args.propertyId,
      templateId: args.templateId,
      totalRecipients: args.recipients.length,
      status: "sending",
    });

    try {
      // Use enhanced bulk messaging with rate limiting
      const twilioResult = await ctx.runAction(internal.twilioService.sendBulkMessages, {
        type: args.type as "sms" | "whatsapp",
        recipients: args.recipients.map(r => ({
          phoneNumber: r.phoneNumber,
          personalizedData: r.personalizedData,
        })),
        message: args.message,
      });

      const results = twilioResult.success ? twilioResult.results : [];

      // Process results and create individual message records
      const messagePromises = results.map(async (result) => {
        const recipient = args.recipients.find(r => r.phoneNumber === result.phoneNumber);
        const messageId = await ctx.runMutation(internal.communications.createMessage, {
          type: args.type,
          recipientPhone: result.phoneNumber,
          content: args.message,
          status: result.success ? "sent" : "failed",
          propertyId: args.propertyId,
          recipientId: recipient?.userId!,
          templateId: args.templateId,
          externalId: result.success ? result.result?.sid : undefined,
          errorMessage: result.success ? undefined : result.error,
        });

        return {
          phoneNumber: result.phoneNumber,
          success: result.success,
          messageId,
          externalId: result.success ? result.result?.sid : undefined,
          error: result.success ? undefined : result.error,
        };
      });

      const processedResults = await Promise.all(messagePromises);
      
      const totalSent = processedResults.filter(r => r.success).length;
      const totalFailed = processedResults.filter(r => !r.success).length;

      // Update bulk message record
      await ctx.runMutation(internal.communications.updateBulkMessage, {
        bulkMessageId,
        status: "completed",
        sentCount: totalSent,
        failedCount: totalFailed,
        completedAt: Date.now(),
      });

      return {
        bulkMessageId,
        totalSent,
        totalFailed,
        results: processedResults,
      };
    } catch (error) {
      // Update bulk message record with error
      await ctx.runMutation(internal.communications.updateBulkMessage, {
        bulkMessageId,
        status: "failed",
        errorMessage: error instanceof Error ? error.message : String(error),
        completedAt: Date.now(),
      });
      
      throw new Error(`Bulk message sending failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  },
});

// Get message history
export const getMessages = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    userId: v.optional(v.id("users")),
    type: v.optional(communicationTypes),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("communicationLogs");
    
    if (args.propertyId) {
      query = query.filter(q => q.eq(q.field("propertyId"), args.propertyId));
    }
    
    if (args.userId) {
      query = query.filter(q => q.eq(q.field("recipientId"), args.userId));
    }
    
    if (args.type) {
      query = query.filter(q => q.eq(q.field("type"), args.type));
    }

    return await query
      .order("desc")
      .take(args.limit || 50);
  },
});

// Get message delivery status
export const getMessageStatus = query({
  args: { messageId: v.id("communicationLogs") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.messageId);
  },
});

// Get notification preferences
export const getNotificationPreferences = query({
  args: {
    userId: v.id("users"),
    propertyId: v.optional(v.id("properties")),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("notificationPreferences")
      .filter(q => q.eq(q.field("userId"), args.userId));
    
    if (args.propertyId) {
      query = query.filter(q => q.eq(q.field("propertyId"), args.propertyId));
    }

    const preferences = await query.first();
    
    if (!preferences) {
      // Return default preferences
      return {
        preferences: {
          sms: {
            enabled: true,
            paymentReminders: true,
            maintenanceUpdates: true,
            leaseNotifications: true,
            emergencyAlerts: true,
            generalAnnouncements: false,
          },
          whatsapp: {
            enabled: false,
            paymentReminders: false,
            maintenanceUpdates: false,
            leaseNotifications: false,
            emergencyAlerts: true,
            generalAnnouncements: false,
          },
          email: {
            enabled: true,
            paymentReminders: true,
            maintenanceUpdates: true,
            leaseNotifications: true,
            emergencyAlerts: true,
            generalAnnouncements: true,
            weeklyReports: true,
            monthlyStatements: true,
          },
          inApp: {
            enabled: true,
            paymentReminders: true,
            maintenanceUpdates: true,
            leaseNotifications: true,
            emergencyAlerts: true,
            generalAnnouncements: true,
          },
        },
        quietHours: {
          enabled: false,
          startTime: '22:00',
          endTime: '08:00',
          timezone: 'Africa/Nairobi',
        },
        language: 'en',
      };
    }

    return preferences;
  },
});

// Save notification preferences
export const saveNotificationPreferences = mutation({
  args: {
    userId: v.id("users"),
    propertyId: v.optional(v.id("properties")),
    preferences: v.object({
      sms: v.object({
        enabled: v.boolean(),
        paymentReminders: v.boolean(),
        maintenanceUpdates: v.boolean(),
        leaseNotifications: v.boolean(),
        emergencyAlerts: v.boolean(),
        generalAnnouncements: v.boolean(),
      }),
      whatsapp: v.object({
        enabled: v.boolean(),
        paymentReminders: v.boolean(),
        maintenanceUpdates: v.boolean(),
        leaseNotifications: v.boolean(),
        emergencyAlerts: v.boolean(),
        generalAnnouncements: v.boolean(),
      }),
      email: v.object({
        enabled: v.boolean(),
        paymentReminders: v.boolean(),
        maintenanceUpdates: v.boolean(),
        leaseNotifications: v.boolean(),
        emergencyAlerts: v.boolean(),
        generalAnnouncements: v.boolean(),
        weeklyReports: v.boolean(),
        monthlyStatements: v.boolean(),
      }),
      inApp: v.object({
        enabled: v.boolean(),
        paymentReminders: v.boolean(),
        maintenanceUpdates: v.boolean(),
        leaseNotifications: v.boolean(),
        emergencyAlerts: v.boolean(),
        generalAnnouncements: v.boolean(),
      }),
    }),
    quietHours: v.object({
      enabled: v.boolean(),
      startTime: v.string(),
      endTime: v.string(),
      timezone: v.string(),
    }),
    language: v.string(),
  },
  handler: async (ctx, args) => {
    // Check if preferences already exist
    let query = ctx.db.query("notificationPreferences")
      .filter(q => q.eq(q.field("userId"), args.userId));
    
    if (args.propertyId) {
      query = query.filter(q => q.eq(q.field("propertyId"), args.propertyId));
    }

    const existing = await query.first();

    if (existing) {
      // Update existing preferences
      return await ctx.db.patch(existing._id, {
        preferences: args.preferences,
        quietHours: args.quietHours,
        language: args.language,
        updatedAt: Date.now(),
      });
    } else {
      // Create new preferences
      return await ctx.db.insert("notificationPreferences", {
        userId: args.userId,
        propertyId: args.propertyId,
        preferences: args.preferences,
        quietHours: args.quietHours,
        language: args.language,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
    }
  },
});

// Get message templates
export const getMessageTemplates = query({
  args: {
    templateId: v.optional(v.id("messageTemplates")),
    propertyId: v.optional(v.id("properties")),
    type: v.optional(communicationTypes),
    category: v.optional(v.union(
      v.literal("payment_reminder"),
      v.literal("maintenance_update"),
      v.literal("lease_expiry"),
      v.literal("welcome"),
      v.literal("general"),
      v.literal("emergency")
    )),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // If templateId is provided, return single template
    if (args.templateId) {
      const template = await ctx.db.get(args.templateId);
      return template ? [template] : [];
    }

    let query = ctx.db.query("messageTemplates");
    
    if (args.propertyId) {
      query = query.filter(q => q.eq(q.field("propertyId"), args.propertyId));
    }
    
    if (args.type) {
      query = query.filter(q => q.eq(q.field("type"), args.type));
    }
    
    if (args.category) {
      query = query.filter(q => q.eq(q.field("category"), args.category));
    }
    
    if (args.isActive !== undefined) {
      query = query.filter(q => q.eq(q.field("isActive"), args.isActive));
    }

    return await query.order("desc").collect();
  },
});

// Create message template
export const createMessageTemplate = mutation({
  args: {
    name: v.string(),
    type: communicationTypes,
    category: v.union(
      v.literal("payment_reminder"),
      v.literal("maintenance_update"),
      v.literal("lease_expiry"),
      v.literal("welcome"),
      v.literal("general"),
      v.literal("emergency")
    ),
    subject: v.optional(v.string()),
    content: v.string(),
    variables: v.array(v.string()),
    isActive: v.boolean(),
    propertyId: v.optional(v.id("properties")),
    createdBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("messageTemplates", {
      ...args,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  },
});

// Update message template
export const updateMessageTemplate = mutation({
  args: {
    templateId: v.id("messageTemplates"),
    name: v.optional(v.string()),
    type: v.optional(communicationTypes),
    category: v.optional(v.union(
      v.literal("payment_reminder"),
      v.literal("maintenance_update"),
      v.literal("lease_expiry"),
      v.literal("welcome"),
      v.literal("general"),
      v.literal("emergency")
    )),
    subject: v.optional(v.string()),
    content: v.optional(v.string()),
    variables: v.optional(v.array(v.string())),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { templateId, ...updates } = args;
    return await ctx.db.patch(templateId, {
      ...updates,
      updatedAt: Date.now(),
    });
  },
});

// Delete message template
export const deleteMessageTemplate = mutation({
  args: { templateId: v.id("messageTemplates") },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.templateId);
  },
});

// Get delivery status report
export const getDeliveryReport = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    type: v.optional(communicationTypes),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("communicationLogs");
    
    if (args.propertyId) {
      query = query.filter(q => q.eq(q.field("propertyId"), args.propertyId));
    }
    
    if (args.type) {
      query = query.filter(q => q.eq(q.field("type"), args.type));
    }
    
    if (args.startDate) {
      query = query.filter(q => q.gte(q.field("createdAt"), args.startDate));
    }
    
    if (args.endDate) {
      query = query.filter(q => q.lte(q.field("createdAt"), args.endDate));
    }

    const messages = await query.collect();
    
    const totalMessages = messages.length;
    const sentMessages = messages.filter(m => m.status === "sent" || m.status === "delivered").length;
    const deliveredMessages = messages.filter(m => m.status === "delivered").length;
    const failedMessages = messages.filter(m => m.status === "failed").length;
    const pendingMessages = messages.filter(m => m.status === "pending").length;
    
    const deliveryRate = totalMessages > 0 ? (deliveredMessages / totalMessages) * 100 : 0;
    const successRate = totalMessages > 0 ? (sentMessages / totalMessages) * 100 : 0;
    
    // Group by type
    const byType = messages.reduce((acc, message) => {
      if (!acc[message.type]) {
        acc[message.type] = { total: 0, sent: 0, delivered: 0, failed: 0, pending: 0 };
      }
      acc[message.type].total++;
      acc[message.type][message.status]++;
      return acc;
    }, {} as Record<string, any>);
    
    // Group by date (daily)
    const byDate = messages.reduce((acc, message) => {
      const date = new Date(message.createdAt).toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = { total: 0, sent: 0, delivered: 0, failed: 0, pending: 0 };
      }
      acc[date].total++;
      acc[date][message.status]++;
      return acc;
    }, {} as Record<string, any>);

    return {
      summary: {
        totalMessages,
        sentMessages,
        deliveredMessages,
        failedMessages,
        pendingMessages,
        deliveryRate: Math.round(deliveryRate * 100) / 100,
        successRate: Math.round(successRate * 100) / 100,
      },
      byType,
      byDate,
      recentMessages: messages
        .sort((a, b) => b.createdAt - a.createdAt)
        .slice(0, 10),
    };
  },
});

// Update message delivery status (webhook handler)
export const updateDeliveryStatus = action({
  args: {
    externalId: v.string(),
    status: messageStatus,
    deliveredAt: v.optional(v.number()),
    errorCode: v.optional(v.string()),
    errorMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Find message by external ID
    const message = await ctx.runQuery(api.communications.getMessageByExternalId, {
      externalId: args.externalId,
    });

    if (!message) {
      throw new Error(`Message not found for external ID: ${args.externalId}`);
    }

    // Update message status
    await ctx.runMutation(internal.communications.updateMessageStatus, {
      messageId: message._id,
      status: args.status,
      deliveredAt: args.deliveredAt,
      error: args.errorMessage,
    });

    return { success: true, messageId: message._id };
  },
});

// Get message by external ID
export const getMessageByExternalId = query({
  args: { externalId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("communicationLogs")
      .filter(q => q.eq(q.field("externalId"), args.externalId))
      .first();
  },
});

// Get bulk message status
export const getBulkMessageStatus = query({
  args: { bulkMessageId: v.id("bulkMessages") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.bulkMessageId);
  },
});

// Get bulk messages history
export const getBulkMessages = query({
  args: {
    propertyId: v.optional(v.id("properties")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("bulkMessages");
    
    if (args.propertyId) {
      query = query.filter(q => q.eq(q.field("propertyId"), args.propertyId));
    }

    return await query
      .order("desc")
      .take(args.limit || 20);
  },
});

// Send templated message
export const sendTemplatedMessage = action({
  args: {
    templateId: v.id("messageTemplates"),
    phoneNumber: v.string(),
    personalizedData: v.record(v.string(), v.any()),
    propertyId: v.optional(v.id("properties")),
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    // Get template
    const template = await ctx.runQuery(api.communications.getMessageTemplates, {
      templateId: args.templateId,
    });

    if (!template || template.length === 0) {
      throw new Error("Template not found");
    }

    const messageTemplate = template[0];

    // Send message based on template type
    if (messageTemplate.type === "sms") {
      return await ctx.runAction(api.communications.sendSMS, {
        phoneNumber: args.phoneNumber,
        message: messageTemplate.content,
        personalizedData: args.personalizedData,
        propertyId: args.propertyId,
        userId: args.userId,
        templateId: args.templateId,
      });
    } else if (messageTemplate.type === "whatsapp") {
      return await ctx.runAction(api.communications.sendWhatsApp, {
        phoneNumber: args.phoneNumber,
        message: messageTemplate.content,
        personalizedData: args.personalizedData,
        propertyId: args.propertyId,
        userId: args.userId,
        templateId: args.templateId,
      });
    } else {
      throw new Error(`Unsupported template type: ${messageTemplate.type}`);
    }
  },
});

// Get notifications for a user
export const getNotifications = query({
  args: {
    userId: v.id("users"),
    propertyId: v.optional(v.id("properties")),
    limit: v.optional(v.number()),
    isRead: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("notifications")
      .filter(q => q.eq(q.field("userId"), args.userId));

    if (args.propertyId) {
      query = query.filter(q => q.eq(q.field("propertyId"), args.propertyId));
    }

    if (args.isRead !== undefined) {
      query = query.filter(q => q.eq(q.field("isRead"), args.isRead));
    }

    return await query
      .order("desc")
      .take(args.limit || 50);
  },
});

// Get scheduled notifications
export const getScheduledNotifications = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("notifications")
      .filter(q => 
        q.and(
          q.eq(q.field("userId"), args.userId),
          q.neq(q.field("scheduledFor"), undefined)
        )
      )
      .order("desc")
      .take(args.limit || 50);
  },
});

// Get notification history
export const getNotificationHistory = query({
  args: {
    userId: v.id("users"),
    fromDate: v.optional(v.number()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("notifications")
      .filter(q => q.eq(q.field("userId"), args.userId));

    if (args.fromDate) {
      query = query.filter(q => q.gte(q.field("createdAt"), args.fromDate));
    }

    return await query
      .order("desc")
      .take(args.limit || 100);
  },
});

// Schedule a notification
export const scheduleNotification = mutation({
  args: {
    userId: v.id("users"),
    title: v.string(),
    message: v.string(),
    type: v.union(
      v.literal("payment_reminder"),
      v.literal("maintenance_update"),
      v.literal("maintenance_assigned"),
      v.literal("maintenance_escalated"),
      v.literal("maintenance_completed"),
      v.literal("sla_warning"),
      v.literal("lease_expiry"),
      v.literal("system"),
      v.literal("report_generated"),
      v.literal("system_alert"),
      v.literal("maintenance_due")
    ),
    scheduledFor: v.number(),
    priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("urgent")),
    actionUrl: v.optional(v.string()),
    metadata: v.optional(v.object({
      invoiceId: v.optional(v.id("invoices")),
      ticketId: v.optional(v.id("maintenanceTickets")),
      leaseId: v.optional(v.id("leases")),
      escalationId: v.optional(v.id("maintenanceEscalations")),
    })),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("notifications", {
      userId: args.userId,
      title: args.title,
      message: args.message,
      type: args.type,
      priority: args.priority,
      isRead: false,
      actionUrl: args.actionUrl,
      metadata: args.metadata,
      scheduledFor: args.scheduledFor,
      createdAt: Date.now(),
    });
  },
});

// Mark notification as read
export const markNotificationAsRead = mutation({
  args: { notificationId: v.id("notifications") },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.notificationId, {
      isRead: true,
    });
  },
});

// Mark all notifications as read
export const markAllNotificationsAsRead = mutation({
  args: {
    userId: v.id("users"),
    propertyId: v.optional(v.id("properties")),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("notifications")
      .filter(q => 
        q.and(
          q.eq(q.field("userId"), args.userId),
          q.eq(q.field("isRead"), false)
        )
      );

    const notifications = await query.collect();
    
    const updatePromises = notifications.map(notification =>
      ctx.db.patch(notification._id, {
        isRead: true,
      })
    );

    await Promise.all(updatePromises);
    return notifications.length;
  },
});

// Delete notification
export const deleteNotification = mutation({
  args: { notificationId: v.id("notifications") },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.notificationId);
  },
});

// Cancel scheduled notification
export const cancelScheduledNotification = mutation({
  args: { notificationId: v.id("notifications") },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.notificationId);
  },
});

// Create in-app notification
export const createNotification = mutation({
  args: {
    userId: v.id("users"),
    title: v.string(),
    message: v.string(),
    type: v.union(
      v.literal("payment_reminder"),
      v.literal("maintenance_update"),
      v.literal("maintenance_assigned"),
      v.literal("maintenance_escalated"),
      v.literal("maintenance_completed"),
      v.literal("sla_warning"),
      v.literal("lease_expiry"),
      v.literal("system"),
      v.literal("report_generated"),
      v.literal("system_alert"),
      v.literal("maintenance_due")
    ),
    priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("urgent")),
    metadata: v.optional(v.object({
      invoiceId: v.optional(v.id("invoices")),
      ticketId: v.optional(v.id("maintenanceTickets")),
      leaseId: v.optional(v.id("leases")),
      escalationId: v.optional(v.id("maintenanceEscalations")),
    })),
    actionUrl: v.optional(v.string()),
    scheduledFor: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("notifications", {
      userId: args.userId,
      title: args.title,
      message: args.message,
      type: args.type,
      priority: args.priority,
      isRead: false,
      metadata: args.metadata,
      actionUrl: args.actionUrl,
      scheduledFor: args.scheduledFor,
      createdAt: Date.now(),
    });
  },
});

// Internal mutations
export const createMessage = internalMutation({
  args: {
    type: communicationTypes,
    recipientPhone: v.string(),
    recipientEmail: v.optional(v.string()),
    content: v.string(),
    status: messageStatus,
    propertyId: v.optional(v.id("properties")),
    recipientId: v.id("users"),
    senderId: v.optional(v.id("users")),
    templateId: v.optional(v.id("messageTemplates")),
    externalId: v.optional(v.string()),
    errorMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("communicationLogs", {
      recipientId: args.recipientId,
      recipientPhone: args.recipientPhone,
      recipientEmail: args.recipientEmail,
      senderId: args.senderId || args.recipientId, // Default to recipient if no sender specified
      propertyId: args.propertyId,
      type: args.type,
      templateId: args.templateId,
      content: args.content,
      status: args.status,
      externalId: args.externalId,
      errorMessage: args.errorMessage,
      createdAt: Date.now(),
    });
  },
});

export const updateMessageStatus = internalMutation({
  args: {
    messageId: v.id("communicationLogs"),
    status: messageStatus,
    externalId: v.optional(v.string()),
    sentAt: v.optional(v.number()),
    deliveredAt: v.optional(v.number()),
    readAt: v.optional(v.number()),
    errorMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { messageId, ...updates } = args;
    return await ctx.db.patch(messageId, updates);
  },
});

export const createBulkMessage = internalMutation({
  args: {
    type: communicationTypes,
    message: v.string(),
    propertyId: v.optional(v.id("properties")),
    templateId: v.optional(v.string()),
    totalRecipients: v.number(),
    status: v.union(
      v.literal("sending"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("cancelled")
    ),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("bulkMessages", {
      name: `Bulk ${args.type.toUpperCase()} - ${new Date().toISOString()}`,
      senderId: args.recipientId, // Simplified for now
      ...args,
      sentCount: 0,
      deliveredCount: 0,
      failedCount: 0,
      startedAt: Date.now(),
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  },
});

export const updateBulkMessage = internalMutation({
  args: {
    bulkMessageId: v.id("bulkMessages"),
    status: v.optional(v.union(
      v.literal("sending"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("cancelled")
    )),
    sentCount: v.optional(v.number()),
    deliveredCount: v.optional(v.number()),
    failedCount: v.optional(v.number()),
    completedAt: v.optional(v.number()),
    errorMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { bulkMessageId, ...updates } = args;
    return await ctx.db.patch(bulkMessageId, {
      ...updates,
      updatedAt: Date.now(),
    });
  },
});

