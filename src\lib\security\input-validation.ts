import { z } from 'zod';
import DOMPurify from 'dompurify';

// Input sanitization utilities
export class InputSanitizer {
  /**
   * Sanitize HTML content to prevent XSS attacks
   */
  static sanitizeHtml(input: string): string {
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
      ALLOWED_ATTR: []
    });
  }

  /**
   * Sanitize text input by removing potentially dangerous characters
   */
  static sanitizeText(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  }

  /**
   * Sanitize SQL-like input (though we use Convex, this is for extra safety)
   */
  static sanitizeSqlInput(input: string): string {
    return input
      .replace(/['";\\]/g, '') // Remove SQL injection characters
      .replace(/--/g, '') // Remove SQL comments
      .replace(/\/\*/g, '') // Remove block comments
      .trim();
  }

  /**
   * Sanitize file names to prevent directory traversal
   */
  static sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
      .replace(/\.\./g, '') // Remove directory traversal
      .replace(/^\./, '') // Remove leading dot
      .substring(0, 255); // Limit length
  }

  /**
   * Sanitize phone numbers
   */
  static sanitizePhoneNumber(phone: string): string {
    return phone.replace(/[^\d+\-\s()]/g, '').trim();
  }

  /**
   * Sanitize email addresses
   */
  static sanitizeEmail(email: string): string {
    return email.toLowerCase().trim();
  }
}

// Validation schemas for different data types
export const ValidationSchemas = {
  // User validation
  email: z.string().email().max(255),
  password: z.string().min(8).max(128).regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
  ),
  name: z.string().min(1).max(100).regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes'),
  phoneNumber: z.string().regex(/^\+?[\d\s\-()]+$/, 'Invalid phone number format'),

  // Property validation
  propertyName: z.string().min(1).max(200),
  address: z.string().min(5).max(500),
  unitNumber: z.string().min(1).max(20).regex(/^[a-zA-Z0-9\-]+$/, 'Unit number can only contain letters, numbers, and hyphens'),
  
  // Financial validation
  amount: z.number().positive().max(999999999.99),
  currency: z.enum(['KES', 'USD', 'EUR']),
  
  // File validation
  fileName: z.string().min(1).max(255).regex(/^[^<>:"/\\|?*]+$/, 'Invalid file name'),
  fileSize: z.number().positive().max(50 * 1024 * 1024), // 50MB max
  
  // ID validation
  convexId: z.string().regex(/^[a-zA-Z0-9]{16}$/, 'Invalid Convex ID format'),
  
  // Text content validation
  description: z.string().max(2000),
  notes: z.string().max(1000),
  
  // Date validation
  dateRange: z.object({
    start: z.number().positive(),
    end: z.number().positive()
  }).refine(data => data.end > data.start, 'End date must be after start date'),
  
  // Maintenance ticket validation
  ticketPriority: z.enum(['low', 'medium', 'high', 'emergency']),
  ticketStatus: z.enum(['open', 'assigned', 'in_progress', 'completed', 'closed']),
  
  // Lease validation
  leaseStatus: z.enum(['active', 'expired', 'terminated']),
  
  // Payment validation
  paymentMethod: z.enum(['mpesa', 'stripe', 'bank_transfer']),
  paymentStatus: z.enum(['pending', 'completed', 'failed', 'refunded'])
};

// Input validation middleware for API endpoints
export class InputValidator {
  /**
   * Validate and sanitize user input based on schema
   */
  static validateInput<T>(schema: z.ZodSchema<T>, input: unknown): T {
    try {
      return schema.parse(input);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  /**
   * Validate file upload
   */
  static validateFileUpload(file: File, allowedTypes: string[], maxSize: number = 50 * 1024 * 1024): void {
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      throw new Error(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
    }

    // Check file size
    if (file.size > maxSize) {
      throw new Error(`File size ${file.size} exceeds maximum allowed size of ${maxSize} bytes`);
    }

    // Check file name
    const sanitizedName = InputSanitizer.sanitizeFileName(file.name);
    if (sanitizedName !== file.name) {
      throw new Error('File name contains invalid characters');
    }
  }

  /**
   * Validate pagination parameters
   */
  static validatePagination(page?: number, limit?: number): { page: number; limit: number } {
    const validatedPage = Math.max(1, page || 1);
    const validatedLimit = Math.min(Math.max(1, limit || 10), 100); // Max 100 items per page
    
    return { page: validatedPage, limit: validatedLimit };
  }

  /**
   * Validate search query
   */
  static validateSearchQuery(query: string): string {
    if (query.length > 100) {
      throw new Error('Search query too long');
    }
    
    return InputSanitizer.sanitizeText(query);
  }
}

// Rate limiting configuration
export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

// Common rate limit configurations
export const RateLimitConfigs = {
  // Authentication endpoints
  login: { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 attempts per 15 minutes
  register: { windowMs: 60 * 60 * 1000, maxRequests: 3 }, // 3 registrations per hour
  passwordReset: { windowMs: 60 * 60 * 1000, maxRequests: 3 }, // 3 resets per hour
  
  // API endpoints
  general: { windowMs: 15 * 60 * 1000, maxRequests: 1000 }, // 1000 requests per 15 minutes
  fileUpload: { windowMs: 60 * 60 * 1000, maxRequests: 50 }, // 50 uploads per hour
  payment: { windowMs: 60 * 60 * 1000, maxRequests: 10 }, // 10 payments per hour
  
  // Communication endpoints
  sms: { windowMs: 60 * 60 * 1000, maxRequests: 100 }, // 100 SMS per hour
  email: { windowMs: 60 * 60 * 1000, maxRequests: 200 }, // 200 emails per hour
  
  // Bulk operations
  bulkImport: { windowMs: 24 * 60 * 60 * 1000, maxRequests: 5 }, // 5 bulk imports per day
  bulkExport: { windowMs: 60 * 60 * 1000, maxRequests: 10 } // 10 bulk exports per hour
};