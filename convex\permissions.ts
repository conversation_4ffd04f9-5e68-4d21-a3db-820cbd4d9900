import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Define permission types
export type Permission = {
  resource: string;
  actions: ("create" | "read" | "update" | "delete")[];
};

// Define role permissions
export const ROLE_PERMISSIONS: Record<string, Permission[]> = {
  owner: [
    { resource: "properties", actions: ["create", "read", "update", "delete"] },
    { resource: "units", actions: ["create", "read", "update", "delete"] },
    { resource: "leases", actions: ["create", "read", "update", "delete"] },
    { resource: "tenants", actions: ["create", "read", "update", "delete"] },
    { resource: "vendors", actions: ["create", "read", "update", "delete"] },
    { resource: "managers", actions: ["create", "read", "update", "delete"] },
    { resource: "maintenance", actions: ["create", "read", "update", "delete"] },
    { resource: "payments", actions: ["create", "read", "update", "delete"] },
    { resource: "reports", actions: ["create", "read", "update", "delete"] },
    { resource: "settings", actions: ["create", "read", "update", "delete"] },
  ],
  manager: [
    { resource: "properties", actions: ["read", "update"] },
    { resource: "units", actions: ["create", "read", "update", "delete"] },
    { resource: "leases", actions: ["create", "read", "update", "delete"] },
    { resource: "tenants", actions: ["create", "read", "update"] },
    { resource: "vendors", actions: ["read", "update"] },
    { resource: "maintenance", actions: ["create", "read", "update", "delete"] },
    { resource: "payments", actions: ["read", "update"] },
    { resource: "reports", actions: ["read"] },
    { resource: "settings", actions: ["read"] },
  ],
  vendor: [
    { resource: "maintenance", actions: ["read", "update"] },
    { resource: "properties", actions: ["read"] },
    { resource: "units", actions: ["read"] },
  ],
  tenant: [
    { resource: "leases", actions: ["read"] },
    { resource: "payments", actions: ["create", "read"] },
    { resource: "maintenance", actions: ["create", "read"] },
    { resource: "units", actions: ["read"] },
  ],
};

// Get user permissions by role
export const getUserPermissions = query({
  args: { 
    sessionToken: v.string(),
    userId: v.optional(v.id("users"))
  },
  handler: async (ctx, args) => {
    // For demo purposes, we'll get the first user
    // In production, this would validate the session token
    const user = args.userId ? 
      await ctx.db.get(args.userId) : 
      await ctx.db.query("users").first();
    
    if (!user) {
      throw new Error("User not found");
    }

    return ROLE_PERMISSIONS[user.role] || [];
  },
});

// Check specific permission for a user
export const hasPermission = query({
  args: {
    sessionToken: v.string(),
    userId: v.optional(v.id("users")),
    resource: v.string(),
    action: v.union(v.literal("create"), v.literal("read"), v.literal("update"), v.literal("delete")),
  },
  handler: async (ctx, args) => {
    const user = args.userId ? 
      await ctx.db.get(args.userId) : 
      await ctx.db.query("users").first();
    
    if (!user || !user.isActive) {
      return false;
    }

    const rolePermissions = ROLE_PERMISSIONS[user.role] || [];
    const resourcePermission = rolePermissions.find(p => p.resource === args.resource);
    
    return resourcePermission ? resourcePermission.actions.includes(args.action) : false;
  },
});

// Assign property access to user
export const assignPropertyAccess = mutation({
  args: {
    sessionToken: v.string(),
    userId: v.id("users"),
    propertyId: v.id("properties"),
  },
  handler: async (ctx, args) => {
    // Simplified auth check - would validate session in production
    const currentUser = await ctx.db.query("users").first();
    if (!currentUser || !["owner", "manager"].includes(currentUser.role)) {
      throw new Error("Not authorized to assign property access");
    }

    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Add property to user's access list if not already present
    if (!user.propertyAccess.includes(args.propertyId)) {
      await ctx.db.patch(args.userId, {
        propertyAccess: [...user.propertyAccess, args.propertyId],
        updatedAt: Date.now(),
      });
    }

    return { success: true };
  },
});

// Remove property access from user
export const removePropertyAccess = mutation({
  args: {
    sessionToken: v.string(),
    userId: v.id("users"),
    propertyId: v.id("properties"),
  },
  handler: async (ctx, args) => {
    // Simplified auth check - would validate session in production
    const currentUser = await ctx.db.query("users").first();
    if (!currentUser || !["owner", "manager"].includes(currentUser.role)) {
      throw new Error("Not authorized to remove property access");
    }

    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Remove property from user's access list
    await ctx.db.patch(args.userId, {
      propertyAccess: user.propertyAccess.filter(id => id !== args.propertyId),
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Update user role
export const updateUserRole = mutation({
  args: {
    sessionToken: v.string(),
    userId: v.id("users"),
    role: v.union(v.literal("owner"), v.literal("manager"), v.literal("vendor"), v.literal("tenant")),
  },
  handler: async (ctx, args) => {
    // Simplified auth check - would validate session in production
    const currentUser = await ctx.db.query("users").first();
    if (!currentUser || currentUser.role !== "owner") {
      throw new Error("Only owners can update user roles");
    }

    await ctx.db.patch(args.userId, {
      role: args.role,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Get users with property access
export const getUsersWithPropertyAccess = query({
  args: { 
    sessionToken: v.string(),
    propertyId: v.id("properties") 
  },
  handler: async (ctx, args) => {
    // Simplified auth check - would validate session in production
    const currentUser = await ctx.db.query("users").first();
    if (!currentUser) {
      throw new Error("Not authenticated");
    }

    const users = await ctx.db.query("users").collect();
    return users.filter(user => 
      user.propertyAccess.includes(args.propertyId) || 
      user.role === "owner"
    );
  },
});

// Helper function to check permissions (used by other modules)
export const checkPermission = async (
  ctx: any,
  userId: string,
  resource: string,
  action: "create" | "read" | "update" | "delete"
): Promise<boolean> => {
  const user = await ctx.db.get(userId);
  
  if (!user || !user.isActive) {
    return false;
  }

  const rolePermissions = ROLE_PERMISSIONS[user.role] || [];
  const resourcePermission = rolePermissions.find(p => p.resource === resource);
  
  return resourcePermission ? resourcePermission.actions.includes(action) : false;
};