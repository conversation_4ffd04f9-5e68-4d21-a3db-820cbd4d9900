import { describe, it, expect } from 'vitest';
import { 
  validatePropertyData, 
  validateUnitData, 
  validateLeaseData, 
  validatePaymentData,
  validateMaintenanceTicketData,
  sanitizeInput,
  validateFileUpload
} from '../validation';

describe('Validation utilities', () => {
  describe('validatePropertyData', () => {
    it('should validate correct property data', () => {
      const validData = {
        name: 'Sunset Apartments',
        type: 'residential',
        address: {
          street: '123 Main Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
      };

      const result = validatePropertyData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should reject property data with missing required fields', () => {
      const invalidData = {
        name: '',
        type: 'residential',
        address: {
          street: '',
          city: '',
          state: '',
          postalCode: '',
          country: '',
        },
      };

      const result = validatePropertyData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Property name is required');
      expect(result.errors).toContain('Street address is required');
      expect(result.errors).toContain('City is required');
    });

    it('should reject invalid property type', () => {
      const invalidData = {
        name: 'Test Property',
        type: 'invalid-type',
        address: {
          street: '123 Main Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: '00100',
          country: 'Kenya',
        },
      };

      const result = validatePropertyData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid property type');
    });

    it('should validate postal code format', () => {
      const invalidData = {
        name: 'Test Property',
        type: 'residential',
        address: {
          street: '123 Main Street',
          city: 'Nairobi',
          state: 'Nairobi County',
          postalCode: 'invalid',
          country: 'Kenya',
        },
      };

      const result = validatePropertyData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid postal code format');
    });
  });

  describe('validateUnitData', () => {
    it('should validate correct unit data', () => {
      const validData = {
        unitNumber: 'A101',
        type: 'apartment',
        size: 1200,
        rent: 50000,
        status: 'vacant',
        amenities: ['parking', 'balcony'],
      };

      const result = validateUnitData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should reject unit data with invalid values', () => {
      const invalidData = {
        unitNumber: '',
        type: 'invalid-type',
        size: -1,
        rent: -100,
        status: 'invalid-status',
        amenities: [],
      };

      const result = validateUnitData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Unit number is required');
      expect(result.errors).toContain('Invalid unit type');
      expect(result.errors).toContain('Size must be positive');
      expect(result.errors).toContain('Rent must be positive');
      expect(result.errors).toContain('Invalid unit status');
    });

    it('should validate unit number format', () => {
      const invalidData = {
        unitNumber: '123!@#',
        type: 'apartment',
        size: 1200,
        rent: 50000,
        status: 'vacant',
        amenities: [],
      };

      const result = validateUnitData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid unit number format');
    });
  });

  describe('validateLeaseData', () => {
    it('should validate correct lease data', () => {
      const validData = {
        tenantEmail: '<EMAIL>',
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: 50000,
        deposit: 100000,
      };

      const result = validateLeaseData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should reject lease data with invalid dates', () => {
      const invalidData = {
        tenantEmail: '<EMAIL>',
        startDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        endDate: Date.now(),
        monthlyRent: 50000,
        deposit: 100000,
      };

      const result = validateLeaseData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('End date must be after start date');
    });

    it('should reject lease data with invalid email', () => {
      const invalidData = {
        tenantEmail: 'invalid-email',
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: 50000,
        deposit: 100000,
      };

      const result = validateLeaseData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid email format');
    });

    it('should reject lease data with negative amounts', () => {
      const invalidData = {
        tenantEmail: '<EMAIL>',
        startDate: Date.now(),
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        monthlyRent: -50000,
        deposit: -100000,
      };

      const result = validateLeaseData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Monthly rent must be positive');
      expect(result.errors).toContain('Deposit must be positive');
    });
  });

  describe('validatePaymentData', () => {
    it('should validate correct M-PESA payment data', () => {
      const validData = {
        method: 'mpesa',
        amount: 50000,
        phoneNumber: '+254712345678',
        reference: 'RENT-001',
      };

      const result = validatePaymentData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should validate correct Stripe payment data', () => {
      const validData = {
        method: 'stripe',
        amount: 50000,
        cardToken: 'tok_visa',
        reference: 'RENT-001',
      };

      const result = validatePaymentData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should reject payment data with invalid amount', () => {
      const invalidData = {
        method: 'mpesa',
        amount: -50000,
        phoneNumber: '+254712345678',
        reference: 'RENT-001',
      };

      const result = validatePaymentData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Amount must be positive');
    });

    it('should reject M-PESA payment without phone number', () => {
      const invalidData = {
        method: 'mpesa',
        amount: 50000,
        reference: 'RENT-001',
      };

      const result = validatePaymentData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Phone number is required for M-PESA payments');
    });

    it('should reject Stripe payment without card token', () => {
      const invalidData = {
        method: 'stripe',
        amount: 50000,
        reference: 'RENT-001',
      };

      const result = validatePaymentData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Card token is required for Stripe payments');
    });
  });

  describe('validateMaintenanceTicketData', () => {
    it('should validate correct maintenance ticket data', () => {
      const validData = {
        title: 'Leaking faucet',
        description: 'The kitchen faucet is leaking water',
        priority: 'medium',
        category: 'plumbing',
      };

      const result = validateMaintenanceTicketData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should reject ticket data with missing required fields', () => {
      const invalidData = {
        title: '',
        description: '',
        priority: 'invalid',
        category: 'invalid',
      };

      const result = validateMaintenanceTicketData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Title is required');
      expect(result.errors).toContain('Description is required');
      expect(result.errors).toContain('Invalid priority level');
      expect(result.errors).toContain('Invalid category');
    });

    it('should validate description length', () => {
      const invalidData = {
        title: 'Test ticket',
        description: 'Short',
        priority: 'medium',
        category: 'plumbing',
      };

      const result = validateMaintenanceTicketData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Description must be at least 10 characters');
    });
  });

  describe('sanitizeInput', () => {
    it('should remove HTML tags', () => {
      const input = '<script>alert("xss")</script>Hello World';
      const result = sanitizeInput(input);
      expect(result).toBe('Hello World');
    });

    it('should trim whitespace', () => {
      const input = '  Hello World  ';
      const result = sanitizeInput(input);
      expect(result).toBe('Hello World');
    });

    it('should handle null and undefined', () => {
      expect(sanitizeInput(null)).toBe('');
      expect(sanitizeInput(undefined)).toBe('');
    });

    it('should preserve safe characters', () => {
      const input = 'Hello World! 123 @example.com';
      const result = sanitizeInput(input);
      expect(result).toBe('Hello World! 123 @example.com');
    });
  });

  describe('validateFileUpload', () => {
    it('should validate correct file upload', () => {
      const file = new File(['content'], 'document.pdf', { type: 'application/pdf' });
      const result = validateFileUpload(file, {
        maxSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['application/pdf', 'image/jpeg', 'image/png'],
      });

      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should reject file that is too large', () => {
      const file = new File(['x'.repeat(10 * 1024 * 1024)], 'large.pdf', { 
        type: 'application/pdf' 
      });
      const result = validateFileUpload(file, {
        maxSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['application/pdf'],
      });

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File size exceeds maximum allowed size');
    });

    it('should reject file with invalid type', () => {
      const file = new File(['content'], 'script.js', { type: 'application/javascript' });
      const result = validateFileUpload(file, {
        maxSize: 5 * 1024 * 1024,
        allowedTypes: ['application/pdf', 'image/jpeg'],
      });

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File type not allowed');
    });

    it('should reject file with suspicious extension', () => {
      const file = new File(['content'], 'document.exe', { type: 'application/pdf' });
      const result = validateFileUpload(file, {
        maxSize: 5 * 1024 * 1024,
        allowedTypes: ['application/pdf'],
      });

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File extension not allowed');
    });
  });
});