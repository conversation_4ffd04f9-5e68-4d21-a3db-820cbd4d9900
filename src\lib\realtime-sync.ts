/**
 * Real-time Data Synchronization System
 * Handles real-time updates, conflict resolution, and optimistic updates
 */

import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useCallback, useEffect, useRef, useState } from "react";

// Types for real-time synchronization
export interface SyncState {
  isOnline: boolean;
  lastSyncTime: number;
  pendingOperations: PendingOperation[];
  conflictResolution: ConflictResolutionStrategy;
}

export interface PendingOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  entity: string;
  entityId?: string;
  data: any;
  timestamp: number;
  retryCount: number;
  optimisticId?: string;
}

export type ConflictResolutionStrategy = 'last-write-wins' | 'user-intervention' | 'merge';

export interface ConflictData {
  entityType: string;
  entityId: string;
  localVersion: any;
  serverVersion: any;
  timestamp: number;
}

// Real-time synchronization hook
export function useRealtimeSync() {
  const [syncState, setSyncState] = useState<SyncState>({
    isOnline: navigator.onLine,
    lastSyncTime: Date.now(),
    pendingOperations: [],
    conflictResolution: 'last-write-wins'
  });

  const [conflicts, setConflicts] = useState<ConflictData[]>([]);
  const syncIntervalRef = useRef<NodeJS.Timeout>();

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setSyncState(prev => ({ ...prev, isOnline: true }));
      processPendingOperations();
    };

    const handleOffline = () => {
      setSyncState(prev => ({ ...prev, isOnline: false }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Process pending operations when coming back online
  const processPendingOperations = useCallback(async () => {
    if (!syncState.isOnline || syncState.pendingOperations.length === 0) {
      return;
    }

    const operations = [...syncState.pendingOperations];
    
    for (const operation of operations) {
      try {
        await executeOperation(operation);
        
        // Remove successful operation from pending list
        setSyncState(prev => ({
          ...prev,
          pendingOperations: prev.pendingOperations.filter(op => op.id !== operation.id),
          lastSyncTime: Date.now()
        }));
      } catch (error) {
        console.error('Failed to sync operation:', operation, error);
        
        // Increment retry count
        setSyncState(prev => ({
          ...prev,
          pendingOperations: prev.pendingOperations.map(op =>
            op.id === operation.id
              ? { ...op, retryCount: op.retryCount + 1 }
              : op
          )
        }));

        // Remove operations that have exceeded retry limit
        if (operation.retryCount >= 3) {
          setSyncState(prev => ({
            ...prev,
            pendingOperations: prev.pendingOperations.filter(op => op.id !== operation.id)
          }));
        }
      }
    }
  }, [syncState.isOnline, syncState.pendingOperations]);

  // Execute a single operation
  const executeOperation = async (operation: PendingOperation) => {
    // This would be implemented with actual Convex mutations
    // For now, we'll simulate the operation
    console.log('Executing operation:', operation);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Simulate potential conflicts
    if (Math.random() < 0.1) { // 10% chance of conflict
      throw new Error('Conflict detected');
    }
  };

  // Add operation to pending queue
  const queueOperation = useCallback((operation: Omit<PendingOperation, 'id' | 'timestamp' | 'retryCount'>) => {
    const newOperation: PendingOperation = {
      ...operation,
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      retryCount: 0
    };

    setSyncState(prev => ({
      ...prev,
      pendingOperations: [...prev.pendingOperations, newOperation]
    }));

    // Try to execute immediately if online
    if (syncState.isOnline) {
      executeOperation(newOperation).catch(() => {
        // Operation will remain in pending queue for retry
      });
    }
  }, [syncState.isOnline]);

  // Resolve conflicts
  const resolveConflict = useCallback((conflictId: string, resolution: 'local' | 'server' | 'merge', mergedData?: any) => {
    setConflicts(prev => prev.filter(conflict => 
      `${conflict.entityType}-${conflict.entityId}` !== conflictId
    ));

    // Apply resolution based on strategy
    switch (resolution) {
      case 'local':
        // Keep local version, queue update operation
        break;
      case 'server':
        // Accept server version, update local state
        break;
      case 'merge':
        // Use merged data, queue update operation
        break;
    }
  }, []);

  return {
    syncState,
    conflicts,
    queueOperation,
    resolveConflict,
    processPendingOperations
  };
}

// Real-time subscription manager
export class RealtimeSubscriptionManager {
  private subscriptions = new Map<string, () => void>();
  private listeners = new Map<string, Set<(data: any) => void>>();

  subscribe<T>(
    queryName: string,
    queryArgs: any,
    callback: (data: T) => void
  ): () => void {
    const subscriptionKey = `${queryName}-${JSON.stringify(queryArgs)}`;
    
    // Add listener
    if (!this.listeners.has(subscriptionKey)) {
      this.listeners.set(subscriptionKey, new Set());
    }
    this.listeners.get(subscriptionKey)!.add(callback);

    // Create subscription if it doesn't exist
    if (!this.subscriptions.has(subscriptionKey)) {
      // This would use actual Convex useQuery hook
      const unsubscribe = () => {
        console.log('Unsubscribing from:', subscriptionKey);
      };
      this.subscriptions.set(subscriptionKey, unsubscribe);
    }

    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(subscriptionKey);
      if (listeners) {
        listeners.delete(callback);
        if (listeners.size === 0) {
          this.listeners.delete(subscriptionKey);
          const unsubscribe = this.subscriptions.get(subscriptionKey);
          if (unsubscribe) {
            unsubscribe();
            this.subscriptions.delete(subscriptionKey);
          }
        }
      }
    };
  }

  notifyListeners(subscriptionKey: string, data: any) {
    const listeners = this.listeners.get(subscriptionKey);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  cleanup() {
    this.subscriptions.forEach(unsubscribe => unsubscribe());
    this.subscriptions.clear();
    this.listeners.clear();
  }
}

// Global subscription manager instance
export const subscriptionManager = new RealtimeSubscriptionManager();

// Optimistic updates manager
export class OptimisticUpdatesManager {
  private optimisticUpdates = new Map<string, any>();
  private rollbackCallbacks = new Map<string, () => void>();

  applyOptimisticUpdate<T>(
    entityType: string,
    entityId: string,
    update: Partial<T>,
    rollbackCallback: () => void
  ): string {
    const updateId = `${entityType}-${entityId}-${Date.now()}`;
    
    this.optimisticUpdates.set(updateId, {
      entityType,
      entityId,
      update,
      timestamp: Date.now()
    });
    
    this.rollbackCallbacks.set(updateId, rollbackCallback);
    
    return updateId;
  }

  confirmUpdate(updateId: string) {
    this.optimisticUpdates.delete(updateId);
    this.rollbackCallbacks.delete(updateId);
  }

  rollbackUpdate(updateId: string) {
    const rollbackCallback = this.rollbackCallbacks.get(updateId);
    if (rollbackCallback) {
      rollbackCallback();
      this.optimisticUpdates.delete(updateId);
      this.rollbackCallbacks.delete(updateId);
    }
  }

  rollbackAll() {
    this.rollbackCallbacks.forEach(callback => callback());
    this.optimisticUpdates.clear();
    this.rollbackCallbacks.clear();
  }

  getOptimisticUpdates() {
    return Array.from(this.optimisticUpdates.values());
  }
}

// Global optimistic updates manager
export const optimisticUpdatesManager = new OptimisticUpdatesManager();