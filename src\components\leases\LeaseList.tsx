import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { 
  Search, 
  Filter, 
  FileText, 
  Calendar, 
  DollarSign, 
  User, 
  Building,
  Eye,
  Edit,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';

interface LeaseListProps {
  propertyId?: Id<"properties">;
  tenantId?: Id<"users">;
  onViewLease?: (leaseId: Id<"leases">) => void;
  onEditLease?: (leaseId: Id<"leases">) => void;
  onCreateLease?: () => void;
}

interface LeaseFilters {
  status: string;
  search: string;
  propertyId: string;
}

export function LeaseList({ 
  propertyId, 
  tenantId, 
  onViewLease, 
  onEditLease, 
  onCreateLease 
}: LeaseListProps) {
  const [filters, setFilters] = useState<LeaseFilters>({
    status: 'all',
    search: '',
    propertyId: propertyId || 'all',
  });

  const leases = useQuery(api.leases.getLeases, {
    propertyId: filters.propertyId !== 'all' ? filters.propertyId as Id<"properties"> : undefined,
    tenantId,
    status: filters.status !== 'all' ? filters.status as any : undefined,
  });

  const properties = useQuery(api.properties.getProperties, {});

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
        return 'bg-gray-100 text-gray-800';
      case 'terminated':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getESignatureStatusColor = (status: string) => {
    switch (status) {
      case 'signed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getDaysUntilExpiry = (endDate: number) => {
    const days = Math.ceil((endDate - Date.now()) / (1000 * 60 * 60 * 24));
    return days;
  };

  // Filter leases based on search term
  const filteredLeases = leases?.filter(lease => {
    if (!filters.search) return true;
    
    const searchTerm = filters.search.toLowerCase();
    // This would need to be enhanced with property and tenant data
    return lease._id.toLowerCase().includes(searchTerm);
  }) || [];

  // Get lease details with related data
  const getLeasesWithDetails = async () => {
    if (!leases) return [];
    
    return Promise.all(
      leases.map(async (lease) => {
        // In a real implementation, you'd fetch related data here
        // For now, we'll use the lease data directly
        return {
          lease,
          property: null, // Would be fetched
          unit: null,     // Would be fetched
          tenant: null,   // Would be fetched
        };
      })
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Leases</h2>
          <p className="text-gray-600">Manage lease agreements and renewals</p>
        </div>
        {onCreateLease && (
          <Button onClick={onCreateLease} className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Create Lease
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search leases..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Status Filter */}
            <Select
              value={filters.status}
              onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
                <SelectItem value="terminated">Terminated</SelectItem>
              </SelectContent>
            </Select>

            {/* Property Filter */}
            {!propertyId && (
              <Select
                value={filters.propertyId}
                onValueChange={(value) => setFilters(prev => ({ ...prev, propertyId: value }))}
              >
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Filter by property" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Properties</SelectItem>
                  {properties?.map((property) => (
                    <SelectItem key={property._id} value={property._id}>
                      {property.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Lease List */}
      <div className="grid gap-4">
        {filteredLeases.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No leases found</h3>
                <p className="text-gray-600 mb-4">
                  {filters.search || filters.status !== 'all' || filters.propertyId !== 'all'
                    ? 'No leases match your current filters.'
                    : 'Get started by creating your first lease.'}
                </p>
                {onCreateLease && (
                  <Button onClick={onCreateLease}>Create First Lease</Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          filteredLeases.map((lease) => {
            const daysUntilExpiry = getDaysUntilExpiry(lease.endDate);
            const isExpiringSoon = daysUntilExpiry <= 30 && daysUntilExpiry > 0;
            
            return (
              <Card key={lease._id} className={isExpiringSoon ? 'border-yellow-200 bg-yellow-50' : ''}>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div className="flex-1 space-y-3">
                      {/* Header Row */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <h3 className="font-semibold text-lg">
                            Lease #{lease._id.slice(-8)}
                          </h3>
                          <Badge className={getStatusColor(lease.status)}>
                            {lease.status}
                          </Badge>
                          <Badge className={getESignatureStatusColor(lease.eSignatureStatus)}>
                            {lease.eSignatureStatus === 'signed' ? 'Signed' : 
                             lease.eSignatureStatus === 'pending' ? 'Pending Signature' : 'Signature Expired'}
                          </Badge>
                        </div>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {onViewLease && (
                              <DropdownMenuItem onClick={() => onViewLease(lease._id)}>
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                            )}
                            {onEditLease && (
                              <DropdownMenuItem onClick={() => onEditLease(lease._id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Lease
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      {/* Details Grid */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-600">Property</p>
                            <p className="font-medium">Property #{lease.propertyId.slice(-8)}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-600">Tenant</p>
                            <p className="font-medium">Tenant #{lease.tenantId.slice(-8)}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-600">Monthly Rent</p>
                            <p className="font-medium">{formatCurrency(lease.monthlyRent)}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <div>
                            <p className="text-sm text-gray-600">Lease Period</p>
                            <p className="font-medium">
                              {formatDate(lease.startDate)} - {formatDate(lease.endDate)}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Expiry Warning */}
                      {isExpiringSoon && (
                        <div className="flex items-center gap-2 p-3 bg-yellow-100 border border-yellow-200 rounded-md">
                          <Calendar className="h-4 w-4 text-yellow-600" />
                          <p className="text-sm text-yellow-800">
                            <strong>Expires in {daysUntilExpiry} days</strong> - Consider renewal
                          </p>
                        </div>
                      )}

                      {/* Lease Terms Summary */}
                      <div className="text-sm text-gray-600 space-y-1">
                        <p>Notice Period: {lease.terms.noticePeriod} days</p>
                        <p>Late Fee: {lease.terms.lateFeePercentage}% after {lease.terms.gracePeriod} day grace period</p>
                        <p>Renewal Option: {lease.terms.renewalOption ? 'Available' : 'Not available'}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
}