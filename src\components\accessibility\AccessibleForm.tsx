/**
 * Accessible form components with proper validation, error handling, and ARIA attributes
 */
import React, { forwardRef, useId } from 'react';
import { Input, InputProps } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { useAccessibility } from '../../contexts/AccessibilityContext';
import { ScreenReaderUtils } from '../../lib/accessibility';

interface AccessibleFieldProps {
  label: string;
  description?: string;
  error?: string;
  required?: boolean;
  helpText?: string;
}

interface AccessibleInputProps extends InputProps, AccessibleFieldProps {
  inputMode?: 'text' | 'email' | 'tel' | 'url' | 'numeric' | 'decimal' | 'search';
}

export const AccessibleInput = forwardRef<HTMLInputElement, AccessibleInputProps>(
  ({
    label,
    description,
    error,
    required = false,
    helpText,
    inputMode,
    className,
    ...props
  }, ref) => {
    const { announceToScreenReader } = useAccessibility();
    const fieldId = useId();
    const descriptionId = useId();
    const errorId = useId();
    const helpId = useId();

    // Announce errors to screen readers
    React.useEffect(() => {
      if (error) {
        announceToScreenReader(`Error in ${label}: ${error}`, 'assertive');
      }
    }, [error, label, announceToScreenReader]);

    const ariaDescribedBy = [
      description ? descriptionId : null,
      helpText ? helpId : null,
      error ? errorId : null,
    ].filter(Boolean).join(' ') || undefined;

    return (
      <div className="space-y-2">
        <Label 
          htmlFor={fieldId}
          className={`block text-sm font-medium ${required ? 'after:content-["*"] after:ml-1 after:text-destructive' : ''}`}
        >
          {label}
        </Label>
        
        {description && (
          <p id={descriptionId} className="text-sm text-muted-foreground">
            {description}
          </p>
        )}
        
        <Input
          ref={ref}
          id={fieldId}
          inputMode={inputMode}
          aria-describedby={ariaDescribedBy}
          aria-invalid={error ? 'true' : 'false'}
          aria-required={required}
          className={`
            ${className || ''}
            ${error ? 'border-destructive focus-visible:ring-destructive' : ''}
            focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2
            focus-visible:outline-none
          `}
          {...props}
        />
        
        {helpText && (
          <p id={helpId} className="text-sm text-muted-foreground">
            {helpText}
          </p>
        )}
        
        {error && (
          <p id={errorId} className="text-sm text-destructive" role="alert" aria-live="polite">
            <span className="font-medium">Error:</span> {error}
          </p>
        )}
      </div>
    );
  }
);

AccessibleInput.displayName = 'AccessibleInput';

interface AccessibleTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement>, AccessibleFieldProps {}

export const AccessibleTextarea = forwardRef<HTMLTextAreaElement, AccessibleTextareaProps>(
  ({
    label,
    description,
    error,
    required = false,
    helpText,
    className,
    ...props
  }, ref) => {
    const { announceToScreenReader } = useAccessibility();
    const fieldId = useId();
    const descriptionId = useId();
    const errorId = useId();
    const helpId = useId();

    // Announce errors to screen readers
    React.useEffect(() => {
      if (error) {
        announceToScreenReader(`Error in ${label}: ${error}`, 'assertive');
      }
    }, [error, label, announceToScreenReader]);

    const ariaDescribedBy = [
      description ? descriptionId : null,
      helpText ? helpId : null,
      error ? errorId : null,
    ].filter(Boolean).join(' ') || undefined;

    return (
      <div className="space-y-2">
        <Label 
          htmlFor={fieldId}
          className={`block text-sm font-medium ${required ? 'after:content-["*"] after:ml-1 after:text-destructive' : ''}`}
        >
          {label}
        </Label>
        
        {description && (
          <p id={descriptionId} className="text-sm text-muted-foreground">
            {description}
          </p>
        )}
        
        <Textarea
          ref={ref}
          id={fieldId}
          aria-describedby={ariaDescribedBy}
          aria-invalid={error ? 'true' : 'false'}
          aria-required={required}
          className={`
            ${className || ''}
            ${error ? 'border-destructive focus-visible:ring-destructive' : ''}
            focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2
            focus-visible:outline-none
          `}
          {...props}
        />
        
        {helpText && (
          <p id={helpId} className="text-sm text-muted-foreground">
            {helpText}
          </p>
        )}
        
        {error && (
          <p id={errorId} className="text-sm text-destructive" role="alert" aria-live="polite">
            <span className="font-medium">Error:</span> {error}
          </p>
        )}
      </div>
    );
  }
);

AccessibleTextarea.displayName = 'AccessibleTextarea';

interface AccessibleSelectProps extends AccessibleFieldProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  children: React.ReactNode;
  disabled?: boolean;
}

export const AccessibleSelect = forwardRef<HTMLButtonElement, AccessibleSelectProps>(
  ({
    label,
    description,
    error,
    required = false,
    helpText,
    value,
    onValueChange,
    placeholder,
    children,
    disabled = false,
  }, ref) => {
    const { announceToScreenReader } = useAccessibility();
    const fieldId = useId();
    const descriptionId = useId();
    const errorId = useId();
    const helpId = useId();

    // Announce errors to screen readers
    React.useEffect(() => {
      if (error) {
        announceToScreenReader(`Error in ${label}: ${error}`, 'assertive');
      }
    }, [error, label, announceToScreenReader]);

    const ariaDescribedBy = [
      description ? descriptionId : null,
      helpText ? helpId : null,
      error ? errorId : null,
    ].filter(Boolean).join(' ') || undefined;

    return (
      <div className="space-y-2">
        <Label 
          htmlFor={fieldId}
          className={`block text-sm font-medium ${required ? 'after:content-["*"] after:ml-1 after:text-destructive' : ''}`}
        >
          {label}
        </Label>
        
        {description && (
          <p id={descriptionId} className="text-sm text-muted-foreground">
            {description}
          </p>
        )}
        
        <Select
          value={value}
          onValueChange={onValueChange}
          disabled={disabled}
        >
          <SelectTrigger
            ref={ref}
            id={fieldId}
            aria-describedby={ariaDescribedBy}
            aria-invalid={error ? 'true' : 'false'}
            aria-required={required}
            className={`
              ${error ? 'border-destructive focus:ring-destructive' : ''}
              focus:ring-2 focus:ring-ring focus:ring-offset-2
              focus:outline-none
            `}
          >
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {children}
          </SelectContent>
        </Select>
        
        {helpText && (
          <p id={helpId} className="text-sm text-muted-foreground">
            {helpText}
          </p>
        )}
        
        {error && (
          <p id={errorId} className="text-sm text-destructive" role="alert" aria-live="polite">
            <span className="font-medium">Error:</span> {error}
          </p>
        )}
      </div>
    );
  }
);

AccessibleSelect.displayName = 'AccessibleSelect';

interface AccessibleFormProps {
  children: React.ReactNode;
  onSubmit?: (event: React.FormEvent) => void;
  title?: string;
  description?: string;
  className?: string;
}

export function AccessibleForm({ 
  children, 
  onSubmit, 
  title, 
  description, 
  className 
}: AccessibleFormProps) {
  const { announceToScreenReader } = useAccessibility();
  const formId = useId();
  const titleId = useId();
  const descriptionId = useId();

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    
    // Check for form validation errors
    const form = event.currentTarget as HTMLFormElement;
    const invalidFields = form.querySelectorAll('[aria-invalid="true"]');
    
    if (invalidFields.length > 0) {
      announceToScreenReader(
        `Form has ${invalidFields.length} error${invalidFields.length > 1 ? 's' : ''}. Please correct them before submitting.`,
        'assertive'
      );
      
      // Focus first invalid field
      const firstInvalid = invalidFields[0] as HTMLElement;
      firstInvalid.focus();
      return;
    }
    
    announceToScreenReader('Form submitted successfully');
    onSubmit?.(event);
  };

  return (
    <form
      id={formId}
      onSubmit={handleSubmit}
      aria-labelledby={title ? titleId : undefined}
      aria-describedby={description ? descriptionId : undefined}
      className={className}
      noValidate
    >
      {title && (
        <h2 id={titleId} className="text-lg font-semibold mb-4">
          {title}
        </h2>
      )}
      
      {description && (
        <p id={descriptionId} className="text-muted-foreground mb-6">
          {description}
        </p>
      )}
      
      {children}
    </form>
  );
}