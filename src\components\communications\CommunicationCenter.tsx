import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { useToast } from '../ui/use-toast';
import { MessageSquare, Phone, Send, Users, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { DeliveryStatusTracker } from './DeliveryStatusTracker';

interface CommunicationCenterProps {
  propertyId?: Id<"properties">;
}

export function CommunicationCenter({ propertyId }: CommunicationCenterProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('send');
  
  // Form states
  const [messageType, setMessageType] = useState<'sms' | 'whatsapp'>('sms');
  const [recipient, setRecipient] = useState('');
  const [message, setMessage] = useState('');
  const [bulkRecipients, setBulkRecipients] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Mutations
  const sendSMS = useMutation(api.communications.sendSMS);
  const sendWhatsApp = useMutation(api.communications.sendWhatsApp);
  const sendBulkMessages = useMutation(api.communications.sendBulkMessages);

  // Queries
  const messages = useQuery(api.communications.getMessages, {
    propertyId,
    limit: 50,
  });

  const handleSendSingle = async () => {
    if (!recipient || !message) {
      toast({
        title: "Missing Information",
        description: "Please provide both recipient and message",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      if (messageType === 'sms') {
        await sendSMS({
          phoneNumber: recipient,
          message,
          propertyId,
        });
      } else {
        await sendWhatsApp({
          phoneNumber: recipient,
          message,
          propertyId,
        });
      }

      toast({
        title: "Message Sent",
        description: `${messageType.toUpperCase()} message sent successfully`,
      });

      // Clear form
      setRecipient('');
      setMessage('');
    } catch (error) {
      toast({
        title: "Send Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendBulk = async () => {
    if (!bulkRecipients || !message) {
      toast({
        title: "Missing Information",
        description: "Please provide both recipients and message",
        variant: "destructive",
      });
      return;
    }

    // Parse recipients (one per line)
    const recipients = bulkRecipients
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .map(phoneNumber => ({ phoneNumber }));

    if (recipients.length === 0) {
      toast({
        title: "No Recipients",
        description: "Please provide at least one phone number",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const result = await sendBulkMessages({
        recipients,
        message,
        type: messageType,
        propertyId,
      });

      toast({
        title: "Bulk Messages Sent",
        description: `${result.totalSent} messages sent successfully, ${result.totalFailed} failed`,
      });

      // Clear form
      setBulkRecipients('');
      setMessage('');
    } catch (error) {
      toast({
        title: "Bulk Send Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <MessageSquare className="h-6 w-6" />
        <h2 className="text-2xl font-bold">Communication Center</h2>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="send">Send Message</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Messages</TabsTrigger>
          <TabsTrigger value="history">Message History</TabsTrigger>
          <TabsTrigger value="delivery">Delivery Status</TabsTrigger>
        </TabsList>

        <TabsContent value="send" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Send className="h-5 w-5" />
                <span>Send Single Message</span>
              </CardTitle>
              <CardDescription>
                Send SMS or WhatsApp message to a single recipient
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Message Type</label>
                  <Select value={messageType} onValueChange={(value: 'sms' | 'whatsapp') => setMessageType(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sms">
                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4" />
                          <span>SMS</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="whatsapp">
                        <div className="flex items-center space-x-2">
                          <MessageSquare className="h-4 w-4" />
                          <span>WhatsApp</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Phone Number</label>
                  <Input
                    placeholder="+254712345678"
                    value={recipient}
                    onChange={(e) => setRecipient(e.target.value)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Message</label>
                <Textarea
                  placeholder="Enter your message here..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={4}
                />
                <div className="text-sm text-gray-500">
                  {message.length}/160 characters
                </div>
              </div>
              <Button 
                onClick={handleSendSingle} 
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? 'Sending...' : `Send ${messageType.toUpperCase()}`}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Bulk Messages</span>
              </CardTitle>
              <CardDescription>
                Send the same message to multiple recipients
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Message Type</label>
                <Select value={messageType} onValueChange={(value: 'sms' | 'whatsapp') => setMessageType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sms">
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4" />
                        <span>SMS</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="whatsapp">
                      <div className="flex items-center space-x-2">
                        <MessageSquare className="h-4 w-4" />
                        <span>WhatsApp</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Phone Numbers</label>
                <Textarea
                  placeholder="Enter phone numbers, one per line:&#10;+254712345678&#10;+254723456789&#10;+254734567890"
                  value={bulkRecipients}
                  onChange={(e) => setBulkRecipients(e.target.value)}
                  rows={6}
                />
                <div className="text-sm text-gray-500">
                  {bulkRecipients.split('\n').filter(line => line.trim().length > 0).length} recipients
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Message</label>
                <Textarea
                  placeholder="Enter your message here..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={4}
                />
                <div className="text-sm text-gray-500">
                  {message.length}/160 characters
                </div>
              </div>
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Bulk messages will be sent individually. Large batches may take some time to process.
                </AlertDescription>
              </Alert>
              <Button 
                onClick={handleSendBulk} 
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? 'Sending...' : `Send Bulk ${messageType.toUpperCase()}`}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Message History</CardTitle>
              <CardDescription>
                Recent messages sent from this property
              </CardDescription>
            </CardHeader>
            <CardContent>
              {messages === undefined ? (
                <div className="text-center py-4">Loading messages...</div>
              ) : messages.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No messages sent yet
                </div>
              ) : (
                <div className="space-y-3">
                  {messages.map((msg) => (
                    <div key={msg._id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge variant="outline" className="uppercase">
                              {msg.type}
                            </Badge>
                            <Badge className={getStatusColor(msg.status)}>
                              <div className="flex items-center space-x-1">
                                {getStatusIcon(msg.status)}
                                <span>{msg.status}</span>
                              </div>
                            </Badge>
                          </div>
                          <div className="text-sm text-gray-600 mb-1">
                            To: {msg.recipient}
                          </div>
                          <div className="text-sm">
                            {msg.content}
                          </div>
                          {msg.error && (
                            <div className="text-sm text-red-600 mt-2">
                              Error: {msg.error}
                            </div>
                          )}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(msg.createdAt).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="delivery" className="space-y-4">
          <DeliveryStatusTracker propertyId={propertyId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}