// Data encryption utilities for sensitive information
export class DataEncryption {
  private static readonly ALGORITHM = 'AES-GCM';
  private static readonly KEY_LENGTH = 256;
  private static readonly IV_LENGTH = 12;

  /**
   * Generate a new encryption key
   */
  static async generateKey(): Promise<CryptoKey> {
    return await crypto.subtle.generateKey(
      {
        name: this.ALGORITHM,
        length: this.KEY_LENGTH
      },
      true, // extractable
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Export key to raw format for storage
   */
  static async exportKey(key: CryptoKey): Promise<ArrayBuffer> {
    return await crypto.subtle.exportKey('raw', key);
  }

  /**
   * Import key from raw format
   */
  static async importKey(keyData: ArrayBuffer): Promise<CryptoKey> {
    return await crypto.subtle.importKey(
      'raw',
      keyData,
      {
        name: this.ALGORITHM,
        length: this.KEY_LENGTH
      },
      true,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Encrypt sensitive data
   */
  static async encrypt(data: string, key: CryptoKey): Promise<EncryptedData> {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    
    // Generate random IV
    const iv = crypto.getRandomValues(new Uint8Array(this.IV_LENGTH));
    
    // Encrypt data
    const encryptedBuffer = await crypto.subtle.encrypt(
      {
        name: this.ALGORITHM,
        iv: iv
      },
      key,
      dataBuffer
    );

    return {
      data: Array.from(new Uint8Array(encryptedBuffer)),
      iv: Array.from(iv),
      algorithm: this.ALGORITHM
    };
  }

  /**
   * Decrypt sensitive data
   */
  static async decrypt(encryptedData: EncryptedData, key: CryptoKey): Promise<string> {
    const dataBuffer = new Uint8Array(encryptedData.data);
    const iv = new Uint8Array(encryptedData.iv);

    const decryptedBuffer = await crypto.subtle.decrypt(
      {
        name: this.ALGORITHM,
        iv: iv
      },
      key,
      dataBuffer
    );

    const decoder = new TextDecoder();
    return decoder.decode(decryptedBuffer);
  }

  /**
   * Encrypt PII data with field-level encryption
   */
  static async encryptPII(piiData: PIIData, key: CryptoKey): Promise<EncryptedPIIData> {
    const encrypted: EncryptedPIIData = {
      id: piiData.id,
      encryptedFields: {}
    };

    // Encrypt sensitive fields
    for (const [field, value] of Object.entries(piiData)) {
      if (field !== 'id' && this.isSensitiveField(field)) {
        encrypted.encryptedFields[field] = await this.encrypt(String(value), key);
      }
    }

    return encrypted;
  }

  /**
   * Decrypt PII data
   */
  static async decryptPII(encryptedData: EncryptedPIIData, key: CryptoKey): Promise<PIIData> {
    const decrypted: PIIData = {
      id: encryptedData.id
    };

    // Decrypt sensitive fields
    for (const [field, encryptedValue] of Object.entries(encryptedData.encryptedFields)) {
      decrypted[field] = await this.decrypt(encryptedValue, key);
    }

    return decrypted;
  }

  /**
   * Hash sensitive data for indexing (one-way)
   */
  static async hashForIndex(data: string, salt?: string): Promise<string> {
    const encoder = new TextEncoder();
    const saltedData = salt ? data + salt : data;
    const dataBuffer = encoder.encode(saltedData);
    
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Generate salt for hashing
   */
  static generateSalt(): string {
    const saltArray = crypto.getRandomValues(new Uint8Array(32));
    return Array.from(saltArray, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Check if field contains sensitive data
   */
  private static isSensitiveField(field: string): boolean {
    const sensitiveFields = [
      'email', 'phone', 'phoneNumber', 'nationalId', 'idNumber',
      'passportNumber', 'bankAccount', 'creditCard', 'ssn',
      'taxId', 'address', 'fullAddress', 'personalAddress',
      'emergencyContact', 'nextOfKin', 'medicalInfo',
      'financialInfo', 'salary', 'income'
    ];

    return sensitiveFields.some(sensitive => 
      field.toLowerCase().includes(sensitive.toLowerCase())
    );
  }
}

// Secure storage for encryption keys
export class KeyManager {
  private static readonly KEY_STORAGE_PREFIX = 'enc_key_';
  private static keys = new Map<string, CryptoKey>();

  /**
   * Generate and store a new encryption key
   */
  static async generateAndStoreKey(keyId: string): Promise<CryptoKey> {
    const key = await DataEncryption.generateKey();
    await this.storeKey(keyId, key);
    return key;
  }

  /**
   * Store encryption key securely
   */
  static async storeKey(keyId: string, key: CryptoKey): Promise<void> {
    // Store in memory
    this.keys.set(keyId, key);

    // TODO: Replace with secure key management service in production
    try {
      const keyData = await DataEncryption.exportKey(key);
      const keyArray = Array.from(new Uint8Array(keyData));
      
      // TODO: Use proper key management service
      const obfuscated = btoa(JSON.stringify(keyArray));
      localStorage.setItem(this.KEY_STORAGE_PREFIX + keyId, obfuscated);
    } catch (error) {
      console.error('Failed to store encryption key:', error);
    }
  }

  /**
   * Retrieve encryption key
   */
  static async getKey(keyId: string): Promise<CryptoKey | null> {
    // Check memory cache first
    if (this.keys.has(keyId)) {
      return this.keys.get(keyId)!;
    }

    // Try to load from storage
    try {
      const stored = localStorage.getItem(this.KEY_STORAGE_PREFIX + keyId);
      if (!stored) return null;

      const keyArray = JSON.parse(atob(stored));
      const keyData = new Uint8Array(keyArray);
      const key = await DataEncryption.importKey(keyData.buffer);
      
      // Cache in memory
      this.keys.set(keyId, key);
      
      return key;
    } catch (error) {
      console.error('Failed to retrieve encryption key:', error);
      return null;
    }
  }

  /**
   * Delete encryption key
   */
  static deleteKey(keyId: string): void {
    this.keys.delete(keyId);
    localStorage.removeItem(this.KEY_STORAGE_PREFIX + keyId);
  }

  /**
   * List available keys
   */
  static getAvailableKeys(): string[] {
    const keys: string[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.KEY_STORAGE_PREFIX)) {
        keys.push(key.substring(this.KEY_STORAGE_PREFIX.length));
      }
    }
    
    return keys;
  }

  /**
   * Rotate encryption key
   */
  static async rotateKey(keyId: string): Promise<CryptoKey> {
    const newKey = await DataEncryption.generateKey();
    await this.storeKey(keyId, newKey);
    return newKey;
  }
}

// Data masking utilities
export class DataMasking {
  /**
   * Mask email address
   */
  static maskEmail(email: string): string {
    const [local, domain] = email.split('@');
    if (!domain) return '***';
    
    const maskedLocal = local.length > 2 
      ? local[0] + '*'.repeat(local.length - 2) + local[local.length - 1]
      : '*'.repeat(local.length);
    
    return `${maskedLocal}@${domain}`;
  }

  /**
   * Mask phone number
   */
  static maskPhone(phone: string): string {
    const digits = phone.replace(/\D/g, '');
    if (digits.length < 4) return '*'.repeat(digits.length);
    
    const visible = digits.slice(-4);
    const masked = '*'.repeat(digits.length - 4);
    
    return masked + visible;
  }

  /**
   * Mask ID number
   */
  static maskIdNumber(idNumber: string): string {
    if (idNumber.length < 4) return '*'.repeat(idNumber.length);
    
    const visible = idNumber.slice(-4);
    const masked = '*'.repeat(idNumber.length - 4);
    
    return masked + visible;
  }

  /**
   * Mask credit card number
   */
  static maskCreditCard(cardNumber: string): string {
    const digits = cardNumber.replace(/\D/g, '');
    if (digits.length < 4) return '*'.repeat(digits.length);
    
    const visible = digits.slice(-4);
    const masked = '*'.repeat(digits.length - 4);
    
    return masked + visible;
  }

  /**
   * Mask address
   */
  static maskAddress(address: string): string {
    const parts = address.split(' ');
    if (parts.length <= 2) return '*'.repeat(address.length);
    
    // Show first and last parts, mask middle
    const first = parts[0];
    const last = parts[parts.length - 1];
    const middle = '*'.repeat(parts.slice(1, -1).join(' ').length);
    
    return `${first} ${middle} ${last}`;
  }

  /**
   * Generic data masking based on data type
   */
  static maskData(data: string, type: DataType): string {
    switch (type) {
      case DataType.EMAIL:
        return this.maskEmail(data);
      case DataType.PHONE:
        return this.maskPhone(data);
      case DataType.ID_NUMBER:
        return this.maskIdNumber(data);
      case DataType.CREDIT_CARD:
        return this.maskCreditCard(data);
      case DataType.ADDRESS:
        return this.maskAddress(data);
      default:
        // Generic masking - show first and last character
        if (data.length <= 2) return '*'.repeat(data.length);
        return data[0] + '*'.repeat(data.length - 2) + data[data.length - 1];
    }
  }
}

// Secure data deletion
export class SecureDataDeletion {
  /**
   * Securely delete data by overwriting
   */
  static secureDelete(data: any): void {
    if (typeof data === 'string') {
      // Overwrite string data
      for (let i = 0; i < data.length; i++) {
        data = data.substring(0, i) + '\0' + data.substring(i + 1);
      }
    } else if (data instanceof ArrayBuffer) {
      // Overwrite buffer data
      const view = new Uint8Array(data);
      crypto.getRandomValues(view);
    } else if (typeof data === 'object' && data !== null) {
      // Recursively overwrite object properties
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          this.secureDelete(data[key]);
          delete data[key];
        }
      }
    }
  }

  /**
   * Clear sensitive data from memory
   */
  static clearSensitiveData(obj: any, sensitiveFields: string[]): void {
    if (typeof obj !== 'object' || obj === null) return;

    for (const field of sensitiveFields) {
      if (obj.hasOwnProperty(field)) {
        this.secureDelete(obj[field]);
        delete obj[field];
      }
    }
  }

  /**
   * Schedule data for deletion
   */
  static scheduleDataDeletion(
    dataId: string,
    deletionDate: Date,
    callback: () => void
  ): void {
    const now = new Date();
    const delay = deletionDate.getTime() - now.getTime();

    if (delay <= 0) {
      // Delete immediately
      callback();
    } else {
      // Schedule deletion
      setTimeout(() => {
        callback();
      }, delay);
    }
  }
}

// Types and interfaces
export interface EncryptedData {
  data: number[];
  iv: number[];
  algorithm: string;
}

export interface PIIData {
  id: string;
  [key: string]: any;
}

export interface EncryptedPIIData {
  id: string;
  encryptedFields: Record<string, EncryptedData>;
}

export enum DataType {
  EMAIL = 'email',
  PHONE = 'phone',
  ID_NUMBER = 'id_number',
  CREDIT_CARD = 'credit_card',
  ADDRESS = 'address',
  GENERIC = 'generic'
}

// Data classification
export enum DataClassification {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  CONFIDENTIAL = 'confidential',
  RESTRICTED = 'restricted'
}

export interface DataClassificationRule {
  field: string;
  classification: DataClassification;
  encryptionRequired: boolean;
  maskingRequired: boolean;
  retentionPeriod?: number; // in days
}

// Default data classification rules
export const DEFAULT_DATA_CLASSIFICATION: DataClassificationRule[] = [
  { field: 'email', classification: DataClassification.CONFIDENTIAL, encryptionRequired: true, maskingRequired: true },
  { field: 'phone', classification: DataClassification.CONFIDENTIAL, encryptionRequired: true, maskingRequired: true },
  { field: 'nationalId', classification: DataClassification.RESTRICTED, encryptionRequired: true, maskingRequired: true },
  { field: 'passportNumber', classification: DataClassification.RESTRICTED, encryptionRequired: true, maskingRequired: true },
  { field: 'bankAccount', classification: DataClassification.RESTRICTED, encryptionRequired: true, maskingRequired: true },
  { field: 'address', classification: DataClassification.CONFIDENTIAL, encryptionRequired: true, maskingRequired: true },
  { field: 'salary', classification: DataClassification.CONFIDENTIAL, encryptionRequired: true, maskingRequired: false },
  { field: 'name', classification: DataClassification.INTERNAL, encryptionRequired: false, maskingRequired: false },
  { field: 'propertyName', classification: DataClassification.INTERNAL, encryptionRequired: false, maskingRequired: false },
  { field: 'unitNumber', classification: DataClassification.INTERNAL, encryptionRequired: false, maskingRequired: false }
];