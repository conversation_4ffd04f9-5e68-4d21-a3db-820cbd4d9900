export interface ElectronAPI {
  // Version and platform
  getVersion: () => Promise<string>
  getAppVersion: () => Promise<string>
  getPlatform: () => Promise<string>
  
  // Auto updater
  checkForUpdates: () => Promise<any>
  downloadUpdate: () => Promise<any>
  installUpdate: () => Promise<void>
  onUpdateStatus: (callback: (status: any) => void) => void
  onUpdateAction: (callback: (action: any) => void) => void
  getAutoLaunch: () => Promise<boolean>
  setAutoLaunch: (enable: boolean) => Promise<boolean>
  reportCrash: (crashData: any) => Promise<void>
  
  // File system
  showOpenDialog: (options: any) => Promise<any>
  showSaveDialog: (options: any) => Promise<any>
  readFile: (filePath: string) => Promise<string>
  writeFile: (filePath: string, content: string) => Promise<boolean>
  
  // Notifications
  showNotification: (options: any) => Promise<boolean>
  
  // Printing
  printDocument: (html: string, options?: any) => Promise<boolean>
  printToPDF: (html: string, options?: any) => Promise<{ success: boolean; filePath?: string }>
  
  // Window management
  minimizeWindow: () => Promise<void>
  maximizeWindow: () => Promise<void>
  hideWindow: () => Promise<void>
  showWindow: () => Promise<void>
  
  // Menu actions
  onMenuAction: (callback: (action: string) => void) => void
  
  // Messages
  onMainMessage: (callback: (message: string) => void) => void
  removeAllListeners: (channel: string) => void
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}