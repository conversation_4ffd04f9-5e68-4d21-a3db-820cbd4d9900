import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { LeaseManagement } from '../LeaseManagement';

// Mock Convex hooks
const mockCreateLease = vi.fn();
const mockUpdateLease = vi.fn();
const mockTerminateLease = vi.fn();

vi.mock('convex/react', () => ({
  useQuery: vi.fn(() => [
    {
      _id: 'lease1',
      propertyId: 'property1',
      unitId: 'unit1',
      tenantId: 'tenant1',
      startDate: Date.now(),
      endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
      monthlyRent: 50000,
      deposit: 100000,
      status: 'active',
      tenant: { name: '<PERSON>', email: '<EMAIL>' },
      unit: { unitNumber: 'A101' },
      property: { name: 'Sunset Apartments' },
    },
    {
      _id: 'lease2',
      propertyId: 'property1',
      unitId: 'unit2',
      tenantId: 'tenant2',
      startDate: Date.now() - 400 * 24 * 60 * 60 * 1000,
      endDate: Date.now() - 35 * 24 * 60 * 60 * 1000,
      monthlyRent: 45000,
      deposit: 90000,
      status: 'expired',
      tenant: { name: '<PERSON>', email: '<EMAIL>' },
      unit: { unitNumber: 'A102' },
      property: { name: 'Sunset Apartments' },
    },
  ]),
  useMutation: vi.fn((mutation) => {
    if (mutation.toString().includes('create')) return mockCreateLease;
    if (mutation.toString().includes('update')) return mockUpdateLease;
    if (mutation.toString().includes('terminate')) return mockTerminateLease;
    return vi.fn();
  }),
}));

// Mock auth context
vi.mock('../../../lib/auth-context', () => ({
  useAuth: vi.fn(() => ({
    user: {
      _id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'manager',
    },
  })),
}));

describe('LeaseManagement', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders lease list with leases', () => {
    render(<LeaseManagement propertyId="property1" />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('A101')).toBeInTheDocument();
    expect(screen.getByText('A102')).toBeInTheDocument();
  });

  it('shows lease status badges correctly', () => {
    render(<LeaseManagement propertyId="property1" />);
    
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('Expired')).toBeInTheDocument();
  });

  it('filters leases by status', () => {
    render(<LeaseManagement propertyId="property1" />);
    
    const statusFilter = screen.getByRole('combobox', { name: /filter by status/i });
    fireEvent.click(statusFilter);
    
    const activeOption = screen.getByText('Active');
    fireEvent.click(activeOption);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
  });

  it('searches leases by tenant name', () => {
    render(<LeaseManagement propertyId="property1" />);
    
    const searchInput = screen.getByPlaceholderText(/search leases/i);
    fireEvent.change(searchInput, { target: { value: 'John' } });
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
  });

  it('opens create lease dialog when add button is clicked', () => {
    render(<LeaseManagement propertyId="property1" />);
    
    const addButton = screen.getByRole('button', { name: /create lease/i });
    fireEvent.click(addButton);
    
    expect(screen.getByText(/new lease agreement/i)).toBeInTheDocument();
  });

  it('creates new lease successfully', async () => {
    mockCreateLease.mockResolvedValue({ _id: 'new-lease' });
    
    render(<LeaseManagement propertyId="property1" />);
    
    const addButton = screen.getByRole('button', { name: /create lease/i });
    fireEvent.click(addButton);
    
    // Fill form
    fireEvent.change(screen.getByLabelText(/tenant email/i), { 
      target: { value: '<EMAIL>' } 
    });
    fireEvent.change(screen.getByLabelText(/monthly rent/i), { 
      target: { value: '52000' } 
    });
    fireEvent.change(screen.getByLabelText(/deposit/i), { 
      target: { value: '104000' } 
    });
    
    const submitButton = screen.getByRole('button', { name: /create lease/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockCreateLease).toHaveBeenCalledWith(
        expect.objectContaining({
          propertyId: 'property1',
          tenantEmail: '<EMAIL>',
          monthlyRent: 52000,
          deposit: 104000,
        })
      );
    });
  });

  it('shows lease details when view button is clicked', () => {
    render(<LeaseManagement propertyId="property1" />);
    
    const viewButtons = screen.getAllByRole('button', { name: /view/i });
    fireEvent.click(viewButtons[0]);
    
    expect(screen.getByText(/lease details/i)).toBeInTheDocument();
    expect(screen.getByText('KES 50,000')).toBeInTheDocument();
    expect(screen.getByText('KES 100,000')).toBeInTheDocument();
  });

  it('terminates lease with confirmation', async () => {
    mockTerminateLease.mockResolvedValue({ success: true });
    
    // Mock window.confirm
    const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(true);
    
    render(<LeaseManagement propertyId="property1" />);
    
    const terminateButtons = screen.getAllByRole('button', { name: /terminate/i });
    fireEvent.click(terminateButtons[0]);
    
    await waitFor(() => {
      expect(confirmSpy).toHaveBeenCalledWith(
        'Are you sure you want to terminate this lease? This action cannot be undone.'
      );
      expect(mockTerminateLease).toHaveBeenCalledWith({ 
        leaseId: 'lease1',
        reason: 'Manual termination'
      });
    });
    
    confirmSpy.mockRestore();
  });

  it('shows renewal notification for expiring leases', () => {
    // Mock lease expiring in 30 days
    const expiringLease = {
      _id: 'lease3',
      propertyId: 'property1',
      unitId: 'unit3',
      tenantId: 'tenant3',
      startDate: Date.now() - 335 * 24 * 60 * 60 * 1000,
      endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      monthlyRent: 48000,
      deposit: 96000,
      status: 'active',
      tenant: { name: 'Bob Wilson', email: '<EMAIL>' },
      unit: { unitNumber: 'A103' },
      property: { name: 'Sunset Apartments' },
    };

    vi.mocked(require('convex/react').useQuery).mockReturnValue([expiringLease]);
    
    render(<LeaseManagement propertyId="property1" />);
    
    expect(screen.getByText(/expires in 30 days/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /renew/i })).toBeInTheDocument();
  });

  it('handles lease renewal', async () => {
    const mockRenewLease = vi.fn().mockResolvedValue({ _id: 'renewed-lease' });
    vi.mocked(require('convex/react').useMutation).mockImplementation((mutation) => {
      if (mutation.toString().includes('renew')) return mockRenewLease;
      return vi.fn();
    });

    render(<LeaseManagement propertyId="property1" />);
    
    const renewButtons = screen.getAllByRole('button', { name: /renew/i });
    if (renewButtons.length > 0) {
      fireEvent.click(renewButtons[0]);
      
      await waitFor(() => {
        expect(screen.getByText(/renew lease/i)).toBeInTheDocument();
      });
    }
  });

  it('shows empty state when no leases exist', () => {
    vi.mocked(require('convex/react').useQuery).mockReturnValue([]);
    
    render(<LeaseManagement propertyId="property1" />);
    
    expect(screen.getByText(/no leases found/i)).toBeInTheDocument();
    expect(screen.getByText(/create your first lease/i)).toBeInTheDocument();
  });

  it('handles loading state', () => {
    vi.mocked(require('convex/react').useQuery).mockReturnValue(undefined);
    
    render(<LeaseManagement propertyId="property1" />);
    
    expect(screen.getByText(/loading leases/i)).toBeInTheDocument();
  });

  it('shows error message on create failure', async () => {
    mockCreateLease.mockRejectedValue(new Error('Tenant already has an active lease'));
    
    render(<LeaseManagement propertyId="property1" />);
    
    const addButton = screen.getByRole('button', { name: /create lease/i });
    fireEvent.click(addButton);
    
    // Fill form
    fireEvent.change(screen.getByLabelText(/tenant email/i), { 
      target: { value: '<EMAIL>' } 
    });
    
    const submitButton = screen.getByRole('button', { name: /create lease/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/tenant already has an active lease/i)).toBeInTheDocument();
    });
  });
});