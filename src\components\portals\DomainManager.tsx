import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { useToast } from '../ui/use-toast';
import { 
  Globe, 
  ExternalLink, 
  Copy, 
  CheckCircle, 
  AlertCircle, 
  Settings,
  RefreshCw
} from 'lucide-react';

interface DomainManagerProps {
  portalId: Id<"portals">;
}

interface DNSRecord {
  type: string;
  name: string;
  value: string;
  ttl: number;
}

export const DomainManager: React.FC<DomainManagerProps> = ({ portalId }) => {
  const { toast } = useToast();
  const [customDomain, setCustomDomain] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [domainStatus, setDomainStatus] = useState<'pending' | 'verified' | 'failed'>('pending');

  const portal = useQuery(api.portals.getPortalById, { portalId });
  const updateCustomDomain = useMutation(api.portals.updateCustomDomain);
  const checkDomainAvailability = useQuery(api.portals.checkCustomDomainAvailability, 
    customDomain ? { customDomain } : 'skip'
  );

  useEffect(() => {
    if (portal?.customDomain) {
      setCustomDomain(portal.customDomain);
    }
  }, [portal]);

  const handleSaveDomain = async () => {
    try {
      await updateCustomDomain({
        portalId,
        customDomain: customDomain || undefined,
      });
      
      toast({
        title: "Domain Updated",
        description: "Custom domain has been successfully updated.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update domain",
        variant: "destructive",
      });
    }
  };

  const handleVerifyDomain = async () => {
    setIsVerifying(true);
    
    // Simulate domain verification process
    setTimeout(() => {
      // In a real implementation, this would make an API call to verify DNS records
      const isVerified = Math.random() > 0.3; // 70% success rate for demo
      setDomainStatus(isVerified ? 'verified' : 'failed');
      setIsVerifying(false);
      
      toast({
        title: isVerified ? "Domain Verified" : "Verification Failed",
        description: isVerified 
          ? "Your custom domain is now active and ready to use."
          : "Domain verification failed. Please check your DNS settings.",
        variant: isVerified ? "default" : "destructive",
      });
    }, 2000);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "DNS record copied to clipboard",
    });
  };

  const getDNSRecords = (): DNSRecord[] => {
    if (!customDomain) return [];
    
    return [
      {
        type: 'CNAME',
        name: customDomain,
        value: 'portals.estatepulse.com',
        ttl: 300,
      },
      {
        type: 'TXT',
        name: `_estatepulse-verification.${customDomain}`,
        value: `estatepulse-verification=${portalId}`,
        ttl: 300,
      },
    ];
  };

  const getPortalUrls = () => {
    const urls = [];
    
    if (portal?.subdomain) {
      urls.push({
        type: 'Subdomain',
        url: `https://${portal.subdomain}.estatepulse.com`,
        status: 'active',
      });
    }
    
    if (portal?.customDomain) {
      urls.push({
        type: 'Custom Domain',
        url: `https://${portal.customDomain}`,
        status: domainStatus,
      });
    }
    
    return urls;
  };

  const DNSRecordCard: React.FC<{ record: DNSRecord }> = ({ record }) => (
    <div className="border rounded-lg p-4 space-y-2">
      <div className="flex items-center justify-between">
        <Badge variant="outline">{record.type}</Badge>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => copyToClipboard(record.value)}
        >
          <Copy className="w-4 h-4" />
        </Button>
      </div>
      <div className="space-y-1">
        <div className="text-sm">
          <span className="font-medium">Name:</span> {record.name}
        </div>
        <div className="text-sm">
          <span className="font-medium">Value:</span> 
          <code className="ml-2 bg-muted px-1 rounded text-xs">{record.value}</code>
        </div>
        <div className="text-sm text-muted-foreground">
          TTL: {record.ttl} seconds
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Domain Management</h3>
          <p className="text-sm text-muted-foreground">
            Configure your portal's domain settings and DNS records
          </p>
        </div>
      </div>

      {/* Current Portal URLs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Globe className="w-5 h-5 mr-2" />
            Portal URLs
          </CardTitle>
          <CardDescription>
            Your portal is accessible through these URLs
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {getPortalUrls().map((urlInfo, index) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                <Badge variant={urlInfo.status === 'active' ? 'default' : 
                              urlInfo.status === 'verified' ? 'default' : 
                              urlInfo.status === 'pending' ? 'secondary' : 'destructive'}>
                  {urlInfo.status === 'active' ? <CheckCircle className="w-3 h-3 mr-1" /> :
                   urlInfo.status === 'verified' ? <CheckCircle className="w-3 h-3 mr-1" /> :
                   urlInfo.status === 'pending' ? <AlertCircle className="w-3 h-3 mr-1" /> :
                   <AlertCircle className="w-3 h-3 mr-1" />}
                  {urlInfo.type}
                </Badge>
                <span className="font-mono text-sm">{urlInfo.url}</span>
              </div>
              <Button variant="ghost" size="sm" asChild>
                <a href={urlInfo.url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="w-4 h-4" />
                </a>
              </Button>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Custom Domain Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            Custom Domain Setup
          </CardTitle>
          <CardDescription>
            Use your own domain for the tenant portal
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="customDomain">Custom Domain</Label>
            <div className="flex space-x-2">
              <Input
                id="customDomain"
                value={customDomain}
                onChange={(e) => setCustomDomain(e.target.value)}
                placeholder="portal.mycompany.com"
                className="flex-1"
              />
              <Button onClick={handleSaveDomain} variant="outline">
                Save
              </Button>
            </div>
            {customDomain && checkDomainAvailability !== undefined && (
              <p className={`text-sm ${checkDomainAvailability ? 'text-green-600' : 'text-red-600'}`}>
                {checkDomainAvailability ? '✓ Domain available' : '✗ Domain already in use'}
              </p>
            )}
          </div>

          {customDomain && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                After saving your custom domain, you'll need to configure DNS records 
                with your domain provider and verify the setup.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* DNS Configuration */}
      {customDomain && (
        <Card>
          <CardHeader>
            <CardTitle>DNS Configuration</CardTitle>
            <CardDescription>
              Add these DNS records to your domain provider to activate your custom domain
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4">
              {getDNSRecords().map((record, index) => (
                <DNSRecordCard key={index} record={record} />
              ))}
            </div>

            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                DNS changes can take up to 24 hours to propagate. After adding these records,
                click "Verify Domain" to check if your domain is properly configured.
              </AlertDescription>
            </Alert>

            <div className="flex items-center space-x-2">
              <Button 
                onClick={handleVerifyDomain} 
                disabled={isVerifying}
                className="flex items-center"
              >
                {isVerifying ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="w-4 h-4 mr-2" />
                )}
                {isVerifying ? 'Verifying...' : 'Verify Domain'}
              </Button>
              
              {domainStatus === 'verified' && (
                <Badge variant="default" className="flex items-center">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Verified
                </Badge>
              )}
              
              {domainStatus === 'failed' && (
                <Badge variant="destructive" className="flex items-center">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  Failed
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* SSL Certificate Status */}
      {customDomain && domainStatus === 'verified' && (
        <Card>
          <CardHeader>
            <CardTitle>SSL Certificate</CardTitle>
            <CardDescription>
              SSL certificate status for your custom domain
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge variant="default" className="flex items-center">
                <CheckCircle className="w-3 h-3 mr-1" />
                SSL Active
              </Badge>
              <span className="text-sm text-muted-foreground">
                Your portal is secured with a valid SSL certificate
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};