import { auditLogger, AuditEventType, AuditSeverity } from './audit-logging';

// Consent types and purposes
export enum ConsentType {
  DATA_PROCESSING = 'data_processing',
  MARKETING = 'marketing',
  ANALYTICS = 'analytics',
  COOKIES = 'cookies',
  THIRD_PARTY_SHARING = 'third_party_sharing',
  LOCATION_TRACKING = 'location_tracking',
  COMMUNICATION = 'communication',
  PROFILING = 'profiling'
}

export enum ConsentStatus {
  GRANTED = 'granted',
  DENIED = 'denied',
  WITHDRAWN = 'withdrawn',
  EXPIRED = 'expired',
  PENDING = 'pending'
}

export enum LegalBasis {
  CONSENT = 'consent',
  CONTRACT = 'contract',
  LEGAL_OBLIGATION = 'legal_obligation',
  VITAL_INTERESTS = 'vital_interests',
  PUBLIC_TASK = 'public_task',
  LEGITIMATE_INTERESTS = 'legitimate_interests'
}

// Consent record interface
export interface ConsentRecord {
  id: string;
  userId: string;
  consentType: ConsentType;
  purpose: string;
  description: string;
  status: ConsentStatus;
  legalBasis: LegalBasis;
  grantedAt?: number;
  withdrawnAt?: number;
  expiresAt?: number;
  version: string; // Version of consent text/policy
  ipAddress?: string;
  userAgent?: string;
  source: string; // Where consent was collected (web, mobile, etc.)
  metadata?: Record<string, any>;
  isRequired: boolean; // Whether consent is required for service
}

// Consent configuration
export interface ConsentConfiguration {
  type: ConsentType;
  purpose: string;
  description: string;
  legalBasis: LegalBasis;
  isRequired: boolean;
  expirationPeriod?: number; // in days
  version: string;
  category: ConsentCategory;
  dependencies?: ConsentType[]; // Other consents this depends on
}

export enum ConsentCategory {
  ESSENTIAL = 'essential',
  FUNCTIONAL = 'functional',
  ANALYTICS = 'analytics',
  MARKETING = 'marketing'
}

// Consent manager
export class ConsentManager {
  private static instance: ConsentManager;
  private consents = new Map<string, ConsentRecord>();
  private configurations = new Map<ConsentType, ConsentConfiguration>();
  private readonly STORAGE_KEY = 'user_consents';
  private readonly CONFIG_KEY = 'consent_configurations';

  constructor() {
    this.loadConsents();
    this.loadConfigurations();
    this.initializeDefaultConfigurations();
    this.startExpirationChecker();
  }

  static getInstance(): ConsentManager {
    if (!this.instance) {
      this.instance = new ConsentManager();
    }
    return this.instance;
  }

  /**
   * Record user consent
   */
  async recordConsent(
    userId: string,
    consentType: ConsentType,
    granted: boolean,
    ipAddress?: string,
    userAgent?: string,
    source: string = 'web',
    metadata?: Record<string, any>
  ): Promise<string> {
    const config = this.configurations.get(consentType);
    if (!config) {
      throw new Error(`Consent configuration not found for type: ${consentType}`);
    }

    const now = Date.now();
    const consentId = crypto.randomUUID();
    const expiresAt = config.expirationPeriod 
      ? now + (config.expirationPeriod * 24 * 60 * 60 * 1000)
      : undefined;

    const consent: ConsentRecord = {
      id: consentId,
      userId,
      consentType,
      purpose: config.purpose,
      description: config.description,
      status: granted ? ConsentStatus.GRANTED : ConsentStatus.DENIED,
      legalBasis: config.legalBasis,
      grantedAt: granted ? now : undefined,
      expiresAt,
      version: config.version,
      ipAddress,
      userAgent,
      source,
      metadata,
      isRequired: config.isRequired
    };

    // Store consent
    const key = this.getConsentKey(userId, consentType);
    this.consents.set(key, consent);
    await this.saveConsents();

    // Log consent event
    await auditLogger.logEvent({
      type: AuditEventType.PRIVACY_SETTINGS_CHANGED,
      severity: AuditSeverity.MEDIUM,
      userId,
      ipAddress,
      userAgent,
      action: `Consent ${granted ? 'granted' : 'denied'} for ${consentType}`,
      details: {
        consentType,
        status: consent.status,
        legalBasis: config.legalBasis,
        version: config.version,
        source
      },
      success: true
    });

    return consentId;
  }

  /**
   * Withdraw consent
   */
  async withdrawConsent(
    userId: string,
    consentType: ConsentType,
    reason?: string
  ): Promise<void> {
    const key = this.getConsentKey(userId, consentType);
    const consent = this.consents.get(key);

    if (!consent) {
      throw new Error(`Consent record not found for user ${userId} and type ${consentType}`);
    }

    if (consent.status !== ConsentStatus.GRANTED) {
      throw new Error(`Cannot withdraw consent that is not granted`);
    }

    // Update consent record
    consent.status = ConsentStatus.WITHDRAWN;
    consent.withdrawnAt = Date.now();
    if (reason) {
      consent.metadata = { ...consent.metadata, withdrawalReason: reason };
    }

    this.consents.set(key, consent);
    await this.saveConsents();

    // Log withdrawal
    await auditLogger.logEvent({
      type: AuditEventType.PRIVACY_SETTINGS_CHANGED,
      severity: AuditSeverity.HIGH,
      userId,
      action: `Consent withdrawn for ${consentType}`,
      details: {
        consentType,
        reason,
        withdrawnAt: consent.withdrawnAt
      },
      success: true
    });

    // Handle dependent consents
    await this.handleDependentConsents(userId, consentType);
  }

  /**
   * Check if user has granted consent
   */
  hasConsent(userId: string, consentType: ConsentType): boolean {
    const key = this.getConsentKey(userId, consentType);
    const consent = this.consents.get(key);

    if (!consent) return false;

    // Check if consent is granted and not expired
    if (consent.status !== ConsentStatus.GRANTED) return false;
    if (consent.expiresAt && Date.now() > consent.expiresAt) return false;

    return true;
  }

  /**
   * Get user consent record
   */
  getConsent(userId: string, consentType: ConsentType): ConsentRecord | null {
    const key = this.getConsentKey(userId, consentType);
    return this.consents.get(key) || null;
  }

  /**
   * Get all consents for user
   */
  getUserConsents(userId: string): ConsentRecord[] {
    return Array.from(this.consents.values())
      .filter(consent => consent.userId === userId);
  }

  /**
   * Get consent summary for user
   */
  getConsentSummary(userId: string): ConsentSummary {
    const userConsents = this.getUserConsents(userId);
    const allConfigs = Array.from(this.configurations.values());

    const summary: ConsentSummary = {
      userId,
      totalConsents: allConfigs.length,
      grantedConsents: 0,
      deniedConsents: 0,
      withdrawnConsents: 0,
      expiredConsents: 0,
      pendingConsents: 0,
      lastUpdated: 0,
      consentsByCategory: {},
      requiredConsentsGranted: true
    };

    // Initialize categories
    Object.values(ConsentCategory).forEach(category => {
      summary.consentsByCategory[category] = {
        total: 0,
        granted: 0,
        denied: 0,
        withdrawn: 0,
        expired: 0
      };
    });

    // Process each configuration
    allConfigs.forEach(config => {
      const consent = userConsents.find(c => c.consentType === config.type);
      const category = config.category;

      summary.consentsByCategory[category].total++;

      if (consent) {
        summary.lastUpdated = Math.max(summary.lastUpdated, consent.grantedAt || consent.withdrawnAt || 0);

        switch (consent.status) {
          case ConsentStatus.GRANTED:
            if (consent.expiresAt && Date.now() > consent.expiresAt) {
              summary.expiredConsents++;
              summary.consentsByCategory[category].expired++;
              if (config.isRequired) summary.requiredConsentsGranted = false;
            } else {
              summary.grantedConsents++;
              summary.consentsByCategory[category].granted++;
            }
            break;
          case ConsentStatus.DENIED:
            summary.deniedConsents++;
            summary.consentsByCategory[category].denied++;
            if (config.isRequired) summary.requiredConsentsGranted = false;
            break;
          case ConsentStatus.WITHDRAWN:
            summary.withdrawnConsents++;
            summary.consentsByCategory[category].withdrawn++;
            if (config.isRequired) summary.requiredConsentsGranted = false;
            break;
          case ConsentStatus.EXPIRED:
            summary.expiredConsents++;
            summary.consentsByCategory[category].expired++;
            if (config.isRequired) summary.requiredConsentsGranted = false;
            break;
          case ConsentStatus.PENDING:
            summary.pendingConsents++;
            if (config.isRequired) summary.requiredConsentsGranted = false;
            break;
        }
      } else {
        // No consent recorded
        summary.pendingConsents++;
        if (config.isRequired) summary.requiredConsentsGranted = false;
      }
    });

    return summary;
  }

  /**
   * Update consent configuration
   */
  async updateConfiguration(
    consentType: ConsentType,
    config: Partial<ConsentConfiguration>
  ): Promise<void> {
    const existing = this.configurations.get(consentType);
    if (!existing) {
      throw new Error(`Configuration not found for consent type: ${consentType}`);
    }

    const updated = { ...existing, ...config };
    this.configurations.set(consentType, updated);
    await this.saveConfigurations();

    // Log configuration update
    await auditLogger.logEvent({
      type: AuditEventType.CONFIGURATION_CHANGED,
      severity: AuditSeverity.MEDIUM,
      action: `Consent configuration updated for ${consentType}`,
      details: { consentType, changes: config },
      success: true
    });
  }

  /**
   * Export user consent data
   */
  exportUserConsentData(userId: string, format: 'json' | 'csv' = 'json'): string {
    const consents = this.getUserConsents(userId);

    if (format === 'csv') {
      const headers = [
        'Consent Type', 'Purpose', 'Status', 'Legal Basis', 'Granted At',
        'Withdrawn At', 'Expires At', 'Version', 'Source', 'IP Address'
      ];

      const rows = consents.map(consent => [
        consent.consentType,
        consent.purpose,
        consent.status,
        consent.legalBasis,
        consent.grantedAt ? new Date(consent.grantedAt).toISOString() : '',
        consent.withdrawnAt ? new Date(consent.withdrawnAt).toISOString() : '',
        consent.expiresAt ? new Date(consent.expiresAt).toISOString() : '',
        consent.version,
        consent.source,
        consent.ipAddress || ''
      ]);

      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }

    return JSON.stringify(consents, null, 2);
  }

  /**
   * Delete all user consent data
   */
  async deleteUserConsentData(userId: string): Promise<void> {
    const userConsents = this.getUserConsents(userId);
    
    // Remove all consent records for user
    userConsents.forEach(consent => {
      const key = this.getConsentKey(userId, consent.consentType);
      this.consents.delete(key);
    });

    await this.saveConsents();

    // Log data deletion
    await auditLogger.logEvent({
      type: AuditEventType.USER_DELETED,
      severity: AuditSeverity.HIGH,
      userId,
      action: 'User consent data deleted',
      details: { deletedConsents: userConsents.length },
      success: true
    });
  }

  /**
   * Check expired consents and update status
   */
  async checkExpiredConsents(): Promise<void> {
    const now = Date.now();
    let expiredCount = 0;

    for (const [key, consent] of this.consents.entries()) {
      if (consent.expiresAt && now > consent.expiresAt && consent.status === ConsentStatus.GRANTED) {
        consent.status = ConsentStatus.EXPIRED;
        this.consents.set(key, consent);
        expiredCount++;

        // Log expiration
        await auditLogger.logEvent({
          type: AuditEventType.PRIVACY_SETTINGS_CHANGED,
          severity: AuditSeverity.MEDIUM,
          userId: consent.userId,
          action: `Consent expired for ${consent.consentType}`,
          details: { consentType: consent.consentType, expiresAt: consent.expiresAt },
          success: true
        });
      }
    }

    if (expiredCount > 0) {
      await this.saveConsents();
      console.log(`Marked ${expiredCount} consents as expired`);
    }
  }

  /**
   * Generate consent report
   */
  generateConsentReport(): ConsentReport {
    const allConsents = Array.from(this.consents.values());
    const allConfigs = Array.from(this.configurations.values());

    const report: ConsentReport = {
      generatedAt: Date.now(),
      totalUsers: new Set(allConsents.map(c => c.userId)).size,
      totalConsents: allConsents.length,
      consentsByType: this.groupConsentsByType(allConsents),
      consentsByStatus: this.groupConsentsByStatus(allConsents),
      consentsByCategory: this.groupConsentsByCategory(allConsents, allConfigs),
      complianceMetrics: this.calculateComplianceMetrics(allConsents, allConfigs)
    };

    return report;
  }

  /**
   * Handle dependent consents when a consent is withdrawn
   */
  private async handleDependentConsents(userId: string, withdrawnType: ConsentType): Promise<void> {
    // Find configurations that depend on the withdrawn consent
    const dependentConfigs = Array.from(this.configurations.values())
      .filter(config => config.dependencies?.includes(withdrawnType));

    for (const config of dependentConfigs) {
      const hasConsent = this.hasConsent(userId, config.type);
      if (hasConsent) {
        await this.withdrawConsent(userId, config.type, `Dependent on withdrawn consent: ${withdrawnType}`);
      }
    }
  }

  /**
   * Get consent key for storage
   */
  private getConsentKey(userId: string, consentType: ConsentType): string {
    return `${userId}:${consentType}`;
  }

  /**
   * Initialize default consent configurations
   */
  private initializeDefaultConfigurations(): void {
    if (this.configurations.size === 0) {
      DEFAULT_CONSENT_CONFIGURATIONS.forEach(config => {
        this.configurations.set(config.type, config);
      });
      this.saveConfigurations();
    }
  }

  /**
   * Start expiration checker
   */
  private startExpirationChecker(): void {
    // Check for expired consents every hour
    setInterval(() => {
      this.checkExpiredConsents();
    }, 60 * 60 * 1000);
  }

  /**
   * Group consents by type
   */
  private groupConsentsByType(consents: ConsentRecord[]): Record<string, number> {
    return consents.reduce((acc, consent) => {
      acc[consent.consentType] = (acc[consent.consentType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Group consents by status
   */
  private groupConsentsByStatus(consents: ConsentRecord[]): Record<string, number> {
    return consents.reduce((acc, consent) => {
      acc[consent.status] = (acc[consent.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Group consents by category
   */
  private groupConsentsByCategory(
    consents: ConsentRecord[],
    configs: ConsentConfiguration[]
  ): Record<string, number> {
    const categoryMap = new Map<ConsentType, ConsentCategory>();
    configs.forEach(config => {
      categoryMap.set(config.type, config.category);
    });

    return consents.reduce((acc, consent) => {
      const category = categoryMap.get(consent.consentType) || ConsentCategory.FUNCTIONAL;
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Calculate compliance metrics
   */
  private calculateComplianceMetrics(
    consents: ConsentRecord[],
    configs: ConsentConfiguration[]
  ): ComplianceMetrics {
    const requiredConfigs = configs.filter(c => c.isRequired);
    const users = new Set(consents.map(c => c.userId));
    
    let compliantUsers = 0;
    let totalRequiredConsents = 0;
    let grantedRequiredConsents = 0;

    users.forEach(userId => {
      let userCompliant = true;
      
      requiredConfigs.forEach(config => {
        totalRequiredConsents++;
        const hasConsent = this.hasConsent(userId, config.type);
        
        if (hasConsent) {
          grantedRequiredConsents++;
        } else {
          userCompliant = false;
        }
      });

      if (userCompliant) {
        compliantUsers++;
      }
    });

    return {
      totalUsers: users.size,
      compliantUsers,
      complianceRate: users.size > 0 ? compliantUsers / users.size : 0,
      totalRequiredConsents,
      grantedRequiredConsents,
      requiredConsentRate: totalRequiredConsents > 0 ? grantedRequiredConsents / totalRequiredConsents : 0
    };
  }

  /**
   * Load consents from storage
   */
  private loadConsents(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const consents = JSON.parse(stored);
        consents.forEach((consent: ConsentRecord) => {
          const key = this.getConsentKey(consent.userId, consent.consentType);
          this.consents.set(key, consent);
        });
      }
    } catch (error) {
      console.error('Failed to load consents:', error);
    }
  }

  /**
   * Save consents to storage
   */
  private async saveConsents(): Promise<void> {
    try {
      const consents = Array.from(this.consents.values());
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(consents));
    } catch (error) {
      console.error('Failed to save consents:', error);
    }
  }

  /**
   * Load configurations from storage
   */
  private loadConfigurations(): void {
    try {
      const stored = localStorage.getItem(this.CONFIG_KEY);
      if (stored) {
        const configs = JSON.parse(stored);
        configs.forEach((config: ConsentConfiguration) => {
          this.configurations.set(config.type, config);
        });
      }
    } catch (error) {
      console.error('Failed to load consent configurations:', error);
    }
  }

  /**
   * Save configurations to storage
   */
  private async saveConfigurations(): Promise<void> {
    try {
      const configs = Array.from(this.configurations.values());
      localStorage.setItem(this.CONFIG_KEY, JSON.stringify(configs));
    } catch (error) {
      console.error('Failed to save consent configurations:', error);
    }
  }
}

// Types and interfaces
export interface ConsentSummary {
  userId: string;
  totalConsents: number;
  grantedConsents: number;
  deniedConsents: number;
  withdrawnConsents: number;
  expiredConsents: number;
  pendingConsents: number;
  lastUpdated: number;
  consentsByCategory: Record<ConsentCategory, {
    total: number;
    granted: number;
    denied: number;
    withdrawn: number;
    expired: number;
  }>;
  requiredConsentsGranted: boolean;
}

export interface ConsentReport {
  generatedAt: number;
  totalUsers: number;
  totalConsents: number;
  consentsByType: Record<string, number>;
  consentsByStatus: Record<string, number>;
  consentsByCategory: Record<string, number>;
  complianceMetrics: ComplianceMetrics;
}

export interface ComplianceMetrics {
  totalUsers: number;
  compliantUsers: number;
  complianceRate: number;
  totalRequiredConsents: number;
  grantedRequiredConsents: number;
  requiredConsentRate: number;
}

// Default consent configurations
export const DEFAULT_CONSENT_CONFIGURATIONS: ConsentConfiguration[] = [
  {
    type: ConsentType.DATA_PROCESSING,
    purpose: 'Process personal data for property management services',
    description: 'We need to process your personal data to provide property management services including lease management, payments, and maintenance.',
    legalBasis: LegalBasis.CONTRACT,
    isRequired: true,
    version: '1.0',
    category: ConsentCategory.ESSENTIAL
  },
  {
    type: ConsentType.COMMUNICATION,
    purpose: 'Send service-related communications',
    description: 'We will send you important notifications about your lease, payments, and maintenance updates.',
    legalBasis: LegalBasis.CONTRACT,
    isRequired: true,
    version: '1.0',
    category: ConsentCategory.ESSENTIAL
  },
  {
    type: ConsentType.MARKETING,
    purpose: 'Send marketing communications',
    description: 'We would like to send you information about new properties, special offers, and property management tips.',
    legalBasis: LegalBasis.CONSENT,
    isRequired: false,
    expirationPeriod: 365, // 1 year
    version: '1.0',
    category: ConsentCategory.MARKETING
  },
  {
    type: ConsentType.ANALYTICS,
    purpose: 'Analyze usage patterns and improve services',
    description: 'We use analytics to understand how you use our platform and improve our services.',
    legalBasis: LegalBasis.LEGITIMATE_INTERESTS,
    isRequired: false,
    version: '1.0',
    category: ConsentCategory.ANALYTICS
  },
  {
    type: ConsentType.COOKIES,
    purpose: 'Store cookies for functionality and analytics',
    description: 'We use cookies to remember your preferences and analyze how you use our website.',
    legalBasis: LegalBasis.CONSENT,
    isRequired: false,
    expirationPeriod: 365, // 1 year
    version: '1.0',
    category: ConsentCategory.FUNCTIONAL
  },
  {
    type: ConsentType.THIRD_PARTY_SHARING,
    purpose: 'Share data with service providers',
    description: 'We may share your data with trusted service providers who help us deliver our services (payment processors, maintenance vendors).',
    legalBasis: LegalBasis.CONTRACT,
    isRequired: true,
    version: '1.0',
    category: ConsentCategory.ESSENTIAL,
    dependencies: [ConsentType.DATA_PROCESSING]
  }
];

// Convenience function to get consent manager instance
export const consentManager = ConsentManager.getInstance();