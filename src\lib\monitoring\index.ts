import SentryMonitoring from './sentry';
import PostHogAnalytics from './posthog';
import PerformanceMonitor from './performance-monitor';
import DatabaseMonitor from './database-monitor';

export interface MonitoringConfig {
  sentry?: {
    dsn: string;
    environment?: string;
    tracesSampleRate?: number;
    profilesSampleRate?: number;
  };
  posthog?: {
    apiKey: string;
    apiHost?: string;
    enableSessionRecording?: boolean;
    enableHeatmaps?: boolean;
  };
  enablePerformanceMonitoring?: boolean;
  enableDatabaseMonitoring?: boolean;
}

export class MonitoringService {
  private static instance: MonitoringService;
  private sentry: SentryMonitoring;
  private posthog: PostHogAnalytics;
  private performanceMonitor: PerformanceMonitor;
  private databaseMonitor: DatabaseMonitor;
  private initialized = false;

  private constructor() {
    this.sentry = SentryMonitoring.getInstance();
    this.posthog = PostHogAnalytics.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.databaseMonitor = DatabaseMonitor.getInstance();
  }

  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  initialize(config: MonitoringConfig): void {
    if (this.initialized) {
      console.warn('Monitoring service already initialized');
      return;
    }

    try {
      // Initialize Sentry
      if (config.sentry?.dsn) {
        this.sentry = SentryMonitoring.getInstance(config.sentry);
        this.sentry.initialize();
        console.log('Sentry monitoring initialized');
      }

      // Initialize PostHog
      if (config.posthog?.apiKey) {
        this.posthog = PostHogAnalytics.getInstance(config.posthog);
        this.posthog.initialize();
        console.log('PostHog analytics initialized');
      }

      // Initialize performance monitoring
      if (config.enablePerformanceMonitoring !== false) {
        // Performance monitor is initialized automatically
        console.log('Performance monitoring initialized');
      }

      // Initialize database monitoring
      if (config.enableDatabaseMonitoring !== false) {
        // Database monitor is initialized automatically
        console.log('Database monitoring initialized');
      }

      this.initialized = true;
      console.log('Monitoring service fully initialized');
    } catch (error) {
      console.error('Failed to initialize monitoring service:', error);
    }
  }

  // User identification
  identifyUser(userId: string, properties?: Record<string, any>): void {
    this.sentry.setUser({ id: userId, ...properties });
    this.posthog.identify(userId, properties);
  }

  // Event tracking
  trackEvent(event: string, properties?: Record<string, any>): void {
    this.posthog.track(event, properties);
  }

  // Error reporting
  reportError(error: Error, context?: Record<string, any>): void {
    this.sentry.captureError(error, context);
    this.posthog.trackError(error.message, context);
  }

  // Performance tracking
  trackPerformance(metric: string, value: number, context?: Record<string, any>): void {
    this.performanceMonitor.recordMetric({
      name: metric,
      value,
      unit: 'ms',
      timestamp: Date.now(),
      context,
    });
  }

  // Database query monitoring
  async monitorQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    return this.databaseMonitor.monitorQuery(queryName, queryFn, context);
  }

  // Feature flag support
  isFeatureEnabled(flag: string): boolean {
    return this.posthog.isFeatureEnabled(flag);
  }

  getFeatureFlag(flag: string): boolean | string {
    return this.posthog.getFeatureFlag(flag);
  }

  // Get monitoring instances for advanced usage
  getSentry(): SentryMonitoring {
    return this.sentry;
  }

  getPostHog(): PostHogAnalytics {
    return this.posthog;
  }

  getPerformanceMonitor(): PerformanceMonitor {
    return this.performanceMonitor;
  }

  getDatabaseMonitor(): DatabaseMonitor {
    return this.databaseMonitor;
  }

  // Cleanup
  destroy(): void {
    this.performanceMonitor.destroy();
    this.initialized = false;
  }
}

// React hooks for monitoring
export { default as SentryMonitoring } from './sentry';
export { default as PostHogAnalytics } from './posthog';
export { default as PerformanceMonitor } from './performance-monitor';
export { default as DatabaseMonitor } from './database-monitor';

// Export monitoring service instance
export const monitoring = MonitoringService.getInstance();

// Convenience functions
export const trackEvent = (event: string, properties?: Record<string, any>) => {
  monitoring.trackEvent(event, properties);
};

export const reportError = (error: Error, context?: Record<string, any>) => {
  monitoring.reportError(error, context);
};

export const trackPerformance = (metric: string, value: number, context?: Record<string, any>) => {
  monitoring.trackPerformance(metric, value, context);
};

export const monitorQuery = <T>(
  queryName: string,
  queryFn: () => Promise<T>,
  context?: Record<string, any>
) => {
  return monitoring.monitorQuery(queryName, queryFn, context);
};

export default MonitoringService;