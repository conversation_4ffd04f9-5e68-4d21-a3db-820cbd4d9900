import { describe, test, expect, vi, beforeEach } from 'vitest';
import { TwilioService } from '../twilio-service';

// Mock Twilio
const mockTwilioClient = {
  messages: {
    create: vi.fn(),
    list: vi.fn(),
  },
  api: {
    accounts: vi.fn(() => ({
      fetch: vi.fn(),
      balance: {
        fetch: vi.fn(),
      },
    })),
  },
};

vi.mock('twilio', () => ({
  Twilio: vi.fn(() => mockTwilioClient),
}));

describe('TwilioService', () => {
  let twilioService: TwilioService;
  
  const mockConfig = {
    accountSid: 'test_account_sid',
    authToken: 'test_auth_token',
    phoneNumber: '+**********',
    whatsappNumber: '+**********',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    twilioService = new TwilioService(mockConfig);
  });

  describe('sendSMS', () => {
    test('should send SMS successfully', async () => {
      const mockResponse = {
        sid: 'SM123456789',
        status: 'sent',
        to: '+************',
        from: '+**********',
        body: 'Test message',
        dateCreated: new Date(),
      };

      mockTwilioClient.messages.create.mockResolvedValue(mockResponse);

      const result = await twilioService.sendSMS('+************', 'Test message');

      expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
        body: 'Test message',
        from: '+**********',
        to: '+************',
      });

      expect(result).toEqual({
        sid: 'SM123456789',
        status: 'sent',
        to: '+************',
        from: '+**********',
        body: 'Test message',
        dateCreated: mockResponse.dateCreated,
        errorCode: undefined,
        errorMessage: undefined,
      });
    });

    test('should format Kenyan phone numbers correctly', async () => {
      const mockResponse = {
        sid: 'SM123456789',
        status: 'sent',
        to: '+************',
        from: '+**********',
        body: 'Test message',
        dateCreated: new Date(),
      };

      mockTwilioClient.messages.create.mockResolvedValue(mockResponse);

      // Test various Kenyan number formats
      await twilioService.sendSMS('**********', 'Test message');
      expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
        body: 'Test message',
        from: '+**********',
        to: '+************',
      });

      await twilioService.sendSMS('*********', 'Test message');
      expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
        body: 'Test message',
        from: '+**********',
        to: '+************',
      });

      await twilioService.sendSMS('************', 'Test message');
      expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
        body: 'Test message',
        from: '+**********',
        to: '+************',
      });
    });

    test('should handle SMS sending errors', async () => {
      const error = new Error('Twilio API error');
      mockTwilioClient.messages.create.mockRejectedValue(error);

      await expect(
        twilioService.sendSMS('+************', 'Test message')
      ).rejects.toThrow('SMS sending failed: Twilio API error');
    });
  });

  describe('sendWhatsApp', () => {
    test('should send WhatsApp message successfully', async () => {
      const mockResponse = {
        sid: 'WA123456789',
        status: 'sent',
        to: 'whatsapp:+************',
        from: 'whatsapp:+**********',
        body: 'Test WhatsApp message',
        dateCreated: new Date(),
      };

      mockTwilioClient.messages.create.mockResolvedValue(mockResponse);

      const result = await twilioService.sendWhatsApp('+************', 'Test WhatsApp message');

      expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
        body: 'Test WhatsApp message',
        from: 'whatsapp:+**********',
        to: 'whatsapp:+************',
      });

      expect(result).toEqual({
        sid: 'WA123456789',
        status: 'sent',
        to: 'whatsapp:+************',
        from: 'whatsapp:+**********',
        body: 'Test WhatsApp message',
        dateCreated: mockResponse.dateCreated,
        errorCode: undefined,
        errorMessage: undefined,
      });
    });

    test('should handle WhatsApp sending errors', async () => {
      const error = new Error('WhatsApp API error');
      mockTwilioClient.messages.create.mockRejectedValue(error);

      await expect(
        twilioService.sendWhatsApp('+************', 'Test message')
      ).rejects.toThrow('WhatsApp sending failed: WhatsApp API error');
    });
  });

  describe('getMessageStatus', () => {
    test('should fetch message status successfully', async () => {
      const mockMessage = {
        sid: 'SM123456789',
        status: 'delivered',
        to: '+************',
        from: '+**********',
        body: 'Test message',
        dateCreated: new Date(),
      };

      mockTwilioClient.messages = vi.fn(() => ({
        fetch: vi.fn().mockResolvedValue(mockMessage),
      }));

      const result = await twilioService.getMessageStatus('SM123456789');

      expect(result).toEqual({
        sid: 'SM123456789',
        status: 'delivered',
        to: '+************',
        from: '+**********',
        body: 'Test message',
        dateCreated: mockMessage.dateCreated,
        errorCode: undefined,
        errorMessage: undefined,
      });
    });
  });

  describe('sendBulkSMS', () => {
    test('should send bulk SMS messages', async () => {
      const recipients = [
        { phoneNumber: '+************', message: 'Message 1' },
        { phoneNumber: '+************', message: 'Message 2' },
      ];

      const mockResponses = [
        {
          sid: 'SM123456789',
          status: 'sent',
          to: '+************',
          from: '+**********',
          body: 'Message 1',
          dateCreated: new Date(),
        },
        {
          sid: 'SM987654321',
          status: 'sent',
          to: '+************',
          from: '+**********',
          body: 'Message 2',
          dateCreated: new Date(),
        },
      ];

      mockTwilioClient.messages.create
        .mockResolvedValueOnce(mockResponses[0])
        .mockResolvedValueOnce(mockResponses[1]);

      const results = await twilioService.sendBulkSMS(recipients);

      expect(results).toHaveLength(2);
      expect(results[0].phoneNumber).toBe('+************');
      expect(results[0].result?.sid).toBe('SM123456789');
      expect(results[1].phoneNumber).toBe('+************');
      expect(results[1].result?.sid).toBe('SM987654321');
    });

    test('should handle partial failures in bulk SMS', async () => {
      const recipients = [
        { phoneNumber: '+************', message: 'Message 1' },
        { phoneNumber: '+************', message: 'Message 2' },
      ];

      const mockResponse = {
        sid: 'SM123456789',
        status: 'sent',
        to: '+************',
        from: '+**********',
        body: 'Message 1',
        dateCreated: new Date(),
      };

      mockTwilioClient.messages.create
        .mockResolvedValueOnce(mockResponse)
        .mockRejectedValueOnce(new Error('API error'));

      const results = await twilioService.sendBulkSMS(recipients);

      expect(results).toHaveLength(2);
      expect(results[0].result?.sid).toBe('SM123456789');
      expect(results[1].error).toBe('SMS sending failed: API error');
    });
  });

  describe('validatePhoneNumber', () => {
    test('should validate international phone numbers', () => {
      expect(twilioService.validatePhoneNumber('+************')).toBe(true);
      expect(twilioService.validatePhoneNumber('+**********')).toBe(true);
      expect(twilioService.validatePhoneNumber('+***********')).toBe(true);
    });

    test('should reject invalid phone numbers', () => {
      expect(twilioService.validatePhoneNumber('**********')).toBe(false);
      expect(twilioService.validatePhoneNumber('*********')).toBe(false);
      expect(twilioService.validatePhoneNumber('invalid')).toBe(false);
      expect(twilioService.validatePhoneNumber('')).toBe(false);
    });
  });

  describe('getAccountInfo', () => {
    test('should fetch account information', async () => {
      const mockAccount = {
        sid: 'test_account_sid',
        status: 'active',
      };

      const mockBalance = {
        balance: '100.00',
        currency: 'USD',
      };

      const mockAccountFetch = vi.fn().mockResolvedValue(mockAccount);
      const mockBalanceFetch = vi.fn().mockResolvedValue(mockBalance);

      mockTwilioClient.api.accounts.mockReturnValue({
        fetch: mockAccountFetch,
        balance: {
          fetch: mockBalanceFetch,
        },
      });

      const result = await twilioService.getAccountInfo();

      expect(result).toEqual({
        balance: '100.00',
        currency: 'USD',
        accountSid: 'test_account_sid',
        status: 'active',
      });
    });
  });

  describe('getRecentMessages', () => {
    test('should fetch recent messages', async () => {
      const mockMessages = [
        {
          sid: 'SM123456789',
          status: 'delivered',
          to: '+************',
          from: '+**********',
          body: 'Message 1',
          dateCreated: new Date(),
        },
        {
          sid: 'SM987654321',
          status: 'sent',
          to: '+************',
          from: '+**********',
          body: 'Message 2',
          dateCreated: new Date(),
        },
      ];

      mockTwilioClient.messages.list.mockResolvedValue(mockMessages);

      const result = await twilioService.getRecentMessages(2);

      expect(mockTwilioClient.messages.list).toHaveBeenCalledWith({ limit: 2 });
      expect(result).toHaveLength(2);
      expect(result[0].sid).toBe('SM123456789');
      expect(result[1].sid).toBe('SM987654321');
    });
  });
});